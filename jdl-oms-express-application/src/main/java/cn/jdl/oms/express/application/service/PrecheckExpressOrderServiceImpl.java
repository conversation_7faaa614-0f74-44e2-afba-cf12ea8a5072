package cn.jdl.oms.express.application.service;

import cn.jdl.batrix.spec.BApiResult;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.ModifyExpressOrderResult;
import cn.jdl.oms.express.domain.bo.PrecheckExpressOrderResult;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.ohs.translator.ApiResultHelper;
import cn.jdl.oms.express.domain.ohs.translator.PrecheckExpressOrderTranslator;
import cn.jdl.oms.express.domain.service.IPrecheckExpressOrderDomainService;
import cn.jdl.oms.express.model.CallBackExpressOrderData;
import cn.jdl.oms.express.model.CreateExpressOrderBatchResponse;
import cn.jdl.oms.express.model.ModifyExpressOrderData;
import cn.jdl.oms.express.model.PrecheckExpressOrderData;
import cn.jdl.oms.express.model.PrecheckExpressOrderRequest;
import cn.jdl.oms.express.model.PrecheckExpressOrderResponse;
import cn.jdl.oms.express.service.PrecheckExpressOrderService;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainException;
import cn.jdl.oms.express.shared.common.exception.DomainServiceException;
import cn.jdl.oms.express.shared.common.exception.ValidationDomainException;
import cn.jdl.oms.express.shared.common.exception.ValidationRequestParamException;
import cn.jdl.oms.express.shared.common.specification.JSR303Specification;
import cn.jdl.oms.express.shared.common.specification.Notification;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.jsf.gd.util.Constants;
import com.jd.jsf.gd.util.RpcContext;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/3/4 09:41:14
 * @Description 前置校验服务实现
 * @Version 1.0
 */
@Service("precheckExpressOrderServiceImpl")
public class PrecheckExpressOrderServiceImpl implements PrecheckExpressOrderService {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(PrecheckExpressOrderServiceImpl.class);

    /**
     * 修改基本信息校验
     */
    @Resource
    private JSR303Specification jsr303Specification;

    /**
     * 前置校验转领域对象
     */
    @Resource
    private PrecheckExpressOrderTranslator precheckExpressOrderTranslator;

    /**
     * 前置校验能力
     */
    @Resource
    private IPrecheckExpressOrderDomainService precheckExpressOrderDomainService;

    @Override
    public PrecheckExpressOrderResponse precheckOrder(RequestProfile requestProfile, PrecheckExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".precheckOrder"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        LOGGER.info("前置校验服务请求入参:profile={},request={}", JSONUtils.beanToJSONDefault(requestProfile), JSONUtils.beanToJSONDefault(request));
        ExpressOrderContext context = null;
        BApiResult<PrecheckExpressOrderData> bApiResult = null;
        StringBuilder businessAlarmMsg = new StringBuilder();
        try {
            businessAlarmMsg.append("客户端调用系统名称:").append(RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME))
                    .append(",服务端调用IP:").append(RpcContext.getContext().getLocalAddress())
                    .append(",链路追踪ID:").append(requestProfile.getTraceId())
                    .append(",业务身份:").append(request.getBusinessIdentity().getBusinessUnit())
                    .append(",业务类型:").append(request.getBusinessIdentity().getBusinessType())
                    .append(",业务场景:前置校验").append(request.getBusinessIdentity().getBusinessScene())
                    .append(",异常信息：");
            // 前置校验
            Notification notification = Notification.create();
            if (!jsr303Specification.isSatisfiedBy(requestProfile, notification)
                    || !jsr303Specification.isSatisfiedBy(request, notification)) {
                LOGGER.error("前置校验基本信息校验入参非法:notification= {}", notification.first().getValue());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withVars(notification.first().getValue())
                        .putExt(DomainException.VALIDATE_FAIL_FIELD, notification.first().getKey());
            }
            // 前置校验领域模型初始化
            context = expressOrderModelOf(requestProfile, request);
            // 前置校验领域服务
            PrecheckExpressOrderResult precheckExpressOrderResult = precheckExpressOrderDomainService.precheckOrder(context);
            if (null == precheckExpressOrderResult) {
                LOGGER.error("前置校验服务处理异常返回参为空,ModifyExpressOrderResult is null");
                throw new DomainServiceException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR);
            }
            PrecheckExpressOrderData precheckExpressOrderData = new PrecheckExpressOrderData();
            precheckExpressOrderData.setCode(precheckExpressOrderResult.getCode());
            precheckExpressOrderData.setOrderNo(request.getOrderNo());
            precheckExpressOrderData.setExtendProps(precheckExpressOrderResult.getExtendProps());
            bApiResult = BApiResult.ofSuccess(precheckExpressOrderData);
        } catch (BusinessDomainException e) {
            // 前置校验服务，业务异常
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(requestProfile, e);
            LOGGER.error("前置校验服务领域服务处理异常,traceId={},BusinessDomainException", requestProfile.getTraceId(), e);
        } catch (DomainException e) {
            // 领域服务异常
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(requestProfile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_PRECHECK_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis(),
                    businessAlarmMsg.append("前置校验服务领域服务处理异常:").append(e.getMessage()).toString());
            LOGGER.error("前置校验服务可用率异常，前置校验服务领域服务处理异常,traceId={},DomainException", requestProfile.getTraceId(), e);
        } catch (Exception e) {
            //未知异常
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(requestProfile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_PRECHECK_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis(),
                    businessAlarmMsg.append("前置校验服务未知异常Exception:").append(e.getMessage()).toString());
            LOGGER.error("前置校验服务可用率异常，前置校验服务未知异常,traceId={},Exception", requestProfile.getTraceId(), e);
        } catch (Throwable throwable) {
            //未知异常
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(requestProfile, throwable);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_PRECHECK_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis(),
                    businessAlarmMsg.append("前置校验服务未知异常throwable:").append(throwable.getMessage()).toString());
            LOGGER.error("前置校验服务可用率异常，前置校验服务未知异常,traceId={},throwable", requestProfile.getTraceId(), throwable);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }

        //封装最终返回结果数据
        PrecheckExpressOrderResponse response = new PrecheckExpressOrderResponse();
        if (null != bApiResult) {
            response.setCode(bApiResult.getCode());
            response.setData(bApiResult.getData());
            response.setMessage(bApiResult.getMessage());
        }
        LOGGER.info("前置校验服务处理完成返回结果:response={}", JSONUtils.beanToJSONDefault(response));
        return response;
    }

    /**
     * 初始化前置校验领域模型
     * @param requestProfile
     * @param request
     * @return
     */
    private ExpressOrderContext expressOrderModelOf(RequestProfile requestProfile, PrecheckExpressOrderRequest request) {
        ExpressOrderModel orderModel = precheckExpressOrderTranslator.translator(requestProfile, request);
        //默认垂直业务身份
        orderModel.setYId("JDL");
        //领域模型上线文
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), requestProfile,
                orderModel.getOrderBusinessIdentity().getBusinessScene());
        context.setOrderModel(orderModel);

        return context;
    }
}
