package cn.jdl.oms.express.application.service;

import cn.jdl.batrix.spec.BApiResult;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CallBackExpressOrderResult;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.model.SendOrderTrack;
import cn.jdl.oms.express.domain.ohs.translator.ApiResultHelper;
import cn.jdl.oms.express.domain.ohs.translator.CallBackExpressOrderTranslator;
import cn.jdl.oms.express.domain.service.ICallBackExpressOrderDomainService;
import cn.jdl.oms.express.model.CallBackExpressOrderData;
import cn.jdl.oms.express.model.CallBackExpressOrderRequest;
import cn.jdl.oms.express.model.CallBackExpressOrderResponse;
import cn.jdl.oms.express.service.CallBackExpressOrderService;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainException;
import cn.jdl.oms.express.shared.common.exception.DomainServiceException;
import cn.jdl.oms.express.shared.common.exception.ValidationDomainException;
import cn.jdl.oms.express.shared.common.exception.ValidationRequestParamException;
import cn.jdl.oms.express.shared.common.specification.JSR303Specification;
import cn.jdl.oms.express.shared.common.specification.Notification;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.jsf.gd.util.Constants;
import com.jd.jsf.gd.util.RpcContext;
import com.jd.traceholder.TraceHolder;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Package： cn.jdl.oms.express.application.service
 * @ClassName: CallBackExpressOrderServiceImpl
 * @Description: 纯配订单中心业务回传
 * @Author： wangjingzhao
 * @CreateDate 2021/3/16 10:22 上午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Service("callBackExpressOrderServiceImpl")
public class CallBackExpressOrderServiceImpl implements CallBackExpressOrderService {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CallBackExpressOrderServiceImpl.class);

    /**
     * 回传能力
     */
    @Resource
    private ICallBackExpressOrderDomainService expressOrderDomainService;
    /**
     * 回传请求参数转换为领域模型model
     */
    @Resource
    private CallBackExpressOrderTranslator expressOrderTranslator;
    /**
     * 基本信息校验
     */
    @Resource
    private JSR303Specification jsr303Specification;
    /**
     * 回传记录消息发送
     */
    @Resource
    private JMQMessageProducer callbackRecordProducer;
    /**
     * 配置中心
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    /**
     * 纯配订单中心回传服务
     *
     * @param profile 用于处理国际化多语言、多租户、业务身份以及未来扩展的需求
     * @param request 纯配回传服务申请入参对象
     * @return
     * <AUTHOR>
     */
    @Override
    public CallBackExpressOrderResponse callBackOrder(@NotNull RequestProfile profile, @NotNull CallBackExpressOrderRequest request) {
        String umpKey = this.getClass().getName() + ".callBackOrder";
        //fixme 【压测】判断是否是压测流量，umpKey增加后缀
        if (TraceHolder.isForcebot()) {
            LOGGER.warn("回传流量含forceBot标识");
            umpKey = umpKey + "_forceBot";
        }

        CallerInfo callerInfo = Profiler.registerInfo(umpKey
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        LOGGER.info("纯配回传服务请求入参:profile={},request={}", JSONUtils.beanToJSONDefault(profile), JSONUtils.beanToJSONDefault(request));
        ExpressOrderContext context = null;
        BApiResult<CallBackExpressOrderData> apiResult = null;
        CallBackExpressOrderResponse response = new CallBackExpressOrderResponse();
        StringBuilder businessAlarmMsg = new StringBuilder();
        try {
            businessAlarmMsg.append("客户端调用系统名称:").append(RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME))
                    .append(",服务端调用IP:").append(RpcContext.getContext().getLocalAddress())
                    .append(",链路追踪ID:").append(profile.getTraceId())
                    .append(",租户:").append(profile.getTenantId())
                    .append(",业务身份:").append(request.getBusinessIdentity().getBusinessUnit())
                    .append(",业务类型:").append(request.getBusinessIdentity().getBusinessType())
                    .append(",业务场景:回传").append(request.getBusinessIdentity().getBusinessScene())
                    .append(",异常信息：");
            //回传前置校验
            Notification notification = Notification.create();
            if (!jsr303Specification.isSatisfiedBy(profile, notification)
                    || !jsr303Specification.isSatisfiedBy(request, notification)) {
                LOGGER.error("纯配回传服务校验基本信息校验入参非法:notification= {}", notification.first());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withVars(notification.first().getValue())
                        //返回具体校验失败字段
                        .putExt(DomainException.VALIDATE_FAIL_FIELD, notification.first().getKey());
            }
            //回传领域模型初始化
            context = this.expressOrderModelOf(profile, request);
            CallBackExpressOrderResult callBackExpressOrderResult = expressOrderDomainService.callBackOrder(context);
            if (callBackExpressOrderResult == null) {
                LOGGER.error("纯配回传服务处理异常返回参为空,callBackExpressOrderResult is null");
                throw new DomainServiceException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR);
            }
            //LOGGER.info("纯配回传领域服务处理完成返回参: callBackExpressOrderResult={}", JSONUtils.beanToJSONDefault(callBackExpressOrderResult));
            CallBackExpressOrderData callBackExpressOrderData = new CallBackExpressOrderData();
            callBackExpressOrderData.setCode(callBackExpressOrderResult.getCode());
            callBackExpressOrderData.setExtendProps(callBackExpressOrderResult.getExtendProps());
            //LOGGER.info("纯配回传API服务处理完成返回参:callBackExpressOrderData={}", JSONUtils.beanToJSONDefault(callBackExpressOrderData));
            apiResult = BApiResult.ofSuccess(callBackExpressOrderData);
            if (MapUtils.isNotEmpty(callBackExpressOrderData.getExtendProps())) {
                Map<String, String> extendProps = callBackExpressOrderData.getExtendProps();
                if (extendProps.containsKey(EnquiryConstants.OVER_LENGTH_AND_WEIGHT_ATTACH_FEE)) {
                    apiResult.putExt(EnquiryConstants.OVER_LENGTH_AND_WEIGHT_ATTACH_FEE, extendProps.get(EnquiryConstants.OVER_LENGTH_AND_WEIGHT_ATTACH_FEE));
                }
            }
        } catch (BusinessDomainException e) {
            // 业务异常
            apiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , businessAlarmMsg.append("纯配回传服务领域服务处理业务异常:").append(e.getMessage()).toString());
            LOGGER.error("纯配回传服务领域服务处理业务异常,exception:{}", e.fullMessage());
        } catch (DomainException e) {
            // 领域服务异常
            Profiler.functionError(callerInfo);
            apiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            LOGGER.error("回传可用率异常，纯配回传服务领域服务处理异常,DomainException: ", e);
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            apiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            LOGGER.error("回传可用率异常，纯配回传服务处理异常,Exception", e);
        } catch (Throwable throwable) {
            Profiler.functionError(callerInfo);
            apiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, throwable);
            LOGGER.error("回传可用率异常，纯配回传服务处理异常,Throwable", throwable);
        } finally {
            if (context != null && context.getUnblockedExceptions() != null && !context.getUnblockedExceptions().isEmpty()) {
                LOGGER.info("回传可用率异常，存在不阻塞回传流程的服务异常，需体现回传可用率打点，unblockedExceptions={}", JSONUtils.beanToJSONDefault(context.getUnblockedExceptions()));
                Profiler.functionError(callerInfo);
            }
            // 封装最终返回结果数据
            try {
                toCallBackExpressOrderResponse(response, apiResult);
            } catch (Exception e) {
                Profiler.functionError(callerInfo);
                LOGGER.error("回传可用率异常，纯配回传服务出参对象转换异常", e);
            }
            Profiler.registerInfoEnd(callerInfo);
        }

        expressOrderFlowService.sendCallbackOrderRecordMq(context, apiResult, request);
        LOGGER.info("纯配回传API服务处理完成返回结果:apiResult={}", JSONUtils.beanToJSONDefault(response));
        return response;
    }

    /**
     * 回传结果信息转换
     *
     * @param response
     * @param apiResult
     * @return
     */
    private void toCallBackExpressOrderResponse(CallBackExpressOrderResponse response, BApiResult<CallBackExpressOrderData> apiResult) {

        if (null != apiResult) {
            response.setCode(apiResult.getCode());
            if (apiResult.getData() == null) {
                CallBackExpressOrderData callBackExpressOrderData = new CallBackExpressOrderData();
                callBackExpressOrderData.setExtendProps(apiResult.getExt());
                response.setData(callBackExpressOrderData);
            } else {
                response.setData(apiResult.getData());
                response.getData().setExtendProps(apiResult.getExt());
            }
            response.setMessage(apiResult.getMessage());
            response.setExtendProps(apiResult.getExt());
        }
    }


    /**
     * 初始化回传领域模型
     *
     * @param profile
     * @param request
     * @return
     */
    private ExpressOrderContext expressOrderModelOf(RequestProfile profile, CallBackExpressOrderRequest request) {
        if (null == profile) {
            LOGGER.error("初始化回传领域模型业务身份识别对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("参数校验失败");
        }
        if (null == request) {
            LOGGER.error("初始化回传领域模型申请入参对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("参数校验失败");
        }
        ExpressOrderModel orderModel = expressOrderTranslator.translator(profile, request);
        //默认垂直业务身份
        orderModel.setYId("JDL");
        //领域模型上线文
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), profile,
                orderModel.getOrderBusinessIdentity().getBusinessScene());
        context.setOrderModel(orderModel);
        SendOrderTrack sendOrderTrack = expressOrderTranslator.translator(request);
        context.setSendOrderTrack(sendOrderTrack);
        return context;
    }

}
