package cn.jdl.oms.express.application.service;

import cn.jdl.batrix.spec.BApiResult;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CancelExpressOrderResult;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.ohs.translator.ApiResultHelper;
import cn.jdl.oms.express.domain.ohs.translator.CancelExpressOrderTranslator;
import cn.jdl.oms.express.domain.service.ICancelExpressOrderDomainService;
import cn.jdl.oms.express.model.CancelExpressOrderBatchRequest;
import cn.jdl.oms.express.model.CancelExpressOrderBatchResponse;
import cn.jdl.oms.express.model.CancelExpressOrderData;
import cn.jdl.oms.express.model.CancelExpressOrderRequest;
import cn.jdl.oms.express.model.CancelExpressOrderResponse;
import cn.jdl.oms.express.service.CancelExpressOrderService;
import cn.jdl.oms.express.shared.common.constant.ABConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.ApplicationDomainException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainException;
import cn.jdl.oms.express.shared.common.exception.ValidationDomainException;
import cn.jdl.oms.express.shared.common.exception.ValidationRequestParamException;
import cn.jdl.oms.express.shared.common.specification.JSR303Specification;
import cn.jdl.oms.express.shared.common.specification.Notification;
import cn.jdl.oms.express.shared.common.thread.MdcThreadPoolExecutor;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.google.common.collect.Lists;
import com.jd.jsf.gd.util.Constants;
import com.jd.jsf.gd.util.RpcContext;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * @Package： cn.jdl.oms.express.application.service
 * @ClassName: CancelExpressOrderServiceImpl
 * @Description: 纯配订单中心订单取消
 * @Author： wangjingzhao
 * @CreateDate 2021/3/16 10:22 上午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Service("cancelExpressOrderServiceImpl")
public class CancelExpressOrderServiceImpl implements CancelExpressOrderService {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CancelExpressOrderServiceImpl.class);

    /**
     * 取消能力
     */
    @Resource
    private ICancelExpressOrderDomainService cancelExpressOrderDomainService;
    /**
     * 取消请求参数转换为领域模型model
     */
    @Resource
    private CancelExpressOrderTranslator expressOrderTranslator;
    /**
     * 取消基本信息校验
     */
    @Resource
    private JSR303Specification jsr303Specification;
    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;

    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    /**
     * 批量执行线程池
     */
    private MdcThreadPoolExecutor threadPoolExecutor;

    /**
     * 核心线程大小
     */
    @Value("${cancel.express.batch.order.thread.corePoolSize:10}")
    private int corePoolSize = 10;

    /**
     * 线程池大小
     */
    @Value("${cancel.express.batch.order.thread.threadPoolSize:50}")
    private int threadPoolSize = 50;

    /**
     * 线程队列大小
     */
    @Value("${cancel.express.batch.order.thread.threadQueueSize:10000}")
    private int threadQueueSize = 10000;


    /**
     *
     *
     * @Description 线城池初始化
     *
     * <AUTHOR>
     *
     * @ModifyDate  2021/6/10 11:33 上午
     *
     * @param
     *
     * @return
     *
     * @exception
     *
     * @throws
     *
     * @lastModify
     *
     */
    @PostConstruct
    public void init() {
        //开启多线程并行查询服务
        threadPoolExecutor = MdcThreadPoolExecutor.newWithInheritedMdc(corePoolSize, threadPoolSize, 60L, TimeUnit.SECONDS
                , (threadQueueSize > 0) ? new ArrayBlockingQueue<Runnable>(threadQueueSize,true) : new LinkedBlockingQueue<Runnable>());
        LOGGER.info("纯配批量取消服务线程池初始化成功:corePoolSize= {},threadPoolSize={}, threadQueueSize={} ", corePoolSize, threadPoolSize, threadQueueSize);
    }

    /**
     * 纯配订单中心取消服务
     *
     * @param profile
     * @param request 纯配取消服务申请入参对象
     * @return
     * <AUTHOR>
     */
    @Override
    public CancelExpressOrderResponse cancelOrder(@NotNull RequestProfile profile, @NotNull CancelExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".cancelOrder"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        LOGGER.info("纯配取消API服务请求入参:profile={},request={}", JSONUtils.beanToJSONDefault(profile), JSONUtils.beanToJSONDefault(request));
        ExpressOrderContext context = null;
        BApiResult<CancelExpressOrderData> bApiResult = null;
        CancelExpressOrderResponse response = new CancelExpressOrderResponse();
        StringBuilder businessAlarmMsg = new StringBuilder();
        try {
            businessAlarmMsg.append("客户端调用系统名称:").append(RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME))
                    .append(",服务端调用IP:").append(RpcContext.getContext().getLocalAddress())
                    .append(",链路追踪ID:").append(profile.getTraceId())
                    .append(",租户:").append(profile.getTenantId())
                    .append(",业务身份:").append(request.getBusinessIdentity().getBusinessUnit())
                    .append(",业务类型:").append(request.getBusinessIdentity().getBusinessType())
                    .append(",业务场景:取消").append(request.getBusinessIdentity().getBusinessScene())
                    .append(",异常信息：");
            // 前置校验
            Notification notification = Notification.create();
            if (!jsr303Specification.isSatisfiedBy(profile, notification)
                    || !jsr303Specification.isSatisfiedBy(request, notification)) {
                LOGGER.error("纯配取消服务基本信息校验入参非法校验失败: notification= {}", notification.first());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withVars(notification.first().getValue())
                        //返回具体校验失败字段
                        .putExt(DomainException.VALIDATE_FAIL_FIELD, notification.first().getKey());
            }
            //设置AB环境标识
            Optional.ofNullable(RpcContext.getContext().getAttachment(ABConstants.ABENVIRONMENT_FLAG)).ifPresent(env -> {
                Map<String, String> extendProps = Optional.ofNullable(request.getExtendProps()).orElse(new HashMap<>());
                extendProps.put(ABConstants.ABENVIRONMENT_FLAG, String.valueOf(RpcContext.getContext().getAttachment(ABConstants.ABENVIRONMENT_FLAG)));
                request.setExtendProps(extendProps);
            });
            //取消领域模型初始化
            context = expressOrderModelOf(profile, request);
            //默认垂直业务身份
            CancelExpressOrderResult cancelExpressOrderResult = cancelExpressOrderDomainService.cancelOrder(context);
            CancelExpressOrderData cancelExpressOrderData = new CancelExpressOrderData();
            cancelExpressOrderData.setCode(cancelExpressOrderResult.getCode());
            cancelExpressOrderData.setOrderNo(request.getOrderNo());
            cancelExpressOrderData.setExtendProps(cancelExpressOrderResult.getExtendProps());
            //LOGGER.info("纯配取消API服务处理完成返回参:cancelExpressOrderData={}", JSONUtils.beanToJSONDefault(cancelExpressOrderData));
            bApiResult = BApiResult.ofSuccess(cancelExpressOrderData);
        } catch (BusinessDomainException e) {
            // 业务异常
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , businessAlarmMsg.append("纯配取消服务领域服务处理业务异常:").append(e.getMessage()).toString());
            LOGGER.error("纯配取消服务领域服务处理业务异常,BusinessDomainException: {}", e.fullMessage());
        } catch (DomainException e) {
            // 领域服务异常
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            LOGGER.error("取消可用率异常，纯配取消服务领域服务处理异常,DomainException", e);
        } catch (Exception e) {
            // 未知异常
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            LOGGER.error("取消可用率异常，纯配取消服务未知异常,Exception", e);
        } catch (Throwable throwable) {
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, throwable);
            LOGGER.error("取消可用率异常，纯配取消服务未知异常,Throwable", throwable);
        } finally {
            try {
                toCancelExpressOrderResponse(response, request, bApiResult);
            } catch (Exception e){
                Profiler.functionError(callerInfo);
                LOGGER.error("取消可用率异常，纯配取消服务出参对象转换异常",e);
            }
            Profiler.registerInfoEnd(callerInfo);
        }

        // 封装最终返回结果数据
        expressOrderFlowService.sendCancelOrderRecordMq(context, bApiResult, request);
        LOGGER.info("纯配取消API服务处理完成返回结果:response={}", JSONUtils.beanToJSONDefault(response));
        return response;
    }

    /**
     * 取消结果信息转换
     *
     * @param response
     * @param request
     * @param bApiResult
     * @return
     */
    private void toCancelExpressOrderResponse(CancelExpressOrderResponse response, CancelExpressOrderRequest request, BApiResult<CancelExpressOrderData> bApiResult) {
            if (null != bApiResult) {
                response.setCode(bApiResult.getCode());
                if (bApiResult.getData() == null) {
                    CancelExpressOrderData cancelExpressOrderData = new CancelExpressOrderData();
                    cancelExpressOrderData.setOrderNo(request.getOrderNo());
                    cancelExpressOrderData.setExtendProps(bApiResult.getExt());
                    response.setData(cancelExpressOrderData);
                } else {
                    response.setData(bApiResult.getData());
                    response.getData().setExtendProps(bApiResult.getExt());
                }
                response.setMessage(bApiResult.getMessage());
                response.setExtendProps(bApiResult.getExt());
            }
    }

    /**
     *
     *
     * @Description 
     *
     * <AUTHOR>
     *
     * @createDate  2021/6/10 3:01 下午 
     *
     * @param  
     *
     * @return 
     *
     * @exception 
     *
     * @throws
     *
     * @lastCancel
     *
     */
    @Override
    public CancelExpressOrderBatchResponse cancelBatchOrder(RequestProfile profile, CancelExpressOrderBatchRequest batchRequest) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".cancelBatchOrder"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        LOGGER.info("纯配批量取消服务请求入参:profile={},batchRequest={}", JSONUtils.beanToJSONDefault(profile), JSONUtils.beanToJSONDefault(batchRequest));
        //批量返回执行结果
        CancelExpressOrderBatchResponse orderBatchResponse = null;
        //批量执行返回结果
        BApiResult<List<CancelExpressOrderResponse>> bApiResult = null;

        /**
         *
         *
         * @Description CancelBatchOrder
         *
         * <AUTHOR>
         *
         * @CancelDate  2021/6/10 12:28 下午
         *
         * @param  [profile, batchRequest]
         *
         * @return cn.jdl.oms.express.model.CancelExpressOrderResponse
         *
         * @exception
         *
         * @throws
         *
         * @lastCancel
         *
         */
        class CancelExpressOrderCallable implements Callable {

            /**
             * 业务
             */
            private RequestProfile requestProfile;

            /**
             * 纯配取消申请对象
             */
            private CancelExpressOrderRequest request;

            /**
             * 有参构造函数
             * @param requestProfile
             * @param request
             */
            public CancelExpressOrderCallable(RequestProfile requestProfile, CancelExpressOrderRequest request) {
                this.requestProfile = requestProfile;
                this.request = request;
            }

            /**
             * Computes a result, or throws an exception if unable to do so.
             *
             * @return computed result
             * @throws Exception if unable to compute a result
             */
            @Override
            public Object call() throws Exception {
                return cancelOrder(requestProfile, request);
            }
        }

        try {
            if (null == batchRequest) {
                LOGGER.error("纯配批量取消服务校验基本信息校验入参非法:batchRequest= {}", batchRequest);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_ALARM_MONITOR
                        , System.currentTimeMillis()
                        , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                                + ","
                                + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                                + ","
                                + "链路追踪ID:" + profile.getTraceId()
                                + ","
                                + "申请租户:" + profile.getTenantId());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL);
            }
            List<CancelExpressOrderRequest> requests = batchRequest.getBatchRequest();
            if (CollectionUtils.isEmpty(requests)) {
                LOGGER.error("纯配批量取消服务校验基本信息校验入参非法:requests= {}", requests);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_ALARM_MONITOR
                        , System.currentTimeMillis()
                        , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                                + ","
                                + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                                + ","
                                + "链路追踪ID:" + profile.getTraceId()
                                + ","
                                + "申请租户:" + profile.getTenantId());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL);
            }
            //初始化取消返回对象集合
            List<Future<CancelExpressOrderResponse>> futureTasks = new ArrayList<>(requests.size());
            for (CancelExpressOrderRequest request : requests) {
                if (null == request) {
                    continue;
                }
                //设置AB环境标识
                Optional.ofNullable(RpcContext.getContext().getAttachment(ABConstants.ABENVIRONMENT_FLAG)).ifPresent(env -> {
                    Map<String, String> extendProps = Optional.ofNullable(request.getExtendProps()).orElse(new HashMap<>());
                    extendProps.put(ABConstants.ABENVIRONMENT_FLAG, String.valueOf(RpcContext.getContext().getAttachment(ABConstants.ABENVIRONMENT_FLAG)));
                    request.setExtendProps(extendProps);
                });
                Future<CancelExpressOrderResponse> future = threadPoolExecutor.submit(new CancelExpressOrderCallable(profile, request));
                if (null != future) {
                    futureTasks.add(future);
                }
            }
            //批量出线程执行结果返回
            if (CollectionUtils.isEmpty(futureTasks)) {
                LOGGER.error("纯配批量取消服务领域服务处理异常执行结果为空,futureTasks={}",futureTasks);
                throw new ApplicationDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR);
            }
            List<CancelExpressOrderResponse> responses = Lists.newArrayListWithCapacity(requests.size());
            for (Future<CancelExpressOrderResponse> future : futureTasks) {
                if (null != future.get()) {
                    LOGGER.info("纯配批量取消服务领域服务结果,future={}",JSONUtils.beanToJSONDefault(future.get()));
                    responses.add(future.get());
                }
            }
            bApiResult = BApiResult.ofSuccess(responses);
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_CANCEL_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                            + ","
                            + "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "纯配批量取消服务处理异常原因:" + e.getMessage());
            LOGGER.error("纯配批量取消服务处理异常,Exception", e);
        } catch (Throwable throwable) {
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, throwable);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_CANCEL_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                            + ","
                            + "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "纯配批量取消服务处理异常原因:" + throwable.getMessage());
            LOGGER.error("纯配批量取消服务处理异常,throwable", throwable);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        //封装最终返回结果数据
        if (null != bApiResult) {
            orderBatchResponse = new CancelExpressOrderBatchResponse();
            orderBatchResponse.setCode(bApiResult.getCode());
            orderBatchResponse.setData(bApiResult.getData());
            orderBatchResponse.setMessage(bApiResult.getMessage());
        }
        LOGGER.info("纯配批量取消API服务处理完成返回结果:orderBatchResponse={}", JSONUtils.beanToJSONDefault(orderBatchResponse));
        return orderBatchResponse;
    }

   

    /**
     * 初始化取消领域模型
     *
     * @param profile
     * @param request
     * @return
     */
    private ExpressOrderContext expressOrderModelOf(RequestProfile profile, CancelExpressOrderRequest request) {
        if (null == profile) {
            LOGGER.error("初始化取消领域模型业务身份识别对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("参数校验失败");
        }
        if (null == request) {
            LOGGER.error("初始化取消领域模型申请入参对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("参数校验失败");
        }
        ExpressOrderModel orderModel = expressOrderTranslator.translator(profile, request);
        //默认垂直业务身份
        orderModel.setYId("JDL");
        //领域模型上线文
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), profile,
                orderModel.getOrderBusinessIdentity().getBusinessScene());
        context.setOrderModel(orderModel);
        //初始化，下发履约执行层打标 目前仅支持下发一个履约层，下发时识别使用
        Set<String> promiseUnits = makingDispatcherHandler.execute(context);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //初始化，下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        return context;
    }

}
