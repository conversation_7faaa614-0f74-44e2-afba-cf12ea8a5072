package cn.jdl.oms.express.application.service;

import cn.jdl.batrix.spec.BApiResult;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;

import cn.jdl.oms.express.domain.bo.ReacceptExpressOrderResult;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.ohs.translator.ApiResultHelper;

import cn.jdl.oms.express.domain.ohs.translator.ReacceptExpressOrderTranslator;
import cn.jdl.oms.express.domain.service.IReacceptExpressOrderDomainService;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.model.ReacceptExpressOrderData;
import cn.jdl.oms.express.model.ReacceptExpressOrderRequest;
import cn.jdl.oms.express.model.ReacceptExpressOrderResponse;
import cn.jdl.oms.express.service.ReacceptExpressOrderService;
import cn.jdl.oms.express.shared.common.constant.ABConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AntiConcurrentException;
import cn.jdl.oms.express.shared.common.exception.ApplicationDomainException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainException;
import cn.jdl.oms.express.shared.common.exception.DomainServiceException;
import cn.jdl.oms.express.shared.common.exception.ValidationDomainException;
import cn.jdl.oms.express.shared.common.exception.ValidationRequestParamException;
import cn.jdl.oms.express.shared.common.specification.JSR303Specification;
import cn.jdl.oms.express.shared.common.specification.Notification;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.jsf.gd.util.Constants;
import com.jd.jsf.gd.util.RpcContext;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * @ProjectName：jdl-oms-express
 * @Package： cn.jdl.oms.express.application.service
 * @ClassName: ReacceptExpressOrderServiceImpl
 * @Description: 纯配订单中心重受理对外服务
 * @Author： jiangwei279
 * @CreateDate 2023/8/7 18:19
 * @Copyright: Copyright (c)2023 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
/**
 * @Description:
 */
@Service("reacceptExpressOrderServiceImpl")
public class ReacceptExpressOrderServiceImpl implements ReacceptExpressOrderService {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ReacceptExpressOrderServiceImpl.class);
    /**
     * 重处理基本信息校验
     */
    @Resource
    private JSR303Specification jsr303Specification;

    /**
     * dto对象转业务对象
     */
    @Resource
    private ReacceptExpressOrderTranslator reacceptExpressOrderTranslator;

    /**
     * 重处理能力
     */
    @Autowired
    private IReacceptExpressOrderDomainService reacceptExpressOrderDomainService;

    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;

    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    /**
     * @Description 纯配订单重处理服务
     */
    @Override
    public ReacceptExpressOrderResponse reacceptOrder(RequestProfile profile, ReacceptExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".reacceptOrder"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        LOGGER.info("纯配重处理服务请求入参:profile={},request={}", JSONUtils.beanToJSONDefault(profile), JSONUtils.beanToJSONDefault(request));
        ReacceptExpressOrderResponse response = null;
        ExpressOrderContext context = null;
        BApiResult<ReacceptExpressOrderData> bApiResult = null;
        try {
            //重处理前置校验
            Notification notification = Notification.create();
            if (!jsr303Specification.isSatisfiedBy(profile, notification)
                    || !jsr303Specification.isSatisfiedBy(request, notification)) {
                LOGGER.error("纯配重处理服务校验基本信息校验入参非法:notification= {}", notification.first().getValue());
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_ALARM_MONITOR
                        , System.currentTimeMillis()
                        , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                                + ","
                                + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                                + ","
                                + "链路追踪ID:" + profile.getTraceId()
                                + ","
                                + "申请租户:" + profile.getTenantId()
                                + ","
                                + "业务身份:" + request.getBusinessIdentity().getBusinessUnit()
                                + ","
                                + "业务类型:" + request.getBusinessIdentity().getBusinessType()
                                + ","
                                + "纯配重处理服务校验基本信息校验入参非法校验失败原因:" + notification.first());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withVars(notification.first().getValue())
                        //返回具体校验失败字段
                        .putExt(DomainException.VALIDATE_FAIL_FIELD, notification.first().getKey());
            }
            //设置AB环境标识
            Optional.ofNullable(RpcContext.getContext().getAttachment(ABConstants.ABENVIRONMENT_FLAG)).ifPresent(env -> {
                Map<String, String> extendProps = Optional.ofNullable(request.getExtendProps()).orElse(new HashMap<>());
                extendProps.put(ABConstants.ABENVIRONMENT_FLAG, String.valueOf(RpcContext.getContext().getAttachment(ABConstants.ABENVIRONMENT_FLAG)));
                request.setExtendProps(extendProps);
            });
            //重处理领域模型初始化
            context = expressOrderModelOf(profile, request);
            //重处理领域服务
            ReacceptExpressOrderResult reacceptExpressOrderResult = reacceptExpressOrderDomainService.reacceptOrder(context);
            if (null == reacceptExpressOrderResult) {
                LOGGER.error("纯配重处理服务处理异常返回参为空,reacceptExpressOrderResult is null");
                throw new DomainServiceException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR);
            }
            LOGGER.info("纯配重处理领域服务处理完成返回参: reacceptExpressOrderResult={}", JSONUtils.beanToJSONDefault(reacceptExpressOrderResult));
            ReacceptExpressOrderData reacceptExpressOrderData = new ReacceptExpressOrderData();
            reacceptExpressOrderData.setCode(reacceptExpressOrderResult.getCode());
            reacceptExpressOrderData.setExtendProps(reacceptExpressOrderResult.getExtendProps());
            LOGGER.info("纯配重处理API服务处理完成返回参:reacceptExpressOrderResult={}", JSONUtils.beanToJSONDefault(reacceptExpressOrderData));
            bApiResult = BApiResult.ofSuccess(reacceptExpressOrderData);
        } catch (ValidationRequestParamException e) {
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "业务身份:" + request.getBusinessIdentity().getBusinessUnit()
                            + ","
                            + "业务类型:" + request.getBusinessIdentity().getBusinessType()
                            + ","
                            + "纯配重处理服务请求入参校验处理异常原因:" + e.getMessage());
            LOGGER.error("纯配重处理服务校验处理异常,traceId={},ValidationRequestParamException", profile.getTraceId(), e);
        } catch (ValidationDomainException e) {
            //Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().buildBizFailure(profile, e.getDooErrorSpec().code(), e.getMessage());
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "业务身份:" + request.getBusinessIdentity().getBusinessUnit()
                            + ","
                            + "业务类型:" + request.getBusinessIdentity().getBusinessType()
                            + ","
                            + "纯配重处理服务校验处理异常原因:" + e.getMessage());
            LOGGER.error("纯配重处理服务校验处理异常,traceId={},ValidationDomainException", profile.getTraceId(), e);
        } catch (AntiConcurrentException e) {
            //重复提交，不异常打点
            //Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().buildBizFailure(profile, e.getDooErrorSpec().code(), e.getMessage());
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REACCEPT_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "业务身份:" + request.getBusinessIdentity().getBusinessUnit()
                            + ","
                            + "业务类型:" + request.getBusinessIdentity().getBusinessType()
                            + ","
                            + "纯配重处理服务领域活动处理异常原因:" + e.getMessage());
            LOGGER.error("纯配重处理服务处理异常-重复提交,处理异常,AntiConcurrentException: {}", e.fullMessage());
        } catch (ApplicationDomainException e) {
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REACCEPT_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "业务身份:" + request.getBusinessIdentity().getBusinessUnit()
                            + ","
                            + "业务类型:" + request.getBusinessIdentity().getBusinessType()
                            + ","
                            + "纯配重处理服务领域活动处理异常原因:" + e.getMessage());
            LOGGER.error("纯配重处理服务处理异常,ApplicationDomainException: ", e);
        } catch (DomainException e) {
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REACCEPT_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "业务身份:" + request.getBusinessIdentity().getBusinessUnit()
                            + ","
                            + "业务类型:" + request.getBusinessIdentity().getBusinessType()
                            + ","
                            + "纯配重处理服务领域服务处理异常原因:" + e.getMessage());
            LOGGER.error("纯配重处理服务领域服务处理异常,DomainException: ", e);
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REACCEPT_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "业务身份:" + request.getBusinessIdentity().getBusinessUnit()
                            + ","
                            + "业务类型:" + request.getBusinessIdentity().getBusinessType()
                            + ","
                            + "纯配重处理服务处理异常原因:" + e.getMessage());
            LOGGER.error("纯配重处理服务处理异常,Exception: ", e);
        } catch (Throwable throwable) {
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, throwable);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REACCEPT_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "业务身份:" + request.getBusinessIdentity().getBusinessUnit()
                            + ","
                            + "业务类型:" + request.getBusinessIdentity().getBusinessType()
                            + ","
                            + "纯配重处理服务处理异常原因:" + throwable.getMessage());
            LOGGER.error("纯配重处理服务处理异常,throwable", throwable);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }

        //封装最终返回结果数据
        if (null != bApiResult) {
            response = new ReacceptExpressOrderResponse();
            response.setCode(bApiResult.getCode());
            if (bApiResult.getData() == null) {
                ReacceptExpressOrderData reacceptExpressOrderData = new ReacceptExpressOrderData();
                reacceptExpressOrderData.setExtendProps(bApiResult.getExt());
                response.setData(reacceptExpressOrderData);
            } else {
                response.setData(bApiResult.getData());
                response.getData().setExtendProps(bApiResult.getExt());
            }
            //批量返回当前序列号识别行级执行结果
            if (MapUtils.isNotEmpty(request.getExtendProps())) {
                //当前执行级序号
                String sequenceNo = request.getExtendProps().get(AttachmentKeyEnum.SEQUENCE_NO.getKey());
                if (StringUtils.isNotBlank(sequenceNo)) {
                    response.getData().getExtendProps().put(AttachmentKeyEnum.SEQUENCE_NO.getKey(), sequenceNo);
                }
            }
            response.setMessage(bApiResult.getMessage());
            response.setExtendProps(bApiResult.getExt());
        }
        expressOrderFlowService.sendReaccpetOrderRecordMq(context, bApiResult, request);
        LOGGER.info("纯配重处理API服务处理完成返回结果:traceId={}, apiResult={}", profile.getTraceId(), JSONUtils.beanToJSONDefault(response));
        return response;
    }

    /**
     * @param profile
     * @param request
     * @return
     * @throws
     * @throws
     * @Description 初始化重处理领域模型
     * <AUTHOR>
     * @createDate 2021/3/11 10:38 下午
     * @lastModify
     */
    public ExpressOrderContext expressOrderModelOf(RequestProfile profile, ReacceptExpressOrderRequest request) throws ParseException {
        if (null == profile) {
            LOGGER.error("初始化重处理领域模型业务身份识别对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("参数校验失败");
        }
        if (null == request) {
            LOGGER.error("初始化重处理领域模型申请入参对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("参数校验失败");
        }
        ExpressOrderModel orderModel = reacceptExpressOrderTranslator.translator(profile, request);
        //默认垂直业务身份
        orderModel.setYId("JDL");
        //领域模型上线文
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), profile
                , orderModel.getOrderBusinessIdentity().getBusinessScene());
        context.setOrderModel(orderModel);
        Set<String> promiseUnits = makingDispatcherHandler.execute(context);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        return context;
    }

}
