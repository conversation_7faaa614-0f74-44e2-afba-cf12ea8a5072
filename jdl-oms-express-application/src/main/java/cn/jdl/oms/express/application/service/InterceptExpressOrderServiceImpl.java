package cn.jdl.oms.express.application.service;

import cn.jdl.batrix.spec.BApiResult;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.InterceptExpressOrderResult;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.ohs.translator.ApiResultHelper;
import cn.jdl.oms.express.domain.ohs.translator.InterceptExpressOrderTranslator;
import cn.jdl.oms.express.domain.service.IInterceptExpressOrderDomainService;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.model.InterceptExpressOrderBatchRequest;
import cn.jdl.oms.express.model.InterceptExpressOrderBatchResponse;
import cn.jdl.oms.express.model.InterceptExpressOrderData;
import cn.jdl.oms.express.model.InterceptExpressOrderRequest;
import cn.jdl.oms.express.model.InterceptExpressOrderResponse;
import cn.jdl.oms.express.service.InterceptExpressOrderService;
import cn.jdl.oms.express.shared.common.constant.ABConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.ApplicationDomainException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainException;
import cn.jdl.oms.express.shared.common.exception.ValidationDomainException;
import cn.jdl.oms.express.shared.common.exception.ValidationRequestParamException;
import cn.jdl.oms.express.shared.common.mdc.MDCTraceConstants;
import cn.jdl.oms.express.shared.common.specification.JSR303Specification;
import cn.jdl.oms.express.shared.common.specification.Notification;
import cn.jdl.oms.express.shared.common.thread.MdcThreadPoolExecutor;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.google.common.collect.Lists;
import com.jd.jsf.gd.util.Constants;
import com.jd.jsf.gd.util.RpcContext;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 纯配订单中心订单拦截
 *
 * @author: lufahai
 * @Date: 2021/5/26
 * @Version 1.0
 */
@Service("interceptExpressOrderServiceImpl")
public class InterceptExpressOrderServiceImpl implements InterceptExpressOrderService {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(InterceptExpressOrderServiceImpl.class);

    @Resource
    private IInterceptExpressOrderDomainService interceptExpressOrderDomainService;

    @Resource
    private InterceptExpressOrderTranslator expressOrderTranslator;
    /**
     * 拦截基本信息校验
     */
    @Resource
    private JSR303Specification jsr303Specification;
    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;
    /**
     * 删单producer
     */
    @Resource
    private JMQMessageProducer deleteOrderMessageProducer;

    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    /**
     * 批量执行线程池
     */
    private MdcThreadPoolExecutor threadPoolExecutor;

    /**
     * 核心线程大小
     */
    @Value("${intercept.express.batch.order.thread.corePoolSize:70}")
    private int corePoolSize = 70;

    /**
     * 线程池大小
     */
    @Value("${intercept.express.batch.order.thread.threadPoolSize:80}")
    private int threadPoolSize = 80;

    /**
     * 线程队列大小
     */
    @Value("${intercept.express.batch.order.thread.threadQueueSize:0}")
    private int threadQueueSize = 0;

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 线城池初始化
     * <AUTHOR>
     * @ModifyDate 2021/6/10 11:33 上午
     * @lastModify
     */
    @PostConstruct
    public void init() {
        //开启多线程并行查询服务
        threadPoolExecutor = MdcThreadPoolExecutor.newWithInheritedMdc(corePoolSize, threadPoolSize, 60L, TimeUnit.SECONDS
                , (threadQueueSize > 0) ? new ArrayBlockingQueue<Runnable>(threadQueueSize, true) : new LinkedBlockingQueue<Runnable>());
        LOGGER.info("纯配批量拦截服务线程池初始化成功:corePoolSize= {},threadPoolSize={}, threadQueueSize={} ", corePoolSize, threadPoolSize, threadQueueSize);
    }


    /**
     * @param profile 接口服务申请profile
     * @param request 接口服务申请
     * @return InterceptExpressOrderResponse
     * @throws
     * @throws
     * @Description 纯配拦截服务
     * <AUTHOR>
     * @createDate 2021/6/10 3:05 下午
     * @lastModify
     */
    @Override
    public InterceptExpressOrderResponse interceptOrder(@NotNull RequestProfile profile, @NotNull InterceptExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".interceptOrder"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        LOGGER.info("纯配拦截API服务请求入参:profile={},request={}", JSONUtils.beanToJSONDefault(profile), JSONUtils.beanToJSONDefault(request));
        InterceptExpressOrderResponse response = null;
        ExpressOrderContext context = null;
        BApiResult<InterceptExpressOrderData> bApiResult;
        try {
            //接单前置校验
            Notification notification = Notification.create();
            if (!jsr303Specification.isSatisfiedBy(profile, notification)
                    || !jsr303Specification.isSatisfiedBy(request, notification)) {
                LOGGER.error("纯配拦截服务基本信息校验入参非法校验失败: notification= {}", notification.first());
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_ALARM_MONITOR
                        , System.currentTimeMillis()
                        , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                                + ","
                                + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                                + ","
                                + "链路追踪ID:" + MDCTraceConstants.TRACEID
                                + ","
                                + "申请租户:" + MDCTraceConstants.TRACEID
                                + ","
                                + "纯配拦截服务校验基本信息校验入参非法校验失败原因:" + notification.first());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withVars(notification.first().getValue())
                        //返回具体校验失败字段
                        .putExt(DomainException.VALIDATE_FAIL_FIELD, notification.first().getKey());
            }
            //设置AB环境标识
            Optional.ofNullable(RpcContext.getContext().getAttachment(ABConstants.ABENVIRONMENT_FLAG)).ifPresent(env -> {
                Map<String, String> extendProps = Optional.ofNullable(request.getExtendProps()).orElse(new HashMap<>());
                extendProps.put(ABConstants.ABENVIRONMENT_FLAG, String.valueOf(RpcContext.getContext().getAttachment(ABConstants.ABENVIRONMENT_FLAG)));
                request.setExtendProps(extendProps);
            });
            //拦截领域模型初始化
            context = expressOrderModelOf(profile, request);
            //默认垂直业务身份
            InterceptExpressOrderResult interceptExpressOrderResult = interceptExpressOrderDomainService.interceptOrder(context);
            InterceptExpressOrderData interceptExpressOrderData = new InterceptExpressOrderData();
            interceptExpressOrderData.setCode(interceptExpressOrderResult.getCode());
            interceptExpressOrderData.setOrderNo(request.getOrderNo());
            interceptExpressOrderData.setExtendProps(interceptExpressOrderResult.getExtendProps());
            LOGGER.info("纯配拦截API服务处理完成返回参:cancelExpressOrderData={}", JSONUtils.beanToJSONDefault(interceptExpressOrderData));
            bApiResult = BApiResult.ofSuccess(interceptExpressOrderData);
        } catch (ValidationRequestParamException e) {
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                            + ","
                            + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                            + ","
                            + "链路追踪ID:" + MDCTraceConstants.TRACEID
                            + ","
                            + "申请租户:" + MDCTraceConstants.TRACEID
                            + ","
                            + "纯配拦截服务请求入参校验处理异常原因:" + e.getMessage());
            LOGGER.error("纯配拦截服务校验处理异常,traceId={},ValidationRequestParamException", profile.getTraceId(), e);
        } catch (ApplicationDomainException e) {
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                            + ","
                            + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                            + ","
                            + "链路追踪ID:" + MDCTraceConstants.TRACEID
                            + ","
                            + "申请租户:" + MDCTraceConstants.TRACEID
                            + ","
                            + "纯配拦截服务领域活动处理异常原因:" + e.getMessage());
            LOGGER.error("纯配拦截服务处理异常,ApplicationDomainException: {}", e.fullMessage());
        } catch (DomainException e) {
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                            + ","
                            + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                            + ","
                            + "链路追踪ID:" + MDCTraceConstants.TRACEID
                            + ","
                            + "申请租户:" + MDCTraceConstants.TRACEID
                            + ","
                            + "纯配拦截服务领域服务处理异常原因:" + e.getMessage());
            LOGGER.error("纯配拦截服务领域服务处理异常,DomainException", e);
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                            + ","
                            + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                            + ","
                            + "链路追踪ID:" + MDCTraceConstants.TRACEID
                            + ","
                            + "申请租户:" + MDCTraceConstants.TRACEID
                            + ","
                            + "纯配拦截服务处理异常原因:" + e.getMessage());
            LOGGER.error("纯配拦截服务处理异常,Exception", e);
        } catch (Throwable throwable) {
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, throwable);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                    , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                            + ","
                            + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                            + ","
                            + "链路追踪ID:" + MDCTraceConstants.TRACEID
                            + ","
                            + "申请租户:" + MDCTraceConstants.TRACEID
                            + ","
                            + "纯配拦截服务处理异常原因:" + throwable.getMessage());
            LOGGER.error("纯配拦截服务处理异常,throwable", throwable);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        //封装最终返回结果数据
        response = new InterceptExpressOrderResponse();
        response.setCode(bApiResult.getCode());
        if (bApiResult.getData() == null) {
            InterceptExpressOrderData interceptExpressOrderData = new InterceptExpressOrderData();
            interceptExpressOrderData.setOrderNo(request.getOrderNo());
            interceptExpressOrderData.setExtendProps(bApiResult.getExt());
            response.setData(interceptExpressOrderData);
        } else {
            response.setData(bApiResult.getData());
            response.getData().setExtendProps(bApiResult.getExt());
        }
        //批量返回当前序列号识别行级执行结果
        if (MapUtils.isNotEmpty(request.getExtendProps())) {
            //当前执行级序号
            String sequenceNo = request.getExtendProps().get(AttachmentKeyEnum.SEQUENCE_NO.getKey());
            if (StringUtils.isNotBlank(sequenceNo)) {
                response.getData().getExtendProps().put(AttachmentKeyEnum.SEQUENCE_NO.getKey(), sequenceNo);
            }
        }
        response.setMessage(bApiResult.getMessage());
        response.setExtendProps(bApiResult.getExt());
        expressOrderFlowService.sendInterceptOrderRecordMq(context,bApiResult,request);
        LOGGER.info("纯配拦截API服务处理完成返回结果:traceId={}, response={}", profile.getTraceId(), JSONUtils.beanToJSONDefault(response));
        return response;
    }


    /**
     * @param profile      接口服务申请profile
     * @param batchRequest 接口服务申请
     * @return
     * @throws
     * @throws
     * @Description 批量纯配拦截服务
     * <AUTHOR>
     * @createDate 2021/6/10 3:05 下午
     * @lastModify
     */
    @Override
    public InterceptExpressOrderBatchResponse interceptBatchOrder(RequestProfile profile, InterceptExpressOrderBatchRequest batchRequest) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".interceptBatchOrder"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        LOGGER.info("纯配批量拦截服务请求入参:profile={},batchRequest={}", JSONUtils.beanToJSONDefault(profile), JSONUtils.beanToJSONDefault(batchRequest));
        //批量返回执行结果
        InterceptExpressOrderBatchResponse orderBatchResponse = null;
        //批量执行返回结果
        BApiResult<List<InterceptExpressOrderResponse>> bApiResult = null;


        /**
         *
         *
         * @Description interceptBatchOrder
         *
         * <AUTHOR>
         *
         * @createDate 2021/6/10 3:11 下午
         *
         * @param  [profile, batchRequest]
         *
         * @return cn.jdl.oms.express.model.InterceptExpressOrderBatchResponse
         *
         * @exception
         *
         * @throws
         *
         * @lastModify
         *
         */
        class InterceptExpressOrderCallable implements Callable {

            /**
             * 业务
             */
            private RequestProfile requestProfile;

            /**
             * 纯配拦截申请对象
             */
            private InterceptExpressOrderRequest request;

            /**
             * 有参构造函数
             * @param requestProfile
             * @param request
             */
            public InterceptExpressOrderCallable(RequestProfile requestProfile, InterceptExpressOrderRequest request) {
                this.requestProfile = requestProfile;
                this.request = request;
            }

            /**
             * Computes a result, or throws an exception if unable to do so.
             *
             * @return computed result
             * @throws Exception if unable to compute a result
             */
            @Override
            public Object call() throws Exception {
                return interceptOrder(requestProfile, request);
            }
        }

        try {
            if (null == batchRequest) {
                LOGGER.error("纯配批量拦截服务校验基本信息校验入参非法:batchRequest= {}", batchRequest);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_ALARM_MONITOR
                        , System.currentTimeMillis()
                        , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                                + ","
                                + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                                + ","
                                + "链路追踪ID:" + profile.getTraceId()
                                + ","
                                + "申请租户:" + profile.getTenantId());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL);
            }
            List<InterceptExpressOrderRequest> requests = batchRequest.getBatchRequest();
            if (CollectionUtils.isEmpty(requests)) {
                LOGGER.error("纯配批量拦截服务校验基本信息校验入参非法:requests= {}", requests);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_ALARM_MONITOR
                        , System.currentTimeMillis()
                        , "客户端调用系统名称:" + RpcContext.getContext().getAttachment(Constants.HIDDEN_KEY_APPNAME)
                                + ","
                                + "服务端调用IP:" + RpcContext.getContext().getLocalAddress()
                                + ","
                                + "链路追踪ID:" + profile.getTraceId()
                                + ","
                                + "申请租户:" + profile.getTenantId());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL);
            }
            //初始化拦截返回对象集合
            List<Future<InterceptExpressOrderResponse>> futureTasks = Lists.newArrayListWithCapacity(requests.size());
            for (InterceptExpressOrderRequest request : requests) {
                if (null == request) {
                    continue;
                }
                //设置AB环境标识
                Optional.ofNullable(RpcContext.getContext().getAttachment(ABConstants.ABENVIRONMENT_FLAG)).ifPresent(env -> {
                    Map<String, String> extendProps = Optional.ofNullable(request.getExtendProps()).orElse(new HashMap<>());
                    extendProps.put(ABConstants.ABENVIRONMENT_FLAG, String.valueOf(RpcContext.getContext().getAttachment(ABConstants.ABENVIRONMENT_FLAG)));
                    request.setExtendProps(extendProps);
                });
                Future<InterceptExpressOrderResponse> future = threadPoolExecutor.submit(new InterceptExpressOrderCallable(profile, request));
                if (null != future) {
                    futureTasks.add(future);
                }
            }
            //批量出线程执行结果返回
            if (CollectionUtils.isEmpty(futureTasks)) {
                LOGGER.error("纯配批量拦截服务领域服务处理异常执行结果为空,futureTasks={}", futureTasks);
                throw new ApplicationDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR);
            }
            List<InterceptExpressOrderResponse> responses = Lists.newArrayListWithCapacity(requests.size());
            for (Future<InterceptExpressOrderResponse> future : futureTasks) {
                if (null != future.get()) {
                    LOGGER.info("纯配批量拦截服务领域服务结果,future={}", JSONUtils.beanToJSONDefault(future.get()));
                    responses.add(future.get());
                }
            }
            bApiResult = BApiResult.ofSuccess(responses);
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_CREATE_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                            + ","
                            + "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "纯配批量拦截服务处理异常原因:" + e.getMessage());
            LOGGER.error("纯配批量拦截服务处理异常,traceId={},Exception", profile.getTraceId(), e);
        } catch (Throwable throwable) {
            Profiler.functionError(callerInfo);
            bApiResult = ApiResultHelper.getInstance().normalizeApiResult(profile, throwable);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_CREATE_EXCEPTION_ALARM_MONITOR
                    , System.currentTimeMillis()
                            + ","
                            + "链路追踪ID:" + profile.getTraceId()
                            + ","
                            + "申请租户:" + profile.getTenantId()
                            + ","
                            + "纯配批量拦截服务处理异常原因:" + throwable.getMessage());
            LOGGER.error("纯配批量拦截服务处理异常,traceId={},throwable", profile.getTraceId(), throwable);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        //封装最终返回结果数据
        if (null != bApiResult) {
            orderBatchResponse = new InterceptExpressOrderBatchResponse();
            orderBatchResponse.setCode(bApiResult.getCode());
            orderBatchResponse.setData(bApiResult.getData());
            orderBatchResponse.setMessage(bApiResult.getMessage());
        }
        LOGGER.info("纯配批量拦截API服务处理完成返回结果:orderBatchResponse={}", JSONUtils.beanToJSONDefault(orderBatchResponse));
        return orderBatchResponse;
    }

    /**
     * 初始化拦截领域模型
     *
     * @param profile
     * @param request
     * @return
     */
    private ExpressOrderContext expressOrderModelOf(RequestProfile profile, InterceptExpressOrderRequest request) {
        if (null == profile) {
            LOGGER.error("初始化拦截领域模型业务身份识别对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("参数校验失败");
        }
        if (null == request) {
            LOGGER.error("初始化拦截领域模型申请入参对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("参数校验失败");
        }
        ExpressOrderModel orderModel = expressOrderTranslator.translator(profile, request);
        //默认垂直业务身份
        orderModel.setYId("JDL");
        //领域模型上线文
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(),
                profile,
                orderModel.getOrderBusinessIdentity().getBusinessScene());
        context.setOrderModel(orderModel);
        //初始化，下发履约执行层打标 目前仅支持下发一个履约层，下发时识别使用
        Set<String> promiseUnits = makingDispatcherHandler.execute(context);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //初始化，下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        return context;
    }


}
