package cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.util.ReceiptCurrencyUtil;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 台账币种转换工具
 * 目前只处理POS台账、B商家台账
 **/
public class OrderBankCurrencyUtil {

    /**
     * 转换台账请求的币种
     */
    public static void handleCurrency(ExpressOrderContext context, OrderBankFacadeRequest orderBankFacadeRequest) {
        if (context == null || orderBankFacadeRequest == null) {
            return;
        }

        ExpressOrderModel orderModel = context.getOrderModel();
        if (BusinessSceneEnum.ENQUIRY.getCode().equals(context.getBusinessIdentity().getBusinessScene())
                || BusinessSceneEnum.MODIFY.getCode().equals(context.getBusinessIdentity().getBusinessScene())) {
            // 询价场景，使用快照
            orderModel = context.getOrderModel().getOrderSnapshot();
        }
        handleCurrency(orderModel, orderBankFacadeRequest);
    }

    /**
     * 转换台账请求的币种
     */
    public static void handleCurrency(ExpressOrderModel orderModel, OrderBankFacadeRequest orderBankFacadeRequest) {
        if (orderModel == null || orderBankFacadeRequest == null) {
            return;
        }

        if (!orderModel.isHKMO() && !orderModel.isIntl()) {
            // 非港澳国际不处理 B商家和POS TODO 后续涉及外单台账需要额外注意
            return;
        }

        // 根据流向、支付方式获取币种，默认是人民币
        CurrencyCodeEnum currencyCodeEnum = getCurrencyCodeEnum(orderModel.getCustoms(), orderModel.getFinance());

        // POS台账、B商家台账使用计费137形式
        String currency = currencyCodeEnum.getFeeSystemCode().toString();

        // POS到付
        if (orderBankFacadeRequest.getPosYun() != null) {
            orderBankFacadeRequest.getPosYun().setCurrency(currency);
        }
        // POS寄付
        if (orderBankFacadeRequest.getPosJfYun() != null) {
            orderBankFacadeRequest.getPosJfYun().setCurrency(currency);
        }
        // B商家修改
        if (orderBankFacadeRequest.getBMerchantModify() != null
                && orderBankFacadeRequest.getBMerchantModify().getBMerchantDueDetailInfo() != null) {
            orderBankFacadeRequest.getBMerchantModify().getBMerchantDueDetailInfo().setCurrency(currency);
        }
        // B商家新增
        if (orderBankFacadeRequest.getBMerchantCreate() != null) {
            orderBankFacadeRequest.getBMerchantCreate().setCurrency(currency);
            if (CollectionUtils.isNotEmpty(orderBankFacadeRequest.getBMerchantCreate().getBMerchantDueDetailInfos())) {
                for (OrderBankFacadeRequest.BMerchantDueDetailInfo each : orderBankFacadeRequest.getBMerchantCreate().getBMerchantDueDetailInfos()) {
                    each.setCurrency(currency);
                }
            }
        }

        if (orderBankFacadeRequest instanceof OrderBankFacadeMiddleRequest) {
            OrderBankFacadeMiddleRequest orderBankFacadeMiddleRequest = (OrderBankFacadeMiddleRequest) orderBankFacadeRequest;

            // B商家寄付修改
            if (orderBankFacadeMiddleRequest.getBMerchantJfModify() != null
                    && orderBankFacadeMiddleRequest.getBMerchantJfModify().getBMerchantDueDetailInfo() != null) {
                orderBankFacadeMiddleRequest.getBMerchantJfModify().getBMerchantDueDetailInfo().setCurrency(currency);
            }

            // B商家到付修改
            if (orderBankFacadeMiddleRequest.getBMerchantDfModify() != null
                    && orderBankFacadeMiddleRequest.getBMerchantDfModify().getBMerchantDueDetailInfo() != null) {
                orderBankFacadeMiddleRequest.getBMerchantDfModify().getBMerchantDueDetailInfo().setCurrency(currency);
            }

            // B商家COD修改
            if (orderBankFacadeMiddleRequest.getBMerchantCodModify() != null
                    && orderBankFacadeMiddleRequest.getBMerchantCodModify().getBMerchantDueDetailInfo() != null) {
                orderBankFacadeMiddleRequest.getBMerchantCodModify().getBMerchantDueDetailInfo().setCurrency(currency);
            }
        }

        // 港澳需求无写外单台账诉求，外单台账暂时不做处理
    }

    /**
     * 获取币种
     */
    public static CurrencyCodeEnum getCurrencyCodeEnum(Customs customs, Finance finance) {
        AdministrativeRegionEnum startFlowDirection = null;
        AdministrativeRegionEnum endFlowDirection = null;
        SettlementTypeEnum settlementType = null;
        if (customs != null) {
            startFlowDirection = customs.getStartFlowDirection();
            endFlowDirection = customs.getEndFlowDirection();
        }
        if (finance != null) {
            settlementType = finance.getSettlementType();
        }
        return ReceiptCurrencyUtil.getCurrency(startFlowDirection, endFlowDirection, settlementType);
    }
}