package cn.jdl.oms.express.domain.infrs.acl.pl.order.dto;

import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName FinanceFacade
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/21 9:36 下午
 * @ModifyDate 2021/3/21 9:36 下午
 * @Version 1.0
 */
@Data
public class FinanceFacade {

    /**
     * 结算方式
     * 1：寄付现结，
     * <p>
     * 2：到付现结，
     * <p>
     * 3：寄付月结
     */
    private Integer settlementType;
    /**
     * 是否询价
     * 1-不询价
     * <p>
     * 2-询价
     */
    private Integer enquiryType;
    /**
     * 预估运费金额，精度3
     */
    private Money estimateAmount;
    /**
     * 财务预占标识, 1：白条预授权
     */
    private Integer preemptType;
    /**
     * 1-先款支付；2-后款支付；
     */
    private Integer paymentStage;
    /**
     * 折前金额
     */
    private Money preAmount;
    /**
     * 折后金额
     */
    private Money discountAmount;
    /**
     * 总优惠金额
     */
    private Money totalDiscountAmount;
    /**
     * 计费重量
     */
    private Weight billingWeight;
    /**
     * 计费体积
     */
    private Volume billingVolume;
    /**
     * 支付账号
     */
    private String paymentAccountNo;
    /**
     * 收款机构
     */
    private String collectionOrgNo;
    /**
     * 支付方式
     */
    private Integer payment;
    /**
     * 结算账号
     */
    private String settlementAccountNo;
    /**
     * 支付状态
     */
    private Integer paymentStatus;
    /**
     * 退款状态
     */
    private Integer refundStatus;
    /**
     * 支付截止时间
     */
    private Date payDeadline;
    /**
     * 费用明细
     */
    private List<FinanceDetailFacade> financeDetails;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 支付时间
     */
    private Date paymentTime;

    /**
     * 积分信息
     */
    private PointsFacade pointsFacade;

    /**
     * 询价状态
     */
    private Integer enquiryStatus;

    /**
     * 计费模式
     */
    private String billingMode;
    /**
     * 计费方式
     */
    private String billingType;


    /**
     * 财务备注，用于存储台账加和时原始单加和过来的信息,以及新单的账信息
     */
    private String remark;

    /**
     * 抵扣信息
     */
    private List<DeductionInfoDto> deductionInfoDtos;

    /**
     * 询价始发市编码
     */
    private String enquiryStartCityNo;

    /**
     * 高峰期附加费时间
     */
    private Date peakPeriodTime;

    /**
     * 收费要求信息
     */
    private List<CostInfo> costInfos;

    /**
     * 扩展属性
     */
    private Map<String, String> extendProps;

    /**
     * 附加费用
     */
    private List<CostInfo> attachFees;

    /**
     * 预估税金
     */
    private Money estimatedTax;

    /**
     * 真实税金
     */
    private Money actualTax;

    /**
     * 费用支付状态归集
     */
    private Map<String, String> payStatusMap;

    /**
     * 支付人Pin集合，需要去重存储
     */
    private List<String> payerPins;

    /**
     * 税金结算方式
     */
    private Integer taxSettlementType;

    /**
     * 预占金额
     */
    private Money occupyAmount;

    /**
     * 预占模式
     */
    private Integer occupyMode;

    /**
     * 预估财务信息
     */
    private FinanceFacade estimateFinanceInfo;

    /**
     * 实际支付方式
     */
    private Integer actualPaymentType;

    /**
     * 加价后总金额
     */
    private Money totalAdditionAmount;

    /**
     * 多方计费收费总额
     */
    private List<CostInfo> multiPartiesTotalAmounts;

}
