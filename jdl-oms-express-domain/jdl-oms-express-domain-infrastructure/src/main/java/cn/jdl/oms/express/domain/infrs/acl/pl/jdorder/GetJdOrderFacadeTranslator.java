package cn.jdl.oms.express.domain.infrs.acl.pl.jdorder;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.infrs.acl.util.AfterSaleServiceUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.security.TdeAcl;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.afs.biz.center.api.base.OperatorInfoReq;
import com.jd.afs.biz.center.api.serviceBill.dto.AfsServiceInfoReq;
import com.jd.afs.biz.center.api.serviceBill.dto.AfsServiceInfoResp;
import com.jd.dropship.center.domain.OrderModel;
import com.jd.i18n.order.dict.FieldKeyEnum;
import com.jd.i18n.order.dict.helper.OrdermidQueryHelper;
import com.jd.jdorders.component.vo.response.OrderQueryResponse;
import com.jd.purchase.domain.old.bean.Cart;
import com.jd.purchase.domain.old.bean.ExtTagPair;
import com.jd.purchase.domain.old.bean.Order;
import com.jd.purchase.domain.old.bean.SKU;
import com.jd.purchase.utils.serializer.helper.SerializersHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ProjectName：jdl-oms-express-shared-common
 * @Package： cn.jdl.oms.express.domain.infrs.acl.pl.jdorder
 * @ClassName: GetJdOrderFacadeTranslator
 * @Description: 零售订单防腐层转换
 * @Author： liyong549
 * @CreateDate 2021/4/29 9:08
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Translator
public class GetJdOrderFacadeTranslator {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(GetJdOrderFacadeTranslator.class);

    @Value("${appName}")
    private String appName;
    /**
     * "301"=商城主站
     * "1"=泰国站
     * “4”=印尼站；
     */
    private static final String REGION = "301";
    /**
     * 三种类型（入参中务必确认好类型）
     * <p>
     * 1 订单中间件表字段(订单基本信息+订单详情信息)
     * <p>
     * 2 订单xml快照 ( cart xml + order xml)
     * <p>
     * 4 同时查询表 和 xml
     */
    private static final String BIZ_TYPE = "4";

    /**
     * 零售订单扩展字段数组ExtTagPair---key为aerfaTime
     * 取出key对应的value后解析
     */
    private static final String AERFA_TIME = "aerfaTime";

    /**
     * 地址替换英文APP识别标识
     * 取出key对应的value后解析
     */
    private static final String IS_NEED_CN_ADDRESS = "isNeedCnAddress";

    /**
     * 订单商品详情
     */
    private static final String GOODS_DETAILS = "details";

    /**
     * 厂直国际订单收件人-地址
     */
    private static final String VC_INTEL_ORDER_ADDRESS = "detailAddr";
    /**
     * 厂直国际订单收件人-姓名
     */
    private static final String VC_INTEL_ORDER_NAME = "name";
    /**
     * 厂直国际订单收件人-电话|手机
     */
    private static final String VC_INTEL_ORDER_PHONE = "phone";
    /**
     * 厂直国际订单收件人-地址-省
     */
    private static final String VC_INTEL_ORDER_PROVICE = "proviceId";
    /**
     * 厂直国际订单收件人-地址-市
     */
    private static final String VC_INTEL_ORDER_CITY = "cityId";
    /**
     * 厂直国际订单收件人-地址-县
     */
    private static final String VC_INTEL_ORDER_COUNTRY = "countryId";
    /**
     * 厂直国际订单收件人-地址-乡
     */
    private static final String VC_INTEL_ORDER_TOWN = "townId";
    /**
     * 加解密工具
     */
    @Resource
    private TdeAcl tdeAcl;

    /**
     * 防腐层入参转换
     *
     * @param context
     * @return
     */
    public List<GetJdOrderFacadeRequest> toGetJdOrderFacadeRequest(ExpressOrderContext context) {
        List<GetJdOrderFacadeRequest> requestList = new ArrayList<>();
        if (context == null || context.getOrderModel() == null
                || context.getOrderModel().getChannel() == null
                || StringUtils.isBlank(context.getOrderModel().getChannel().getChannelOrderNo())) {
            return Collections.emptyList();
        }
        Channel channel = context.getOrderModel().getChannel();
        String[] jdOrderNoArray = channel.getChannelOrderNo().split(",");
        for (String jdOrderNo : jdOrderNoArray) {
            requestList.add(toGetJdOrderFacadeRequest(jdOrderNo));
        }
        return requestList;
    }

    /**
     * 防腐层入参转换
     *
     * @param context
     * @return
     */
    public List<GetJdOrderFacadeRequest> toGetJdVcOrderFacadeRequest(ExpressOrderContext context) {
        List<GetJdOrderFacadeRequest> requestList = new ArrayList<>();
        if (context == null || context.getOrderModel() == null
                || context.getOrderModel().getChannel() == null
                || StringUtils.isBlank(context.getOrderModel().getChannel().getChannelOrderNo())) {
            return Collections.emptyList();
        }
        String vendorCode = context.getCustomerConfig().getExternalUniqueCode();
        if(StringUtils.isBlank(vendorCode)){
            return Collections.emptyList();
        }
        Channel channel = context.getOrderModel().getChannel();
        String[] jdOrderNoArray = channel.getChannelOrderNo().split(",");
        for (String jdOrderNo : jdOrderNoArray) {
            requestList.add(toGetJdVcOrderFacadeRequest(jdOrderNo,vendorCode));
        }
        return requestList;
    }

    /**
     * 防腐层入参转换
     *
     * @param context 上下文
     * @param outboundVenderId 商家出库id
     * @return
     */
    public List<GetJdOrderFacadeRequest> toGetJdVcOrderFacadeRequestByOutboundVenderId(ExpressOrderContext context, String outboundVenderId) {
        List<GetJdOrderFacadeRequest> requestList = new ArrayList<>();
        if (context == null || context.getOrderModel() == null
                || context.getOrderModel().getChannel() == null
                || StringUtils.isBlank(context.getOrderModel().getChannel().getChannelOrderNo())) {
            return Collections.emptyList();
        }
        Channel channel = context.getOrderModel().getChannel();
        String[] jdOrderNoArray = channel.getChannelOrderNo().split(",");
        for (String jdOrderNo : jdOrderNoArray) {
            requestList.add(toGetJdVcOrderFacadeRequest(jdOrderNo,outboundVenderId));
        }
        return requestList;
    }

    public GetJdOrderFacadeRequest toGetJdOrderFacadeRequest(String channelOrderNo) {
        GetJdOrderFacadeRequest getJdOrderFacadeRequest = new GetJdOrderFacadeRequest();
        getJdOrderFacadeRequest.setOrderId(Long.valueOf(channelOrderNo));
        getJdOrderFacadeRequest.setAppName(this.appName);
        getJdOrderFacadeRequest.setRegion(REGION);
        getJdOrderFacadeRequest.setBizType(BIZ_TYPE);
        getJdOrderFacadeRequest.setQueryKeys(getQueryKeys());
        Map<String, String> extMap = new HashMap<>();
        //查询入参包含有cartxml或者orderxml,入参必须加上
        extMap.put("serializeType","raw");
        getJdOrderFacadeRequest.setExtMap(extMap);
        return getJdOrderFacadeRequest;
    }

    public GetJdOrderFacadeRequest toGetJdVcOrderFacadeRequest(String channelOrderNo,String vendorCode) {
        GetJdOrderFacadeRequest getJdOrderFacadeRequest = new GetJdOrderFacadeRequest();
        getJdOrderFacadeRequest.setOrderId(Long.valueOf(channelOrderNo));
        getJdOrderFacadeRequest.setVendorCode(vendorCode);
        return getJdOrderFacadeRequest;
    }

    public GetJdOrderFacadeResponse toGetJdOrderFacadeResponse(OrderQueryResponse orderQueryResponse,Long orderId) throws BusinessDomainException {
        GetJdOrderFacadeResponse response = new GetJdOrderFacadeResponse();
        response.setOrderId(orderId);
        if (null == orderQueryResponse || !orderQueryResponse.isSuccess() || MapUtils.isEmpty(orderQueryResponse.getDataMap())) {
            return response;
        }
        Map<String, Object> dataMap = orderQueryResponse.getDataMap();
        //解析idSopShipmentType
        String orderXml = (String) dataMap.get(FieldKeyEnum.V_ORDERXML.getFieldName());
        orderXml = com.jd.orderver.component.utils.ZipUtils.gunzip(orderXml);
        String serializationType = (String) dataMap.get(FieldKeyEnum.V_CBDFLAG.getFieldName());
        if (orderXml != null){
            Order serializeOrder = SerializersHelper.ofString(orderXml,
                    Order.class, serializationType);

            //零售订单扩展信息构建
            buildJdOrderExt(serializeOrder,response);

            //英文APP地址替换标识
            response.setNeedCnAddress(isNeedCnAddress(serializeOrder));

            response.setVenderId(serializeOrder.getPop() != null ? String.valueOf(serializeOrder.getPop().getVenderId()) : "");
            LOGGER.info("orderxml中加密后的收件人手机:{},电话:{},姓名:{},详细地址:{}",serializeOrder.getMobileENC(),
                    serializeOrder.getPhoneENC(),serializeOrder.getNameENC(),serializeOrder.getWhereENC());
            try {
                //取订orderxml中加密后的收件人手机，解密后给收件人手机赋值
                response.setMobile(tdeAcl.decrypt(serializeOrder.getMobileENC()));
                //取订orderxml中加密后的收件人电话，解密后给收件人电话赋值
                response.setPhone(tdeAcl.decrypt(serializeOrder.getPhoneENC()));
                //收货人姓名
                response.setCustomerName(tdeAcl.decrypt(serializeOrder.getNameENC()));
                //收货人详细地址 密文取不到取明文
                if(StringUtils.isNotBlank(serializeOrder.getWhereENC())){
                    response.setAddress(tdeAcl.decrypt(serializeOrder.getWhereENC()));
                }else{
                    response.setAddress(serializeOrder.getWhere());
                }
                LOGGER.info("解密后的收件人手机:{},电话:{},姓名:{},详细地址:{}",response.getMobile(),response.getPhone(),
                        response.getCustomerName(),response.getAddress());
            }catch (Exception e){
                LOGGER.warn("orderxml中加密后的收件人信息解密异常",e);
            }
            // 厂直国际详细信息赋值
            response.setVcIntelOrder(buildFdcOrder(orderId, serializeOrder));
            // 厂直国际详细信息赋值
        }
        // orderType
        if (null != dataMap.get(FieldKeyEnum.M_ORDERTYPE.getFieldName())) {
            response.setOrderType(Integer.valueOf(dataMap.get(FieldKeyEnum.M_ORDERTYPE.getFieldName()).toString()));
        }
        // sendPay
        if (null != dataMap.get(FieldKeyEnum.M_SENDPAY.getFieldName())) {
            response.setSendPay(dataMap.get(FieldKeyEnum.M_SENDPAY.getFieldName()).toString());
        }
        // sendPayMap
        if (null != dataMap.get(FieldKeyEnum.M_SENDPAY_MAP.getFieldName())) {
            response.setSendPayMap(dataMap.get(FieldKeyEnum.M_SENDPAY_MAP.getFieldName()).toString());
        }
        // 配送方式
        if (null != dataMap.get(FieldKeyEnum.M_SHIPMENTTYPE.getFieldName())) {
            response.setShipmentType(Integer.valueOf(dataMap.get(FieldKeyEnum.M_SHIPMENTTYPE.getFieldName()).toString()));
        }
        // 支付方式
        if (null != dataMap.get(FieldKeyEnum.M_PAYMENTTYPE.getFieldName())) {
            response.setPaymentType(Integer.valueOf(dataMap.get(FieldKeyEnum.M_PAYMENTTYPE.getFieldName()).toString()));
        }
        // 是否有效
        if (null != dataMap.get(FieldKeyEnum.M_YN.getFieldName())) {
            response.setYn(Integer.valueOf(dataMap.get(FieldKeyEnum.M_YN.getFieldName()).toString()));
        }
        // 国际化-订单状态枚举
        if (null != dataMap.get(FieldKeyEnum.M_STATE.getFieldName())) {
            response.setState(Integer.valueOf(dataMap.get(FieldKeyEnum.M_STATE.getFieldName()).toString()));
        }
        // 收货人姓名
        if (null != dataMap.get(FieldKeyEnum.M_E_CUSTOMERNAME.getFieldName())) {
            response.setCustomerName(this.decrypt(dataMap.get(FieldKeyEnum.M_E_CUSTOMERNAME.getFieldName()).toString()));
        }
        // 收货人手机
        if (null != dataMap.get(FieldKeyEnum.M_E_USERMOB.getFieldName())){
            response.setMobile(this.decrypt(dataMap.get(FieldKeyEnum.M_E_USERMOB.getFieldName()).toString()));
        }
        // 收货人座机
        if (null != dataMap.get(FieldKeyEnum.M_E_PHONE.getFieldName())) {
            response.setPhone(this.decrypt(dataMap.get(FieldKeyEnum.M_E_PHONE.getFieldName()).toString()));
        }
        // 省
        if (null != dataMap.get(FieldKeyEnum.M_PROVINCE.getFieldName())) {
            response.setProvince(Integer.valueOf(dataMap.get(FieldKeyEnum.M_PROVINCE.getFieldName()).toString()));
        }
        // 市
        if (null != dataMap.get(FieldKeyEnum.M_CITY.getFieldName())) {
            response.setCity(Integer.valueOf(dataMap.get(FieldKeyEnum.M_CITY.getFieldName()).toString()));
        }
        // 县
        if (null != dataMap.get(FieldKeyEnum.M_COUNTY.getFieldName())) {
            response.setCounty(Integer.valueOf(dataMap.get(FieldKeyEnum.M_COUNTY.getFieldName()).toString()));
        }
        // 镇
        if (null != dataMap.get(FieldKeyEnum.M_TOWN.getFieldName())) {
            response.setTown(Integer.valueOf(dataMap.get(FieldKeyEnum.M_TOWN.getFieldName()).toString()));
        }
        // 地址
        if (null != dataMap.get(FieldKeyEnum.M_E_ADDRESS.getFieldName())) {
            response.setAddress(this.decrypt(dataMap.get(FieldKeyEnum.M_E_ADDRESS.getFieldName()).toString()));
        }

        //商品信息
        List<Map<String, Object>> details = (List<Map<String, Object>>) dataMap.get(GOODS_DETAILS);
        List<Long> skuIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(details)) {
            for (Map<String, Object> orderDetailMap : details) {
                // 商品SKU
                skuIdList.add(Long.valueOf(orderDetailMap.get(FieldKeyEnum.M_DETAIL_PRODUCTID.getFieldName()).toString()));
            }
        }
        // 商品（sku）ID
        response.setDetailProductIds(skuIdList);

        // 三级类目id（需解析XML数据获取）
        String cartxml = (String) dataMap.get(FieldKeyEnum.V_CARTXML.getFieldName());
        cartxml = com.jd.orderver.component.utils.ZipUtils.gunzip(cartxml);
        Cart serializeCart = SerializersHelper.ofString(cartxml,
                Cart.class, serializationType);
        // 获取所有sku
        List<SKU> skuLists = serializeCart.findAllSkusAndNum();
        // skuUuid在SKU的(extTags的skuUuid节点里)
        List<GetJdOrderFacadeResponse.Cart> carts = skuLists.stream().map(sku -> {
            GetJdOrderFacadeResponse.Cart cart = new GetJdOrderFacadeResponse.Cart();
            cart.setSkuId(sku.getId());
            cart.setCid(sku.getCid());
            cart.setExtTags(sku.getExtTags());
            return cart;
        }).collect(Collectors.toList());
        response.setCartList(carts);
        return response;
    }

    /**
     * 识别是否英文APP
     * @param serializeOrder
     * @return
     */
    private boolean isNeedCnAddress(Order serializeOrder) {
        boolean needCnAddress = Boolean.FALSE;
        List<ExtTagPair> extTagPairs = Arrays.stream(serializeOrder.getTheExtTags()).filter(extTagPair ->
                        IS_NEED_CN_ADDRESS.equals(extTagPair.getKey()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(extTagPairs)){
            String val = extTagPairs.get(0).getVal();
            if(StringUtils.isNotBlank(val) && "true".equals(val)){
                needCnAddress = Boolean.TRUE;
            }
        }
        return needCnAddress;
    }

    /**
     * 构建厂直国际订单信息
     * @param serializeOrder
     */
    private GetJdOrderFacadeResponse.VcIntelOrder buildFdcOrder(Long orderId, Order serializeOrder) {
        String consolidatorInfo = serializeOrder.getExtTags().get("consolidator_info");
        if(StringUtils.isBlank(consolidatorInfo)){
            LOGGER.info("未获取到厂直国际订单详细信息,orderId:{}",orderId);
            return null;
        }else{
            LOGGER.info("orderId:{},获取到厂直国际订单详细信息,info:{}",orderId,consolidatorInfo);
        }
        JSONArray infoArray = JSONObject.parseArray(consolidatorInfo);
        JSONObject info = infoArray.getJSONObject(0);
        GetJdOrderFacadeResponse.VcIntelOrder vcIntelOrder = new GetJdOrderFacadeResponse.VcIntelOrder();
        //收件人信息
        vcIntelOrder.setProvince("");
        vcIntelOrder.setCity("");
        vcIntelOrder.setCounty("");
        vcIntelOrder.setReceiveAddress(info.getString(VC_INTEL_ORDER_ADDRESS));
        vcIntelOrder.setReceiveName(info.getString(VC_INTEL_ORDER_NAME));
        vcIntelOrder.setReceiveTel(info.getString(VC_INTEL_ORDER_PHONE));
        vcIntelOrder.setReceiveMobile(info.getString(VC_INTEL_ORDER_PHONE));
        //地址信息
        vcIntelOrder.setProvinceId(info.getInteger(VC_INTEL_ORDER_PROVICE));
        vcIntelOrder.setCityId(info.getInteger(VC_INTEL_ORDER_CITY));
        vcIntelOrder.setCountyId(info.getInteger(VC_INTEL_ORDER_COUNTRY));
        vcIntelOrder.setTownId(info.getInteger(VC_INTEL_ORDER_TOWN));
        return vcIntelOrder;
    }

    /**
     * 收件人信息（姓名、手机、电话、详细地址）解密
     * @param encryptValue（姓名、手机、电话、详细地址）密文
     * @return
     */
    private String decrypt(String encryptValue){
        try {
            return tdeAcl.decrypt(encryptValue);
        }catch (Exception e){
            LOGGER.warn("dataMap中加密后的收件人信息解密异常,encryptValue:{}",encryptValue,e);
        }
        return null;
    }

    private List<String> getQueryKeys() {
        return OrdermidQueryHelper.buildQuery(FieldKeyEnum.M_YN,//是否有效（0：代表订单已取消，1：代表订单正常）
                FieldKeyEnum.M_STATE,//订单状态
                FieldKeyEnum.M_SENDPAY,//sendpay
                FieldKeyEnum.M_SENDPAY_MAP,//sendpayMap -2024-03-04 源头直发引入
                FieldKeyEnum.M_ORDERTYPE,//orderType
                //FieldKeyEnum.M_CUSTOMERNAME,//收货人姓名--零售姓名加密了，不给返回
                FieldKeyEnum.M_E_CUSTOMERNAME,//加密后的收货人姓名
                //FieldKeyEnum.M_MOBILE, //收货人手机号
                FieldKeyEnum.M_E_USERMOB,//加密后的收货人手机号
                //FieldKeyEnum.M_PHONE,//电话
                FieldKeyEnum.M_E_PHONE,//加密后的收货人电话
                FieldKeyEnum.M_PAYMENTTYPE,//支付方式
                FieldKeyEnum.M_SHIPMENTTYPE,//配送方式
                FieldKeyEnum.M_PROVINCE,//省
                FieldKeyEnum.M_CITY,//市
                FieldKeyEnum.M_COUNTY,//县
                FieldKeyEnum.M_TOWN,//镇
                //FieldKeyEnum.M_ADDRESS,//收货地址
                FieldKeyEnum.M_E_ADDRESS,//加密后的收货地址
                FieldKeyEnum.M_DETAIL_PRODUCTID,//商品（sku）ID
                FieldKeyEnum.V_ORDERXML,//商品信息--主要是获取商品三级分类id
                FieldKeyEnum.V_CARTXML);//订单信息--主要是获取idSopShipmentType
    }

    public GetJdOrderFacadeResponse toJdVcOrderFacadeResponse(OrderModel orderModel, Long orderId) throws BusinessDomainException {
        GetJdOrderFacadeResponse jdVcOrderInfo = new GetJdOrderFacadeResponse();
        jdVcOrderInfo.setOrderId(orderId);
        if (null == orderModel) {
            return jdVcOrderInfo;
        }
        // paymentType
        if (null != orderModel.getPaymentType()) {
            jdVcOrderInfo.setPaymentType(orderModel.getPaymentType());
        }
        // orderState
        if (null != orderModel.getOrderState()) {
            jdVcOrderInfo.setState(orderModel.getOrderState());
        }
        // sendPay
        if (null != orderModel.getSendPay()) {
            jdVcOrderInfo.setSendPay(orderModel.getSendPay());
        }
        // 收货人姓名
        if (null != orderModel.getCustomerName()) {
            jdVcOrderInfo.setCustomerName(orderModel.getCustomerName());
        }
        // 收货人手机
        if (null != orderModel.getPhone()){
            jdVcOrderInfo.setMobile(orderModel.getPhone());
        }
        // 收货人座机
        if (null != orderModel.getTel()) {
            jdVcOrderInfo.setPhone(orderModel.getTel());
        }
        // 省
        if (null != orderModel.getProvinceId()) {
            jdVcOrderInfo.setProvince(orderModel.getProvinceId());
        }
        // 省
        if (null != orderModel.getProvinceName()) {
            jdVcOrderInfo.setProvinceName(orderModel.getProvinceName());
        }
        // 市
        if (null != orderModel.getCityId()) {
            jdVcOrderInfo.setCity(orderModel.getCityId());
        }
        if (null != orderModel.getCityName()) {
            jdVcOrderInfo.setCityName(orderModel.getCityName());
        }
        // 县
        if (null != orderModel.getCountyId()) {
            jdVcOrderInfo.setCounty(orderModel.getCountyId());
        }
        if (null != orderModel.getCountyName()) {
            jdVcOrderInfo.setCountyName(orderModel.getCountyName());
        }
        // 镇
        if (null != orderModel.getTownId()) {
            jdVcOrderInfo.setTown(orderModel.getTownId());
        }
        if (null != orderModel.getTownName()) {
            jdVcOrderInfo.setTownName(orderModel.getTownName());
        }
        // 地址
        if (null != orderModel.getAddress()) {
            jdVcOrderInfo.setAddress(orderModel.getAddress());
        }
        return jdVcOrderInfo;
    }

    /**
     * 构建零售订单扩展信息
     * @param serializeOrder
     * @param response
     */
    private void buildJdOrderExt(Order serializeOrder,GetJdOrderFacadeResponse response){
        List<ExtTagPair> extTagPairs = Arrays.stream(serializeOrder.getTheExtTags()).filter(extTagPair ->
                AERFA_TIME.equals(extTagPair.getKey()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(extTagPairs)){
            String val = extTagPairs.get(0).getVal();
            if(StringUtils.isNotBlank(val)){
                Map map = JSONUtils.jsonToMap(val);
                if(MapUtils.isNotEmpty(map)){
                    JSONArray apt = (JSONArray) map.get("apt");
                    if(null == apt || apt.isEmpty())
                        return;
                    if(null != apt.getJSONObject(0).get("idSopShipmentType")){
                        response.setIdSopShipmentType((Integer) apt.getJSONObject(0).get("idSopShipmentType"));
                    }
                    if(null != apt.getJSONObject(0).get("shipType")){
                        response.setShipType((Integer) apt.getJSONObject(0).get("shipType"));
                    }
                }
            }
        }
    }

    /**
     * 构建：服务单查询请求
     * @param snapshot 快照信息
     * @return AfsServiceInfoReq 服务单查询请求
     */
    public AfsServiceInfoReq toAfsServiceInfoReq(ExpressOrderModel snapshot) {
        AfsServiceInfoReq request = new AfsServiceInfoReq();
        // 服务单ID 合单场景 去第0个 非合单场景取 customerOrderNo

        request.setAfsServiceId(getAfsNo(snapshot));
        // 操作人信息
        OperatorInfoReq operatorInfoReq = new OperatorInfoReq();
        operatorInfoReq.setOperatorPin(snapshot.getOperator());
        operatorInfoReq.setOperatorNick("服务单查询");
        operatorInfoReq.setOperatorDate(new Date());
        operatorInfoReq.setPlatformSrc(427);// TODO
        request.setOperatorInfoExport(operatorInfoReq);
        // 查询订单域
        Set<String> serviceRelationInfo = new HashSet<>();
        serviceRelationInfo.add("REASON_APPLY");
        request.setServiceRelationInfo(serviceRelationInfo);

        return request;
    }

    private Long getAfsNo(ExpressOrderModel snapshot) {
        List<Goods> goodsInfoList = snapshot.getGoodsDelegate().getGoodsInfoList();
        if (CollectionUtils.isEmpty(goodsInfoList)) {
            return null;
        }
        Goods goods = goodsInfoList.get(0);
        String afsNo = MapUtils.getString(goods.getExtendProps(), AttachmentKeyEnum.AFTER_SALES_ORDER_NOS.getKey());
        if (StringUtils.isBlank(afsNo)) {
            return null;
        } else if (StringUtils.isNumeric(afsNo)) {
            return Long.valueOf(afsNo);
        }
        return null;
    }

    /**
     * 售后单信息转换
     * @param response 售后单rpc相应
     * @return 防腐层售后单详情
     */
    public AfsOrderInfo toAfsOrderInfo(AfsServiceInfoResp response) {
        if (response == null) {
            return null;
        }

        AfsOrderInfo afsOrderInfo = new AfsOrderInfo();
        afsOrderInfo.setAfsServiceId(response.getAfsServiceId());
        if (null != response.getApplyReasonInfo()) {
            afsOrderInfo.setReasonCategoryId(response.getApplyReasonInfo().getReasonCategoryId());
        }
        return afsOrderInfo;
    }

}
