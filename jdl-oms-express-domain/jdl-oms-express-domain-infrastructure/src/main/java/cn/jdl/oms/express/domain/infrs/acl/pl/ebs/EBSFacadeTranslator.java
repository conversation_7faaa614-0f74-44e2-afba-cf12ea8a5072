package cn.jdl.oms.express.domain.infrs.acl.pl.ebs;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.shared.common.constant.EBSConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import com.alibaba.fastjson.JSONObject;
import com.jd.lbs.ebs.dto.EbsJfOtherRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;

/**
 * 收入集成防腐层转换类
 */
@Translator
public class EBSFacadeTranslator {
    //来源系统  uat传test
    @Value("${jsf.ebsService.YtSrcSystem}")
    private String YT_SRC_SYSTEM;
    //含义为物流快递快运改址现结
    @Value("${jsf.ebsService.kk.readdress.xj}")
    private String WLKDGZXJ;

    //自提暂存 收入集成来源系统 uat传test
    @Value("${jsf.ebsService.SelfPickTemporaryStorage.YtSrcSystem}")
    private String SELF_PICKUP_TEMPORARY_STORAGE_YT_SRC_SYSTEM;

    //含义为物流快递暂存费现结
    @Value("${jsf.ebsService.kd.zc.xj}")
    private String WLKDZCXJ;

    /**
     * 根据修改记录推送收入集成
     * @param snapshot 订单信息（回传为快照）
     * @param modifyRecord 待推送的修改记录
     * @return
     */
    public EBSFacadeRequest toRejectionEBSFacadeRequest(ExpressOrderModel snapshot, ModifyRecord modifyRecord) {
        EBSFacadeRequest request = new EBSFacadeRequest();

        request.setYtSrcSystem(WLKDGZXJ);
        request.setPreId(modifyRecord.getModifyRecordNo());

        //业务单号--运单号
        request.setPreDocNum(snapshot.getCustomOrderNo());
        //事业部编码
        request.setVendorId(snapshot.getCustomer().getAccountNo());
        //事业部名称
        request.setVendorName(snapshot.getCustomer().getAccountName());
        //申请时间
        request.setAppliedDate(new Date());
        //应收：1
        request.setDirection((byte) 1);
        //京东收支机构
        request.setOrgId(snapshot.getFinance().getCollectionOrgNo());

        ReaddressRecordDetailInfo modifyRecordDetail = (ReaddressRecordDetailInfo) modifyRecord.getModifyRecordDetail();
        FinanceInfo finance = modifyRecordDetail.getFinance();
        // 结算方式
        request.setSettlemetType(String.valueOf(finance.getSettlementType()));
        MoneyInfo pendingMoney = finance.getPendingMoney();
        //金额
        request.setTotalAmount(pendingMoney.getAmount());
        //币种
        Integer feeSystemCode = CurrencyCodeEnum.of(pendingMoney.getCurrencyCode()).getFeeSystemCode();
        request.setCurrencyCode(String.valueOf(feeSystemCode));

        //签约区域-寄件人省份//fixme 京标，异步流程处理转国标
        request.setRegion(snapshot.getConsignor().getAddress().getCityNoGis());
        if (snapshot.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(snapshot)) {
            //费用类型编号 快递改址费用
            request.setLineDocTypeName("KYJSGZF");
        } else {
            //费用类型编号 快运改址费用
            request.setLineDocTypeName("KDJSGZF");
        }
        //服务单号
        request.setAttribute5(snapshot.getCustomOrderNo());

        JSONObject extendInfos = new JSONObject();
        // 寄件二级地址-京标
        extendInfos.put("startTwoAddress", snapshot.getConsignor().getAddress().getCityNoGis());
        ProductDelegate productDelegate = snapshot.getProductDelegate();
        String addOnProductNo = productDelegate.getReaddressProductNo();
        //增值服务编码
        if (StringUtils.isNotBlank(addOnProductNo)) {
            extendInfos.put("addedProductFlag", addOnProductNo);
        }
        // 产品三级标
        generateExtenInfosForProduct(productDelegate, extendInfos);

        request.setExtendJsonInfos(extendInfos.toJSONString());
        return request;
    }

    /**
     * 服务询价单收入集成入参转换
     *
     * @param orderModel
     * @param originOrderModel
     * @param regionNo
     * @return
     */
    public EbsJfOtherRequest toServiceEnquiryEBSRequest(ExpressOrderModel orderModel, ExpressOrderModel originOrderModel, String regionNo) {
        EbsJfOtherRequest request = new EbsJfOtherRequest();
        request.setYtSrcSystem(YT_SRC_SYSTEM);
        request.setPreId(orderModel.orderNo());
        //业务单号--运单号
        request.setPreDocNum(originOrderModel.getCustomOrderNo());
        if (UnitedB2CUtil.isUnitedFreightB2C(originOrderModel)) {
            //事业部编码
            request.setVendorId(originOrderModel.getCustomer().getAccountNo());
            //事业部名称
            request.setVendorName(originOrderModel.getCustomer().getAccountName());
        } else {
            //事业部编码
            request.setVendorId(originOrderModel.getCustomer().getAccountNo2());
            //事业部名称
            request.setVendorName(originOrderModel.getCustomer().getAccountName2());
        }
        //申请时间
        request.setAppliedDate(new Date());
        //金额
        request.setTotalAmount(orderModel.getFinance().getDiscountAmount().getAmount());
        //结算方式
        request.setSettlemetType("2");
        //应收：1
        request.setDirection((byte) 1);
        //币种
        request.setCurrencyCode(String.valueOf(CurrencyCodeEnum.CNY.getFeeSystemCode()));
        //京东收支机构
        request.setOrgId(originOrderModel.getFinance().getCollectionOrgNo());
        //签约区域-寄件人省份，国标
        request.setRegion(regionNo);
        //费用类型编号
        request.setLineDocTypeName("SLF");
        //服务单号
        request.setAttribute5(orderModel.getCustomOrderNo());

        JSONObject extendInfos = new JSONObject();
        //寄件二级地址-京标
        extendInfos.put("startTwoAddress", originOrderModel.getConsignor().getAddress().getCityNoGis());
        ProductDelegate productDelegate = orderModel.getProductDelegate();
        String addOnProductNo = null;
        for (Product product : productDelegate.getProductList()) {
            if (ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode().equals(product.getProductType())) {
                //目前只有一个，所以取一个就行
                addOnProductNo = product.getProductNo();
                break;
            }
        }
        //增值服务编码
        if (StringUtils.isNotBlank(addOnProductNo)) {
            extendInfos.put("addedProductFlag", addOnProductNo);
        }
        // 产品三级标
        generateExtenInfosForProduct(productDelegate, extendInfos);

        request.setExtendJsonInfos(extendInfos.toJSONString());

        return request;
    }

    /**
     * 服务询价单收入集成入参转换
     *
     * @param originOrderModel
     * @return
     */
    public EBSFacadeRequest toServiceEnquiryEBSRequestForHKToNorth(ExpressOrderModel originOrderModel) {
        EBSFacadeRequest request = new EBSFacadeRequest();

        request.setYtSrcSystem(SELF_PICKUP_TEMPORARY_STORAGE_YT_SRC_SYSTEM);
        request.setPreId(originOrderModel.orderNo());
        //业务单号--运单号
        request.setPreDocNum(OrderConstants.SELF_PICKUP_TEMPORARY_STORAGE_PREFIX + originOrderModel.getCustomOrderNo());
        //事业部编码
        request.setVendorId(originOrderModel.getCustomer().getAccountNo());
        //事业部名称
        request.setVendorName(originOrderModel.getCustomer().getAccountName());
        //申请时间
        request.setAppliedDate(new Date());
        //金额
        request.setTotalAmount(originOrderModel.getFinance().getDiscountAmount().getAmount());
        //应收：1
        request.setDirection((byte) 1);
        //币种
        request.setCurrencyCode(String.valueOf(CurrencyCodeEnum.CNY.getFeeSystemCode()));
        //京东收支机构
        request.setOrgId(originOrderModel.getFinance().getCollectionOrgNo());
        //签约区域-寄件人省份，国标
        request.setRegion(originOrderModel.getConsignor().getAddress().getCityNoGis());
        //费用类型编号
        request.setLineDocTypeName("ZANCUNFW");
        //结算方式
        request.setSettlemetType(originOrderModel.getFinance().getSettlementType().getCode().toString());

        JSONObject extendInfos = new JSONObject();
        ProductDelegate productDelegate = originOrderModel.getProductDelegate();

        // 产品三级标
        generateExtenInfosForProduct(productDelegate, extendInfos);

        request.setExtendJsonInfos(extendInfos.toJSONString());

        return request;
    }


    /**
     * 服务询价单收入集成入参转换
     *
     * @param orderModel
     * @param originOrderModel
     * @return
     */
    public EBSFacadeRequest toThroughOrderModifyRecordEBSFacadeRequest(ExpressOrderModel orderModel, ExpressOrderModel originOrderModel, ModifyRecord modifyRecord) {
        EBSFacadeRequest request = new EBSFacadeRequest();
        request.setYtSrcSystem(WLKDGZXJ);
        request.setPreId(modifyRecord.getModifyRecordNo());
        //业务单号--运单号
        request.setPreDocNum(originOrderModel.getCustomOrderNo());
        //事业部编码
        request.setVendorId(originOrderModel.getCustomer().getAccountNo());
        //事业部名称
        request.setVendorName(originOrderModel.getCustomer().getAccountName());
        //申请时间
        request.setAppliedDate(new Date());
        //金额
        ReaddressRecordDetailInfo modifyRecordDetail = (ReaddressRecordDetailInfo) modifyRecord.getModifyRecordDetail();
        FinanceInfo finance = modifyRecordDetail.getFinance();
        request.setTotalAmount(finance.getPendingMoney().getAmount());
        // 结算方式
        request.setSettlemetType(String.valueOf(finance.getSettlementType()));
        //应收：1
        request.setDirection((byte) 1);
        //币种
        request.setCurrencyCode(String.valueOf(CurrencyCodeEnum.CNY.getFeeSystemCode()));
        //京东收支机构
        request.setOrgId(originOrderModel.getFinance().getCollectionOrgNo());
        //签约区域-寄件人省份//fixme 京标，异步流程处理转国标
        request.setRegion(originOrderModel.getConsignor().getAddress().getCityNoGis());
        if(orderModel.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(orderModel)){
            //费用类型编号 快递改址费用
            request.setLineDocTypeName("KYGZF");
        } else {
            //费用类型编号 快运改址费用
            request.setLineDocTypeName("KDGZF");
        }
        //服务单号
        request.setAttribute5(orderModel.getCustomOrderNo());

        JSONObject extendInfos = new JSONObject();
        //寄件二级地址-京标
        extendInfos.put("startTwoAddress", originOrderModel.getConsignor().getAddress().getCityNoGis());
        ProductDelegate productDelegate = originOrderModel.getProductDelegate();
        String addOnProductNo = productDelegate.getReaddressProductNo();
        //增值服务编码
        if (StringUtils.isNotBlank(addOnProductNo)) {
            extendInfos.put("addedProductFlag", addOnProductNo);
        }

        // 产品三级标
        generateExtenInfosForProduct(productDelegate, extendInfos);

        request.setExtendJsonInfos(extendInfos.toJSONString());
        return request;
    }

    private static void generateExtenInfosForProduct(ProductDelegate productDelegate, JSONObject extendInfos) {
        // 产品三级标
        List<Product> newProducts = productDelegate.getNewProducts();
        if(CollectionUtils.isNotEmpty(newProducts)){
            Product newProduct = productDelegate.getNewProduct(10);
            if(null != newProduct){
                extendInfos.put("productL1No", newProduct.getProductNo());
            }
            Product newProduct2 = productDelegate.getNewProduct(20);
            if(null != newProduct2){
                extendInfos.put("productL2No", newProduct2.getProductNo());
            }
            Product newProduct3 = productDelegate.getNewProduct(30);
            if(null != newProduct3){
                extendInfos.put("productL3No", newProduct3.getProductNo());
            }
        }
    }

    /**
     * 服务询价单收入集成入参转换
     *
     * @param ebsFacadeRequest
     * @return request
     */
    public EbsJfOtherRequest toServiceEnquiryEBSRequest(EBSFacadeRequest ebsFacadeRequest) {
        EbsJfOtherRequest request = new EbsJfOtherRequest();
        request.setYtSrcSystem(ebsFacadeRequest.getYtSrcSystem());
        request.setPreId(ebsFacadeRequest.getPreId());
        //业务单号--运单号
        request.setPreDocNum(ebsFacadeRequest.getPreDocNum());
        //事业部编码
        request.setVendorId(ebsFacadeRequest.getVendorId());
        //事业部名称
        request.setVendorName(ebsFacadeRequest.getVendorName());
        //申请时间
        request.setAppliedDate(new Date());
        //金额
        request.setTotalAmount(ebsFacadeRequest.getTotalAmount());
        //结算方式
        request.setSettlemetType(ebsFacadeRequest.getSettlemetType());
        //应收：1
        request.setDirection(ebsFacadeRequest.getDirection());
        //币种
        request.setCurrencyCode(ebsFacadeRequest.getCurrencyCode());
        //京东收支机构
        request.setOrgId(ebsFacadeRequest.getOrgId());
        //签约区域-寄件人省份，国标
        request.setRegion(ebsFacadeRequest.getRegion());
        //费用类型编号
        request.setLineDocTypeName(ebsFacadeRequest.getLineDocTypeName());
        //服务单号
        request.setAttribute5(ebsFacadeRequest.getAttribute5());

        request.setExtendJsonInfos(ebsFacadeRequest.getExtendJsonInfos());

        return request;
    }

    /**
     * 构建暂存费推送ebs入参
     * @param orderSnapshot
     * @return
     */
    public EBSFacadeRequest toTempStorageOrderEBSFacadeRequest(ExpressOrderModel orderSnapshot, RequestProfile requestProfile) {
        EBSFacadeRequest ebsFacadeRequest = new EBSFacadeRequest();
        ebsFacadeRequest.setPreId(orderSnapshot.getRefOrderInfoDelegate().getTempStorageOrderNo());
        ebsFacadeRequest.setYtSrcSystem(WLKDZCXJ);
        ebsFacadeRequest.setPreDocNum(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());
        //事业部编码
        ebsFacadeRequest.setVendorId(orderSnapshot.getCustomer().getAccountNo());
        //事业部名称
        ebsFacadeRequest.setVendorName(orderSnapshot.getCustomer().getAccountName());
        //申请时间
        ebsFacadeRequest.setAppliedDate(new Date());
        //金额 异步获取
        //结算方式
        ebsFacadeRequest.setSettlemetType(SettlementTypeEnum.CASH_ON_DELIVERY.getCode().toString());
        //应收：1
        ebsFacadeRequest.setDirection((byte) 1);
        //币种
        ebsFacadeRequest.setCurrencyCode(String.valueOf(CurrencyCodeEnum.CNY.getFeeSystemCode()));
        //京东收支机构
        ebsFacadeRequest.setOrgId(orderSnapshot.getFinance().getCollectionOrgNo());

        //费用类型编号
        ebsFacadeRequest.setLineDocTypeName("ZCF");
        //服务单号
        ebsFacadeRequest.setAttribute5(orderSnapshot.orderNo());
        String cityNoGis = orderSnapshot.getConsignor().getAddress().getCityNoGis();
        //签约区域-寄件人省份，京标
        ebsFacadeRequest.setRegion(cityNoGis);

        JSONObject extendInfos = new JSONObject();
        //寄件二级地址-京标
        extendInfos.put("startTwoAddress", cityNoGis);

        // 产品三级标
        ProductDelegate productDelegate = orderSnapshot.getProductDelegate();
        generateExtenInfosForProduct(productDelegate, extendInfos);
        ebsFacadeRequest.setExtendJsonInfos(extendInfos.toJSONString());
        ebsFacadeRequest.setTempStorageOrder(true);
        ebsFacadeRequest.setRequestProfile(requestProfile);
        return ebsFacadeRequest;
    }

    /**
     * 构建交管12123推送ebs入参
     * @param orderSnapshot
     * @return
     */
    public EBSFacadeRequest toJG12123EBSFacadeRequest(ExpressOrderModel orderSnapshot, RequestProfile requestProfile) {
        EBSFacadeRequest ebsFacadeRequest = new EBSFacadeRequest();
        // 交管12123 客户单号 商家单号交互
        ebsFacadeRequest.setPreId(orderSnapshot.getChannel().getCustomerOrderNo());
        ebsFacadeRequest.setOrderId(orderSnapshot.getChannel().getCustomerOrderNo());
        // 来源系统
        ebsFacadeRequest.setYtSrcSystem(EBSConstants.WLJGHZCJ);
        ebsFacadeRequest.setPreDocNum(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());
        //事业部编码
        ebsFacadeRequest.setVendorId(orderSnapshot.getCustomer().getAccountNo());
        //事业部名称
        ebsFacadeRequest.setVendorName(orderSnapshot.getCustomer().getAccountName());
        //申请时间
        ebsFacadeRequest.setAppliedDate(new Date());
        //金额 异步获取
        //结算方式
        ebsFacadeRequest.setSettlemetType(SettlementTypeEnum.CASH_ON_DELIVERY.getCode().toString());
        //应收：1
        ebsFacadeRequest.setDirection((byte) 1);
        //计提总金额
        ebsFacadeRequest.setTotalAmount(orderSnapshot.getFinance().getDiscountAmount().getAmount());
        //币种
        ebsFacadeRequest.setCurrencyCode(String.valueOf(CurrencyCodeEnum.CNY.getFeeSystemCode()));
        //京东收支机构
        ebsFacadeRequest.setOrgId(orderSnapshot.getFinance().getCollectionOrgNo());

        // 费用类型编号
        ebsFacadeRequest.setLineDocTypeName(EBSConstants.QIPSF);
        //服务单号
        ebsFacadeRequest.setAttribute5(orderSnapshot.orderNo());
        String cityNoGis = orderSnapshot.getConsignor().getAddress().getCityNoGis();
        //签约区域-寄件人省份，京标
        ebsFacadeRequest.setRegion(cityNoGis);

        JSONObject extendInfos = new JSONObject();
        //寄件二级地址-京标
        extendInfos.put("startTwoAddress", cityNoGis);

        // 产品三级标
        ProductDelegate productDelegate = orderSnapshot.getProductDelegate();
        generateExtenInfosForProduct(productDelegate, extendInfos);
        ebsFacadeRequest.setExtendJsonInfos(extendInfos.toJSONString());
        ebsFacadeRequest.setRequestProfile(requestProfile);
        return ebsFacadeRequest;
    }
}
