package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.AddressMapper;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.dto.ConsigneeInfoDto;
import cn.jdl.oms.express.domain.dto.ConsignorInfoDto;
import cn.jdl.oms.express.domain.dto.FenceInfoDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.FenceTrustEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Dimension;
import cn.jdl.oms.express.domain.vo.Enquiry;
import cn.jdl.oms.express.domain.vo.Fence;
import cn.jdl.oms.express.domain.vo.Package;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.ShipmentConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusExtendEnum;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.pms.basic.api.bean.domain.AreaInfo;
import cn.jdl.pms.basic.api.bean.domain.CargoDimensionInfo;
import cn.jdl.pms.basic.api.bean.domain.CargoInfo;
import cn.jdl.pms.basic.api.bean.domain.CargoSurchargeInfo;
import cn.jdl.pms.basic.api.bean.domain.FenceInfo;
import cn.jdl.pms.basic.api.bean.domain.ShipmentInfo;
import cn.jdl.pms.basic.api.bean.domain.SurchargeInfo;
import cn.jdl.pms.basic.api.bean.domain.SurchargeQueryElement;
import cn.jdl.pms.basic.api.bean.domain.Volume;
import cn.jdl.pms.basic.api.request.ProductSurchargeRequest;
import cn.jdl.pms.basic.api.request.TargetSurcharge;
import cn.jdl.pms.basic.api.response.core.ApiResult;
import com.google.common.collect.Lists;
import com.jd.open.sp.lpc.cache.domain.dto.RouteAddressImprove;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Version 1.0
 */
@Translator
public class SurchargeFacadeTranslator {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(SurchargeFacadeTranslator.class);

    /**
     * 入参转换
     * @param request
     * @return
     */
    public ProductSurchargeRequest toProductSurchargeRequest(SurchargeRequest request) {
        ProductSurchargeRequest queryRequest = new ProductSurchargeRequest();
        //租户id
        queryRequest.setTenantId(request.getTenantId());
        //链路ID
        queryRequest.setTraceId(request.getTraceId());
        Map<String, SurchargeQueryElement> elements = new HashMap<>();
        SurchargeQueryElement surchargeQueryElement = new SurchargeQueryElement();
        //主产品
        surchargeQueryElement.setProductNoList(Arrays.asList(request.getProductNo()));
        //调用时间
        surchargeQueryElement.setSendTime(request.getSendTime());
        //包裹数
        surchargeQueryElement.setPackageNum(request.getPackageNum());
        //体积
        if(null != request.getPackageVolume()){
            Volume volume = new Volume();
            volume.setLength(request.getPackageVolume().getLength());
            volume.setWidth(request.getPackageVolume().getWidth());
            volume.setHeight(request.getPackageVolume().getHeight());
            surchargeQueryElement.setVolume(volume);
        }
        //重量
        surchargeQueryElement.setWeight(request.getPackageWeight());
        //配送信息
        if(null != request.getShipmentFacadeDto()){
            SurchargeRequest.ShipmentFacadeDto shipmentFacadeDto = request.getShipmentFacadeDto();
            ShipmentInfo shipmentInfo = new ShipmentInfo();
            //暂存天数
            shipmentInfo.setTempStorageDay(shipmentFacadeDto.getTempStorageDay());
            surchargeQueryElement.setShipmentInfo(shipmentInfo);
        }
        elements.put(request.getOrderNo(),surchargeQueryElement);
        queryRequest.setElements(elements);
        return queryRequest;
    }

    /**
     * 响应转换
     * @param response
     * @return
     */
    public List<SurchargeResponse> toSurchargeResponse(ApiResult<List<TargetSurcharge>> response) {
        List<SurchargeResponse> responseList = new ArrayList<>();
        if (response != null) {
            for (TargetSurcharge targetSurcharge : response.getData()) {
                SurchargeResponse surchargeResponse = new SurchargeResponse();
                surchargeResponse.setBusinessId(targetSurcharge.getBusinessId());
                Map<String, List<SurchargeInfo>> surchargeInfoMap = targetSurcharge.getSurchargeInfoMap();
                if(MapUtils.isNotEmpty(surchargeInfoMap)){
                    Map<String, List<SurchargeResponse.SurchargeInfo>> map = new HashMap<>();
                    surchargeInfoMap.forEach((k,v) ->{
                        List<SurchargeResponse.SurchargeInfo> surchargeInfoList = Lists.newArrayListWithCapacity(v.size());
                        v.forEach(surchargeInfo -> {
                            SurchargeResponse.SurchargeInfo info = new SurchargeResponse.SurchargeInfo();
                            //附加费编码
                            info.setSurchargeNo(surchargeInfo.getSurchargeNo());
                            //附加费名称
                            info.setSurchargeName(surchargeInfo.getSurchargeName());
                            //附加费类型编码
                            info.setSurchargeTypeName(surchargeInfo.getSurchargeTypeName());
                            //附加费类型名称
                            info.setSurchargeTypePrefix(surchargeInfo.getSurchargeTypePrefix());
                            surchargeInfoList.add(info);
                        });
                        map.put(k,surchargeInfoList);
                    });
                    surchargeResponse.setSurchargeInfoMap(map);
                }
                responseList.add(surchargeResponse);
            }
        }
        return responseList;
    }

    /**
     * 反腐层入参转换
     * @return
     */
    public SurchargeRequest toSurchargeRequest(ExpressOrderModel orderModel, ExpressOrderModel snapshot, RequestProfile profile) {
        SurchargeRequest surchargeRequest = new SurchargeRequest();

        //主产品编码
        String mainProductNo = snapshot.getProductDelegate().getMainProduct().getProductNo();
        surchargeRequest.setProductNo(mainProductNo);
        surchargeRequest.setOrderNo(snapshot.orderNo());
        Enquiry enquiry = orderModel.getEnquiry();
        if (BusinessSceneEnum.CALLBACK.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessScene())) {
            if (StringUtils.isNotBlank(orderModel.getExecutedStatus())) {
                ExpressOrderStatusExtendEnum orderStatusExtendEnum = ExpressOrderStatusExtendEnum.ofExtendStatus(orderModel.getExecutedStatus());
                if (ExpressOrderStatusExtendEnum.PEI_SONG_YUAN_PICKED_UP.equals(orderStatusExtendEnum)) {
                    surchargeRequest.setSendTime(DateUtils.formatDatetime(DateUtils.now()));
                    surchargeRequest.setConsigneeInfoDto(toConsigneeInfoDto(snapshot.getConsignee()));
                    surchargeRequest.setConsignorInfoDto(toConsignorInfoDto(snapshot.getConsignor()));
                }
            }
        } else if (null != enquiry) {
            //高峰期附加费时间 如果为空则赋值当前时间
            String pieceTime = enquiry.getFormatPeakPeriodTime();
            surchargeRequest.setSendTime(pieceTime);
            if (null != enquiry.getEnquiryDimension()) {
                Dimension enquiryDimension = enquiry.getEnquiryDimension();
                SurchargeRequest.Volume volume = new SurchargeRequest.Volume();
                volume.setLength(enquiryDimension.getLength().doubleValue());
                volume.setWidth(enquiryDimension.getWidth().doubleValue());
                volume.setHeight(enquiryDimension.getHeight().doubleValue());
                surchargeRequest.setPackageVolume(volume);
            }
            if (null != enquiry.getEnquiryQuantity() && null != enquiry.getEnquiryQuantity().getValue()) {
                surchargeRequest.setPackageNum(enquiry.getEnquiryQuantity().getValue().intValue());
            }
            if (null != enquiry.getEnquiryWeight() && null != enquiry.getEnquiryWeight().getValue()) {
                surchargeRequest.setPackageWeight(enquiry.getEnquiryWeight().getValue().doubleValue());
            }
        }
        if(null != profile){
            surchargeRequest.setTraceId(profile.getTraceId());
            surchargeRequest.setTenantId(profile.getTenantId());
        }
        //配送信息
        if(null != orderModel.getShipment()
                && StringUtils.isNotBlank(orderModel.getShipment().getServiceRequirementByKey(ShipmentConstants.TEMP_STORAGE_DAY))
                && StringUtils.isNumeric(orderModel.getShipment().getServiceRequirementByKey(ShipmentConstants.TEMP_STORAGE_DAY))){
            SurchargeRequest.ShipmentFacadeDto shipmentFacadeDto = new SurchargeRequest.ShipmentFacadeDto();
            //暂存天数
            shipmentFacadeDto.setTempStorageDay(Integer.valueOf(orderModel.getShipment().getServiceRequirementByKey(ShipmentConstants.TEMP_STORAGE_DAY)));
            surchargeRequest.setShipmentFacadeDto(shipmentFacadeDto);
        }
        return surchargeRequest;
    }

    /**
     * 构建收货信息
     *
     * @param consignee
     * @return
     */
    private ConsigneeInfoDto toConsigneeInfoDto(Consignee consignee) {
        if (consignee == null || consignee.getAddress() == null) {
            return null;
        }
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
        consigneeInfoDto.setAddressInfoDto(AddressMapper.INSTANCE.toAddressInfoDto(consignee.getAddress()));
        return consigneeInfoDto;
    }

    /**
     * 构建发货信息
     *
     * @param consignor
     * @return
     */
    private ConsignorInfoDto toConsignorInfoDto(Consignor consignor) {
        if (consignor == null || consignor.getAddress() == null) {
            return null;
        }
        ConsignorInfoDto consignorInfoDto = new ConsignorInfoDto();
        consignorInfoDto.setAddressInfoDto(AddressMapper.INSTANCE.toAddressInfoDto(consignor.getAddress()));
        return consignorInfoDto;
    }

    /**
     * 反腐层入参转换-支持运单+包裹维度附加费
     *
     * @param orderModel
     * @param snapshot
     * @param profile
     * @return
     */
    public SurchargeRequest toSurchargeFacadeRequest(ExpressOrderModel orderModel, ExpressOrderModel snapshot,
                                                     RequestProfile profile, List<Package> packageList) {

        // 【首先复用原逻辑】
        SurchargeRequest surchargeRequest = toSurchargeRequest(orderModel, snapshot, profile);

        // 【补充包裹明细】
        if (orderModel.getEnquiry().isPackageWeightingMode()) {
            // 包裹模式
            // 补充包裹维度入参包裹明细
            // 包裹模式下 入参一定有包裹明细，基础校验已校验过
            fillPackageListToSurchargeRequest(packageList, surchargeRequest, false);
        } else {
            // 运单模式
            // 兼容逻辑，由于包裹维度和运单维度使用同一个超长超重附加费编码，产品中心做运单维度到包裹维度的切换动作，切换过程中订单中心做兼容处理
            // 因此，运单模式下也需补充包裹明细
            // 使用运单维度入参构造一单一包裹
            List<SurchargeRequest.Package> surchargePackageList = new ArrayList<>();
            SurchargeRequest.Package sPackage = new SurchargeRequest.Package();
            // 固定包裹号
            sPackage.setPackageNo(EnquiryConstants.PACKAGE_NO);
            // 包裹重量
            sPackage.setPackageWeight(surchargeRequest.getPackageWeight());
            // 包裹体积-长宽高
            sPackage.setPackageVolume(surchargeRequest.getPackageVolume());
            surchargePackageList.add(sPackage);
            surchargeRequest.setPackageList(surchargePackageList);
        }

        return surchargeRequest;
    }

    /**
     * 反腐层入参转换
     *
     * @param orderModel
     * @param snapshot
     * @param profile
     * @param isWarehouseOutboundMsg
     * @return
     */
    public SurchargeRequest toPackageSurchargeRequest(ExpressOrderModel orderModel, ExpressOrderModel snapshot,
                                                     RequestProfile profile, List<Package> packageList, boolean isWarehouseOutboundMsg) {

        // 构建附加费查询入参
        SurchargeRequest surchargeRequest = toSurchargeRequest(orderModel, snapshot, profile);

        if (CollectionUtils.isNotEmpty(packageList)) {

            // 补充运单维度入参（兼容产品中心超长超重附加费维度切换）
            fillWaybillParamToSurchargeRequest(packageList, surchargeRequest);

            // 补充包裹维度入参包裹明细
            fillPackageListToSurchargeRequest(packageList, surchargeRequest, isWarehouseOutboundMsg);
        }

        return surchargeRequest;
    }

    /**
     * 附加费查询入参补充运单维度信息
     *
     * @param packageList
     * @param surchargeRequest
     */
    private void fillWaybillParamToSurchargeRequest(List<Package> packageList, SurchargeRequest surchargeRequest) {

        Package waybillPackage = packageList.get(0);

        // 包裹体积
        if (null != waybillPackage.getPackageDimension()) {
            Dimension dimension = waybillPackage.getPackageDimension();
            SurchargeRequest.Volume volume = new SurchargeRequest.Volume();
            volume.setLength(dimension.getLength() == null ? null : dimension.getLength().doubleValue());
            volume.setWidth(dimension.getWidth() == null ? null : dimension.getWidth().doubleValue());
            volume.setHeight(dimension.getHeight() == null ? null : dimension.getHeight().doubleValue());
            surchargeRequest.setPackageVolume(volume);
        }
        // 包裹数量
        surchargeRequest.setPackageNum(packageList.size());

        // 包裹重量
        if(null != waybillPackage.getPackageWeight() && null != waybillPackage.getPackageWeight().getValue()){
            surchargeRequest.setPackageWeight(waybillPackage.getPackageWeight().getValue().doubleValue());
        }

    }

    /**
     * 从综合附加费查询结果中获取【运单】维度附加费
     *
     * @param mainProductNo 主产品编码
     * @param responseList  综合附加费查询结果
     * @return
     */
    public List<Product> fetchWaybillSurcharge(String mainProductNo, List<SurchargeResponse> responseList) {

        if (StringUtils.isBlank(mainProductNo) || CollectionUtils.isEmpty(responseList)) {
            return new ArrayList<>();
        }

        List<Product> products = new ArrayList<>();
        for (SurchargeResponse surchargeResponse : responseList) {

            // 整单维度附加费
            Map<String, List<SurchargeResponse.SurchargeInfo>> surchargeInfoMap = surchargeResponse.getSurchargeInfoMap();
            if (MapUtils.isEmpty(surchargeInfoMap)) {
                continue;
            }

            // 当前主产品编码下的整单维度附加费
            List<SurchargeResponse.SurchargeInfo> surchargeInfoList = surchargeInfoMap.get(mainProductNo);
            if (CollectionUtils.isEmpty(surchargeInfoList)) {
                continue;
            }

            // 结构转换
            surchargeInfoList.forEach(surchargeInfo -> {
                Product product = new Product();
                product.setProductNo(surchargeInfo.getSurchargeNo());
                product.setProductName(surchargeInfo.getSurchargeName());
                product.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                products.add(product);
            });
        }

        return products;
    }

    /**
     * 从综合附加费查询结果中获取【包裹】维度附加费
     *
     * @param mainProductNo 主产品编码
     * @param responseList  综合附加费查询结果
     * @param packageList   包裹明细列表
     * @return
     */
    public List<BillingEnquiryFacadeRequest.PackageFacadeDto> fetchPackageSurcharge(String mainProductNo, List<SurchargeResponse> responseList, List<Package> packageList) {

        if (StringUtils.isBlank(mainProductNo) || CollectionUtils.isEmpty(responseList)) {
            return new ArrayList<>();
        }

        // [包裹号:包裹]
        Map<String, Package> packageMap = packageList.stream().collect(Collectors.toMap(Package::getPackageNo, Function.identity(), (p1, p2) -> p2));

        // 包裹维度附加费列表
        List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList = new ArrayList<>();

        for (SurchargeResponse surchargeResponse : responseList) {

            // 包裹维度附加费
            Map<String, List<SurchargeResponse.CargoSurchargeInfo>> cargoSurchargeInfoMap = surchargeResponse.getCargoSurchargeMap();
            if (MapUtils.isEmpty(cargoSurchargeInfoMap)) {
                continue;
            }

            // 当前主产品下的包裹维度附加费
            List<SurchargeResponse.CargoSurchargeInfo> cargoSurchargeInfoList = cargoSurchargeInfoMap.get(mainProductNo);
            if (CollectionUtils.isEmpty(cargoSurchargeInfoList)) {
                continue;
            }

            cargoSurchargeInfoList.forEach(cargoSurchargeInfo -> {
                BillingEnquiryFacadeRequest.PackageFacadeDto packageFacadeDto = new BillingEnquiryFacadeRequest.PackageFacadeDto();
                // 包裹号
                String packageNo = cargoSurchargeInfo.getCargoNo();
                // 附加费列表
                List<BillingEnquiryFacadeRequest.ProductFacadeDto> productList = new ArrayList<>();
                List<SurchargeResponse.SurchargeInfo> surchargeInfoList = cargoSurchargeInfo.getSurchargeInfos();
                if (CollectionUtils.isNotEmpty(surchargeInfoList)) {
                    // 结构转换
                    surchargeInfoList.forEach(surchargeInfo -> {
                        BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                        productFacadeDto.setProductNo(surchargeInfo.getSurchargeNo());
                        productFacadeDto.setProductName(surchargeInfo.getSurchargeName());
                        productFacadeDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                        productList.add(productFacadeDto);
                    });

                    // 设置包裹号
                    packageFacadeDto.setPackageNo(packageNo);
                    // 设置包裹数（固定为1）
                    packageFacadeDto.setQuantity(1);
                    // 设置产品列表
                    packageFacadeDto.setProductList(productList);
                    Package enquiryPackage = packageMap.get(packageNo);
                    if (enquiryPackage != null) {
                        // 补全包裹重量
                        packageFacadeDto.setWeight(enquiryPackage.getPackageWeight().getValue());
                        // 补全包裹长、宽、高、体积
                        Dimension dimension = enquiryPackage.getPackageDimension();
                        if (dimension != null) {
                            BigDecimal length = dimension.getLength();
                            BigDecimal width = dimension.getWidth();
                            BigDecimal height = dimension.getHeight();
                            packageFacadeDto.setLength(length);
                            packageFacadeDto.setWidth(width);
                            packageFacadeDto.setHeight(height);
                            if (length != null && width != null && height != null) {
                                packageFacadeDto.setVolume(length.multiply(width).multiply(height));
                            }
                        }
                    }
                    packageSurchargeList.add(packageFacadeDto);
                } else {
                    LOGGER.info("包裹[{}]未命中附加费", packageNo);
                }

            });
        }
        return packageSurchargeList;
    }

    /**
     * 结构转换
     *
     * @param packageSurchargeList 转换前结构 package-productList
     * @param productNo2NameMap 记录产品编码和产品名称映射关系
     * @param product2PackageListMap 转换后结构 product:packageList
     */
    public void convertPackageProductListToProductPackageList(List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList,
                                                               Map<String, String> productNo2NameMap,
                                                               Map<String, List<BillingEnquiryFacadeRequest.PackageFacadeDto>> product2PackageListMap) {
        if (CollectionUtils.isEmpty(packageSurchargeList)) {
            return;
        }

        for (BillingEnquiryFacadeRequest.PackageFacadeDto packageFacadeDto : packageSurchargeList) {

            List<BillingEnquiryFacadeRequest.ProductFacadeDto> productList = packageFacadeDto.getProductList();
            for (BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto : productList) {

                if (!productNo2NameMap.containsKey(productFacadeDto.getProductNo())) {
                    productNo2NameMap.put(productFacadeDto.getProductNo(), productFacadeDto.getProductName());
                }

                if (product2PackageListMap.containsKey(productFacadeDto.getProductNo())) {
                    product2PackageListMap.get(productFacadeDto.getProductNo()).add(packageFacadeDto);
                } else {
                    List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageFacadeDtoList = new ArrayList<>();
                    packageFacadeDtoList.add(packageFacadeDto);
                    product2PackageListMap.put(productFacadeDto.getProductNo(), packageFacadeDtoList);
                }
            }
        }
    }

    /**
     * 获取包裹号列表
     * -- 超100个截取处理并告警
     *
     * @param orderNo
     * @param packageFacadeDtoList
     * @param costInfo
     * @return
     */
    public List<String> fetchPackageNoList(String orderNo, List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageFacadeDtoList, CostInfo costInfo) {

        if (CollectionUtils.isEmpty(packageFacadeDtoList)) {
            return new ArrayList<>();
        }

        // 包裹号列表
        List<String> packageNoList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(packageFacadeDtoList)) {

            // 过滤掉虚拟包裹号
            packageFacadeDtoList = packageFacadeDtoList.stream()
                    .filter(packageFacadeDto -> !EnquiryConstants.PACKAGE_NO.equals(packageFacadeDto.getPackageNo()))
                    .collect(Collectors.toList());

            // 包裹明细列表最大不能超过100个
            if (packageFacadeDtoList.size() > EnquiryConstants.MAX_PACKAGE_FOR_SURCHARGE) {

                // 包裹明细数量超阈值告警信息
                String businessAlarmMsg = new StringBuilder()
                        .append("订单[")
                        .append(orderNo)
                        .append("],附加费编码[")
                        .append(costInfo.getCostNo())
                        .append("],附加费名称[")
                        .append(costInfo.getCostName()).append("],涉及包裹个数[")
                        .append(packageFacadeDtoList.size())
                        .append("]超过最大限制[")
                        .append(EnquiryConstants.MAX_PACKAGE_FOR_SURCHARGE).append("],已按包裹重量倒叙排序并截取前100个").toString();

                // 若超过100，按包裹重量倒叙排列，截取前100个，并告警
                packageFacadeDtoList = packageFacadeDtoList.stream()
                        .sorted((p1, p2) -> p2.getWeight().compareTo(p1.getWeight()))
                        .limit(EnquiryConstants.MAX_PACKAGE_FOR_SURCHARGE).collect(Collectors.toList());

                // 自定义告警
                LOGGER.warn(businessAlarmMsg);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_SURCHARGE_PACKAGE_NUM_ALARM
                        , System.currentTimeMillis()
                        , businessAlarmMsg);
            }
            for (BillingEnquiryFacadeRequest.PackageFacadeDto packageFacadeDto : packageFacadeDtoList) {
                // 包裹号
                packageNoList.add(packageFacadeDto.getPackageNo());
            }
        }
        return packageNoList;
    }

    /**
     * 附加费查询入参补充包裹明细
     *
     * @param packageList
     * @param surchargeRequest
     */
    private void fillPackageListToSurchargeRequest(List<Package> packageList, SurchargeRequest surchargeRequest, boolean isWarehouseOutboundMsg) {

        if (CollectionUtils.isEmpty(packageList)) {
            return;
        }

        List<SurchargeRequest.Package> surchargePackageList = new ArrayList<>();

        for (Package aPackage : packageList) {
            SurchargeRequest.Package sPackage = new SurchargeRequest.Package();
            // 包裹号
            sPackage.setPackageNo(aPackage.getPackageNo());
            // 包裹重量
            if (aPackage.getPackageWeight() != null && aPackage.getPackageWeight().getValue() != null) {
                sPackage.setPackageWeight(aPackage.getPackageWeight().getValue().doubleValue());
            }
            // 仓出库消息不传长宽高体积
            if (!isWarehouseOutboundMsg) {
                // 包裹体积-长宽高
                Dimension dimension = aPackage.getPackageDimension();
                if (null != dimension) {
                    SurchargeRequest.Volume sVolume = new SurchargeRequest.Volume();
                    sVolume.setLength(dimension.getLength() == null ? null : dimension.getLength().doubleValue());
                    sVolume.setWidth(dimension.getWidth() == null ? null : dimension.getWidth().doubleValue());
                    sVolume.setHeight(dimension.getHeight() == null ? null : dimension.getHeight().doubleValue());
                    sPackage.setPackageVolume(sVolume);
                }
            }

            surchargePackageList.add(sPackage);
        }
        surchargeRequest.setPackageList(surchargePackageList);
    }

    /**
     * 支持运单+包裹模式附加费
     *
     * @param request
     * @return
     */
    public ProductSurchargeRequest toProductSurchargeRequestFacade(SurchargeRequest request) {
        ProductSurchargeRequest queryRequest = new ProductSurchargeRequest();
        //租户id
        queryRequest.setTenantId(request.getTenantId());
        //链路ID
        queryRequest.setTraceId(request.getTraceId());
        Map<String, SurchargeQueryElement> elements = new HashMap<>();
        SurchargeQueryElement surchargeQueryElement = new SurchargeQueryElement();
        //主产品
        surchargeQueryElement.setProductNoList(Arrays.asList(request.getProductNo()));

        //调用时间
        if (StringUtils.isNotBlank(request.getSendTime())) {
            surchargeQueryElement.setSendTime(request.getSendTime());
        }

        //包裹数
        if (null != request.getPackageNum()) {
            surchargeQueryElement.setPackageNum(request.getPackageNum());
        }

        //体积
        if(null != request.getPackageVolume()){
            Volume volume = new Volume();
            volume.setLength(request.getPackageVolume().getLength());
            volume.setWidth(request.getPackageVolume().getWidth());
            volume.setHeight(request.getPackageVolume().getHeight());
            surchargeQueryElement.setVolume(volume);
        }
        //重量
        if (null != request.getPackageWeight()) {
            surchargeQueryElement.setWeight(request.getPackageWeight());
        }
        // 包裹明细
        if (CollectionUtils.isNotEmpty(request.getPackageList())) {
            // 转换成产品中心要求的入参格式
            List<CargoInfo> cargoInfos = new ArrayList<>();
            for (SurchargeRequest.Package aPackage : request.getPackageList()) {
                CargoInfo cargoInfo = new CargoInfo();
                // 包裹号
                cargoInfo.setCargoNo(aPackage.getPackageNo());
                // 包裹长宽高、重量等维度信息
                CargoDimensionInfo dimensionInfo = new CargoDimensionInfo();
                dimensionInfo.setWeight(aPackage.getPackageWeight());
                if (aPackage.getPackageVolume() != null) {
                    Volume volume = new Volume();
                    volume.setLength(aPackage.getPackageVolume().getLength());
                    volume.setWidth(aPackage.getPackageVolume().getWidth());
                    volume.setHeight(aPackage.getPackageVolume().getHeight());
                    dimensionInfo.setVolume(volume);
                }
                cargoInfo.setDimensionInfo(dimensionInfo);
                cargoInfos.add(cargoInfo);
            }
            surchargeQueryElement.setCargoInfos(cargoInfos);
        }

        // 揽收地址
        if (null != request.getConsignorInfoDto()
                && null != request.getConsignorInfoDto().getAddressInfoDto()) {

            RouteAddressImprove sendAddress = new RouteAddressImprove();
            sendAddress.setProvinceNo(NumberUtils.toInt(request.getConsignorInfoDto().getAddressInfoDto().getProvinceNoGis(), 0));
            sendAddress.setCityNo(NumberUtils.toInt(request.getConsignorInfoDto().getAddressInfoDto().getCityNoGis(), 0));
            sendAddress.setCountyNo(NumberUtils.toInt(request.getConsignorInfoDto().getAddressInfoDto().getCountyNoGis(), 0));
            sendAddress.setTownNo(NumberUtils.toInt(request.getConsignorInfoDto().getAddressInfoDto().getTownNoGis(), 0));

            sendAddress.setFullAddress(getFullAddress(request.getConsignorInfoDto().getAddressInfoDto()));

            // 邮编
            if (StringUtils.isNotBlank(request.getConsignorInfoDto().getConsignorZipCode())) {
                sendAddress.setPostalCode(request.getConsignorInfoDto().getConsignorZipCode());
            }

            surchargeQueryElement.setSendAddress(sendAddress);

            // 揽收区域信息
            if (StringUtils.isNotBlank(request.getConsignorInfoDto().getAddressInfoDto().getRegionNo())) {

                AreaInfo pickupArea = new AreaInfo();
                pickupArea.setArea(request.getConsignorInfoDto().getAddressInfoDto().getRegionNo());

                if (null != request.getConsignorInfoDto().getAddressInfoDto().getFenceTrusted()) {
                    // 围栏信息 目前只有传信任标识才会传入围栏信息
                    if (FenceTrustEnum.TRUSTED.getCode().equals(request.getConsignorInfoDto().getAddressInfoDto().getFenceTrusted())) {
                        pickupArea.setFenceTrusted(FenceTrustEnum.TRUSTED.getCode());
                        pickupArea.setFenceInfos(toFenceInfoList(request.getConsignorInfoDto().getAddressInfoDto().getFenceInfos()));
                    }
                }

                surchargeQueryElement.setPickupArea(pickupArea);
            }
        }

        // 派送地址
        if (null !=  request.getConsigneeInfoDto()
                && null !=  request.getConsigneeInfoDto().getAddressInfoDto()) {

            RouteAddressImprove receiveAddress = new RouteAddressImprove();
            receiveAddress.setProvinceNo(NumberUtils.toInt(request.getConsigneeInfoDto().getAddressInfoDto().getProvinceNoGis(), 0));
            receiveAddress.setCityNo(NumberUtils.toInt( request.getConsigneeInfoDto().getAddressInfoDto().getCityNoGis(), 0));
            receiveAddress.setCountyNo(NumberUtils.toInt( request.getConsigneeInfoDto().getAddressInfoDto().getCountyNoGis(), 0));
            receiveAddress.setTownNo(NumberUtils.toInt( request.getConsigneeInfoDto().getAddressInfoDto().getTownNoGis(), 0));

            receiveAddress.setFullAddress(getFullAddress(request.getConsigneeInfoDto().getAddressInfoDto()));

            // 邮编
            if (StringUtils.isNotBlank(request.getConsigneeInfoDto().getConsigneeZipCode())) {
                receiveAddress.setPostalCode(request.getConsigneeInfoDto().getConsigneeZipCode());
            }

            surchargeQueryElement.setReceiveAddress(receiveAddress);

            // 派送区域信息
            if (StringUtils.isNotBlank(request.getConsigneeInfoDto().getAddressInfoDto().getRegionNo())) {

                AreaInfo deliveryArea = new AreaInfo();
                deliveryArea.setArea(request.getConsigneeInfoDto().getAddressInfoDto().getRegionNo());

                if (null != request.getConsigneeInfoDto().getAddressInfoDto().getFenceTrusted()) {
                    // 围栏信息 目前只有传信任标识才会传入围栏信息
                    if (FenceTrustEnum.TRUSTED.getCode().equals(request.getConsigneeInfoDto().getAddressInfoDto().getFenceTrusted())) {
                        deliveryArea.setFenceTrusted(FenceTrustEnum.TRUSTED.getCode());
                        deliveryArea.setFenceInfos(toFenceInfoList(request.getConsigneeInfoDto().getAddressInfoDto().getFenceInfos()));
                    }
                }

                surchargeQueryElement.setDeliveryArea(deliveryArea);
            }
        }



        elements.put(request.getOrderNo(),surchargeQueryElement);
        queryRequest.setElements(elements);
        return queryRequest;
    }

    /**
     * 支持运单+包裹模式附加费
     * -- 产品中心附加费结构转换成订单中心内部结构
     *
     * @param response
     * @return
     */
    public List<SurchargeResponse> toSurchargeResponseFacade(ApiResult<List<TargetSurcharge>> response) {
        List<SurchargeResponse> responseList = new ArrayList<>();
        if (response != null) {
            for (TargetSurcharge targetSurcharge : response.getData()) {
                SurchargeResponse surchargeResponse = new SurchargeResponse();
                surchargeResponse.setBusinessId(targetSurcharge.getBusinessId());

                // 整单维度附加费
                // SurchargeInfoMap结构：[产品编码：整单维度附加费列表]
                Map<String, List<SurchargeInfo>> surchargeInfoMap = targetSurcharge.getSurchargeInfoMap();
                if(MapUtils.isNotEmpty(surchargeInfoMap)){
                    Map<String, List<SurchargeResponse.SurchargeInfo>> map = new HashMap<>();
                    surchargeInfoMap.forEach((k,v) ->{
                        List<SurchargeResponse.SurchargeInfo> surchargeInfoList = Lists.newArrayListWithCapacity(v.size());
                        v.forEach(surchargeInfo -> {
                            surchargeInfoList.add(convertSurchargeInfo(surchargeInfo));
                        });
                        map.put(k,surchargeInfoList);
                    });
                    surchargeResponse.setSurchargeInfoMap(map);
                }

                // 包裹维度附加费
                // CargoSurchargeMap结构：[产品编码：包裹维度附加费列表]
                Map<String, List<CargoSurchargeInfo>> cargoSurchargeMap = targetSurcharge.getCargoSurchargeMap();
                if (MapUtils.isNotEmpty(cargoSurchargeMap)) {
                    Map<String, List<SurchargeResponse.CargoSurchargeInfo>> map = new HashMap<>();
                    cargoSurchargeMap.forEach((k, v) -> {
                        List<SurchargeResponse.CargoSurchargeInfo> cargoSurchargeInfoList = Lists.newArrayListWithCapacity(v.size());
                        v.forEach(cargoSurchargeInfo -> {
                            SurchargeResponse.CargoSurchargeInfo info = new SurchargeResponse.CargoSurchargeInfo();
                            // 包裹号
                            info.setCargoNo(cargoSurchargeInfo.getCargoNo());
                            // 附加费集合
                            List<SurchargeInfo> surchargeInfoList = cargoSurchargeInfo.getSurchargeInfos();
                            if (CollectionUtils.isNotEmpty(surchargeInfoList)) {
                                List<SurchargeResponse.SurchargeInfo> surchargeList = Lists.newArrayListWithCapacity(surchargeInfoList.size());
                                surchargeInfoList.forEach(surchargeInfo -> {
                                    surchargeList.add(convertSurchargeInfo(surchargeInfo));
                                });
                                info.setSurchargeInfos(surchargeList);
                            }
                            cargoSurchargeInfoList.add(info);
                        });
                        map.put(k, cargoSurchargeInfoList);
                    });
                    surchargeResponse.setCargoSurchargeMap(map);
                }

                responseList.add(surchargeResponse);
            }
        }
        return responseList;
    }

    /**
     * surchargeInfo 类型转换
     * @param surchargeInfo
     * @return
     */
    private SurchargeResponse.SurchargeInfo convertSurchargeInfo(SurchargeInfo surchargeInfo) {
        SurchargeResponse.SurchargeInfo info = new SurchargeResponse.SurchargeInfo();
        //附加费编码
        info.setSurchargeNo(surchargeInfo.getSurchargeNo());
        //附加费名称
        info.setSurchargeName(surchargeInfo.getSurchargeName());
        //附加费类型编码
        info.setSurchargeTypeName(surchargeInfo.getSurchargeTypeName());
        //附加费类型名称
        info.setSurchargeTypePrefix(surchargeInfo.getSurchargeTypePrefix());
        return info;
    }

    /**
     * 入参转换，接单场景
     *
     * @param context
     * @return
     */
    public ProductSurchargeRequest toCreateProductSurchargeRequest(ExpressOrderContext context) {

        ProductSurchargeRequest queryRequest = new ProductSurchargeRequest();

        // 租户id
        queryRequest.setTenantId(context.getRequestProfile().getTenantId());
        // 链路ID
        queryRequest.setTraceId(context.getRequestProfile().getTraceId());

        ExpressOrderModel orderModel = context.getOrderModel();

        Map<String, SurchargeQueryElement> elements = new HashMap<>();
        SurchargeQueryElement surchargeQueryElement = new SurchargeQueryElement();
        // 主产品
        surchargeQueryElement.setProductNoList(Arrays.asList(orderModel.getProductDelegate().getMainProduct().getProductNo()));

        // 调用时间
        if (null != orderModel.getOperateTime()) {
            surchargeQueryElement.setSendTime(DateUtils.formatDatetime(orderModel.getOperateTime()));
        }

        // 包裹数
        if (null != orderModel.getCargoDelegate()
                && null != orderModel.getCargoDelegate().totalCargoQuantity()) {
            surchargeQueryElement.setPackageNum(orderModel.getCargoDelegate().totalCargoQuantity().intValue());
        }

        // 体积
        if (null != orderModel.getCargoDelegate()
                && null != orderModel.getCargoDelegate().totalCargoVolume()) {
            /* 长宽高，整单维度没有超长超重附加费的话，可以不传
            Volume volume = new Volume();
            volume.setLength(request.getPackageVolume().getLength());
            volume.setWidth(request.getPackageVolume().getWidth());
            volume.setHeight(request.getPackageVolume().getHeight());
            surchargeQueryElement.setVolume(orderModel.getCargoDelegate().totalCargoVolume());
            */
        }

        // 重量
        if (null != orderModel.getCargoDelegate()
                && null != orderModel.getCargoDelegate().totalCargoWeight()) {
            surchargeQueryElement.setWeight(orderModel.getCargoDelegate().totalCargoWeight().doubleValue());
        }

        // 配送信息
        if (null != orderModel.getShipment()
                && StringUtils.isNotBlank(orderModel.getShipment().getServiceRequirementByKey(ShipmentConstants.TEMP_STORAGE_DAY))
                && StringUtils.isNumeric(orderModel.getShipment().getServiceRequirementByKey(ShipmentConstants.TEMP_STORAGE_DAY))) {

            ShipmentInfo shipmentInfo = new ShipmentInfo();
            //暂存天数
            shipmentInfo.setTempStorageDay(Integer.valueOf(orderModel.getShipment().getServiceRequirementByKey(ShipmentConstants.TEMP_STORAGE_DAY)));
            surchargeQueryElement.setShipmentInfo(shipmentInfo);
        }

        /* 包裹明细只在获取超长超重附加费时需要，接单时无需传入
        // 包裹明细
        if (null != orderModel.getCargoDelegate()
                && CollectionUtils.isNotEmpty(orderModel.getCargoDelegate().getCargoList())) {
            // 转换成产品中心要求的入参格式
            List<Cargo> cargoList = (List<Cargo>) orderModel.getCargoDelegate().getCargoList();
            List<CargoInfo> cargoInfos = new ArrayList<>(cargoList.size());

            for (Cargo cargo : cargoList) {
                CargoInfo cargoInfo = new CargoInfo();
                // 包裹号
                cargoInfo.setCargoNo(cargo.getCargoNo());

                // 包裹长宽高、重量等维度信息
                CargoDimensionInfo dimensionInfo = new CargoDimensionInfo();

                // 重量
                if (null != cargo.getCargoWeight()
                        && null != cargo.getCargoWeight().getValue()) {
                    dimensionInfo.setWeight(cargo.getCargoWeight().getValue().doubleValue());
                }

                // 包裹体积要素
                if (null != cargo.getCargoDimension()) {

                    Volume volume = new Volume();

                    if (null != cargo.getCargoDimension().getLength()) {
                        volume.setLength(cargo.getCargoDimension().getLength().doubleValue());
                    }
                    if (null != cargo.getCargoDimension().getWidth()) {
                        volume.setLength(cargo.getCargoDimension().getWidth().doubleValue());
                    }
                    if (null != cargo.getCargoDimension().getHeight()) {
                        volume.setLength(cargo.getCargoDimension().getHeight().doubleValue());
                    }

                    dimensionInfo.setVolume(volume);
                }

                cargoInfo.setDimensionInfo(dimensionInfo);
                cargoInfos.add(cargoInfo);
            }
            surchargeQueryElement.setCargoInfos(cargoInfos);
        }
        */

        // 揽收地址
        if (null != orderModel.getConsignor()
                && null != orderModel.getConsignor().getAddress()) {

            RouteAddressImprove sendAddress = new RouteAddressImprove();
            sendAddress.setProvinceNo(NumberUtils.toInt(orderModel.getConsignor().getAddress().getProvinceNoGis(), 0));
            sendAddress.setCityNo(NumberUtils.toInt(orderModel.getConsignor().getAddress().getCityNoGis(), 0));
            sendAddress.setCountyNo(NumberUtils.toInt(orderModel.getConsignor().getAddress().getCountyNoGis(), 0));
            sendAddress.setTownNo(NumberUtils.toInt(orderModel.getConsignor().getAddress().getTownNoGis(), 0));

            sendAddress.setFullAddress(getFullAddress(orderModel.getConsignor().getAddress()));

            // 邮编
            if (StringUtils.isNotBlank(orderModel.getConsignor().getConsignorZipCode())) {
                sendAddress.setPostalCode(orderModel.getConsignor().getConsignorZipCode());
            }

            surchargeQueryElement.setSendAddress(sendAddress);

            // 揽收区域信息
            if (StringUtils.isNotBlank(orderModel.getConsignor().getAddress().getRegionNo())) {

                AreaInfo pickupArea = new AreaInfo();
                pickupArea.setArea(orderModel.getConsignor().getAddress().getRegionNo());

                if (null != orderModel.getConsignor().getAddress().getFenceTrusted()) {
                    // 围栏信息 目前只有传信任标识才会传入围栏信息
                    if (FenceTrustEnum.TRUSTED.getCode().equals(orderModel.getConsignor().getAddress().getFenceTrusted())) {
                        pickupArea.setFenceTrusted(FenceTrustEnum.TRUSTED.getCode());
                        pickupArea.setFenceInfos(toFenceInfos(orderModel.getConsignor().getAddress().getFenceInfos()));
                    }
                }

                surchargeQueryElement.setPickupArea(pickupArea);
            }
        }

        // 派送地址
        if (null != orderModel.getConsignee()
                && null != orderModel.getConsignee().getAddress()) {

            RouteAddressImprove receiveAddress = new RouteAddressImprove();
            receiveAddress.setProvinceNo(NumberUtils.toInt(orderModel.getConsignee().getAddress().getProvinceNoGis(), 0));
            receiveAddress.setCityNo(NumberUtils.toInt(orderModel.getConsignee().getAddress().getCityNoGis(), 0));
            receiveAddress.setCountyNo(NumberUtils.toInt(orderModel.getConsignee().getAddress().getCountyNoGis(), 0));
            receiveAddress.setTownNo(NumberUtils.toInt(orderModel.getConsignee().getAddress().getTownNoGis(), 0));

            receiveAddress.setFullAddress(getFullAddress(orderModel.getConsignee().getAddress()));

            // 邮编
            if (StringUtils.isNotBlank(orderModel.getConsignee().getConsigneeZipCode())) {
                receiveAddress.setPostalCode(orderModel.getConsignee().getConsigneeZipCode());
            }

            surchargeQueryElement.setReceiveAddress(receiveAddress);

            // 派送区域信息
            if (StringUtils.isNotBlank(orderModel.getConsignee().getAddress().getRegionNo())) {

                AreaInfo deliveryArea = new AreaInfo();
                deliveryArea.setArea(orderModel.getConsignee().getAddress().getRegionNo());

                if (null != orderModel.getConsignee().getAddress().getFenceTrusted()) {
                    // 围栏信息 目前只有传信任标识才会传入围栏信息
                    if (FenceTrustEnum.TRUSTED.getCode().equals(orderModel.getConsignee().getAddress().getFenceTrusted())) {
                        deliveryArea.setFenceTrusted(FenceTrustEnum.TRUSTED.getCode());
                        deliveryArea.setFenceInfos(toFenceInfos(orderModel.getConsignee().getAddress().getFenceInfos()));
                    }
                }

                surchargeQueryElement.setDeliveryArea(deliveryArea);
            }
        }

        elements.put(orderModel.orderNo(), surchargeQueryElement);
        queryRequest.setElements(elements);

        return queryRequest;
    }

    /**
     * 围栏信息转换
     *
     * @param fenceInfoList
     * @return
     */
    private List<FenceInfo> toFenceInfos(List<Fence> fenceInfoList) {
        if (CollectionUtils.isEmpty(fenceInfoList)) {
            return Collections.emptyList();
        }
        List<FenceInfo> fenceInfos = new ArrayList<>(fenceInfoList.size());
        for (Fence fence : fenceInfoList) {
            FenceInfo fenceInfo = new FenceInfo();
            fenceInfo.setFenceId(fence.getFenceId());
            fenceInfo.setFenceType(fence.getFenceType());
            fenceInfos.add(fenceInfo);
        }
        return fenceInfos;
    }

    private List<FenceInfo> toFenceInfoList (List<FenceInfoDto> fenceInfoList) {
        if (CollectionUtils.isEmpty(fenceInfoList)) {
            return Collections.emptyList();
        }
        List<FenceInfo> fenceInfos = new ArrayList<>(fenceInfoList.size());
        for (FenceInfoDto fence : fenceInfoList) {
            FenceInfo fenceInfo = new FenceInfo();
            fenceInfo.setFenceId(fence.getFenceId());
            fenceInfo.setFenceType(fence.getFenceType());
            fenceInfos.add(fenceInfo);
        }
        return fenceInfos;
    }

    /**
     * 过滤特殊附加费
     *
     * @param products
     * @param attachFeeCodes
     * @return
     */
    public List<CostInfo> filterSpecialSurcharge(List<Product> products, String... attachFeeCodes) {

        if (CollectionUtils.isEmpty(products) || attachFeeCodes == null || attachFeeCodes.length < 1) {
            return Collections.emptyList();
        }

        Set<String> feeCodeSet = Arrays.stream(attachFeeCodes)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        if (feeCodeSet.isEmpty()) {
            return Collections.emptyList();
        }

        List<CostInfo> attachFees = products.stream()
                .filter(Objects::nonNull)
                .filter(p -> feeCodeSet.contains(p.getProductNo()))
                .map(p -> {
                    CostInfo cost = new CostInfo();
                    cost.setCostNo(p.getProductNo());
                    cost.setCostName(p.getProductName());
                    return cost;
                })
                .collect(Collectors.toList());

        return attachFees;
    }

    /**
     * 将附加费列表追加到订单模型中
     *
     * @param orderModel
     * @param attachFees
     */
    public void appendAttachFeesToOrderModel(ExpressOrderModel orderModel, List<CostInfo> attachFees) {

        if (orderModel == null || orderModel.getFinance() == null) {
            throw new IllegalArgumentException("OrderModel 或 Finance 对象不可为 null");
        }

        if (attachFees == null || attachFees.isEmpty()) {
            LOGGER.info("附加费为空。");
            return;
        }

        List<CostInfo> existAttachFees = Optional.ofNullable(orderModel.getFinance().getAttachFees())
                .orElse(new ArrayList<>());

        // 合并
        List<CostInfo> mergedList = new ArrayList<>(existAttachFees);
        mergedList.addAll(attachFees);

        // 去重（保证顺序）
        LinkedHashMap<String, CostInfo> map = mergedList.stream()
                .filter(c -> c != null && c.getCostNo() != null)
                .collect(Collectors.toMap(
                        CostInfo::getCostNo,
                        Function.identity(),
                        (v1, v2) -> v2,
                        LinkedHashMap::new
                ));

        // 赋值
        orderModel.getFinance().setAttachFees(new ArrayList<>(map.values()));
    }

    private String getFullAddress(AddressInfoDto address) {
        StringBuilder builder = new StringBuilder();
        if (StringUtils.isNotBlank(address.getProvinceNameGis())) {
            builder.append(address.getProvinceNameGis());
        }
        if (StringUtils.isNotBlank(address.getCityNameGis())) {
            builder.append(address.getCityNameGis());
        }
        if (StringUtils.isNotBlank(address.getCountyNameGis())) {
            builder.append(address.getCountyNameGis());
        }
        if (StringUtils.isNotBlank(address.getTownNameGis())) {
            builder.append(address.getTownNameGis());
        }
        if (StringUtils.isNotBlank(address.getAddressGis())) {
            builder.append(address.getAddressGis());
        } else {
            builder.append(address.getAddress());
        }
        return builder.toString();
    }

    /**
     * 获取完整地址。首先拼接GIS全地址，若为空，返回 address.getFullAddress()
     *
     * @param address
     * @return
     */
    private String getFullAddress(Address address) {

        StringBuilder builder = new StringBuilder();
        if (StringUtils.isNotBlank(address.getProvinceNameGis())) {
            builder.append(address.getProvinceNameGis());
        }
        if (StringUtils.isNotBlank(address.getCityNameGis())) {
            builder.append(address.getCityNameGis());
        }
        if (StringUtils.isNotBlank(address.getCountyNameGis())) {
            builder.append(address.getCountyNameGis());
        }
        if (StringUtils.isNotBlank(address.getTownNameGis())) {
            builder.append(address.getTownNameGis());
        }
        if (StringUtils.isNotBlank(address.getAddressGis())) {
            builder.append(address.getAddressGis());
        }

        String gisFullAddress = builder.toString();
        if (StringUtils.isNotBlank(gisFullAddress)) {
            return gisFullAddress;
        } else {
            return address.getFullAddress();
        }

    }

}
