package cn.jdl.oms.express.domain.infrs.acl.pl.order;

import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.FenceInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.record.ModifyRecordDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.AttachmentFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.CustomsFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.MoneyFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ActivityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AgreementFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AttachmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessSolutionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CargoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ChannelFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsigneeFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsignorFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.DiscountFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceDetailFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FulfillmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.GoodsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.MarketEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PromotionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.RefOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ReturnInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ShipmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.StationTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.TicketFacade;
import cn.jdl.oms.express.domain.infrs.ohs.locals.security.TdeAcl;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.FenceTrustEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderUsageEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderExtendTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderSubType;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.ABConstants;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.NodeClassIfcationEnum;
import cn.jdl.oms.express.shared.common.dict.NodeUsageEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.RefOrderTypeStrEnum;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DataMaskUtil;
import com.google.common.collect.Lists;
import com.jdl.cp.core.spec.RequestProfile;
import com.jdl.cp.core.ts.entity.AdditionPriceInfo;
import com.jdl.cp.core.ts.entity.ConsignBaseInfo;
import com.jdl.cp.core.ts.entity.CostInfo;
import com.jdl.cp.core.ts.entity.CustomsInfo;
import com.jdl.cp.core.ts.entity.FulfillmentInfo;
import com.jdl.cp.core.ts.entity.InterceptInfo;
import com.jdl.cp.core.ts.entity.LengthInfo;
import com.jdl.cp.core.ts.entity.ModifyInfo;
import com.jdl.cp.core.ts.entity.Order;
import com.jdl.cp.op.client.dto.ModifyEnquiryStatusRequest;
import com.jdl.cp.op.client.dto.ModifyOrderSomeDataRequest;
import com.jdl.cp.op.client.dto.OrderNullFieldsRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName ModifyOrderRpcTranslator
 * @Description 创建订单rpc服务对象转换
 * <AUTHOR>
 * @Date 2021/3/26 5:08 下午
 * @ModifyDate 2021/3/21 5:08 下午
 * @Version 1.0
 */
@Translator
public class ModifyOrderRpcTranslator {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyOrderRpcTranslator.class);

    @Resource
    private TdeAcl tdeAcl;

    private static final String EMPTY_STRING = "";

    /**
     * 数据层信息修改--全量删除
     */
    private static final Integer REPOSITORY_ALL_DELETE = 4;

    /**
     * 数据层信息修改--部分修改
     */
    private static final Integer REPOSITORY_PART_UPDATE = 3;


    /**
     * 批量部分修改单批次最大数量
     */
    private static final int MAX_BATCH_MODIFY_NUM = 50;

    /**
     * 配置中心
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 转换为订单rpc服务的profile
     *
     * @param facadeRequestProfile
     * @return
     */
    public RequestProfile toRpcRequestProfile(cn.jdl.batrix.spec.RequestProfile facadeRequestProfile) {
        RequestProfile rpcRequestProfile = new RequestProfile();
        //国别
        rpcRequestProfile.setLocale(facadeRequestProfile.getLocale());
        //租户
        rpcRequestProfile.setTenantId(facadeRequestProfile.getTenantId());
        //时区
        rpcRequestProfile.setTimezone(facadeRequestProfile.getTimeZone());
        //调用链id
        rpcRequestProfile.setTraceId(facadeRequestProfile.getTraceId());
        return rpcRequestProfile;
    }

    /**
     * 功能：逆向单信息，改址单信息
     *
     * @param facadeRequestProfile 1
     * @param facadeRequest        2
     * @return com.jdl.cp.op.client.dto.ModifyOrderSomeDataRequest
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/4/12 10:53
     */
    public ModifyOrderSomeDataRequest toPersistRelatedOrderRequest(cn.jdl.batrix.spec.RequestProfile facadeRequestProfile, ModifyOrderFacadeRequest facadeRequest) throws Exception {
        ModifyOrderSomeDataRequest modifyOrderRequest = new ModifyOrderSomeDataRequest();
        //订单主档信息转换
        Order order = toReturnOrderMain(facadeRequestProfile, facadeRequest);
        //关联单信息
        order.setRefOrderInfo(toOrderRelationNew(facadeRequest));
        order.setExtendInfo(toOrderExtend(facadeRequest.getExtendProps()));
        modifyOrderRequest.setOrder(order);
        return modifyOrderRequest;
    }

    /**
     * 逆向单,改址单,订单主档
     */
    private Order toReturnOrderMain(cn.jdl.batrix.spec.RequestProfile facadeRequestProfile, ModifyOrderFacadeRequest facadeRequest) {
        Order order = new Order();
        //租户id
        order.setTenantId(facadeRequestProfile.getTenantId());
        //原订单号
        order.setOrderNo(facadeRequest.getOrderNo());
        //order.setOrderId(facadeRequest.getOrderId());
        order.setExtendInfo(toOrderExtend(facadeRequest.getExtendProps()));
        return order;
    }

    public ModifyOrderSomeDataRequest toPersistOrderRequest(cn.jdl.batrix.spec.RequestProfile facadeRequestProfile, ModifyOrderFacadeRequest facadeRequest) throws Exception {
        ModifyOrderSomeDataRequest modifyOrderRequest = new ModifyOrderSomeDataRequest();
        //订单主档信息转换
        Order order = toOrderMain(facadeRequestProfile, facadeRequest);
        if (CollectionUtils.isNotEmpty(facadeRequest.getModifyBusinessIdentityTypes())) {
            modifyOrderRequest.setModifyBusinessIdentityTypes(facadeRequest.getModifyBusinessIdentityTypes());
        }
        // 订单标识
        if (MapUtils.isNotEmpty(facadeRequest.getOrderSign())) {
            order.setOrderSign(facadeRequest.getOrderSign());
        } else {
            Map<String, String> orderSign = new HashMap<>();
            order.setOrderSign(orderSign);
        }

        Map<String, String> orderSign = order.getOrderSign();
        //客户渠道信息转化
        order.setCustomerChannelInfo(toCustomerChannel(facadeRequest.getChannel(), facadeRequest.getCustomerFacade()));

        //货品信息
        order.setCargoInfoList(toCargoList(facadeRequest.getCargos()));
        //商品信息
        order.setGoodsInfoList(toGoodsInfoList(facadeRequest.getGoodsList()));
        //计费信息
        order.setTransactionCostInfo(toTransactionCost(facadeRequest.getFinance()));
        //主产品和增值服务
        Map<String, Integer> productOperateType = new HashMap<>();
        order.setServiceProductInfoList(toServiceProductList(facadeRequest.getProducts(), productOperateType));
        //收发件人信息
        order.setConsignInfo(toConsign(facadeRequest.getConsignor(), facadeRequest.getConsignee(), orderSign));
        //收发件人扩展信息
        order.setConsignExtendInfo(this.toConsignExtendInfo(facadeRequest.getConsignor(), facadeRequest.getConsignee()));
        //配送信息
        order.setShipmentInfo(toShipment(facadeRequest));
        //营销信息
        Map<String, Integer> marketOperateMap = new HashMap<>();
        order.setMarketInfoList(toMarketInfos(facadeRequest.getPromotion(), marketOperateMap));
        //关联单信息
        //order.setRelationInfoList(toOrderRelation(facadeRequest.getRefOrders()));
        order.setRefOrderInfo(this.toOrderRelationNew(facadeRequest));
        // 删除关联单
        modifyOrderRequest.setRefOrderInfoDeleteMap(toRefOrderInfoDeleteMap(facadeRequest));

        //扩展字段信息
        order.setExtendInfo(toOrderExtend(facadeRequest.getExtendProps()));
        //系统扩展信息
        //order.setSysExt(toSysExt(facadeRequest.getExtendProps()));

        //节点信息
        //发货仓编码及收货信息转物流节点LogisticsNodes
        order.setLogisticsNodesList(this.toLogisticsNodes(facadeRequest.getConsignor(),facadeRequest.getConsignee()));
        if (order.getLogisticsNodesList().isEmpty()) {
            order.setLogisticsNodesList(null);
        }

        // 协议信息
        order.setAgreementInfoList(this.toAgreementInfos(facadeRequest.getAgreementFacades()));
        //退货信息
        order.setReturnInfo(this.toOrderReturnInfo(facadeRequest.getReturnInfoFacade()));

        //跨境报关信息
        order.setCustomsInfo(this.toCustomsInfo(facadeRequest.getCustomsFacade()));
        //附件列表
        order.setAttachmentInfoList(toAttachmentInfos(facadeRequest.getAttachmentFacades()));

        //订单总毛重
        if (null != facadeRequest.getOrderWeight()) {
            order.setOrderWeight(facadeRequest.getOrderWeight().getValue());
            // 目前数据库只有一个重量单位，经维金和卫东确认，使用毛重单位
            if (null != facadeRequest.getOrderWeight().getUnit()) {
                order.setWeightUnit(facadeRequest.getOrderWeight().getUnit().getCode());
            }
        }
        //订单总净重
        if (null != facadeRequest.getOrderNetWeight()) {
            order.setOrderNetWeight(facadeRequest.getOrderNetWeight().getValue());
        }
        //订单总体积
        if (null != facadeRequest.getOrderVolume()) {
            order.setOrderVolume(facadeRequest.getOrderVolume().getValue());
            if (null != facadeRequest.getOrderVolume().getUnit()) {
                order.setVolumeUnit(facadeRequest.getOrderVolume().getUnit().getCode());
            }
        }

        //改址状态
        order.setReaddressStatus(facadeRequest.getReaddressStatus());

        if (facadeRequest.getFinance() != null && CollectionUtils.isNotEmpty(facadeRequest.getFinance().getDeductionInfoDtos())) {
            //抵扣信息
            order.setDeductionInfoList(this.toDeductionInfoList(facadeRequest.getFinance().getDeductionInfoDtos()));
        }
        //履约信息
        order.setFulfillmentInfo(this.toFulfillmentInfo(facadeRequest.getFulfillment()));

        //解决方案
        order.setSolution(this.toSolution(facadeRequest.getBusinessSolutionFacade()));

        //变更记录
        this.toModifyInfoList(facadeRequest,order,modifyOrderRequest);

        modifyOrderRequest.setOrder(order);
        // 操作产品信息
        modifyOrderRequest.setProductOperateType(productOperateType);
        // 操作营销信息
        modifyOrderRequest.setMarketOperateType(marketOperateMap);
        // 清空字段
        modifyOrderRequest.setOrderNullFieldsRequest(toClearFieldData(order, facadeRequest));

        //修改类型
        if (MapUtils.isNotEmpty(facadeRequest.getModifiedFields())) {
            //货品修改类型
            if (StringUtils.isNumeric(facadeRequest.getModifiedFields().get(ModifiedFieldEnum.CARGO_INFOS.getCode()))) {
                modifyOrderRequest.setCargoInsertType(this.toRepositoryModifyType(facadeRequest.getModifiedFields().get(ModifiedFieldEnum.CARGO_INFOS.getCode())));
            }
            //商品修改类型
            if (StringUtils.isNumeric(facadeRequest.getModifiedFields().get(ModifiedFieldEnum.GOODS_INFOS.getCode()))) {
                modifyOrderRequest.setGoodsInsertType(this.toRepositoryModifyType(facadeRequest.getModifiedFields().get(ModifiedFieldEnum.GOODS_INFOS.getCode())));
            }
        }
        return modifyOrderRequest;
    }

    /**
     * 解决方案信息
     *
     * @param facade
     * @return
     */
    private Order.Solution toSolution(BusinessSolutionFacade facade) {
        if (facade == null) {
            return null;
        }
        Order.Solution solution = new Order.Solution();
        //编码
        solution.setBusinessSolutionNo(facade.getBusinessSolutionNo());
        //名称
        solution.setBusinessSolutionName(facade.getBusinessSolutionName());
        //要素
        solution.setProductAttrs(facade.getProductAttrs());
        return solution;
    }

    /**
     * 客户渠道信息
     */
    private Order.CustomerChannelInfo toCustomerChannel(ChannelFacade channelFacade, CustomerFacade customerFacade) {
        if (null == channelFacade && null == customerFacade) {
            return null;
        }
        Order.CustomerChannelInfo customerChannel = new Order.CustomerChannelInfo();

        if(null != channelFacade){
            Map<String, String> channelFacadeExt = channelFacade.getExtendProps();
            //扩展属性
            customerChannel.setExt(channelFacade.getExtendProps());
            if(MapUtils.isNotEmpty(channelFacadeExt)){
                String modifySystemSubCaller = channelFacadeExt.get(OrderConstants.MODIFY_SYSTEM_SUB_CALLER);
                // 仅供融合修改使用，订单做透传。modifySystemSubCaller传值（非空）时同步修改systemSubCaller。
                if(StringUtils.isNotBlank(modifySystemSubCaller)){
                    //修改接单渠道来源
                    customerChannel.setChannelReceiveSubSource(modifySystemSubCaller);
                }
            }

        }

        if (null != customerFacade && null != customerFacade.getStorePin()){
            customerChannel.setStorePin(customerFacade.getStorePin());
        }
        return customerChannel;
    }

    private void toModifyInfoList(ModifyOrderFacadeRequest facadeRequest, Order order, ModifyOrderSomeDataRequest modifyOrderRequest) {
        List<ModifyRecordDto> modifyRecordDtos = facadeRequest.getModifyRecordDtos();
        if(CollectionUtils.isNotEmpty(modifyRecordDtos)){
            modifyOrderRequest.setModifyInfoOperateType(facadeRequest.getModifyRecordListOperateType());
            Map<String, Integer> modifyInfoUpdateType = new HashMap<>(modifyRecordDtos.size());
            List<ModifyInfo> modifyInfoList = new ArrayList<>(modifyRecordDtos.size());
            modifyRecordDtos.forEach(modifyRecordDto -> {
                modifyInfoUpdateType.put(modifyRecordDto.getModifyRecordNo(),modifyRecordDto.getModifyInfoUpdateType());
                ModifyInfo modifyInfo = new ModifyInfo();
                modifyInfo.setTenantId(modifyRecordDto.getTenantId());
                modifyInfo.setModifyNo(modifyRecordDto.getModifyRecordNo());
                modifyInfo.setModifySequence(modifyRecordDto.getModifyRecordSequence());
                modifyInfo.setModifyType(modifyRecordDto.getModifyRecordType());
                modifyInfo.setModifyStatus(modifyRecordDto.getModifyRecordStatus());
                modifyInfo.setOperator(modifyRecordDto.getOperator());
                modifyInfo.setOperateTime(modifyRecordDto.getOperateTime());
                modifyInfo.setOrderNo(modifyRecordDto.getOrderNo());
                modifyInfo.setExtendProps(modifyRecordDto.getExtendProps());
                modifyInfo.setModifyMsg(modifyRecordDto.getModifyRecordMsg());
                modifyInfoList.add(modifyInfo);
            });
            order.setModifyInfoList(modifyInfoList);
            modifyOrderRequest.setModifyInfoUpdateType(modifyInfoUpdateType);
        }
    }

    public List<List<ModifyOrderSomeDataRequest>> toPersistOrderRequestList(cn.jdl.batrix.spec.RequestProfile facadeRequestProfile,
                                                                      List<ModifyOrderFacadeRequest> facadeRequestList) throws Exception {
        if(CollectionUtils.isEmpty(facadeRequestList)) {
            return Collections.emptyList();
        }
        List<ModifyOrderSomeDataRequest> requestList = new ArrayList<>(facadeRequestList.size());
        for(ModifyOrderFacadeRequest facadeRequest : facadeRequestList) {
            requestList.add(toPersistOrderRequest(facadeRequestProfile, facadeRequest));
        }
        return Lists.partition(requestList, MAX_BATCH_MODIFY_NUM);
    }

    /**
     * 退货信息
     * @param returnInfoFacade
     * @return
     * @throws Exception
     */
    private Order.ReturnInfo toOrderReturnInfo(ReturnInfoFacade returnInfoFacade) throws Exception {
        if (null == returnInfoFacade) {
            return null;
        }
        Order.ReturnInfo returnInfo = new Order.ReturnInfo();
        returnInfo.setReturnType(returnInfoFacade.getReturnType());
        if (null != returnInfoFacade.getConsigneeFacade()) {
            // 退货收货人基本信息
            ConsignBaseInfo consignBaseInfo = new ConsignBaseInfo();
            consignBaseInfo.setName(tdeAcl.encrypt(returnInfoFacade.getConsigneeFacade().getConsigneeName()));
            consignBaseInfo.setMobile(tdeAcl.encrypt(returnInfoFacade.getConsigneeFacade().getConsigneeMobile()));
            consignBaseInfo.setPhone(tdeAcl.encrypt(returnInfoFacade.getConsigneeFacade().getConsigneePhone()));
            returnInfo.setConsigneeInfo(consignBaseInfo);
            // 退货收货人地址信息
            AddressInfo address = returnInfoFacade.getConsigneeFacade().getAddress();
            if (null != address) {
                com.jdl.cp.core.ts.entity.AddressInfo addressInfo = new com.jdl.cp.core.ts.entity.AddressInfo();
                //发货人省编码
                addressInfo.setProvinceNo(address.getProvinceNo());
                //发货人省名称
                addressInfo.setProvinceName(address.getProvinceName());
                //发货人市编码
                addressInfo.setCityNo(address.getCityNo());
                //发货人市名称
                addressInfo.setCityName(address.getCityName());
                //发货人区编码
                addressInfo.setCountyNo(address.getCountyNo());
                //发货人区名称
                addressInfo.setCountyName(address.getCountyName());
                //发货人镇编码
                addressInfo.setTownNo(address.getTownNo());
                //发货人镇名称
                addressInfo.setTownName(address.getTownName());
                //发货人镇详细地址
                addressInfo.setAddress(tdeAcl.encrypt(address.getAddress()));


                //发货人省编码
                addressInfo.setProvinceNoGis(address.getProvinceNoGis());
                //发货人省名称
                addressInfo.setProvinceNameGis(address.getProvinceNameGis());
                //发货人市编码
                addressInfo.setCityNoGis(address.getCityNoGis());
                //发货人市名称
                addressInfo.setCityNameGis(address.getCityNameGis());
                //发货人区编码
                addressInfo.setCountyNoGis(address.getCountyNoGis());
                //发货人区名称
                addressInfo.setCountyNameGis(address.getCountyNameGis());
                //发货人镇编码
                addressInfo.setTownNoGis(address.getTownNoGis());
                //发货人镇名称
                addressInfo.setTownNameGis(address.getTownNameGis());
                //发货人镇详细地址
                addressInfo.setAddressGis(tdeAcl.encrypt(address.getAddressGis()));

                // GIS解析精确度
                addressInfo.setPreciseGis(address.getPreciseGis());
                // 维度
                if (address.getLatitude() != null && NumberUtils.isParsable(address.getLatitude())) {
                    addressInfo.setLatitude(address.getLatitude());
                }
                // 经度
                if (address.getLongitude() != null && NumberUtils.isParsable(address.getLongitude())) {
                    addressInfo.setLongitude(address.getLongitude());
                }
                //坐标系类型
                addressInfo.setCoordinateType(address.getCoordinateType());

                //地址嵌套等级
                addressInfo.setConflictLevel(address.getConflictLevel());
                //gis打标地址来源
                addressInfo.setAddressSource(address.getAddressSource());
                //围栏信任标识
                addressInfo.setFenceTrusted(address.getFenceTrusted());
                // 围栏信息
                addressInfo.setFenceInfos(this.toFenceInfos(address.getFenceInfos()));
                returnInfo.setAddressInfo(addressInfo);
            }
        }
        return returnInfo;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductionInfoDtos
     * @return
     */
    private List<Order.DeductionInfo> toDeductionInfoList(List<DeductionInfoDto> deductionInfoDtos) {
        if (CollectionUtils.isEmpty(deductionInfoDtos)) {
            return null;
        }
        List<Order.DeductionInfo> deductionInfoList = new ArrayList<>(deductionInfoDtos.size());
        deductionInfoDtos.forEach(dto -> {
            if (dto != null) {
                Order.DeductionInfo deductionInfo = new Order.DeductionInfo();
                // 抵扣编码
                deductionInfo.setDeductionNo(dto.getDeductionNo());
                // 抵扣金额
                if (null != dto.getDeductionAmount()) {
                    deductionInfo.setCurrency(null == dto.getDeductionAmount().getCurrencyCode() ? null : dto.getDeductionAmount().getCurrencyCode().getCode());
                    deductionInfo.setDeductionAmount(dto.getDeductionAmount().getAmount());
                }
                // 抵扣方
                deductionInfo.setDeductionOrg(dto.getDeductionOrg());
                // 扩展信息
                deductionInfo.setExt(dto.getExtendProps());
                // 收费方
                deductionInfo.setChargingSource(dto.getChargingSource());

                deductionInfoList.add(deductionInfo);
            }
        });
        return deductionInfoList;
    }

    private List<Order.GoodsInfo> toGoodsInfoList(List<GoodsFacade> goodsList) {
        List<Order.GoodsInfo> goodsInfoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }
        for (GoodsFacade goodsFacade : goodsList) {
            Order.GoodsInfo goodsInfo = new Order.GoodsInfo();
            goodsInfo.setGoodsNo(goodsFacade.getGoodsNo());
            goodsInfo.setUniqueMark(goodsFacade.getGoodsUniqueCode());
            if (goodsFacade.getGoodsAmount() != null) {
                goodsInfo.setGoodsAmount(goodsFacade.getGoodsAmount().getAmount());
                if (goodsFacade.getGoodsAmount().getCurrency() != null) {
                    goodsInfo.setCurrency(goodsFacade.getGoodsAmount().getCurrency().getCode());
                }
            }
            goodsInfo.setGoodsName(goodsFacade.getGoodsName());
            goodsInfo.setChannelGoodsNo(goodsFacade.getChannelGoodsNo());
            if (goodsFacade.getGoodsType() != null) {
                goodsInfo.setGoodsType(goodsFacade.getGoodsType());
            }
            if (goodsFacade.getGoodsQuantity() != null) {
                if(goodsFacade.getGoodsQuantity().getValue() != null){
                    goodsInfo.setGoodsQuantity(goodsFacade.getGoodsQuantity().getValue().intValue());
                }
                goodsInfo.setQuantityUnit(goodsFacade.getGoodsQuantity().getUnit());
            }

            // 重量
            if (null != goodsFacade.getGoodsWeight() && null != goodsFacade.getGoodsWeight().getValue()) {
                goodsInfo.setGoodsWeight(goodsFacade.getGoodsWeight().getValue());
            }

            if (null != goodsFacade.getGoodsWeight() && null != goodsFacade.getGoodsWeight().getUnit()) {
                goodsInfo.setWeightUnit(goodsFacade.getGoodsWeight().getUnit().getCode());
            }

            // 体积
            if (null != goodsFacade.getGoodsVolume() && null != goodsFacade.getGoodsVolume().getValue()) {
                goodsInfo.setGoodsVolume(goodsFacade.getGoodsVolume().getValue());
            }

            if (null != goodsFacade.getGoodsVolume() && null != goodsFacade.getGoodsVolume().getUnit()) {
                goodsInfo.setVolumeUnit(goodsFacade.getGoodsVolume().getUnit().getCode());
            }

            // 三维
            if (null != goodsFacade.getGoodsDimension() && null != goodsFacade.getGoodsDimension().getHeight()) {
                goodsInfo.setGoodsHeight(goodsFacade.getGoodsDimension().getHeight());
            }

            if (null != goodsFacade.getGoodsDimension()  && null != goodsFacade.getGoodsDimension().getLength()) {
                goodsInfo.setGoodsLength(goodsFacade.getGoodsDimension().getLength());
            }

            if (null != goodsFacade.getGoodsDimension() && null != goodsFacade.getGoodsDimension().getWidth()) {
                goodsInfo.setGoodsWidth(goodsFacade.getGoodsDimension().getWidth());
            }

            if (null != goodsFacade.getGoodsDimension()  && null != goodsFacade.getGoodsDimension().getUnit()) {
                goodsInfo.setDimensionUnit(goodsFacade.getGoodsDimension().getUnit().getCode());
            }

            // 促销信息
            goodsInfo.setSalesInfos(goodsFacade.getSalesInfos());

            goodsInfo.setCombinationGoodsVersion(goodsFacade.getCombinationGoodsVersion());
            if (goodsFacade.getGoodsPrice() != null) {
                goodsInfo.setGoodsUnitPrice(goodsFacade.getGoodsPrice().getAmount());
                if (goodsFacade.getGoodsPrice().getCurrency() != null) {
                    goodsInfo.setCurrency(goodsFacade.getGoodsPrice().getCurrency().getCode());
                }
            }
            if (CollectionUtils.isNotEmpty(goodsFacade.getGoodsSerialInfos())){
                List<Order.GoodsCodesInfo> goodsCodesInfos = goodsFacade.getGoodsSerialInfos().stream().map(goodsSerial -> {
                    Order.GoodsCodesInfo goodsCodesInfo = new Order.GoodsCodesInfo();
                    //识别码类型 1-PP码， 2-SN码，和维金确认，B2C只有SN码
                    goodsCodesInfo.setGoodsIdType(goodsSerial.getSerialType());
                    goodsCodesInfo.setGoodsIdCode(goodsSerial.getSerialNo());
                    goodsCodesInfo.setGoodsIdQuantity(goodsSerial.getSerialQuantity());
                    return goodsCodesInfo;
                }).collect(Collectors.toList());
                goodsInfo.setGoodsCodesInfoList((ArrayList<Order.GoodsCodesInfo>) goodsCodesInfos);
            }
            // 商品-增值服务
            if(CollectionUtils.isNotEmpty(goodsFacade.getGoodsProductInfos())){
                List<Order.ServiceProductInfo> serviceProductInfoList = goodsFacade.getGoodsProductInfos().stream().map(goodsProduct ->{
                    Order.ServiceProductInfo serviceProductInfo = new Order.ServiceProductInfo();
                    serviceProductInfo.setProductNo(goodsProduct.getProductNo());
                    serviceProductInfo.setProductName(goodsProduct.getProductName());
                    serviceProductInfo.setParentNo(goodsProduct.getParentNo());
                    serviceProductInfo.setProductType(goodsProduct.getProductType().byteValue());
                    serviceProductInfo.setExt((HashMap<String, String>) goodsProduct.getExtendProps());
                    serviceProductInfo.setAttrs(goodsProduct.getProductAttrs());
                    serviceProductInfo.setProductExecutionResult(goodsProduct.getProductExecutionResult());
                    return serviceProductInfo;
                }).collect(Collectors.toList());
                goodsInfo.setServiceProductInfoList(serviceProductInfoList);
            }
            if(MapUtils.isNotEmpty(goodsFacade.getExtendProps())){
                goodsInfo.setExt(goodsFacade.getExtendProps());
            }
            //附件
            if (CollectionUtils.isNotEmpty(goodsFacade.getAttachmentInfos())) {
                List<Order.AttachmentInfo> attachmentList = goodsFacade.getAttachmentInfos().stream().map(attachmentInfo -> {
                    Order.AttachmentInfo attachment = new Order.AttachmentInfo();
                    attachment.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                    //附件名称
                    attachment.setAttachmentName(attachmentInfo.getAttachmentName());
                    //附件业务类型
                    attachment.setAttachmentType(attachmentInfo.getAttachmentType());
                    //附件文档类型
                    attachment.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                    //附件路径
                    attachment.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                    //附件备注
                    attachment.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                    return attachment;
                }).collect(Collectors.toList());
                goodsInfo.setGoodsAttachmentInfos(attachmentList);
            }

            // 商品净重
            if (null != goodsFacade.getNetWeight() && null != goodsFacade.getNetWeight().getValue()) {
                goodsInfo.setNetWeight(goodsFacade.getNetWeight().getValue());
            }

            if (null != goodsFacade.getNetWeight() && null != goodsFacade.getNetWeight().getUnit()) {
                goodsInfo.setWeightUnit(goodsFacade.getNetWeight().getUnit().getCode());
            }

            goodsInfoList.add(goodsInfo);
        }
        return goodsInfoList;
    }

    private List<Order.LogisticsNodes> toLogisticsNodes(ConsignorFacade consignorFacade, ConsigneeFacade consigneeFacade){
        List<Order.LogisticsNodes> list = new ArrayList<>();
        if (consignorFacade != null && consignorFacade.getCustomerWarehouse() != null){
            Order.LogisticsNodes logisticsNodes = new Order.LogisticsNodes();
            logisticsNodes.setNodeNo(consignorFacade.getCustomerWarehouse().getWarehouseNo());
            logisticsNodes.setNodeName(consignorFacade.getCustomerWarehouse().getWarehouseName());
            logisticsNodes.setNodeType(consignorFacade.getCustomerWarehouse().getWarehouseSource());
            //节点分类,1-仓库
            logisticsNodes.setNodeClassification(NodeClassIfcationEnum.WAREHOUSE.getCode());
            logisticsNodes.setNodeUsage(NodeUsageEnum.SEND.getCode());//1、发货，2、收货
            list.add(logisticsNodes);
        }
        if (consigneeFacade != null && consigneeFacade.getReceiveWarehouse() != null) {
            Order.LogisticsNodes logisticsNodes = new Order.LogisticsNodes();
            logisticsNodes.setNodeNo(consigneeFacade.getReceiveWarehouse().getWarehouseNo());
            logisticsNodes.setNodeName(consigneeFacade.getReceiveWarehouse().getWarehouseName());
            logisticsNodes.setNodeType(consigneeFacade.getReceiveWarehouse().getWarehouseSource());
            //节点分类,1-仓库
            logisticsNodes.setNodeClassification(NodeClassIfcationEnum.WAREHOUSE.getCode());
            logisticsNodes.setNodeUsage(NodeUsageEnum.RECEIVE.getCode());//1、发货，2、收货
            list.add(logisticsNodes);
        }
        return list;
    }

    /**
     * 需清空的字段数据处理
     *
     * @param facadeRequest
     * @return
     */
    private OrderNullFieldsRequest toClearFieldData(Order order,ModifyOrderFacadeRequest facadeRequest) {
        List<String> clearFields = facadeRequest.getClearFields();
        OrderNullFieldsRequest orderNullFieldsRequest = new OrderNullFieldsRequest();
        // 因为ServiceRequirement在持久化时会被默认成需要覆盖，所以需要手动设置为{@value FALSE}
        orderNullFieldsRequest.setServiceRequirements(Boolean.FALSE);
//
        if (OrderUsageEnum.deploy.getCode().equals(facadeRequest.getOrderUsage())) {
            order.setOrderUsage(null);
            orderNullFieldsRequest.setOrderUsage(true);
        }

        if (CollectionUtils.isNotEmpty(clearFields)) {
            //循环置空的集合
            for (String clearFiled : clearFields) {
                ModifyItemConfigEnum configEnum = ModifyItemConfigEnum.getConfigEnumByCode(clearFiled);
                if (configEnum == null) {
                    continue;
                }

                switch (configEnum) {
                    case CONSIGNOR_NAME:
                        //这里不需要判空。如果为空就报错，说明赋值逻辑有问题

                        break;
                    case CONSIGNOR_MOBILE:

                        break;
                    case CONSIGNOR_ID_TYPE:

                        break;
                    case CONSIGNEE_ID_TYPE:

                        break;
                    case PLAN_DELIVERY_TIME:
                        orderNullFieldsRequest.setPlanDeliveryTime(true);
                        break;
                    case EXPECT_PICKUP_START_TIME:

                        break;
                    case EXPECT_PICKUP_END_TIME:

                        break;
                    case PICKUP_TYPE:

                        break;
                    case DELIVERY_TYPE:

                        break;
                    case TRANSPORT_TYPE:
                        orderNullFieldsRequest.setTransportType(true);
                        break;
                    case PLAN_RECEIVE_TIME:

                        break;
                    case CONTACTLESS_TYPE:

                        break;
                    case SETTLEMENT_TYPE:

                        break;
                    case PAYMENT_STAGE:

                        break;
                    case PAYMENT:
                        orderNullFieldsRequest.setPaymentType(true);
                        break;
                    case PREEMPT_TYPE:
                        orderNullFieldsRequest.setPreemptType(true);
                        break;
                    case SERVICE_REQUIREMENTS:
                        orderNullFieldsRequest.setServiceRequirements(true);
                        break;
                    case AGREEMENTS:
                        orderNullFieldsRequest.setAgreementInfo(true);
                        break;
                    case ATTACH_FEES: // 全量覆盖财务信息的附加费用
                        orderNullFieldsRequest.setAttachFee(true);
                        break;
                    case ATTACHMENTS: // 全量覆盖附件信息
                        orderNullFieldsRequest.setAttachmentInfo(true);
                        break;
                    case FINANCE_DETAILS:
                        orderNullFieldsRequest.setFinanceDetailInfo(true);
                        break;
                    case DEDUCTION_INFOS:
                        orderNullFieldsRequest.setDeductionInfoList(true);
                        break;
                    default:
                        break;
                }
            }
        }

        //清空集合操作
        Map<String, String> modifiedFields = facadeRequest.getModifiedFields();
        if (MapUtils.isNotEmpty(modifiedFields)) {
            if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFields.get(ModifiedFieldEnum.FINANCE_DETAIL_INFOS.getCode()))) {
                orderNullFieldsRequest.setFinanceDetailInfo(true);
            }
        }
        return orderNullFieldsRequest;
    }

    /**
     * 订单主档
     */
    private Order toOrderMain(cn.jdl.batrix.spec.RequestProfile facadeRequestProfile, ModifyOrderFacadeRequest facadeRequest) throws Exception {
        Order order = new Order();
        //租户id
        order.setTenantId(facadeRequestProfile.getTenantId());
        //orderId
        //order.setOrderId(facadeRequest.getOrderId());
        //订单号
        order.setOrderNo(facadeRequest.getOrderNo());
        //业务身份
        order.setBusinessUnit(facadeRequest.getBusinessIdentity() != null ? facadeRequest.getBusinessIdentity().getBusinessUnit() : null);
        //业务类型
        order.setBusinessType(facadeRequest.getBusinessIdentity() != null ? facadeRequest.getBusinessIdentity().getBusinessType() : null);
        //业务策略
        if (facadeRequest.getBusinessIdentity() != null
                && StringUtils.isNotBlank(facadeRequest.getBusinessIdentity().getBusinessStrategy())
                && CollectionUtils.isNotEmpty(facadeRequest.getModifyBusinessIdentityTypes())
                // 0-全量修改；1-修改业务身份，2-修改业务类型，3-修改业务策略
                && (facadeRequest.getModifyBusinessIdentityTypes().contains(0)
                || facadeRequest.getModifyBusinessIdentityTypes().contains(3))) {
            order.setBusinessStrategy(facadeRequest.getBusinessIdentity().getBusinessStrategy());
        }
        //订单类型
        //order.setOrderType(facadeRequest.getOrderType() != null ? Integer.valueOf(facadeRequest.getOrderType()) : null);
        //订单主状态
        order.setOrderStatus(facadeRequest.getOrderStatus());
        //订单状态
        order.setOrderStatusCustom(facadeRequest.getOrderStatusCustom());
        //扩展状态
        order.setOrderExtendStatus(facadeRequest.getExecutedStatus());

        //备注
        order.setRemark(facadeRequest.getRemark());
        //修改人
        order.setUpdateUser(facadeRequest.getOperator());
        //取消状态
        order.setCancelStatus(facadeRequest.getCancelStatus() != null ? facadeRequest.getCancelStatus().byteValue() : null);
        //拦截类型
        order.setInterceptType(facadeRequest.getInterceptType());
        //拦截时间
        order.setInterceptTime(facadeRequest.getInterceptTime());
        //修改时间
        order.setUpdateTime(new Date());
        //核算体积
        if (facadeRequest.getRecheckVolume() != null) {
            order.setRecheckVolume(facadeRequest.getRecheckVolume().getValue());
            if (facadeRequest.getRecheckVolume().getUnit() != null) {
                order.setRecheckVolumeUnit(facadeRequest.getRecheckVolume().getUnit().getCode());
            }
        }
        //核算重量
        if (facadeRequest.getRecheckWeight() != null) {
            order.setRecheckWeight(facadeRequest.getRecheckWeight().getValue());
            if (facadeRequest.getRecheckWeight().getUnit() != null) {
                order.setRecheckWeightUnit(facadeRequest.getRecheckWeight().getUnit().getCode());
            }
        }
        order.setExtendInfo(this.toOrderExtend(facadeRequest.getExtendProps()));
        // 拦截信息
        if(null != facadeRequest.getInterceptFacade()) {
            InterceptInfo interceptInfo = new InterceptInfo();
            interceptInfo.setInterceptHandlingMode(facadeRequest.getInterceptFacade().getInterceptHandlingMode());
            interceptInfo.setInterceptStationNo(facadeRequest.getInterceptFacade().getInterceptStationNo());
            interceptInfo.setAddressInfo(addressTransfer(facadeRequest.getInterceptFacade().getInterceptAddress()));
            interceptInfo.setInterceptResultTime(facadeRequest.getInterceptFacade().getInterceptResultTime());
            interceptInfo.setInterceptRouteNode(facadeRequest.getInterceptFacade().getInterceptRouteNode());
            interceptInfo.setInterceptPayAccountNo(facadeRequest.getInterceptFacade().getInterceptPayAccountNo());
            order.setInterceptInfo(interceptInfo);
        }
        //父单号
        order.setParentOrderNo(facadeRequest.getParentOrderNo());
        order.setCustomOrderNo(facadeRequest.getCustomOrderNo());
        if (facadeRequest.getYn() != null) {
            order.setYn(facadeRequest.getYn());
        }
        //弃货状态
        order.setDiscardStatus(facadeRequest.getDiscardStatus());

        return order;
    }

    private com.jdl.cp.core.ts.entity.AddressInfo addressTransfer(AddressInfo addressInfo) throws Exception {
        com.jdl.cp.core.ts.entity.AddressInfo interceptAddress = null;
        if (addressInfo != null) {
            interceptAddress = new com.jdl.cp.core.ts.entity.AddressInfo();
            //省编码
            interceptAddress.setProvinceNo(addressInfo.getProvinceNo());
            //省名称
            interceptAddress.setProvinceName(addressInfo.getProvinceName());
            //市编码
            interceptAddress.setCityNo(addressInfo.getCityNo());
            //市名称
            interceptAddress.setCityName(addressInfo.getCityName());
            //区编码
            interceptAddress.setCountyNo(addressInfo.getCountyNo());
            //区名称
            interceptAddress.setCountyName(addressInfo.getCountyName());
            //镇编码
            interceptAddress.setTownNo(addressInfo.getTownNo());
            //镇名称
            interceptAddress.setTownName(addressInfo.getTownName());
            //详细地址
            interceptAddress.setAddress(tdeAcl.encrypt(addressInfo.getAddress()));
            //GIS解析后的省编码
            interceptAddress.setProvinceNoGis(addressInfo.getProvinceNoGis());
            //GIS解析后的省名称
            interceptAddress.setProvinceNameGis(addressInfo.getProvinceNameGis());
            //GIS解析后的市编码
            interceptAddress.setCityNoGis(addressInfo.getCityNoGis());
            //GIS解析后的市名称
            interceptAddress.setCityNameGis(addressInfo.getCityNameGis());
            //GIS解析后的区编码
            interceptAddress.setCountyNoGis(addressInfo.getCountyNoGis());
            //GIS解析后的区名称
            interceptAddress.setCountyNameGis(addressInfo.getCountyNameGis());
            //GIS解析后的镇编码
            interceptAddress.setTownNoGis(addressInfo.getTownNoGis());
            //GIS解析后的镇名称
            interceptAddress.setTownNameGis(addressInfo.getTownNameGis());
            // 纬度
            if (addressInfo.getLatitude() != null) {
                interceptAddress.setLatitude(addressInfo.getLatitude());
            }
            // 经度
            if (addressInfo.getLongitude() != null) {
                interceptAddress.setLongitude(addressInfo.getLongitude());
            }
            //地址嵌套等级
            interceptAddress.setConflictLevel(addressInfo.getConflictLevel());
            //gis打标地址来源
            interceptAddress.setAddressSource(addressInfo.getAddressSource());
        }
        return interceptAddress;
    }

    /**
     * 客户渠道信息
     */
    /*private Order.CustomerChannelInfo toCustomerChannel(ChannelFacade channelFacade) {
        if (channelFacade == null) {
            return null;
        }
        Order.CustomerChannelInfo customerChannel = new Order.CustomerChannelInfo();
        //渠道来源
        customerChannel.setChannelReceiveSource(channelFacade.getSystemCaller());
        //渠道子来源
        customerChannel.setChannelReceiveSubSource(channelFacade.getSystemSubCaller());
        //客户编码
        customerChannel.setCustomerNo(channelFacade.getChannelCustomerNo());
        //渠道单号
        customerChannel.setChannelOrderNo(channelFacade.getChannelOrderNo());
        //渠道客户编码
        customerChannel.setChannelCustomerNo(channelFacade.getChannelCustomerNo());
        //渠道编码
        customerChannel.setChannelNo(channelFacade.getChannelNo());
        return customerChannel;
    }*/

    /**
     * 货品信息
     */
    private List<Order.CargoInfo> toCargoList(List<CargoFacade> cargoFacades) {
        List<Order.CargoInfo> cargoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(cargoFacades)) {
            return null;
        }
        for (CargoFacade cargoFacade : cargoFacades) {
            Order.CargoInfo orderCargo = new Order.CargoInfo();
            //货品名称
            orderCargo.setCargoName(cargoFacade.getCargoName());
            //货品编码
            orderCargo.setCargoNo(cargoFacade.getCargoNo());
            //货品类型
            orderCargo.setCargoType(cargoFacade.getCargoType());
            if (CollectionUtils.isNotEmpty(cargoFacade.getSerialInfos())){
                List<Order.CargoCodesInfo> cargoCodesInfos = cargoFacade.getSerialInfos().stream().map(cargoSerial -> {
                    Order.CargoCodesInfo cargoCodesInfo = new Order.CargoCodesInfo();
                    if (cargoSerial.getSerialType() != null) {
                        cargoCodesInfo.setCargoIdType(cargoSerial.getSerialType().byteValue());
                    }
                    cargoCodesInfo.setCargoIdCode(cargoSerial.getSerialNo());
                    return cargoCodesInfo;
                }).collect(Collectors.toList());
                orderCargo.setCargoCodesInfos( cargoCodesInfos);
            }

            //货品体积
            if (cargoFacade.getCargoVolume() != null) {
                orderCargo.setCargoVolume(cargoFacade.getCargoVolume().getValue());
                if (cargoFacade.getCargoVolume().getUnit() != null) {
                    orderCargo.setVolumeUnit(cargoFacade.getCargoVolume().getUnit().getCode());
                }
            }
            //货品重量
            if (cargoFacade.getCargoWeight() != null) {
                orderCargo.setCargoWeight(cargoFacade.getCargoWeight().getValue());
                if (cargoFacade.getCargoWeight().getUnit() != null) {
                    orderCargo.setWeightUnit(cargoFacade.getCargoWeight().getUnit().getCode());
                }
            }
            //货品尺寸
            if (cargoFacade.getCargoDimension() != null) {
                //长
                orderCargo.setCargoLength(cargoFacade.getCargoDimension().getLength());
                //宽
                orderCargo.setCargoWidth(cargoFacade.getCargoDimension().getWidth());
                //高
                orderCargo.setCargoHeight(cargoFacade.getCargoDimension().getHeight());
                //单位
                if (cargoFacade.getCargoDimension().getUnit() != null) {
                    orderCargo.setDimensionUnit(cargoFacade.getCargoDimension().getUnit().getCode());
                }
            }
            //易污染标识
            if (cargoFacade.getPolluteSign() != null) {
                orderCargo.setPolluteSign(cargoFacade.getPolluteSign().byteValue());
            }
            //TODO 货品数量
            if (cargoFacade.getCargoQuantity() != null && cargoFacade.getCargoQuantity().getValue() != null) {
                orderCargo.setCargoQuantity(cargoFacade.getCargoQuantity().getValue().intValue());
                orderCargo.setCargoUnit(cargoFacade.getCargoQuantity().getUnit());
            }
            // 货品内数量
            if (cargoFacade.getCargoInnerQuantity() != null) {
                orderCargo.setCargoInnerQuantity(cargoFacade.getCargoInnerQuantity().intValue());
            }
            // 货品附件信息
            if (CollectionUtils.isNotEmpty(cargoFacade.getAttachmentInfos())) {
                List<Order.AttachmentInfo> attachmentList = cargoFacade.getAttachmentInfos().stream().map(attachmentInfo -> {
                    Order.AttachmentInfo attachment = new Order.AttachmentInfo();
                    attachment.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                    //附件名称
                    attachment.setAttachmentName(attachmentInfo.getAttachmentName());
                    //附件业务类型
                    attachment.setAttachmentType(attachmentInfo.getAttachmentType());
                    //附件文档类型
                    attachment.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                    //附件路径
                    attachment.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                    //附件备注
                    attachment.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                    return attachment;
                }).collect(Collectors.toList());
                orderCargo.setCargoAttachmentInfos(attachmentList);
            }

            //货品备注
            orderCargo.setCargoRemark(cargoFacade.getCargoRemark());
            //货品拓展信息
            orderCargo.setExt(cargoFacade.getExtendProps());
            //是否易损
            orderCargo.setCargoVulnerable(cargoFacade.getCargoVulnerable());
            //货品标识
            orderCargo.setCargoSign(cargoFacade.getCargoSign());
            // 货品信息增值服务
            if(CollectionUtils.isNotEmpty(cargoFacade.getCargoProductInfos())){
                List<Order.ServiceProductInfo> serviceProductInfoList = cargoFacade.getCargoProductInfos().stream().map(cargoProduct ->{
                    Order.ServiceProductInfo serviceProductInfo = new Order.ServiceProductInfo();
                    serviceProductInfo.setProductNo(cargoProduct.getProductNo());
                    serviceProductInfo.setProductName(cargoProduct.getProductName());
                    serviceProductInfo.setParentNo(cargoProduct.getParentNo());
                    serviceProductInfo.setProductType(cargoProduct.getProductType().byteValue());
                    serviceProductInfo.setExt((HashMap<String, String>) cargoProduct.getExtendProps());
                    serviceProductInfo.setAttrs(cargoProduct.getProductAttrs());
                    return serviceProductInfo;
                }).collect(Collectors.toList());
                orderCargo.setServiceProductInfoList(serviceProductInfoList);
            }
            //隐私货品展示信息
            orderCargo.setPrivacyCargoName(cargoFacade.getPrivacyCargoName());
            cargoList.add(orderCargo);
        }
        return cargoList;
    }


    /**
     * 发货信息
     */
    private Order.ShipmentInfo toShipment(ModifyOrderFacadeRequest facadeRequest) {
        if (facadeRequest == null) {
            return null;
        }
        ShipmentFacade shipmentFacade = facadeRequest.getShipment();
        if (shipmentFacade == null) {
            return null;
        }
        Order.ShipmentInfo shipmentInfo = new Order.ShipmentInfo();
        //预计送达时间
        shipmentInfo.setPlanDeliveryTime(shipmentFacade.getPlanDeliveryTime());
        //预计送达时间段
        shipmentInfo.setPlanDeliveryPeriod(shipmentFacade.getPlanDeliveryPeriod());
        //期望配送开始时间
        shipmentInfo.setExpectDeliveryStartTime(shipmentFacade.getExpectDeliveryStartTime());
        //期望配送结束时间
        shipmentInfo.setExpectDeliveryEndTime(shipmentFacade.getExpectDeliveryEndTime());
        //期望取件开始时间
        shipmentInfo.setExpectPickupStartTime(shipmentFacade.getExpectPickupStartTime());
        //期望取件结束时间
        shipmentInfo.setExpectPickupEndTime(shipmentFacade.getExpectPickupEndTime());
        //取件类型
        if (shipmentFacade.getPickupType() != null) {
            shipmentInfo.setPickupType(shipmentFacade.getPickupType().byteValue());
        }
        //派送类型
        if (shipmentFacade.getDeliveryType() != null) {
            shipmentInfo.setDeliveryType(shipmentFacade.getDeliveryType().byteValue());
        }
        if (shipmentFacade.getTransportType() != null) {
            shipmentInfo.setTransportType(shipmentFacade.getTransportType().byteValue());
        }

        //温层,数据同步存在清空场景
        if (facadeRequest.getClearFields() != null && facadeRequest.getClearFields().contains(ModifyItemConfigEnum.WARM_LAYER.getCode())) {
            shipmentInfo.setDeliveryTempLayer("");
        } else {
            shipmentInfo.setDeliveryTempLayer(shipmentFacade.getWarmLayer());
        }

        //揽收站点编码
        shipmentInfo.setFirstStationNo(shipmentFacade.getStartStationNo());
        //揽收站点名称
        shipmentInfo.setFirstStationName(shipmentFacade.getStartStationName());
        if (shipmentFacade.getStartStationType() != null) {
            shipmentInfo.setFirstStationType(StationTypeEnum.STATION.getType());
            shipmentInfo.setStartStationType(shipmentFacade.getStartStationType());
        }

        //派送站点编码
        shipmentInfo.setLastStationNo(shipmentFacade.getEndStationNo());
        //派送站点名称
        shipmentInfo.setLastStationName(shipmentFacade.getEndStationName());
        //目的站点类型
        if (shipmentFacade.getEndStationType() != null) {
            shipmentInfo.setLastStationType(StationTypeEnum.STATION.getType());
            shipmentInfo.setEndStationType(shipmentFacade.getEndStationType());
        }

        //发货仓库编码
        shipmentInfo.setWarehouseNo(shipmentFacade.getWarehouseNo());
        //收货仓库编码
        shipmentInfo.setReceiveWarehouseNo(shipmentFacade.getReceiveWarehouseNo());
        //车型
        shipmentInfo.setVehicleType(shipmentFacade.getVehicleType());
        //预计接单时间
        shipmentInfo.setPlanReceiveTime(shipmentFacade.getPlanReceiveTime());
        //取件码
        shipmentInfo.setPickupCode(shipmentFacade.getPickupCode());
        //取件码生产方式
        shipmentInfo.setPickupCodeCreateType(shipmentFacade.getPickupCodeCreateType() != null ? shipmentFacade.getPickupCodeCreateType().getCode() : null);
        //服务要求
        shipmentInfo.setServiceRequirements(shipmentFacade.getServiceRequirements());
        //指定地点
        shipmentInfo.setAssignedAddress(shipmentFacade.getAssignedAddress());
        //无接触类型
        shipmentInfo.setContactlessType(shipmentFacade.getContactlessType() != null ? shipmentFacade.getContactlessType().byteValue() : null);
        //收货偏好
        shipmentInfo.setReceivingPreference(shipmentFacade.getReceivingPreference());
        //扩展信息
        shipmentInfo.setExt(shipmentFacade.getExtendProps());
        //起始配送中心
        shipmentInfo.setStartCenterNo(shipmentFacade.getStartCenterNo());
        //目的配送中心
        shipmentInfo.setEndCenterNo(shipmentFacade.getEndCenterNo());
        //承运商编码
        shipmentInfo.setShipperNo(shipmentFacade.getShipperNo());
        //承运商名称
        shipmentInfo.setShipperName(shipmentFacade.getShipperName());
        //承运商类型
        shipmentInfo.setShipperType(shipmentFacade.getShipperType() != null ? shipmentFacade.getShipperType().toString() : null);
        //期望派货开始时间
        shipmentInfo.setExpectDispatchStartTime(shipmentFacade.getExpectDispatchStartTime());
        //期望派货结束时间
        shipmentInfo.setExpectDispatchEndTime(shipmentFacade.getExpectDispatchEndTime());
        shipmentInfo.setEndStationTypeL3(shipmentFacade.getEndStationTypeL3());
        shipmentInfo.setStartStationTypeL3(shipmentFacade.getStartStationTypeL3());
        return shipmentInfo;
    }

    /**
     * 交易费用
     */
    private Order.TransactionCostInfo toTransactionCost(FinanceFacade financeFacade) {
        if (financeFacade == null) {
            return null;
        }

        Order.TransactionCostInfo transactionCostInfo = new Order.TransactionCostInfo();
        if (financeFacade.getSettlementType() != null) {
            transactionCostInfo.setSettlementType(financeFacade.getSettlementType().byteValue());
        }
        //预估费用字段没改
        if (financeFacade.getEstimateAmount() != null) {
            transactionCostInfo.setFreightAmount(financeFacade.getEstimateAmount().getAmount());
            if (financeFacade.getEstimateAmount().getCurrency() != null) {
                transactionCostInfo.setCurrency(financeFacade.getEstimateAmount().getCurrency().getCode());
            }
        }
        // 预占模式 & 金额
        transactionCostInfo.setOccupyMode(financeFacade.getOccupyMode());
        transactionCostInfo.setOccupyAmount(MoneyFacadeMapper.INSTANCE.toMoneyInfo(financeFacade.getOccupyAmount()));

        //预估财务信息
        Optional.ofNullable(financeFacade.getEstimateFinanceInfo()).ifPresent(estimateFinanceInfo -> {
            Order.TransactionCostInfo transactionEstimatedFinanceInfo = new Order.TransactionCostInfo();
            // 预估-折前金额
            Optional.ofNullable(estimateFinanceInfo.getPreAmount()).ifPresent(estimatePreAmount -> {
                transactionEstimatedFinanceInfo.setPreAmount(estimatePreAmount.getAmount());
                transactionEstimatedFinanceInfo.setPreAmountCurrency(estimatePreAmount.getCurrency() != null ? estimatePreAmount.getCurrency().getCode() : null);
            });
            // 预估-折后金额
            Optional.ofNullable(estimateFinanceInfo.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
                transactionEstimatedFinanceInfo.setDiscountAmount(estimateDiscountAmount.getAmount());
                transactionEstimatedFinanceInfo.setDiscountAmountCurrency(estimateDiscountAmount.getCurrency() != null ? estimateDiscountAmount.getCurrency().getCode() : null);
            });
            // 预估-计费重量
            Optional.ofNullable(estimateFinanceInfo.getBillingWeight()).ifPresent(estimateBillingWeight -> {
                transactionEstimatedFinanceInfo.setBillingWeight(estimateBillingWeight.getValue());
                transactionEstimatedFinanceInfo.setWeightUnit(estimateBillingWeight.getUnit() != null ? estimateBillingWeight.getUnit().getCode() : null);
            });
            // 预估-计费体积
            Optional.ofNullable(estimateFinanceInfo.getBillingVolume()).ifPresent(estimateBillingVolume -> {
                transactionEstimatedFinanceInfo.setBillingVolume(estimateBillingVolume.getValue());
                transactionEstimatedFinanceInfo.setVolumeUnit(estimateBillingVolume.getUnit() != null ? estimateBillingVolume.getUnit().getCode() : null);
            });
            // 预估-加价后总金额
            Optional.ofNullable(estimateFinanceInfo.getTotalAdditionAmount()).ifPresent(totalAdditionAmount -> {
                transactionEstimatedFinanceInfo.setTotalAdditionAmount(MoneyFacadeMapper.INSTANCE.toMoneyInfo(totalAdditionAmount));
            });
            // 预估-费用明细
            Optional.ofNullable(estimateFinanceInfo.getFinanceDetails()).ifPresent(estimateFinanceDetails -> {
                List<Order.FinanceDetailInfo> estimateFinanceDetailInfoList = new ArrayList<>(estimateFinanceDetails.size());
                estimateFinanceDetails.forEach(estimateFinanceDetail -> {
                    // 预估-费用明细对象
                    Order.FinanceDetailInfo estimateFinanceDetailInfo = new Order.FinanceDetailInfo();
                    // 预估-费用编号
                    estimateFinanceDetailInfo.setCostNo(estimateFinanceDetail.getCostNo());
                    // 预估-费用名称
                    estimateFinanceDetailInfo.setCostName(estimateFinanceDetail.getCostName());
                    // 预估-费用产品编码
                    estimateFinanceDetailInfo.setProductNo(estimateFinanceDetail.getProductNo());
                    // 预估-费用产品名称
                    estimateFinanceDetailInfo.setProductName(estimateFinanceDetail.getProductName());
                    // 预估-折扣信息
                    if (CollectionUtils.isNotEmpty(estimateFinanceDetail.getDiscountFacades())) {
                        List<Order.MarketInfo> marketInfoList = new ArrayList<>(estimateFinanceDetail.getDiscountFacades().size());
                        estimateFinanceDetail.getDiscountFacades().forEach(estimateDiscountFacade -> {
                            Order.MarketInfo estimateMarketInfo = new Order.MarketInfo();
                            // 预估-折扣编号
                            estimateMarketInfo.setCouponNo(estimateDiscountFacade.getDiscountNo());
                            // 预估-折扣类型
                            estimateMarketInfo.setCouponType(estimateDiscountFacade.getDiscountType());
                            if (estimateDiscountFacade.getDiscountedAmount() != null) {
                                // 预估-折扣金额
                                estimateMarketInfo.setDiscountAmount(estimateDiscountFacade.getDiscountedAmount().getAmount());
                                if (estimateDiscountFacade.getDiscountedAmount().getCurrency() != null) {
                                    estimateMarketInfo.setCurrency(estimateDiscountFacade.getDiscountedAmount().getCurrency().getCode());
                                }
                            }
                            marketInfoList.add(estimateMarketInfo);
                        });

                        estimateFinanceDetailInfo.setMarketInfoList(marketInfoList);
                    }

                    // 预估-折前金额
                    if (estimateFinanceDetail.getPreAmount() != null) {
                        estimateFinanceDetailInfo.setPreAmount(estimateFinanceDetail.getPreAmount().getAmount());
                        estimateFinanceDetailInfo.setPreAmountCurrency(estimateFinanceDetail.getPreAmount().getCurrency() != null ?
                                estimateFinanceDetail.getPreAmount().getCurrency().getCode() : null);
                    }

                    // 预估-折后金额
                    if (estimateFinanceDetail.getDiscountAmount() != null) {
                        estimateFinanceDetailInfo.setDiscountAmount(estimateFinanceDetail.getDiscountAmount().getAmount());
                        estimateFinanceDetailInfo.setDiscountAmountCurrency(estimateFinanceDetail.getDiscountAmount().getCurrency() != null ?
                                estimateFinanceDetail.getDiscountAmount().getCurrency().getCode() : null);
                    }

                    // 预估-加价后金额
                    if (estimateFinanceDetail.getAdditionAmount() != null) {
                        estimateFinanceDetailInfo.setAdditionAmount(MoneyFacadeMapper.INSTANCE.toMoneyInfo(estimateFinanceDetail.getAdditionAmount()));
                    }

                    // 预估-扩展字段
                    estimateFinanceDetailInfo.setExt(estimateFinanceDetail.getExtendProps());

                    estimateFinanceDetailInfoList.add(estimateFinanceDetailInfo);
                });

                transactionEstimatedFinanceInfo.setFinanceDetailInfoList(estimateFinanceDetailInfoList);

            });

            transactionCostInfo.setEstimatedFinanceInfo(transactionEstimatedFinanceInfo);
        });

        //折扣金额
        if (financeFacade.getDiscountAmount() != null) {
            transactionCostInfo.setDiscountAmount(financeFacade.getDiscountAmount().getAmount());
            if (financeFacade.getDiscountAmount().getCurrency() != null) {
                transactionCostInfo.setCurrency(financeFacade.getDiscountAmount().getCurrency().getCode());
            }
        }
        transactionCostInfo.setSettlementAccountNo(financeFacade.getSettlementAccountNo());
        //财务预占标识, 1：白条预授权
        transactionCostInfo.setPreemptType(financeFacade.getPreemptType());
        //支付账号
        transactionCostInfo.setCustomerDeductionAccountNo(financeFacade.getPaymentAccountNo());
        //支付方式
        if (financeFacade.getPayment() != null) {
            transactionCostInfo.setPaymentType(financeFacade.getPayment().byteValue());
        }
        // 实际支付方式
        transactionCostInfo.setActualPaymentType(financeFacade.getActualPaymentType());
        //支付状态
        transactionCostInfo.setPaymentStatus(financeFacade.getPaymentStatus());
        //是否询价
        if (financeFacade.getEnquiryType() != null) {
            transactionCostInfo.setEnquiry(financeFacade.getEnquiryType().byteValue());
        }
        //支付截止时间
        transactionCostInfo.setPayDeadline(financeFacade.getPayDeadline());
        //退款状态
        transactionCostInfo.setRefundStatus(financeFacade.getRefundStatus());
        //折前金额
        if (financeFacade.getPreAmount() != null) {
            transactionCostInfo.setPreAmount(financeFacade.getPreAmount().getAmount());
            if (financeFacade.getPreAmount().getCurrency() != null) {
                transactionCostInfo.setCurrency(financeFacade.getPreAmount().getCurrency().getCode());
            }
        }
        //总优惠金额 TODO  接口做聚合
        if (financeFacade.getTotalDiscountAmount() != null) {
            transactionCostInfo.setTotalDiscountAmount(financeFacade.getTotalDiscountAmount().getAmount());
            if(null != financeFacade.getTotalDiscountAmount().getCurrency()){
                transactionCostInfo.setCurrency(financeFacade.getTotalDiscountAmount().getCurrency().getCode());
            }
        }
        //计费重量 TODO  接口做聚合
        if (financeFacade.getBillingWeight() != null) {
            transactionCostInfo.setBillingWeight(financeFacade.getBillingWeight().getValue());
            transactionCostInfo.setWeightUnit(financeFacade.getBillingWeight().getUnit() != null ? financeFacade.getBillingWeight().getUnit().getCode() : null);
        }
        //计费体积
        if (financeFacade.getBillingVolume() != null) {
            transactionCostInfo.setBillingVolume(financeFacade.getBillingVolume().getValue());
            transactionCostInfo.setVolumeUnit(financeFacade.getBillingVolume().getUnit() != null ? financeFacade.getBillingVolume().getUnit().getCode() : null);
        }
        //计费模式
        if (StringUtils.isNotBlank(financeFacade.getBillingMode())) {
            transactionCostInfo.setBillingMode(financeFacade.getBillingMode());
        }
        //计费类型
        if (StringUtils.isNotBlank(financeFacade.getBillingType())) {
            transactionCostInfo.setBillingType(financeFacade.getBillingType());
        }
        //收款机构
        transactionCostInfo.setCollectionOrgNo(financeFacade.getCollectionOrgNo());
        //付款环节
        transactionCostInfo.setPaymentStage(financeFacade.getPaymentStage());
        //费用明细
        if (CollectionUtils.isNotEmpty(financeFacade.getFinanceDetails())) {
            List<Order.FinanceDetailInfo> financeDetailInfoList = new ArrayList<>(financeFacade.getFinanceDetails().size());
            for (FinanceDetailFacade financeDetailFacade : financeFacade.getFinanceDetails()) {
                Order.FinanceDetailInfo financeDetailInfo = new Order.FinanceDetailInfo();
                //费用编码
                financeDetailInfo.setCostNo(financeDetailFacade.getCostNo());
                //费用名称
                financeDetailInfo.setCostName(financeDetailFacade.getCostName());
                //产品编码
                financeDetailInfo.setProductNo(financeDetailFacade.getProductNo());
                //产品名称
                financeDetailInfo.setProductName(financeDetailFacade.getProductName());
                if (financeDetailFacade.getPreAmount() != null) {
                    //折前金额
                    financeDetailInfo.setPreAmount(financeDetailFacade.getPreAmount().getAmount());
                    if (financeDetailFacade.getPreAmount().getCurrency() != null) {
                        financeDetailInfo.setPreAmountCurrency(financeDetailFacade.getPreAmount().getCurrency().getCode());
                    }
                }
                //折扣信息存储转换
                if (CollectionUtils.isNotEmpty(financeDetailFacade.getDiscountFacades())) {
                    List<Order.MarketInfo> marketInfoList = new ArrayList<>(financeDetailFacade.getDiscountFacades().size());
                    financeDetailFacade.getDiscountFacades().forEach(discountFacade -> {
                        Order.MarketInfo marketInfo = new Order.MarketInfo();
                        marketInfo.setCouponNo(discountFacade.getDiscountNo());
                        marketInfo.setCouponType(discountFacade.getDiscountType());
                        marketInfo.setCouponSource(MarketEnum.DISCOUNT_FINANCE.getCode());
                        if (discountFacade.getDiscountedAmount() != null) {
                            marketInfo.setDiscountAmount(discountFacade.getDiscountedAmount().getAmount());
                            if (discountFacade.getDiscountedAmount().getCurrency() != null) {
                                marketInfo.setCurrency(discountFacade.getDiscountedAmount().getCurrency().getCode());
                            }
                        }
                        marketInfo.setExt(discountFacade.getExtendProps());
                        marketInfoList.add(marketInfo);
                    });
                    financeDetailInfo.setMarketInfoList(marketInfoList);
                }
                if (financeDetailFacade.getDiscountAmount() != null) {
                    //折扣金额
                    financeDetailInfo.setDiscountAmount(financeDetailFacade.getDiscountAmount().getAmount());
                    if (financeDetailFacade.getDiscountAmount().getCurrency() != null) {
                        financeDetailInfo.setDiscountAmountCurrency(financeDetailFacade.getDiscountAmount().getCurrency().getCode());
                    }
                }
                if (financeDetailFacade.getPointsFacade() != null) {
                    //积分金额
                    if (financeDetailFacade.getPointsFacade().getRedeemPointsAmount() != null) {
                        financeDetailInfo.setRedeemPointsAmount(financeDetailFacade.getPointsFacade().getRedeemPointsAmount().getAmount());
                        if (financeDetailFacade.getPointsFacade().getRedeemPointsAmount().getCurrency() != null) {
                            financeDetailInfo.setRedeemPointsAmountCurrency(financeDetailFacade.getPointsFacade().getRedeemPointsAmount().getCurrency().getCode());
                        }
                    }
                    //积分数量
                    if (financeDetailFacade.getPointsFacade().getRedeemPointsQuantity() != null) {
                        financeDetailInfo.setRedeemPointsQuantity(financeDetailFacade.getPointsFacade().getRedeemPointsQuantity().getValue());
                    }

                }
                //备注
                financeDetailInfo.setRemark(financeDetailFacade.getRemark());
                // 向谁收
                financeDetailInfo.setChargingSource(financeDetailFacade.getChargingSource());
                // 扩展信息
                financeDetailInfo.setExt(financeDetailFacade.getExtendProps());

                financeDetailInfoList.add(financeDetailInfo);
            }
            transactionCostInfo.setFinanceDetailInfoList(financeDetailInfoList);
        }
        //积分信息
        if (financeFacade.getPointsFacade() != null && financeFacade.getPointsFacade().getRedeemPointsAmount() != null) {
            transactionCostInfo.setRedeemPointsAmount(financeFacade.getPointsFacade().getRedeemPointsAmount().getAmount());
            if(null != financeFacade.getPointsFacade().getRedeemPointsAmount().getCurrency()){
                transactionCostInfo.setCurrency(financeFacade.getPointsFacade().getRedeemPointsAmount().getCurrency().getCode());
            }
        }
        if (financeFacade.getPointsFacade() != null && financeFacade.getPointsFacade().getRedeemPointsQuantity() != null) {
            transactionCostInfo.setRedeemPointsQuantity(financeFacade.getPointsFacade().getRedeemPointsQuantity().getValue());
            transactionCostInfo.setQuantityUnit(financeFacade.getPointsFacade().getRedeemPointsQuantity().getUnit());
        }
        //询价状态
        transactionCostInfo.setEnquiryStatus(financeFacade.getEnquiryStatus());
        //财务备注
        transactionCostInfo.setRemark(financeFacade.getRemark());
        //询价始发市No
        transactionCostInfo.setEnquiryStartCityNo(financeFacade.getEnquiryStartCityNo());
        //高峰期附加费时间
        transactionCostInfo.setPeakPeriodTime(financeFacade.getPeakPeriodTime());
        // 计费信息
        if (CollectionUtils.isNotEmpty(financeFacade.getCostInfos())) {
            List<Order.TransactionCostInfo> costInfos = financeFacade.getCostInfos().stream().map(costInfo ->{
                Order.TransactionCostInfo tranCostInfo = new Order.TransactionCostInfo();
                tranCostInfo.setCostNo(costInfo.getCostNo());
                tranCostInfo.setCostName(costInfo.getCostName());
                tranCostInfo.setChargingSource(costInfo.getChargingSource());
                tranCostInfo.setSettlementAccountNo(costInfo.getSettlementAccountNo());
                tranCostInfo.setAdditionPriceInfo(toAdditionPriceInfo(costInfo.getAdditionPriceInfo()));
                return tranCostInfo;
            }).collect(Collectors.toList());
            transactionCostInfo.setCostInfos(costInfos);
        }

        // 附加费用
        if(CollectionUtils.isNotEmpty(financeFacade.getAttachFees())){
            List<CostInfo> attachFees = financeFacade.getAttachFees().stream().map(attachFee ->{
                if (attachFee.getOperateType() == null || OperateTypeEnum.DELETE != attachFee.getOperateType()) {
                    CostInfo costInfo = new CostInfo();
                    costInfo.setCostNo(attachFee.getCostNo());
                    costInfo.setCostName(attachFee.getCostName());
                    costInfo.setChargingSource(attachFee.getChargingSource());
                    costInfo.setSettlementAccountNo(attachFee.getSettlementAccountNo());
                    costInfo.setExt(attachFee.getExtendProps());
                    return costInfo;
                } else {
                    return null;
                }
            }).collect(Collectors.toList());
            transactionCostInfo.setAttachFees(attachFees);
        }
        // 税金信息:预估税金
        transactionCostInfo.setEstimatedTax(MoneyFacadeMapper.INSTANCE.toMoneyInfo(financeFacade.getEstimatedTax()));
        // 税金信息:真实税金
        transactionCostInfo.setActualTax(MoneyFacadeMapper.INSTANCE.toMoneyInfo(financeFacade.getActualTax()));
        // 费用支付状态归集
        transactionCostInfo.setPayStatusMap(financeFacade.getPayStatusMap());
        //扩展信息
        transactionCostInfo.setExt(financeFacade.getExtendProps());
        // 支付时间
        transactionCostInfo.setPaymentTime(financeFacade.getPaymentTime());
        // 支付单号
        transactionCostInfo.setPaymentNo(financeFacade.getPaymentNo());
        // 支付人集合
        if (null != financeFacade.getPayerPins() && financeFacade.getPayerPins().size() > 0) {
            transactionCostInfo.setPayerPins(financeFacade.getPayerPins());
        }
        // 税金结算方式
        if (null != financeFacade.getTaxSettlementType()) {
            transactionCostInfo.setTaxSettlementType(financeFacade.getTaxSettlementType());
        }
        if (financeFacade.getMultiPartiesTotalAmounts() != null) {
            List<CostInfo> dataList = new ArrayList<>();
            for (cn.jdl.oms.express.domain.vo.CostInfo costInfo : financeFacade.getMultiPartiesTotalAmounts()) {
                CostInfo data = toDataCost(costInfo);
                if (data != null) {
                    dataList.add(data);
                }
            }
            transactionCostInfo.setMultiPartiesTotalAmounts(dataList);
        }
        return transactionCostInfo;
    }

    private AdditionPriceInfo toAdditionPriceInfo(cn.jdl.oms.express.domain.vo.AdditionPriceInfo additionPriceInfo) {
        AdditionPriceInfo info = new AdditionPriceInfo();
        if(Objects.isNull(additionPriceInfo)) {
            return info;
        }
        info.setFormulaNo(additionPriceInfo.getFormulaNo());
        info.setPriceItems(additionPriceInfo.getPriceItems());
        return info;
    }

    /**
     * 转换为数据层对象
     *
     * @param costInfo
     * @return
     */
    public CostInfo toDataCost(cn.jdl.oms.express.domain.vo.CostInfo costInfo) {
        if (costInfo == null) {
            return null;
        }
        CostInfo dataCost = new CostInfo();
        if (costInfo.getPreAmount() != null) {
            dataCost.setPreAmount(costInfo.getPreAmount().getAmount());
            if (costInfo.getPreAmount().getCurrency() != null) {
                dataCost.setPreAmountCurrency(costInfo.getPreAmount().getCurrency().getCode());
            }
        }
        if (costInfo.getDiscountAmount() != null) {
            dataCost.setDiscountAmount(costInfo.getDiscountAmount().getAmount());
            if (costInfo.getDiscountAmount().getCurrency() != null) {
                dataCost.setDiscountAmountCurrency(costInfo.getDiscountAmount().getCurrency().getCode());
            }
        }
        dataCost.setChargingSource(costInfo.getChargingSource());
        if (costInfo.getSettlementType() != null) {
            dataCost.setSettlementType(costInfo.getSettlementType().getCode());
        }
        if (costInfo.getPaymentStage() != null) {
            dataCost.setPaymentStage(costInfo.getPaymentStage().getCode());
        }
        dataCost.setExt(costInfo.getExtendProps());
        return dataCost;
    }
    /**
     * 收发件信息
     */
    private Order.ConsignInfo toConsign(ConsignorFacade consignorFacade, ConsigneeFacade consigneeFacade, Map<String, String> orderSign) throws Exception {
        if (consignorFacade == null && consigneeFacade == null) {
            return null;
        }
        Order.ConsignInfo orderConsign = new Order.ConsignInfo();
        //发货人信息
        if (consignorFacade != null) {
            //发货人名称
            orderConsign.setConsignorName(tdeAcl.encrypt(consignorFacade.getConsignorName()));
            orderConsign.setConsignorNameQuery(tdeAcl.obtainKeyWordIndex(consignorFacade.getConsignorName()));
            //发货人手机号
            orderConsign.setConsignorMobile(tdeAcl.encrypt(consignorFacade.getConsignorMobile()));
            orderConsign.setConsignorMobileMask(DataMaskUtil.blurPhone(consignorFacade.getConsignorMobile()));
            orderConsign.setConsignorMobileQuery(tdeAcl.obtainWildCardKeyWordIndex(consignorFacade.getConsignorMobile()));
            //发货人电话
            orderConsign.setConsignorPhone(tdeAcl.encrypt(consignorFacade.getConsignorPhone()));
            orderConsign.setConsignorPhoneQuery(tdeAcl.obtainWildCardKeyWordIndex(consignorFacade.getConsignorPhone()));
            //发货人公司
            orderConsign.setConsignorCompany(consignorFacade.getConsignorCompany());
            //发货人邮编
            orderConsign.setConsignorZipCode(consignorFacade.getConsignorZipCode());
            //发货人国家编码
            orderConsign.setConsignorNationNo(consignorFacade.getConsignorNationNo());
            //发货人国家
            orderConsign.setConsignorNation(consignorFacade.getConsignorNation());
            //发货人证件类型
            if (consignorFacade.getConsignorIdType() != null) {
                orderConsign.setConsignorIdType(consignorFacade.getConsignorIdType().byteValue());
            }
            //发货人证件号
            orderConsign.setConsignorIdNo(tdeAcl.encrypt(consignorFacade.getConsignorIdNo()));
            orderConsign.setConsignorIdNoQuery(tdeAcl.obtainWildCardKeyWordIndex(consignorFacade.getConsignorIdNo()));
            //发货人证件姓名
            orderConsign.setConsignorIdName(tdeAcl.encrypt(consignorFacade.getConsignorIdName()));
            //发货人英文名
            orderConsign.setConsignorEnName(tdeAcl.encrypt(consignorFacade.getConsignorEnName()));

            AddressInfo consignorAddress = consignorFacade.getAddress();
            if (consignorAddress != null) {
                //发货人省编码
                orderConsign.setConsignorProvinceNo(consignorAddress.getProvinceNo());
                //发货人省名称
                orderConsign.setConsignorProvinceName(consignorAddress.getProvinceName());
                //发货人市编码
                orderConsign.setConsignorCityNo(consignorAddress.getCityNo());
                //发货人市名称
                orderConsign.setConsignorCityName(consignorAddress.getCityName());
                //发货人区编码
                orderConsign.setConsignorCountyNo(consignorAddress.getCountyNo());
                //发货人区名称
                orderConsign.setConsignorCountyName(consignorAddress.getCountyName());
                //发货人镇编码
                orderConsign.setConsignorTownNo(consignorAddress.getTownNo());
                //发货人镇名称
                orderConsign.setConsignorTownName(consignorAddress.getTownName());
                //发货人镇详细地址
                orderConsign.setConsignorAddress(tdeAcl.encrypt(consignorAddress.getAddress()));
                orderConsign.setConsignorAddressQuery(tdeAcl.obtainKeyWordIndex(consignorAddress.getAddress()));

                //GIS解析后的发货人省编码
                orderConsign.setConsignorProvinceNoGis(consignorAddress.getProvinceNoGis());
                //GIS解析后的发货人省名称
                orderConsign.setConsignorProvinceNameGis(consignorAddress.getProvinceNameGis());
                //GIS解析后的发货人市编码
                orderConsign.setConsignorCityNoGis(consignorAddress.getCityNoGis());
                //GIS解析后的发货人市名称
                orderConsign.setConsignorCityNameGis(consignorAddress.getCityNameGis());
                //GIS解析后的发货人区编码
                orderConsign.setConsignorCountyNoGis(consignorAddress.getCountyNoGis());
                //GIS解析后的发货人区名称
                orderConsign.setConsignorCountyNameGis(consignorAddress.getCountyNameGis());
                //GIS解析后的发货人镇编码
                orderConsign.setConsignorTownNoGis(consignorAddress.getTownNoGis());
                //GIS解析后的发货人镇名称
                orderConsign.setConsignorTownNameGis(consignorAddress.getTownNameGis());
                // 纬度
                if (consignorAddress.getLatitude() != null && NumberUtils.isParsable(consignorAddress.getLatitude())) {
                    orderConsign.setConsignorLatitude(new BigDecimal(consignorAddress.getLatitude()));
                }
                // 经度
                if (consignorAddress.getLongitude() != null && NumberUtils.isParsable(consignorAddress.getLongitude())) {
                    orderConsign.setConsignorLongitude(new BigDecimal(consignorAddress.getLongitude()));
                }
                //地址嵌套等级
                orderConsign.setConsignorConflictLevel(consignorAddress.getConflictLevel());
                //gis打标地址来源
                orderConsign.setConsignorAddressSource(consignorAddress.getAddressSource());

                // 围栏信任打标
                if (null != consignorAddress.getFenceTrusted()) {
                    // 修改场景传入信任标识，则围栏信息落库
                    // 否则 - 清空围栏信息
                    if (FenceTrustEnum.TRUSTED.getCode().equals(consignorAddress.getFenceTrusted())) {
                        orderSign.put(OrderSignEnum.CONSIGNOR_FENCE_TRUSTED.getCode(), consignorAddress.getFenceTrusted().toString());
                        orderConsign.setConsignorFenceList(toFenceInfos(consignorAddress.getFenceInfos()));
                    } else {
                        // 清空围栏信息
                        orderSign.put(OrderSignEnum.CONSIGNOR_FENCE_TRUSTED.getCode(), EMPTY_STRING);
                        orderConsign.setConsignorFenceList(Collections.emptyList());
                    }
                }
                //行政区编码
                orderConsign.setConsignorRegionNo(consignorAddress.getRegionNo());
                //行政区名称
                orderConsign.setConsignorRegionName(consignorAddress.getRegionName());
                //英文城市
                orderConsign.setConsignorEnCity(consignorAddress.getEnCityName());
                //英文地址
                orderConsign.setConsignorEnAddress(tdeAcl.encrypt(consignorAddress.getEnAddress()));
                orderConsign.setConsignorPoiCode(consignorAddress.getPoiCode());
                orderConsign.setConsignorPoiName(tdeAcl.encrypt(consignorAddress.getPoiName()));
                orderConsign.setConsignorHouseNumber(tdeAcl.encrypt(consignorAddress.getHouseNumber()));
                orderConsign.setConsignorAddressExt(consignorAddress.getExtendProps());
            }
            //发件人扩展信息
            orderConsign.setConsignorExt(consignorFacade.getExtendProps());
        }
        //收货人人信息
        if (consigneeFacade != null) {
            //收货人名称
            orderConsign.setConsigneeName(tdeAcl.encrypt(consigneeFacade.getConsigneeName()));
            orderConsign.setConsigneeNameQuery(tdeAcl.obtainKeyWordIndex(consigneeFacade.getConsigneeName()));
            //收货人手机号
            orderConsign.setConsigneeMobile(tdeAcl.encrypt(consigneeFacade.getConsigneeMobile()));
            orderConsign.setConsigneeMobileQuery(tdeAcl.obtainWildCardKeyWordIndex(consigneeFacade.getConsigneeMobile()));
            orderConsign.setConsigneeMobileMask(DataMaskUtil.blurPhone(consigneeFacade.getConsigneeMobile()));
            //收货人电话
            orderConsign.setConsigneePhone(tdeAcl.encrypt(consigneeFacade.getConsigneePhone()));
            orderConsign.setConsigneePhoneQuery(tdeAcl.obtainWildCardKeyWordIndex(consigneeFacade.getConsigneePhone()));
            //收货人公司
            orderConsign.setConsigneeCompany(consigneeFacade.getConsigneeCompany());
            //收货人邮编
            orderConsign.setConsigneeZipCode(consigneeFacade.getConsigneeZipCode());
            //收货人国家编码
            orderConsign.setConsigneeNationNo(consigneeFacade.getConsigneeNationNo());
            //收货人国家
            orderConsign.setConsigneeNation(consigneeFacade.getConsigneeNation());
            //收货人证件类型
            if (consigneeFacade.getConsigneeIdType() != null) {
                orderConsign.setConsigneeIdType(consigneeFacade.getConsigneeIdType().byteValue());
            }
            //收货人证件号
            orderConsign.setConsigneeIdNo(tdeAcl.encrypt(consigneeFacade.getConsigneeIdNo()));
            //收货人证件姓名
            orderConsign.setConsigneeIdName(tdeAcl.encrypt(consigneeFacade.getConsigneeIdName()));
            orderConsign.setConsigneeIdNoQuery(tdeAcl.obtainWildCardKeyWordIndex(consigneeFacade.getConsigneeIdNo()));
            //收货人地址
            AddressInfo consigneeAddress = consigneeFacade.getAddress();
            if (consigneeAddress != null) {
                //收货人省编码
                orderConsign.setConsigneeProvinceNo(consigneeAddress.getProvinceNo());
                //收货人省名称
                orderConsign.setConsigneeProvinceName(consigneeAddress.getProvinceName());
                //收货人市编码
                orderConsign.setConsigneeCityNo(consigneeAddress.getCityNo());
                //收货人市名称
                orderConsign.setConsigneeCityName(consigneeAddress.getCityName());
                //收货人区编码
                orderConsign.setConsigneeCountyNo(consigneeAddress.getCountyNo());
                //收货人区名称
                orderConsign.setConsigneeCountyName(consigneeAddress.getCountyName());
                //收货人镇编码
                orderConsign.setConsigneeTownNo(consigneeAddress.getTownNo());
                //收货人镇名称
                orderConsign.setConsigneeTownName(consigneeAddress.getTownName());
                //收货人详细地址
                orderConsign.setConsigneeAddress(tdeAcl.encrypt(consigneeAddress.getAddress()));
                orderConsign.setConsigneeAddressQuery(tdeAcl.obtainKeyWordIndex(consigneeAddress.getAddress()));
                //GIS解析后的收货人省编码
                orderConsign.setConsigneeProvinceNoGis(consigneeAddress.getProvinceNoGis());
                //GIS解析后的收货人省名称
                orderConsign.setConsigneeProvinceNameGis(consigneeAddress.getProvinceNameGis());
                //GIS解析后的收货人市编码
                orderConsign.setConsigneeCityNoGis(consigneeAddress.getCityNoGis());
                //GIS解析后的收货人市名称
                orderConsign.setConsigneeCityNameGis(consigneeAddress.getCityNameGis());
                //GIS解析后的收货人区编码
                orderConsign.setConsigneeCountyNoGis(consigneeAddress.getCountyNoGis());
                //GIS解析后的收货人区名称
                orderConsign.setConsigneeCountyNameGis(consigneeAddress.getCountyNameGis());
                //GIS解析后的收货人镇编码
                orderConsign.setConsigneeTownNoGis(consigneeAddress.getTownNoGis());
                //GIS解析后的收货人镇名称
                orderConsign.setConsigneeTownNameGis(consigneeAddress.getTownNameGis());
                // 纬度
                if (consigneeAddress.getLatitude() != null && NumberUtils.isParsable(consigneeAddress.getLatitude())) {
                    orderConsign.setConsigneeLatitude(new BigDecimal(consigneeAddress.getLatitude()));
                }
                // 经度
                if (consigneeAddress.getLongitude() != null && NumberUtils.isParsable(consigneeAddress.getLongitude())) {
                    orderConsign.setConsigneeLongitude(new BigDecimal(consigneeAddress.getLongitude()));
                }
                //地址嵌套等级
                orderConsign.setConsigneeConflictLevel(consigneeAddress.getConflictLevel());
                //gis打标地址来源
                orderConsign.setConsigneeAddressSource(consigneeAddress.getAddressSource());

                // 围栏信任打标
                if (null != consigneeAddress.getFenceTrusted()) {
                    // 修改场景传入信任标识，则围栏信息落库
                    // 否则，不信任 - 清空围栏信息
                    if (FenceTrustEnum.TRUSTED.getCode().equals(consigneeAddress.getFenceTrusted())) {
                        orderSign.put(OrderSignEnum.CONSIGNEE_FENCE_TRUSTED.getCode(), consigneeAddress.getFenceTrusted().toString());
                        // 围栏信息
                        orderConsign.setConsigneeFenceList(this.toFenceInfos(consigneeAddress.getFenceInfos()));
                    } else {
                        // 清空围栏信息
                        orderSign.put(OrderSignEnum.CONSIGNEE_FENCE_TRUSTED.getCode(), EMPTY_STRING);
                        orderConsign.setConsigneeFenceList(Collections.emptyList());
                    }
                }
                //行政区编码
                orderConsign.setConsigneeRegionNo(consigneeAddress.getRegionNo());
                //行政区名称
                orderConsign.setConsigneeRegionName(consigneeAddress.getRegionName());
                //英文城市
                orderConsign.setConsigneeEnCity(consigneeAddress.getEnCityName());
                //英文地址
                orderConsign.setConsigneeEnAddress(tdeAcl.encrypt(consigneeAddress.getEnAddress()));
                orderConsign.setConsigneePoiCode(consigneeAddress.getPoiCode());
                orderConsign.setConsigneePoiName(tdeAcl.encrypt(consigneeAddress.getPoiName()));
                orderConsign.setConsigneeHouseNumber(tdeAcl.encrypt(consigneeAddress.getHouseNumber()));
                orderConsign.setConsigneeAddressExt(consigneeAddress.getExtendProps());
            }
            //收件人扩展信息
            if(MapUtils.isNotEmpty(consigneeFacade.getExtendProps())){
                orderConsign.setConsigneeExt(consigneeFacade.getExtendProps());
                // ext里特殊字段加密
                if (consigneeFacade.getExtendProps().containsKey(AttachmentKeyEnum.APPLIER_NAME.getKey())) {
                    String applierName = consigneeFacade.getExtendProps().get(AttachmentKeyEnum.APPLIER_NAME.getKey());
                    orderConsign.getConsigneeExt().put(AttachmentKeyEnum.APPLIER_NAME.getKey(), tdeAcl.encrypt(applierName));
                }
                if (consigneeFacade.getExtendProps().containsKey(AttachmentKeyEnum.APPLIER_PHONE.getKey())) {
                    String applierPhone = consigneeFacade.getExtendProps().get(AttachmentKeyEnum.APPLIER_PHONE.getKey());
                    orderConsign.getConsigneeExt().put(AttachmentKeyEnum.APPLIER_PHONE.getKey(), tdeAcl.encrypt(applierPhone));
                }
                if (consigneeFacade.getExtendProps().containsKey(AttachmentKeyEnum.APPLY_ID_NO.getKey())) {
                    String applyIdNo = consigneeFacade.getExtendProps().get(AttachmentKeyEnum.APPLY_ID_NO.getKey());
                    orderConsign.getConsigneeExt().put(AttachmentKeyEnum.APPLY_ID_NO.getKey(), tdeAcl.encrypt(applyIdNo));
                }
            }
        }
        //TODO GIS解析地址类型
        //orderConsign.setAddressType((byte) (addressType != null ? addressType.getCode() : AddressTypeEnum.JD_STANDARD.getCode()));
        return orderConsign;
    }

    /**
     * 围栏信息转换
     * @param fenceInfos
     * @return
     */
    private List<Order.FenceInfo> toFenceInfos(List<FenceInfo> fenceInfos) {
        if (CollectionUtils.isEmpty(fenceInfos)) {
            return Collections.emptyList();
        }
        List<Order.FenceInfo> fenceInfoList = new ArrayList<>(fenceInfos.size());
        for (FenceInfo fenceInfo : fenceInfos) {
            Order.FenceInfo fence = new Order.FenceInfo();
            fence.setFenceId(fenceInfo.getFenceId());
            fence.setFenceType(fenceInfo.getFenceType());
            fenceInfoList.add(fence);
        }
        return fenceInfoList;
    }

    /**
     * 关联单
     */
    private List<Order.RelationInfo> toOrderRelation(List<RefOrderFacade> refOrderFacades) {
        if (CollectionUtils.isEmpty(refOrderFacades)) {
            return null;
        }

        List<Order.RelationInfo> list = new ArrayList<>(refOrderFacades.size());
        refOrderFacades.forEach(refOrderInfo -> {
            Order.RelationInfo relationInfo = new Order.RelationInfo();
            //关联单号
            relationInfo.setRefOrderNo(refOrderInfo.getRefOrderNo());
            //关联单类型
            relationInfo.setRefOrderType(refOrderInfo.getRefOrderType());
            //关联单子类型
            if (refOrderInfo.getRefOrderSubType() != null) {
                relationInfo.setRefOrderSubType(refOrderInfo.getRefOrderSubType().byteValue());
            }
            relationInfo.setRemark(refOrderInfo.getRemark());
            list.add(relationInfo);
        });
        return list;
    }

    /**
     * 新字段持久化-关联单
     */
    private Order.RefOrderInfo toOrderRelationNew(ModifyOrderFacadeRequest facadeRequest) {
        Order.RefOrderInfo refOrderInfo = new Order.RefOrderInfo();
        refOrderInfo.setExt(facadeRequest.getExtendRefOrder());

        if (CollectionUtils.isEmpty(facadeRequest.getRefOrders())) {
            return refOrderInfo;
        }

        List<RefOrderFacade> refOrderFacades = facadeRequest.getRefOrders();
        List<String> waybillNos = new ArrayList<>();
        List<String> reservationOrderNos = new ArrayList<>();
        List<String> purchaseOrderNos = new ArrayList<>();
        List<String> originalOrderNos = new ArrayList<>();
        List<String> readdressOrderNos = new ArrayList<>();
        List<String> reverseOrderNos = new ArrayList<>();
        List<String> pickupOrderNos = new ArrayList<>();
        List<String> pickupWaybillNos = new ArrayList<>();
        List<String> deliveryOrderNos = new ArrayList<>();
        List<String> deliveryWaybillNos = new ArrayList<>();
        List<String> enquiryOrderNos = new ArrayList<>();

        List<String> refundOrderNos = new ArrayList<>();
        List<String> serviceEnquiryOrderNos = new ArrayList<>();
        List<String> serviceEnquiryWaybillNos = new ArrayList<>();
        List<String> collectionOrderNos  = new ArrayList<>();

        Map<String,String> ext = refOrderInfo.getExt();
        if (null == ext) {
            ext = new HashMap<>();
        }
        /**
         * 遍历取出关联单
         */
        Map<String, String> finalExt = ext;
        refOrderFacades.forEach(refOrderFacade -> {
            //运单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY.getCode()) && refOrderFacade.getRefOrderSubType().equals(RefOrderSubType.DeliveryEnum.FORWARD_DELIVERY.getCode())) {
                waybillNos.add(refOrderFacade.getRefOrderNo());
            }

            //预约单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.WORK_ORDER.getCode()) && refOrderFacade.getRefOrderSubType().equals(RefOrderSubType.WorkOrderEnum.RESERVATION.getCode())) {
                reservationOrderNos.add(refOrderFacade.getRefOrderNo());
            }

            //采购单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.WORK_ORDER.getCode()) && refOrderFacade.getRefOrderSubType().equals(RefOrderSubType.WorkOrderEnum.INBOUND.getCode())) {
                purchaseOrderNos.add(refOrderFacade.getRefOrderNo());
            }

            //改址单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.READDRESS.getCode())) {
                readdressOrderNos.add(refOrderFacade.getRefOrderNo());
            }

            //逆向单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.RETURN_ORDER.getCode())) {
                reverseOrderNos.add(refOrderFacade.getRefOrderNo());
            }

            //送取同步-取件单订单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode())) {
                pickupOrderNos.add(refOrderFacade.getRefOrderNo());
            }
            //送取同步-取件单运单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode())) {
                pickupWaybillNos.add(refOrderFacade.getRefOrderNo());
            }
            //送取同步-配送单订单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY_ORDER.getCode())) {
                deliveryOrderNos.add(refOrderFacade.getRefOrderNo());
            }
            //送取同步-配送单运单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY.getCode())) {
                deliveryWaybillNos.add(refOrderFacade.getRefOrderNo());
            }
            //询价单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.ENQUIRY.getCode())) {
                enquiryOrderNos.add(refOrderFacade.getRefOrderNo());
            }

            //退款单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.REFUND_ORDER.getCode())) {
                refundOrderNos.add(refOrderFacade.getRefOrderNo());
            }
            //增值服务询价订单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.SERVICE_ENQUIRY_ORDER.getCode())) {
                serviceEnquiryOrderNos.add(refOrderFacade.getRefOrderNo());
            }
            //增值服务询价运单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.SERVICE_ENQUIRY_WAYBILL.getCode())) {
                serviceEnquiryWaybillNos.add(refOrderFacade.getRefOrderNo());
            }

            //扩展单据-签单返单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.SIGN_RETURN_ORDER.getCode())) {
                finalExt.put(RefOrderExtendTypeEnum.SIGN_RETURN_ORDER_NO.getCode(),refOrderFacade.getRefOrderNo());
            }

            //扩展单据-签单返单运单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.SIGN_RETURN_WAYBILL.getCode())) {
                finalExt.put(RefOrderExtendTypeEnum.SIGN_RETURN_WAYBILL_NO.getCode(),refOrderFacade.getRefOrderNo());
            }

            //扩展单据-逆向单运单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.REVERSE_WAYBILL.getCode())) {
                finalExt.put(RefOrderExtendTypeEnum.REVERSE_WAYBILL_NO.getCode(),refOrderFacade.getRefOrderNo());
            }

            //扩展单据-支付单号
            if (RefOrderTypeEnum.PAY_ORDER.getCode().equals(refOrderFacade.getRefOrderType())) {
                finalExt.put(RefOrderExtendTypeEnum.PAY_ORDER_NO.getCode(), refOrderFacade.getRefOrderNo());
            }

            //集单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.COLLECTION_ORDER.getCode())
                    && StringUtils.isNotBlank(refOrderFacade.getRefOrderNo())) {
                collectionOrderNos.add(refOrderFacade.getRefOrderNo());
            }

        });

        refOrderInfo.setOriginalOrderNos(originalOrderNos);
        refOrderInfo.setWaybillNos(waybillNos);
        refOrderInfo.setReservationOrderNos(reservationOrderNos);
        refOrderInfo.setPurchaseOrderNos(purchaseOrderNos);
        refOrderInfo.setReaddressOrderNos(readdressOrderNos);
        refOrderInfo.setReverseOrderNos(reverseOrderNos);
        refOrderInfo.setPickupOrderNos(pickupOrderNos);
        refOrderInfo.setPickupWaybillNos(pickupWaybillNos);
        refOrderInfo.setDeliveryOrderNos(deliveryOrderNos);
        refOrderInfo.setDeliveryWaybillNos(deliveryWaybillNos);
        refOrderInfo.setRefundOrderNos(refundOrderNos);
        refOrderInfo.setEnquiryOrderNos(enquiryOrderNos);
        refOrderInfo.setServiceEnquiryOrderNos(serviceEnquiryOrderNos);
        refOrderInfo.setServiceEnquiryWaybillNos(serviceEnquiryWaybillNos);
        refOrderInfo.setCollectionOrderNos(collectionOrderNos);
        refOrderInfo.setExt(ext);
        return refOrderInfo;
    }

    /**
     * 删除关联单转换
     */
    private Map<String, Set<String>> toRefOrderInfoDeleteMap(ModifyOrderFacadeRequest facadeRequest) {
        if (facadeRequest == null) {
            return null;
        }
        List<RefOrderFacade> deleteRefOrders = facadeRequest.getDeleteRefOrders();
        if (CollectionUtils.isEmpty(deleteRefOrders)) {
            return null;
        }

        Map<String, Set<String>> deleteMap = new HashMap<>();

        Set<String> waybillNos = new HashSet<>();
        Set<String> reservationOrderNos = new HashSet<>();
        Set<String> purchaseOrderNos = new HashSet<>();
        Set<String> originalOrderNos = new HashSet<>();
        Set<String> readdressOrderNos = new HashSet<>();
        Set<String> reverseOrderNos = new HashSet<>();
        Set<String> pickupOrderNos = new HashSet<>();
        Set<String> pickupWaybillNos = new HashSet<>();
        Set<String> deliveryOrderNos = new HashSet<>();
        Set<String> deliveryWaybillNos = new HashSet<>();

        Set<String> refundOrderNos = new HashSet<>();
        Set<String> enquiryOrderNos = new HashSet<>();
        Set<String> serviceEnquiryOrderNos = new HashSet<>();
        Set<String> serviceEnquiryWaybillNos = new HashSet<>();
        Set<String> collectionOrderNos = new HashSet<>();

        /**
         * 遍历取出关联单
         */
        deleteRefOrders.forEach(refOrderFacade -> {
            //运单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY.getCode()) && refOrderFacade.getRefOrderSubType().equals(RefOrderSubType.DeliveryEnum.FORWARD_DELIVERY.getCode())) {
                waybillNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.WAYBILL_NO.getCode(), waybillNos);
            }

            //预约单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.WORK_ORDER.getCode()) && refOrderFacade.getRefOrderSubType().equals(RefOrderSubType.WorkOrderEnum.RESERVATION.getCode())) {
                reservationOrderNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.RESERVATION_ORDER_NO.getCode(), reservationOrderNos);
            }

            //采购单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.WORK_ORDER.getCode()) && refOrderFacade.getRefOrderSubType().equals(RefOrderSubType.WorkOrderEnum.INBOUND.getCode())) {
                purchaseOrderNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.PURCHASE_ORDER_NO.getCode(), purchaseOrderNos);
            }

            //改址单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.READDRESS.getCode())) {
                readdressOrderNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.READDRESS_ORDER_NO.getCode(), readdressOrderNos);
            }

            //逆向单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.RETURN_ORDER.getCode())) {
                reverseOrderNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.REVERSE_ORDER_NO.getCode(), reverseOrderNos);
            }

            //送取同步-取件单订单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode())) {
                pickupOrderNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.PICKUP_ORDER_NOS.getCode(), pickupOrderNos);
            }
            //送取同步-取件单运单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode())) {
                pickupWaybillNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.PICKUP_WAYBILL_NOS.getCode(), pickupWaybillNos);
            }
            //送取同步-配送单订单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY_ORDER.getCode())) {
                deliveryOrderNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.DELIVERY_ORDER_NOS.getCode(), deliveryOrderNos);
            }
            //送取同步-配送单运单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY.getCode())) {
                deliveryWaybillNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.DELIVERY_WAYBILL_NOS.getCode(), deliveryWaybillNos);
            }

            //退款单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.REFUND_ORDER.getCode())) {
                refundOrderNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.REFUND_ORDER_NOS.getCode(), refundOrderNos);
            }

            //询价单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.ENQUIRY.getCode())) {
                enquiryOrderNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.ENQUIRY_ORDER_NOS.getCode(), enquiryOrderNos);
            }

            //增值服务询价订单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.SERVICE_ENQUIRY_ORDER.getCode())) {
                serviceEnquiryOrderNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.SERVICE_ENQUIRY_ORDERNOS.getCode(), serviceEnquiryOrderNos);
            }

            //增值服务询价订单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.SERVICE_ENQUIRY_WAYBILL.getCode())) {
                serviceEnquiryWaybillNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.SERVICE_ENQUIRY_WAYBILLNOS.getCode(), serviceEnquiryWaybillNos);
            }

            //集单号订单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.COLLECTION_ORDER.getCode())) {
                collectionOrderNos.add(refOrderFacade.getRefOrderNo());
                deleteMap.put(RefOrderTypeStrEnum.COLLECTION_ORDER.getCode(),  collectionOrderNos);
            }

        });

        return deleteMap;
    }

    /**
     * 物流服务产品信息
     */
    private List<Order.ServiceProductInfo> toServiceProductList(List<ProductFacade> productFacades, Map<String, Integer> productOperateType) {
        if (CollectionUtils.isEmpty(productFacades)) {
            return null;
        }

        List<Order.ServiceProductInfo> list = new ArrayList<>(productFacades.size());

        for (ProductFacade productFacade : productFacades) {
            Order.ServiceProductInfo serviceProduct = new Order.ServiceProductInfo();
            serviceProduct.setProductNo(productFacade.getProductNo());
            serviceProduct.setProductName(productFacade.getProductName());
            serviceProduct.setProductType(productFacade.getProductType().byteValue());
            //降级前的产品编码
            serviceProduct.setPreDegradeProductNo(productFacade.getOriginalProductNo());
            //降级前的产品名称
            serviceProduct.setPreDegradeProductName(productFacade.getOriginalProductName());
            // 主产品编码
            serviceProduct.setParentNo(productFacade.getParentNo());
            //产品要素
            serviceProduct.setAttrs(productFacade.getProductAttrs());
            //扩展字段
            if (productFacade.getExtendProps() != null) {
                HashMap<String, String> ext = new HashMap<>();
                productFacade.getExtendProps().forEach((key, value) -> ext.put(key, value));
                serviceProduct.setExt(ext);
            }
            if (productFacade.getOperateType() != null) {
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.MODIFY_REPOSITORY_PRODUCT_KEY_ADD_PRODUCT_TYPE)) {
                    //由于三级产品标的产品编码和主产品编码一致，故需要拼产品类型
                    productOperateType.put(productFacade.getProductNo() + "&" + productFacade.getProductType(), productFacade.getOperateType().getCode());
                } else {
                    productOperateType.put(productFacade.getProductNo(), productFacade.getOperateType().getCode());
                }
            }
            list.add(serviceProduct);
        }
        return list;
    }

    /**
     * 收发件人扩展信息
     *
     * @param consignorFacade
     * @param consigneeFacade
     * @return
     * @throws Exception
     */
    private Order.ConsignExtendInfo toConsignExtendInfo(ConsignorFacade consignorFacade, ConsigneeFacade consigneeFacade) throws Exception {
        if (consignorFacade == null && consigneeFacade == null) {
            return null;
        }
        Order.ConsignExtendInfo consignExtendInfo = new Order.ConsignExtendInfo();
        //发货人信息
        if (consignorFacade != null && consignorFacade.getAddress() != null) {
            //发货人地址
            AddressInfo consignorAddress = consignorFacade.getAddress();
            //GIS解析后的发货人详细地址
            consignExtendInfo.setConsignorAddressGis(tdeAcl.encrypt(consignorAddress.getAddressGis()));
            consignExtendInfo.setConsignorAddressGisQuery(tdeAcl.obtainKeyWordIndex(consignorAddress.getAddressGis()));
            consignExtendInfo.setConsignorPreciseGis(consignorAddress.getPreciseGis());
            consignExtendInfo.setConsignorChinaPostAddressCode(consignorAddress.getChinaPostAddressCode());
        }
        //收货人人信息
        if (consigneeFacade != null && consigneeFacade.getAddress() != null) {
            //收货人地址
            AddressInfo consigneeAddress = consigneeFacade.getAddress();
            consignExtendInfo.setConsigneeAddressGis(tdeAcl.encrypt(consigneeAddress.getAddressGis()));
            consignExtendInfo.setConsigneeAddressGisQuery(tdeAcl.obtainKeyWordIndex(consigneeAddress.getAddressGis()));
            consignExtendInfo.setConsigneePreciseGis(consigneeAddress.getPreciseGis());
            consignExtendInfo.setConsigneeChinaPostAddressCode(consigneeAddress.getChinaPostAddressCode());
        }
        //收货人邮箱
        if (consigneeFacade != null && consigneeFacade.getConsigneeEmail() != null) {
            consignExtendInfo.setConsigneeEmail(consigneeFacade.getConsigneeEmail());
        }
        return consignExtendInfo;
    }

    /**
     * 营销信息
     *
     * @param promotionFacade
     * @return
     */
    public List<Order.MarketInfo> toMarketInfos(PromotionFacade promotionFacade, Map<String, Integer> marketOperateMap) {
        if (promotionFacade == null) {
            return null;
        }

        List<Order.MarketInfo> marketInfos = new ArrayList<>();
        //优惠券信息
        List<TicketFacade> ticketFacades = promotionFacade.getTickets();
        if (CollectionUtils.isNotEmpty(ticketFacades)) {
            for (TicketFacade ticketFacade : ticketFacades) {
                if (ticketFacade.getOperateType() == null) {
                    continue;
                }
                Order.MarketInfo marketInfo = new Order.MarketInfo();
                //营销类型：券
                marketInfo.setCouponSource(MarketEnum.COUPON.getCode());
                //优惠券编码
                marketInfo.setCouponNo(ticketFacade.getTicketNo());
                //优惠券类型
                marketInfo.setCouponType(String.valueOf(ticketFacade.getTicketType()));
                //优惠券类别
                marketInfo.setCouponCategory(ticketFacade.getTicketCategory());
                //优惠券文案描述
                marketInfo.setDescription(ticketFacade.getTicketDescription());
                //优惠金额
                if (ticketFacade.getTicketDiscountAmount() != null) {
                    marketInfo.setDiscountAmount(ticketFacade.getTicketDiscountAmount().getAmount());
                    if (ticketFacade.getTicketDiscountAmount().getCurrency() != null) {
                        marketInfo.setCurrency(ticketFacade.getTicketDiscountAmount().getCurrency().getCode());
                    }
                }
                //折扣率
                marketInfo.setDiscountRate(ticketFacade.getTicketDiscountRate());
                //折扣上限
                if (ticketFacade.getTicketDiscountUpperLimit() != null) {
                    marketInfo.setDiscountUpperLimit(ticketFacade.getTicketDiscountUpperLimit().getAmount());
                    if (ticketFacade.getTicketDiscountUpperLimit().getCurrency() != null) {
                        marketInfo.setCurrency(ticketFacade.getTicketDiscountUpperLimit().getCurrency().getCode());
                    }
                }
                //使用金额
                if (ticketFacade.getTicketUseAmount() != null) {
                    marketInfo.setUseAmount(ticketFacade.getTicketUseAmount().getAmount());
                    if (ticketFacade.getTicketUseAmount().getCurrency() != null) {
                        marketInfo.setCurrency(ticketFacade.getTicketUseAmount().getCurrency().getCode());
                    }
                }
                marketInfo.setCouponStatus(ticketFacade.getCouponStatus());
                //营销标识
                Map<String, String> promotionSign = new HashMap<>();
                if (ticketFacade.getTicketSource() != null) {
                    //优惠券来源
                    promotionSign.put(PromotionSignEnum.TICKET_SOURCE.getKey(), String.valueOf(ticketFacade.getTicketSource()));
                }
                marketInfo.setPromotionSign(promotionSign);
                //批次号
                marketInfo.setCouponBatchNo(ticketFacade.getTicketBatchNo());
                marketInfos.add(marketInfo);
                marketOperateMap.put(ticketFacade.getTicketNo() + "_" + MarketEnum.COUPON.getCode(), ticketFacade.getOperateType().getCode());
            }
        }
        //折扣码信息
        List<DiscountFacade> discountFacades = promotionFacade.getDiscounts();
        if (CollectionUtils.isNotEmpty(discountFacades)) {
            for (DiscountFacade discountFacade : discountFacades) {
                if (discountFacade.getOperateType() == null) {
                    continue;
                }
                Order.MarketInfo marketInfo = new Order.MarketInfo();
                //营销类型：折扣码
                marketInfo.setCouponSource(MarketEnum.DISCOUNT.getCode());
                //折扣码
                marketInfo.setCouponNo(discountFacade.getDiscountNo());
                marketInfo.setCouponType(discountFacade.getDiscountType());
                marketInfo.setExt(discountFacade.getExtendProps());
                marketInfos.add(marketInfo);
                marketOperateMap.put(discountFacade.getDiscountNo() + "_" + MarketEnum.DISCOUNT.getCode(), discountFacade.getOperateType().getCode());
            }
        }

        //营销折扣信息
        List<DiscountFacade> operationDiscountFacades = promotionFacade.getOperationDiscounts();
        if (CollectionUtils.isNotEmpty( operationDiscountFacades)) {
            for (DiscountFacade discountFacade : operationDiscountFacades) {
                if (discountFacade.getOperateType() == null) {
                    continue;
                }
                Order.MarketInfo marketInfo = new Order.MarketInfo();
                //营销类型：折扣码
                marketInfo.setCouponSource(MarketEnum.OPERATION_DISCOUNT.getCode());
                //折扣码
                marketInfo.setCouponNo(discountFacade.getDiscountNo());
                marketInfo.setCouponType(discountFacade.getDiscountType());
                marketInfo.setExt(discountFacade.getExtendProps());
                marketInfos.add(marketInfo);
                marketOperateMap.put(discountFacade.getDiscountNo() + "_" + MarketEnum.OPERATION_DISCOUNT.getCode(), discountFacade.getOperateType().getCode());
            }
        }
        //营销活动信息
        List<ActivityFacade> activityFacades = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(activityFacades)) {
            for (ActivityFacade activityFacade : activityFacades) {
                if (activityFacade.getOperateType() == null) {
                    continue;
                }
                Order.MarketInfo marketInfo = new Order.MarketInfo();
                //营销类型：活动信息
                marketInfo.setCouponSource(MarketEnum.ACTIVITY.getCode());
                //活动编码
                marketInfo.setCouponNo(activityFacade.getActivityNo());
                //活动名称
                marketInfo.setCouponName(activityFacade.getActivityName());
                //活动状态
                marketInfo.setCouponStatus(activityFacade.getActivityStatus());
                //活动内容
                marketInfo.setCouponValue(activityFacade.getActivityValue());

                marketInfos.add(marketInfo);
                marketOperateMap.put(activityFacade.getActivityNo() + "_" + MarketEnum.ACTIVITY.getCode(), activityFacade.getOperateType().getCode());
            }
        }

        if (marketInfos.size() == 0) {
            return null;
        }
        return marketInfos;
    }

    /**
     * 主档扩展信息
     *
     * @param extendProps
     * @return
     */
    public Order.ExtendInfo toOrderExtend(Map<String, String> extendProps) {
        if (extendProps == null || extendProps.isEmpty()) {
            return null;
        }
        Order.ExtendInfo orderExtend = new Order.ExtendInfo();
        orderExtend.setExt(new HashMap<>(extendProps));
        return orderExtend;
    }

    /**
     * 系统环境扩展信息
     *
     * @param extendProps
     * @return
     */
    private HashMap<String, String> toSysExt(Map<String, String> extendProps) {
        if (extendProps == null || extendProps.isEmpty()) {
            return null;
        }
        HashMap<String, String> sysExt = new HashMap<>();
        // 环境标识
        sysExt.put(ABConstants.ABENVIRONMENT_FLAG, extendProps.get(ABConstants.ABENVIRONMENT_FLAG));
        return sysExt;
    }

    /**
     * 功能：转换profile
     *
     * @param requestProfile 1
     * @return com.jdl.cp.core.spec.RequestProfile
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/27 19:40
     */
    public RequestProfile toRequestProfile(cn.jdl.batrix.spec.RequestProfile requestProfile) {
        if (requestProfile == null) {
            return null;
        }
        RequestProfile profile = new RequestProfile();
        profile.setTimezone(requestProfile.getTimeZone());
        profile.setLocale(requestProfile.getLocale());
        profile.setTenantId(requestProfile.getTenantId());
        profile.setTraceId(requestProfile.getTraceId());
        return profile;
    }

    /**
     * 功能：转换请求
     *
     * @param facadeRequest 1
     * @return com.jdl.cp.op.client.dto.ModifyOrderSomeDataRequest
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/27 20:07
     */
    public ModifyOrderSomeDataRequest toDeleteOrderSomeDataRequest(ModifyOrderFacadeRequest facadeRequest) {
        if (facadeRequest == null) {
            return null;
        }
        ModifyOrderSomeDataRequest modifyOrderSomeDataRequest = new ModifyOrderSomeDataRequest();
        Order order = new Order();
        order.setTenantId(facadeRequest.getTenantId());
        //order.setOrderId(facadeRequest.getOrderId());
        order.setOrderNo(facadeRequest.getOrderNo());
        order.setBusinessUnit(facadeRequest.getBusinessIdentity() != null ? facadeRequest.getBusinessIdentity().getBusinessUnit() : null);
        order.setExtendInfo(toOrderExtend(facadeRequest.getExtendProps()));
        //修改人
        order.setUpdateUser(facadeRequest.getOperator());
        //修改时间
        order.setUpdateTime(new Date());

        //删除关联单
        if (CollectionUtils.isNotEmpty(facadeRequest.getRefOrders())) {
            List<RefOrderFacade> refOrders = facadeRequest.getRefOrders();
            Map<String, Set<String>> delMap = new HashMap<>();
            Set<String> set = new HashSet<>();
            for (RefOrderFacade refOrderFacade : refOrders) {
                if (OperateTypeEnum.DELETE == refOrderFacade.getOperateType()) {
                    set.add(refOrderFacade.getRefOrderNo());
                }
            }
            if (!set.isEmpty()) {
                delMap.put(RefOrderTypeStrEnum.READDRESS_ORDER_NO.getCode(), set);
                modifyOrderSomeDataRequest.setRefOrderInfoDeleteMap(delMap);
            }
        }

        //隐藏标
        order.setHiddenMark(facadeRequest.getHiddenMark());
        modifyOrderSomeDataRequest.setOrder(order);
        return modifyOrderSomeDataRequest;

    }

    /**
     * 功能：转换请求
     *
     * @param facadeRequest 1
     * @return com.jdl.cp.op.client.dto.ModifyOrderSomeDataRequest
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/27 20:07
     */
    public ModifyOrderSomeDataRequest toCallbackOrderSomeDataRequest(ModifyOrderFacadeRequest facadeRequest) {
        // 包裹维度附加费开关
        boolean packageModeAttachFeesSwitch = expressUccConfigCenter.isPackageModeAttachFeesSwitch();
        LOGGER.info("包裹维度附加费开关:{}", packageModeAttachFeesSwitch);
        ModifyOrderSomeDataRequest modifyOrderSomeDataRequest = new ModifyOrderSomeDataRequest();
        Order order = new Order();
        Map<String, Integer> productOperateType = new HashMap<>();
        if (facadeRequest != null) {
            // 拦截信息
            if(null != facadeRequest.getInterceptFacade()) {
                InterceptInfo interceptInfo = new InterceptInfo();
                interceptInfo.setInterceptResultTime(facadeRequest.getInterceptFacade().getInterceptResultTime());
                order.setInterceptInfo(interceptInfo);
            }
            if (facadeRequest.getBusinessIdentity() != null) {
                order.setBusinessUnit(facadeRequest.getBusinessIdentity().getBusinessUnit());
                order.setBusinessType(facadeRequest.getBusinessIdentity().getBusinessType());
            }
            //order.setOrderId(facadeRequest.getOrderId());
            order.setOrderNo(facadeRequest.getOrderNo());
            order.setOrderStatus(facadeRequest.getOrderStatus());
            order.setOrderStatusCustom(facadeRequest.getOrderStatusCustom());
            order.setOrderExtendStatus(facadeRequest.getExecutedStatus());
            order.setTenantId(facadeRequest.getTenantId());
            //order.setCustomerChannelInfo(toCustomerChannel(facadeRequest.getChannel()));
            order.setTransactionCostInfo(toTransactionCost(facadeRequest.getFinance()));
            order.setUpdateUser(facadeRequest.getOperator());
            order.setRemark(facadeRequest.getRemark());

            if(null != facadeRequest.getInterceptType()){
                order.setInterceptType(facadeRequest.getInterceptType());
            }
            if(null != facadeRequest.getCancelStatus()){
                order.setCancelStatus(facadeRequest.getCancelStatus().byteValue());
            }
            order.setServiceProductInfoList(toServiceProductInfoList(facadeRequest.getProducts(), productOperateType));
            order.setExtendInfo(toOrderExtend(facadeRequest.getExtendProps()));
            if (!packageModeAttachFeesSwitch) {
                // TODO 开关下掉后 此段代码可以删除，因为toClearFieldData已经涵盖了此段逻辑
                if (OrderUsageEnum.deploy.getCode().equals(facadeRequest.getOrderUsage())) {
                    order.setOrderUsage(null);
                    OrderNullFieldsRequest orderNullFieldsRequest = new OrderNullFieldsRequest();
                    orderNullFieldsRequest.setOrderUsage(true);
                    modifyOrderSomeDataRequest.setOrderNullFieldsRequest(orderNullFieldsRequest);
                }
            }
            if (facadeRequest.getShipment() != null) {
                ShipmentFacade shipment = facadeRequest.getShipment();
                Order.ShipmentInfo shipmentInfo = new Order.ShipmentInfo();
                shipmentInfo.setFirstStationNo(shipment.getStartStationNo());
                shipmentInfo.setFirstStationName(shipment.getStartStationName());
                shipmentInfo.setFirstStationType(StationTypeEnum.STATION.getType());
                shipmentInfo.setStartStationType(shipment.getStartStationType());
                shipmentInfo.setLastStationNo(shipment.getEndStationNo());
                shipmentInfo.setLastStationName(shipment.getEndStationName());
                //当为1是，表示站点
                shipmentInfo.setLastStationType(StationTypeEnum.STATION.getType());
                //具体目的站点类型
                shipmentInfo.setEndStationType(shipment.getEndStationType());
                shipmentInfo.setActualPlanDeliveryTime(shipment.getActualPlanDeliveryTime());
                //承运商编码
                shipmentInfo.setShipperNo(shipment.getShipperNo());
                //承运商名称
                shipmentInfo.setShipperName(shipment.getShipperName());
                //承运商类型
                shipmentInfo.setShipperType(shipment.getShipperType() == null ? null : String.valueOf(shipment.getShipperType()));
                order.setShipmentInfo(shipmentInfo);
            }
            Order.RefOrderInfo refOrderInfo = null;
            if (CollectionUtils.isNotEmpty(facadeRequest.getRefOrders())) {
                List<String> waybillNos = facadeRequest.getRefOrders().stream().filter(e -> RefOrderTypeEnum.DELIVERY.getCode().equals(e.getRefOrderType()))
                        .map(e -> e.getRefOrderNo()).collect(Collectors.toList());
                refOrderInfo = new Order.RefOrderInfo();
                refOrderInfo.setWaybillNos(waybillNos);
            }

            //关联单 - 扩展单据
            if (MapUtils.isNotEmpty(facadeRequest.getExtendRefOrder())) {
                if(null == refOrderInfo){
                    refOrderInfo = new Order.RefOrderInfo();
                }
                refOrderInfo.setExt(facadeRequest.getExtendRefOrder());
            }
            order.setRefOrderInfo(refOrderInfo);

            // 复核体积
            if(facadeRequest.getRecheckVolume() != null) {
                if(facadeRequest.getRecheckVolume().getValue() != null) {
                    order.setRecheckVolume(facadeRequest.getRecheckVolume().getValue());
                }
                if(facadeRequest.getRecheckVolume().getUnit() != null) {
                    order.setRecheckVolumeUnit(facadeRequest.getRecheckVolume().getUnit().getCode());
                }
            }
            // 复核重量
            if(facadeRequest.getRecheckWeight() != null) {
                if(facadeRequest.getRecheckWeight().getValue() != null) {
                    order.setRecheckWeight(facadeRequest.getRecheckWeight().getValue());
                }
                if(facadeRequest.getRecheckWeight().getUnit() != null) {
                    order.setRecheckWeightUnit(facadeRequest.getRecheckWeight().getUnit().getCode());
                }
            }
            // 履约信息
            order.setFulfillmentInfo(this.toFulfillmentInfo(facadeRequest.getFulfillment()));
            // 商品信息
            order.setGoodsInfoList(toGoodsInfoList(facadeRequest.getGoodsList()));
            if (packageModeAttachFeesSwitch) {
                // 清空字段
                modifyOrderSomeDataRequest.setOrderNullFieldsRequest(toClearFieldData(order, facadeRequest));
            }
            // 弃货状态
            order.setDiscardStatus(facadeRequest.getDiscardStatus());
        }
        // 列表修改的处理
        modifyOrderSomeDataRequest.setProductOperateType(productOperateType);
        modifyOrderSomeDataRequest.setOrder(order);
        return modifyOrderSomeDataRequest;
    }

    /**
     * 持久化转化
     *
     * @param productList
     * @return
     */
    List<Order.ServiceProductInfo> toServiceProductInfoList(List<ProductFacade> productList, Map<String, Integer> productOperateType) {
        if (CollectionUtils.isEmpty(productList)) {
            return null;
        }
        List<Order.ServiceProductInfo> serviceProductInfoList = new ArrayList<>();
        for (ProductFacade product : productList) {
            Order.ServiceProductInfo serviceProductInfo = new Order.ServiceProductInfo();
            serviceProductInfo.setProductNo(product.getProductNo());
            serviceProductInfo.setProductName(product.getProductName());
            serviceProductInfo.setProductType((byte) product.getProductType().intValue());
            serviceProductInfo.setPreDegradeProductNo(product.getOriginalProductNo());
            serviceProductInfo.setPreDegradeProductName(product.getOriginalProductName());
            serviceProductInfo.setParentNo(product.getParentNo());
            serviceProductInfo.setExt((HashMap<String, String>) product.getExtendProps());
            serviceProductInfo.setAttrs(product.getProductAttrs());
            serviceProductInfoList.add(serviceProductInfo);

            if (product.getOperateType() != null) {
                //由于三级产品标的产品编码和主产品编码一致，故需要拼产品类型
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.MODIFY_REPOSITORY_PRODUCT_KEY_ADD_PRODUCT_TYPE)) {
                    //由于三级产品标的产品编码和主产品编码一致，故需要拼产品类型
                    productOperateType.put(product.getProductNo() + "&" + product.getProductType(), product.getOperateType().getCode());
                } else {
                    productOperateType.put(product.getProductNo(), product.getOperateType().getCode());
                }
            }
        }
        return serviceProductInfoList;
    }


    /**
     * 接单场景下发成功后组装部分修改参数
     *
     * @param facadeRequest
     * @return
     */
    public ModifyOrderSomeDataRequest toCreateIssueOrderSomeDataRequest(ModifyOrderFacadeRequest facadeRequest) {
        Order order = new Order();
        if (facadeRequest.getBusinessIdentity() != null) {
            order.setBusinessUnit(facadeRequest.getBusinessIdentity().getBusinessUnit());
            order.setBusinessType(facadeRequest.getBusinessIdentity().getBusinessType());
        }
        order.setOrderNo(facadeRequest.getOrderNo());
        order.setOrderStatus(facadeRequest.getOrderStatus());
        if(StringUtils.isNotBlank(facadeRequest.getExecutedStatus())){
            order.setOrderExtendStatus(facadeRequest.getExecutedStatus());
        }
        order.setOrderStatusCustom(facadeRequest.getOrderStatusCustom());
        ModifyOrderSomeDataRequest modifyOrderSomeDataRequest = new ModifyOrderSomeDataRequest();
        if (CollectionUtils.isNotEmpty(facadeRequest.getProducts())) {
            Map<String, Integer> productOperateType = new HashMap<>();
            order.setServiceProductInfoList(toServiceProductList(facadeRequest.getProducts(), productOperateType));
            modifyOrderSomeDataRequest.setProductOperateType(productOperateType);
        }
        if (facadeRequest.getFinance() != null) {
            Order.TransactionCostInfo transactionCostInfo = new Order.TransactionCostInfo();
            transactionCostInfo.setSettlementType(facadeRequest.getFinance().getSettlementType().byteValue());
            transactionCostInfo.setPaymentStage(facadeRequest.getFinance().getPaymentStage());
            //支付状态
            transactionCostInfo.setPaymentStatus(facadeRequest.getFinance().getPaymentStatus());
            order.setTransactionCostInfo(transactionCostInfo);
        }
        modifyOrderSomeDataRequest.setOrder(order);
        return modifyOrderSomeDataRequest;
    }

    public ModifyEnquiryStatusRequest toModifyEnquiryStatusRequest(ModifyOrderFacadeRequest facadeRequest) {
        ModifyEnquiryStatusRequest request = new ModifyEnquiryStatusRequest();
        request.setOrderNo(facadeRequest.getOrderNo());
        request.setEnquiryStatus(facadeRequest.getFinance().getEnquiryStatus());
        request.setBusinessUnit(facadeRequest.getBusinessIdentity().getBusinessUnit());
        return request;
    }


    /**
     * 接收信息转换节点信息
     *
     * @param consigneeFacade
     * @return
     */
    private List<Order.LogisticsNodes> toReceiveLogisticsNodes(ConsigneeFacade consigneeFacade) {
        List<Order.LogisticsNodes> list = null;
        if (consigneeFacade != null && consigneeFacade.getReceiveWarehouse() != null) {
            list = new ArrayList<>();
            Order.LogisticsNodes logisticsNodes = new Order.LogisticsNodes();
            logisticsNodes.setNodeNo(consigneeFacade.getReceiveWarehouse().getWarehouseNo());
            logisticsNodes.setNodeName(consigneeFacade.getReceiveWarehouse().getWarehouseName());
            logisticsNodes.setNodeType(consigneeFacade.getReceiveWarehouse().getWarehouseSource());
            //节点分类,1-仓库
            logisticsNodes.setNodeClassification(NodeClassIfcationEnum.WAREHOUSE.getCode());
            logisticsNodes.setNodeUsage(NodeUsageEnum.RECEIVE.getCode());//1、发货，2、收货
            list.add(logisticsNodes);
        }
        return list;
    }

    /**
     * 协议信息列表转换
     * @param agreementFacades
     * @return
     */
    private List<Order.AgreementInfo> toAgreementInfos(List<AgreementFacade> agreementFacades) {
        if (CollectionUtils.isEmpty(agreementFacades)) {
            return null;
        }

        return agreementFacades.stream().filter(Objects::nonNull).map(agreementFacade -> {
            Order.AgreementInfo agreementInfo = new Order.AgreementInfo();
            agreementInfo.setAgreementType(agreementFacade.getAgreementType());
            agreementInfo.setAgreementId(agreementFacade.getAgreementId());
            agreementInfo.setSigner(agreementFacade.getSigner());
            agreementInfo.setSigningTime(agreementFacade.getSigningTime());
            agreementInfo.setExtendProps(agreementFacade.getExtendProps());
            return agreementInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 信息操作类型转换为数据库的类型
     * cf : https://cf.jd.com/pages/viewpage.action?pageId=466097549
     * @param modifyValue
     * @return
     */
    private Integer toRepositoryModifyType(String modifyValue) {

        // 订单 全量删除 3 对应到数据层是 全量删除 4
        if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifyValue)) {
            return REPOSITORY_ALL_DELETE;
        } else if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifyValue) || ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifyValue)) {
            return Integer.valueOf(modifyValue);
        } else {
            return null;
        }
    }


    /**
     * 履约信息
     * @param fulfillmentFacade
     * @return
     */
    private FulfillmentInfo toFulfillmentInfo(FulfillmentFacade fulfillmentFacade) {
        if (fulfillmentFacade == null) {
            return null;
        }
        FulfillmentInfo fulfillmentInfo = new FulfillmentInfo();
        fulfillmentInfo.setFulfillmentSign(fulfillmentFacade.getFulfillmentSign());
        fulfillmentInfo.setExtendProps(fulfillmentFacade.getExtendProps());
        // 实际揽收包裹数量
        if(fulfillmentFacade.getActualReceivedQuantity() != null) {
            fulfillmentInfo.setActualReceivedQuantity(fulfillmentFacade.getActualReceivedQuantity().getValue());
            if(fulfillmentFacade.getActualReceivedQuantity().getUnit() != null) {
                fulfillmentInfo.setPackageUnit(fulfillmentFacade.getActualReceivedQuantity().getUnit());
            }
        }
        // 实际签收包裹数量
        if(fulfillmentFacade.getActualSignedQuantity() != null) {
            fulfillmentInfo.setActualSignedQuantity(fulfillmentFacade.getActualSignedQuantity().getValue());
            if (fulfillmentFacade.getActualSignedQuantity().getUnit() != null) {
                fulfillmentInfo.setPackageUnit(fulfillmentFacade.getActualSignedQuantity().getUnit());
            }
        }

        fulfillmentInfo.setActualPickupTime(fulfillmentFacade.getActualPickupTime());
        fulfillmentInfo.setActualSignedTime(fulfillmentFacade.getActualSignedTime());

        if(fulfillmentFacade.getPackageMaxLen() != null) {
            LengthInfo packageMaxLen = new LengthInfo();
            if(null != fulfillmentFacade.getPackageMaxLen().getUnit()) {
                packageMaxLen.setUnit(fulfillmentFacade.getPackageMaxLen().getUnit().getCode());
            }
            if(fulfillmentFacade.getPackageMaxLen().getValue() != null) {
                packageMaxLen.setValue(fulfillmentFacade.getPackageMaxLen().getValue());
            }
            fulfillmentInfo.setPackageMaxLen(packageMaxLen);
        }
        return fulfillmentInfo;
    }
    /**
     * 跨境报关信息转换
     */
    private CustomsInfo toCustomsInfo(CustomsFacade customsFacade) {
        if (customsFacade == null) {
            return null;
        }
        return CustomsFacadeMapper.INSTANCE.toCustomsInfo(customsFacade);
    }

    /**
     * 附件列表转换
     */
    private List<Order.AttachmentInfo> toAttachmentInfos(List<AttachmentFacade> attachmentFacades) {
        if (CollectionUtils.isEmpty(attachmentFacades)) {
            return null;
        }
        return AttachmentFacadeMapper.INSTANCE.toAttachmentInfosOrder(attachmentFacades);
    }

}
