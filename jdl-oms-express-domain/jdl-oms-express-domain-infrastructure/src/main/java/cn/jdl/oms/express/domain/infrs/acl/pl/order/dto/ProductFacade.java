package cn.jdl.oms.express.domain.infrs.acl.pl.order.dto;

import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import lombok.Data;

import java.util.Map;

/**
 * @ClassName ProductFacade
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/21 9:34 下午
 * @ModifyDate 2021/3/21 9:34 下午
 * @Version 1.0
 */
@Data
public class ProductFacade {

    /**
     * 产品编码
     */
    private String productNo;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品类型
     */
    private Integer productType;
    /**
     * 产品关系
     */
    private String parentNo;
    /**
     * 降级前产品编码
     */
    private String originalProductNo;
    /**
     * 降级前产品名称
     */
    private String originalProductName;
    /**
     * 增值服务服务产品属性（产品要素属性）
     */
    private Map<String, String> productAttrs;
    /**
     * 扩展字段说明
     */
    private Map<String, String> extendProps;

    /**
     * 操作类型
     */
    private OperateTypeEnum operateType;
}
