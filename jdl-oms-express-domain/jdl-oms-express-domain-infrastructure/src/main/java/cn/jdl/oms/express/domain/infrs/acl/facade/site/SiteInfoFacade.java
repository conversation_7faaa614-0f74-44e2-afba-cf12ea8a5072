package cn.jdl.oms.express.domain.infrs.acl.facade.site;

import cn.jdl.oms.express.domain.dto.SiteInfoQueryDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortStoreVirtualSiteRelationFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.SiteInfoFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.SiteInfoFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.SiteInfoRpcTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.StaffSiteOrgFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.VirtualSiteFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.rpc.RpcResult;
import cn.jdl.oms.express.domain.infrs.acl.rpc.site.ISiteInfoService;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.ldop.alpha.lp.api.dto.output.StoreVirtualSiteRelationDTO;
import com.jd.ql.basic.dto.BaseSiteInfoDto;
import com.jd.ql.basic.dto.BaseStaffSiteOrgDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SiteInfoFacade {

    private final static Logger LOGGER = LoggerFactory.getLogger(SiteInfoFacade.class);
    
    @Resource
    ISiteInfoService iSiteInfoService;

    @Resource
    SiteInfoRpcTranslator siteInfoRpcTranslator;

    public SiteInfoFacadeResponse getBaseSiteInfoBySiteId(SiteInfoFacadeRequest siteInfoFacadeRequest) {
        SiteInfoQueryDto siteInfoQueryDto = siteInfoRpcTranslator.toSiteInfoQueryDto(siteInfoFacadeRequest);
        RpcResult<BaseSiteInfoDto> rpcResult = iSiteInfoService.getBaseSiteInfoBySiteId(siteInfoQueryDto);
        if (rpcResult.isSuccess()) {
            return siteInfoRpcTranslator.toSiteInfoFacadeResponse(rpcResult.getResult());
        } else {
            LOGGER.error("网点信息查询服务失败rpcResult={}", JSONUtils.beanToJSONDefault(rpcResult));
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORG_QUERY_FAIL)
                    .withCustom("网点信息查询服务异常");
        }
    }

    /**
     * 功能：防腐层 查询阿尔法获取虚拟站点
     * @param request
     * @return
     */
    public VirtualSiteFacadeResponse getVirtualCodeSiteByStoreId(PresortStoreVirtualSiteRelationFacadeRequest request) {
        try {
            StoreVirtualSiteRelationDTO dto = siteInfoRpcTranslator.toStoreVirtualSiteRelationFacadeRequest(request);
            RpcResult<com.jd.ldop.alpha.lp.api.dto.output.ResponseDTO<List<StoreVirtualSiteRelationDTO>>> rpcResult = iSiteInfoService.getVirtualCodeSiteByStoreId(dto);

            if (rpcResult.isSuccess() && rpcResult.getResult() != null) {
                return siteInfoRpcTranslator.toVirtualSiteFacadeResponse(rpcResult.getResult());
            } else {
                LOGGER.error("预分拣查询阿尔法获取虚拟站点失败rpcResult={}", JSONUtils.beanToJSONDefault(rpcResult));
                if (rpcResult.getResult() != null) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                        .withCustom("预分拣查询阿尔法获取虚拟站点异常")
                        .withSubCode(rpcResult.getResult().getStatusCode())
                        .withSubMessage(rpcResult.getResult().getStatusMessage());
                } else {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                        .withCustom("预分拣查询阿尔法获取虚拟站点异常");
                }
            }
        } catch (BusinessDomainException e) {
            LOGGER.error("查询阿尔法获取虚拟站点异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("查询阿尔法获取虚拟站点异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL, e).withCustom("虚拟站点校验失败");
        }
    }

    /**
     * 根据ERP获取员工及站点信息
     */
    public StaffSiteOrgFacadeResponse getBaseStaffSiteInfoByErp(String erp) {
        try {
            RpcResult<BaseStaffSiteOrgDto> rpcResult = iSiteInfoService.getBaseStaffSiteInfoByErp(erp);
            if (rpcResult.isSuccess() && rpcResult.getResult() != null) {
                return siteInfoRpcTranslator.toStaffSiteOrgFacadeResponse(rpcResult.getResult());
            } else {
                LOGGER.error("根据ERP获取员工及站点信息失败，erp={}，rpcResult={}", erp, JSONUtils.beanToJSONDefault(rpcResult));
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.QUERY_STAFF_AND_SITE_FAIL)
                        .withCustom("根据ERP获取员工及站点信息失败，erp=" + erp);
            }
        } catch (BusinessDomainException e) {
            LOGGER.error("根据ERP获取员工及站点信息异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("根据ERP获取员工及站点信息异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.QUERY_STAFF_AND_SITE_FAIL, e).withCustom("根据ERP获取员工及站点信息异常");
        }
    }
}
