package cn.jdl.oms.express.domain.infrs.acl.pl.issue;

import cn.jdl.ofc.api.oms.common.model.CreateOrderOfcResponse;
import cn.jdl.oms.core.model.ActivityInfo;
import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.AgreementInfo;
import cn.jdl.oms.core.model.AttachmentInfo;
import cn.jdl.oms.core.model.BusinessSolutionInfo;
import cn.jdl.oms.core.model.CargoInfo;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.ConsignorInfo;
import cn.jdl.oms.core.model.CostInfo;
import cn.jdl.oms.core.model.CustomerInfo;
import cn.jdl.oms.core.model.CustomsInfo;
import cn.jdl.oms.core.model.DeductionInfo;
import cn.jdl.oms.core.model.DimensionInfo;
import cn.jdl.oms.core.model.DiscountInfo;
import cn.jdl.oms.core.model.FenceInfo;
import cn.jdl.oms.core.model.FinanceDetailInfo;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.FulfillmentInfo;
import cn.jdl.oms.core.model.GoodsInfo;
import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.core.model.PointsInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.PromotionInfo;
import cn.jdl.oms.core.model.QuantityInfo;
import cn.jdl.oms.core.model.RefOrderInfo;
import cn.jdl.oms.core.model.ReturnInfo;
import cn.jdl.oms.core.model.SerialInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.core.model.TicketInfo;
import cn.jdl.oms.core.model.VolumeInfo;
import cn.jdl.oms.core.model.WarehouseInfo;
import cn.jdl.oms.core.model.WeightInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.AttachmentMapper;
import cn.jdl.oms.express.domain.converter.CustomsMapper;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.converter.SerialMapper;
import cn.jdl.oms.express.domain.converter.VolumeMapper;
import cn.jdl.oms.express.domain.converter.WeightMapper;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.FenceTrustEnum;
import cn.jdl.oms.express.domain.spec.dict.LengthTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.VolumeTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightTypeEnum;
import cn.jdl.oms.express.domain.spec.model.ICargo;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.vo.Activity;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.AgreementDelegate;
import cn.jdl.oms.express.domain.vo.Attachment;
import cn.jdl.oms.express.domain.vo.BusinessSolution;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.Dimension;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Fulfillment;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Points;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Promotion;
import cn.jdl.oms.express.domain.vo.Quantity;
import cn.jdl.oms.express.domain.vo.RefOrder;
import cn.jdl.oms.express.domain.vo.RefOrderDelegate;
import cn.jdl.oms.express.domain.vo.ReturnInfoVo;
import cn.jdl.oms.express.domain.vo.Serial;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Warehouse;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.shared.common.dict.B2BOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.LAS1stExtendOrderStatusEnum;
import cn.jdl.oms.express.shared.common.dict.LAS2ndExtendOrderStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ProjectName：jdl-oms-express
 * @Package： cn.jdl.oms.express.domain.infs.acl.pl.issue
 * @ClassName: CreateIssueFacadeTranslator
 * @Description: 接单下发防腐层参数转换
 * @Author： liyong549
 * @CreateDate 2021/3/20 11:40
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Translator
public class CreateIssueFacadeTranslator {
    /**
     * 发货
     */
    private static final String CONSIGNOR = "consignor";
    /**
     * 收货
     */
    private static final String CONSIGNEE = "consignee";

    /**
     * 接单下发请求转换
     *
     * @param context
     * @return
     * <AUTHOR>
     */
    public CreateIssueFacadeRequest toCreateIssueFacadeRequest(ExpressOrderContext context) {
        return (context == null || context.getOrderModel() == null)
                ? null
                : toCreateIssueFacadeRequest(context.getOrderModel());
    }

    /**
     * 功能：接单下发请求转换
     *
     * @param orderModel 订单中心纯配领域订单模型
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.issue.CreateIssueFacadeRequest
     * @description 原先就已经写好的函数，liufarui认为不是很合理，即拆分成两个函数，重载，不影响原有功能
     * <AUTHOR>
     * @date 2021/4/28 16:55
     */
    public CreateIssueFacadeRequest toCreateIssueFacadeRequest(ExpressOrderModel orderModel) {
        CreateIssueFacadeRequest request = new CreateIssueFacadeRequest();
        CreateIssueFacadeRequest.Data data = new CreateIssueFacadeRequest.Data();
        request.setProfile(orderModel.requestProfile());
        // 交易订单号
        data.setOrderNo(orderModel.orderNo());
        // 业务自定义单号(值是运单号)
        data.setCustomOrderNo(orderModel.getRefOrderInfoDelegate().getWaybillNo());

        // 交易单据类型
        // 纯配 - 500
        // 逆向 - 504
        // 改址 - 505
        data.setOrderType(orderModel.getOrderType() == null
                ? null
                : orderModel.getOrderType().getCode());
        // 交易单据子类型
        // C2C
        // O2O(特瞬送同城)
        data.setOrderSubType(orderModel.getOrderSubType() == null
                ? null
                : orderModel.getOrderSubType().getCode());

        // 交易客户信息
        data.setCustomerInfo(this.toCustomerInfo(orderModel.getCustomer()));
        // 渠道信息
        data.setChannelInfo(this.toChannelInfo(orderModel.getChannel()));
        // 产品信息
        data.setProductInfos(this.toProductInfoList(
                orderModel.getProductDelegate() == null
                        ? null
                        : orderModel.getProductDelegate().getProducts()));
        // 发货人信息
        data.setConsignorInfo(this.toConsignorInfo(orderModel.getConsignor()));
        // 收货人信息
        data.setConsigneeInfo(this.toConsigneeInfo(orderModel.getConsignee()));
        // 货品信息
        if (null != orderModel.getCargoDelegate()) {
            List<? extends ICargo> cargoList = orderModel.getCargoDelegate().getCargoList();
            data.setCargoInfos(this.toCargoInfoList(cargoList));
        }
        // 商品信息
        if (null != orderModel.getGoodsDelegate()) {
            List<Goods> goodsList = (List<Goods>) orderModel.getGoodsDelegate().getGoodsList();
            data.setGoodsInfos(this.toGoodsInfoList(goodsList));
        }
        // 配送要求信息
        data.setShipmentInfo(this.toShipmentInfo(orderModel));

        // 财务信息
        data.setFinanceInfo(this.toFinanceInfo(orderModel.getFinance()));
        // 营销信息
        // B2C无此项
        data.setPromotionInfo(this.toPromotionInfo(orderModel.getPromotion()));
        // 关联单信息
        data.setRefOrderInfo(this.toRefOrderInfo(orderModel.getRefOrderInfoDelegate()));
        //解决方案信息
        data.setBusinessSolutionInfo(toBusinessSolutionInfo(orderModel.getBusinessSolution()));
        // 协议信息
        data.setAgreementInfos(this.toAgreementInfos(orderModel.getAgreementDelegate()));
        // 下单人类型
        data.setInitiatorType(orderModel.getInitiatorType() != null
                ? orderModel.getInitiatorType().getCode()
                : null);
        data.setOperator(orderModel.getOperator());
        data.setRemark(orderModel.getRemark());
        data.setExtendProps(orderModel.getExtendProps());
        data.setBusinessIdentity(orderModel.toBusinessIdentity());
        // 订单标示
        data.setOrderSign(orderModel.getOrderSign());
        // 退货信息
        data.setReturnInfo(this.toReturnInfo(orderModel.getReturnInfoVo()));
        data.setFulfillmentInfo(this.toFulfillmentInfo(orderModel.getFulfillment()));
        // 跨境报关信息
        data.setCustomsInfo(this.toCustomsInfo(orderModel.getCustoms()));
        // 附件信息
        data.setAttachmentInfos(toAttachmentInfos(orderModel.getAttachments()));
        // 总毛重
        data.setOrderWeightInfo(toOrderWeightInfo(orderModel.getOrderWeight()));
        // 总净重
        data.setOrderNetWeightInfo(toOrderWeightInfo(orderModel.getOrderNetWeight()));
        // 总体积
        data.setOrderVolumeInfo(toOrderVolumeInfo(orderModel.getOrderVolume()));
        request.setData(data);
        return request;
    }

    /**
     * 异步下发履约层结果转换
     * @param issueResult
     * @return
     */
    public CreateIssueFacadeResponse toCreateIssueFacadeResponse(boolean issueResult) {
        CreateIssueFacadeResponse response = new CreateIssueFacadeResponse();
        response.setIssueResult(issueResult);
        return response;
    }

    /**
     * 同步下发履约层结果转换
     * @param response
     * @return
     */
    public CreateIssueFacadeResponse toSyncCreateIssueFacadeResponse(CreateOrderOfcResponse response) {
        return IssueMapper.INSTANCE.toCreateIssueFacadeResponse(response);
    }

    /**
     * 补齐订单下发状态
     *
     * @param orderModel
     */
    public void complementIssueStatus(ExpressOrderModel orderModel) {
        if (orderModel != null) {
            ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
            modelCreator.setOrderStatusCustom(ExpressOrderStatusCustomEnum.ISSUED.customOrderStatus());
            orderModel.complement().complementCustomStatus(this, modelCreator);
        }
    }

    /**
     * 补齐订单下发状态
     *
     * @param orderModel
     */
    public void complementB2BIssueStatus(ExpressOrderModel orderModel) {
        if (orderModel != null) {
            ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
            modelCreator.setOrderStatusCustom(B2BOrderStatusCustomEnum.ISSUED.customOrderStatus());
            orderModel.complement().complementCustomStatus(this, modelCreator);
        }
    }


    /**
     * 补齐订单下发状态
     *
     * @param orderModel
     */
    public void complementLASIssueOrderStatus(ExpressOrderModel orderModel) {
        if (orderModel != null) {
            ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
            modelCreator.setOrderStatusCustom(Integer.valueOf(LAS1stExtendOrderStatusEnum.ISSUED.getStatus()));
            modelCreator.setExecutedStatus(LAS2ndExtendOrderStatusEnum.ISSUED.getStatus());
            // 扩展状态描述
            modelCreator.setExecutedStatusDesc(LAS2ndExtendOrderStatusEnum.ISSUED.getDesc());
            orderModel.complement().complementLASOrderStatus(this, modelCreator);
        }
    }

    /**
     * 退货信息
     * @param returnInfoVo
     * @return
     */
    private ReturnInfo toReturnInfo(ReturnInfoVo returnInfoVo){
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setReturnType(returnInfoVo.getReturnType());
        returnInfo.setReturnConsigneeInfo(toConsigneeInfo(returnInfoVo.getConsignee()));
        return returnInfo;
    }

    /**
     * 接单下发客户信息转换
     *
     * @param customer
     * @return
     */
    private CustomerInfo toCustomerInfo(Customer customer) {
        CustomerInfo customerInfo = new CustomerInfo();
        if (customer != null) {
            customerInfo.setAccountNo(customer.getAccountNo());
            customerInfo.setAccountName(customer.getAccountName());
            customerInfo.setAccount2No(customer.getAccountNo2());
            customerInfo.setAccount2Name(customer.getAccountName2());
            customerInfo.setAccount3No(customer.getAccountNo3());
            customerInfo.setAccount3Name(customer.getAccountName3());
            customerInfo.setCustomerType(customer.getCustomerType());
        }
        return customerInfo;
    }

    /**
     * 接单下发渠道信息转换
     *
     * @param channel
     * @return
     */
    private ChannelInfo toChannelInfo(Channel channel) {
        ChannelInfo channelInfo = new ChannelInfo();
        if (channel != null) {
            channelInfo.setChannelNo(channel.getChannelNo());
            channelInfo.setChannelCustomerNo(channel.getChannelCustomerNo());
            channelInfo.setChannelOrderNo(channel.getChannelOrderNo());
            channelInfo.setCustomerOrderNo(channel.getCustomerOrderNo());
            channelInfo.setChannelOperateTime(channel.getChannelOperateTime());
            channelInfo.setSecondLevelChannel(channel.getSecondLevelChannel());
            channelInfo.setSecondLevelChannelOrderNo(channel.getSecondLevelChannelOrderNo());
            channelInfo.setSecondLevelChannelCustomerNo(channel.getSecondLevelChannelCustomerNo());
            channelInfo.setThirdLevelChannelOrderNo(channel.getThirdLevelChannelOrderNo());
            channelInfo.setSystemCaller(channel.getSystemCaller().getCode());
            channelInfo.setSystemSubCaller(channel.getSystemSubCaller());

            // 业务订单来源
            // 这个地方暂时只有waybillChannel字段在扩展字段中
            channelInfo.setExtendProps(channel.getExtendProps());
        }
        return channelInfo;
    }

    /**
     * 商品信息
     *
     * @param goodsList
     * @return
     */
    private List<GoodsInfo> toGoodsInfoList(List<Goods> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }
        List<GoodsInfo> goodsInfos = new ArrayList<>(goodsList.size());
        goodsList.forEach(goods -> {
            GoodsInfo goodsInfo = new GoodsInfo();
            goodsInfo.setGoodsUniqueCode(goods.getGoodsUniqueCode());
            goodsInfo.setGoodsNo(goods.getGoodsNo());
            goodsInfo.setChannelGoodsNo(goods.getChannelGoodsNo());
            goodsInfo.setGoodsName(goods.getGoodsName());
            Optional.ofNullable(goods.getGoodsAmount()).ifPresent(goodsAmount -> {
                MoneyInfo money = new MoneyInfo();
                money.setAmount(goodsAmount.getAmount());
                if (goodsAmount.getCurrency() != null) {
                    money.setCurrencyCode(goodsAmount.getCurrency().getCode());
                }
                goodsInfo.setGoodsAmount(money);
            });
            Optional.ofNullable(goods.getGoodsPrice()).ifPresent(goodsPrice -> {
                MoneyInfo money = new MoneyInfo();
                money.setAmount(goodsPrice.getAmount());
                if (goodsPrice.getCurrency() != null) {
                    money.setCurrencyCode(goodsPrice.getCurrency().getCode());
                }
                goodsInfo.setGoodsPrice(money);
            });
            goodsInfo.setGoodsType(goods.getGoodsType());
            goodsInfo.setCombinationGoodsVersion(goods.getCombinationGoodsVersion());
            goodsInfo.setExtendProps(goods.getExtendProps());
            //货品数量
            Optional.ofNullable(goods.getGoodsQuantity()).ifPresent(goodsQuantity -> {
                QuantityInfo quantity = new QuantityInfo();
                quantity.setValue(goodsQuantity.getValue());
                quantity.setUnit(goodsQuantity.getUnit());
                goodsInfo.setGoodsQuantity(quantity);
            });

            //货品重量
            Optional.ofNullable(goods.getGoodsWeight()).ifPresent(goodsWeight -> {
                WeightInfo weightInfo = new WeightInfo();
                weightInfo.setValue(goodsWeight.getValue());
                if (null != goodsWeight.getUnit()) {
                    weightInfo.setUnit(goodsWeight.getUnit().getCode());
                }
                goodsInfo.setGoodsWeight(weightInfo);
            });

            //货品体积
            Optional.ofNullable(goods.getGoodsVolume()).ifPresent(goodsVolume -> {
                VolumeInfo volumeInfo = new VolumeInfo();
                volumeInfo.setValue(goodsVolume.getValue());
                if (null != goodsVolume.getUnit()) {
                    volumeInfo.setUnit(goodsVolume.getUnit().getCode());
                }
                goodsInfo.setGoodsVolume(volumeInfo);
            });

            //货品三维
            Optional.ofNullable(goods.getGoodsDimension()).ifPresent(goodsDimension -> {
                DimensionInfo dimensionInfo = new DimensionInfo();
                dimensionInfo.setHeight(goodsDimension.getHeight());
                dimensionInfo.setLength(goodsDimension.getLength());
                dimensionInfo.setWidth(goodsDimension.getWidth());
                if (null != goodsDimension.getUnit()) {
                    dimensionInfo.setUnit(goodsDimension.getUnit().getCode());
                }
                goodsInfo.setGoodsDimension(dimensionInfo);
            });

            //促销信息
            Optional.ofNullable(goods.getSalesInfos()).ifPresent(salesInfos -> {
                goodsInfo.setSalesInfos(salesInfos);
            });
            // 商品序列号信息
            goodsInfo.setGoodsSerialInfos(
                    SerialMapper.INSTANCE.toSerialInfoList(goods.getGoodsSerialInfos())
            );
            // 商品纬度增值服务
            Optional.ofNullable(goods.getGoodsProductInfos()).ifPresent(productList -> {
                List<ProductInfo> productInfoList = new ArrayList<>(productList.size());
                productList.forEach(product -> {
                    ProductInfo productInfo = new ProductInfo();
                    productInfo.setProductNo(product.getProductNo());
                    productInfo.setProductName(product.getProductName());
                    productInfo.setExtendProps(product.getExtendProps());
                    productInfo.setProductAttrs(product.getProductAttrs());
                    productInfo.setProductType(product.getProductType());
                    productInfo.setParentNo(product.getParentNo());
                    productInfoList.add(productInfo);
                });
                goodsInfo.setGoodsProductInfos(productInfoList);
            });
            //附件信息
            Optional.ofNullable(goods.getAttachments()).ifPresent(attachmentInfos -> {
                //遍历附件
                List<AttachmentInfo> attachmentInfoList = attachmentInfos.stream().map(attachment -> {
                    AttachmentInfo attachmentInfo = new AttachmentInfo();
                    attachmentInfo.setAttachmentSortNo(attachment.getAttachmentSortNo());
                    attachmentInfo.setAttachmentName(attachment.getAttachmentName());
                    attachmentInfo.setAttachmentType(attachment.getAttachmentType());
                    attachmentInfo.setAttachmentDocType(attachment.getAttachmentDocType());
                    attachmentInfo.setAttachmentUrl(attachment.getAttachmentUrl());
                    attachmentInfo.setAttachmentRemark(attachment.getAttachmentRemark());
                    return attachmentInfo;
                }).collect(Collectors.toList());
                //附件集
                goodsInfo.setGoodsAttachmentInfos(attachmentInfoList);
            });

            //商品净重
            Optional.ofNullable(goods.getNetWeight()).ifPresent(netWeight -> {
                WeightInfo weightInfo = new WeightInfo();
                weightInfo.setValue(netWeight.getValue());
                if (null != netWeight.getUnit()) {
                    weightInfo.setUnit(netWeight.getUnit().getCode());
                }
                goodsInfo.setNetWeight(weightInfo);
            });

            goodsInfos.add(goodsInfo);

        });
        return goodsInfos;
    }

    /**
     * 接单下发产品信息转换
     *
     * @param products 产品集合
     * @return List<ProductInfo>
     */
    private List<ProductInfo> toProductInfoList(List<? extends IProduct> products) {
        if (CollectionUtils.isEmpty(products)) {
            return Collections.emptyList();
        }
        List<Product> productList = (List<Product>) products;
        return productList.stream().map(product -> {
            ProductInfo productInfo = new ProductInfo();
            productInfo.setProductNo(product.getProductNo());
            productInfo.setProductName(product.getProductName());
            productInfo.setProductType(product.getProductType());
            productInfo.setParentNo(product.getParentNo());
            productInfo.setProductAttrs(product.getProductAttrs());
            productInfo.setExtendProps(product.getExtendProps());
            return productInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 接单下发发货人信息转换
     *
     * @param consignor
     * @return
     */
    private ConsignorInfo toConsignorInfo(Consignor consignor) {
        ConsignorInfo consignorInfo = new ConsignorInfo();
        if (consignor != null) {
            consignorInfo.setConsignorName(consignor.getConsignorName());
            consignorInfo.setConsignorMobile(consignor.getConsignorMobile());
            consignorInfo.setConsignorPhone(consignor.getConsignorPhone());
            consignorInfo.setConsignorZipCode(consignor.getConsignorZipCode());
            consignorInfo.setConsignorCompany(consignor.getConsignorCompany());
            consignorInfo.setConsignorNationNo(consignor.getConsignorNationNo());
            consignorInfo.setConsignorNation(consignor.getConsignorNation());
            // 身份证 - 1
            // 护照 - 2
            // 户口簿 - 3
            consignorInfo.setConsignorIdType(consignor.getConsignorIdType() == null
                    ? null
                    : consignor.getConsignorIdType().getCode());
            consignorInfo.setConsignorIdNo(consignor.getConsignorIdNo());
            consignorInfo.setConsignorIdName(consignor.getConsignorIdName());
            consignorInfo.setAddressInfo(this.toAddressInfo(consignor.getAddress(), CONSIGNOR));
            consignorInfo.setCustomerWarehouse(this.toWarehouseInfo(consignor.getCustomerWarehouse()));
            consignorInfo.setPickupPlaceCode(consignor.getPickupPlaceCode());
            consignorInfo.setConsignorAddressAbbreviation(consignor.getConsignorAddressAbbreviation());
            //扩展信息
            consignorInfo.setExtendProps(consignor.getExtendProps());
            //英文发货人姓名
            consignorInfo.setConsignorEnName(consignor.getConsignorEnName());
        }
        return consignorInfo;
    }

    /**
     * 接单下发收货人信息转换
     *
     * @param consignee
     * @return
     */
    private ConsigneeInfo toConsigneeInfo(Consignee consignee) {
        ConsigneeInfo consigneeInfo = new ConsigneeInfo();
        if (null != consignee) {
            consigneeInfo.setConsigneeName(consignee.getConsigneeName());
            consigneeInfo.setConsigneeMobile(consignee.getConsigneeMobile());
            consigneeInfo.setConsigneePhone(consignee.getConsigneePhone());
            consigneeInfo.setConsigneeZipCode(consignee.getConsigneeZipCode());
            consigneeInfo.setConsigneeEmail(consignee.getConsigneeEmail());
            consigneeInfo.setConsigneeCompany(consignee.getConsigneeCompany());
            consigneeInfo.setConsigneeNationNo(consignee.getConsigneeNationNo());
            consigneeInfo.setConsigneeNation(consignee.getConsigneeNation());
            if (consignee.getConsigneeIdType() != null && consignee.getConsigneeIdType().getCode() != null) {
                consigneeInfo.setConsigneeIdType(consignee.getConsigneeIdType().getCode());
            }
            consigneeInfo.setConsigneeIdNo(consignee.getConsigneeIdNo());
            consigneeInfo.setConsigneeIdName(consignee.getConsigneeIdName());
            consigneeInfo.setAddressInfo(this.toAddressInfo(consignee.getAddress(), CONSIGNEE));
            consigneeInfo.setDeliveryPlaceCode(consignee.getDeliveryPlaceCode());
            consigneeInfo.setReceiveWarehouse(this.toWarehouseInfo(consignee.getReceiveWarehouse()));
            consigneeInfo.setConsigneeAddressAbbreviation(consignee.getConsigneeAddressAbbreviation());
            if (consignee.getExtendProps() != null) {
                consigneeInfo.setExtendProps(consignee.getExtendProps());
            }
        }
        return consigneeInfo;
    }


    /**
     * 下发仓库信息
     *
     * @param warehouse
     * @return
     */
    private WarehouseInfo toWarehouseInfo(Warehouse warehouse) {
        WarehouseInfo warehouseInfo = new WarehouseInfo();
        if (null != warehouse) {
            warehouseInfo.setWarehouseNo(warehouse.getWarehouseNo());
            warehouseInfo.setWarehouseName(warehouse.getWarehouseName());
            warehouseInfo.setWarehouseSource(warehouse.getWarehouseSource());
            // 京东仓库编码
            warehouseInfo.setActualWarehouseNo(warehouse.getActualWarehouseNo());
        }
        return warehouseInfo;
    }

    /**
     * 接单下发收货人地址信息转换
     *
     * @param address
     * @return
     */
    private AddressInfo toAddressInfo(Address address, String consigneeOrConsignor) {
        AddressInfo addressInfo = new AddressInfo();
        if (null != address) {
            addressInfo.setAddress(address.getAddress());
            addressInfo.setProvinceNo(address.getProvinceNo());
            addressInfo.setProvinceName(address.getProvinceName());
            addressInfo.setCityNo(address.getCityNo());
            addressInfo.setCityName(address.getCityName());
            addressInfo.setCountyNo(address.getCountyNo());
            addressInfo.setCountyName(address.getCountyName());
            addressInfo.setTownNo(address.getTownNo());
            addressInfo.setTownName(address.getTownName());
            addressInfo.setCoordinateType(address.getCoordinateType() == null
                    ? null
                    : address.getCoordinateType().getCode());
            addressInfo.setLongitude(address.getLongitude());
            addressInfo.setLatitude(address.getLatitude());
            addressInfo.setProvinceNoGis(address.getProvinceNoGis());
            addressInfo.setProvinceNameGis(address.getProvinceNameGis());
            addressInfo.setCityNoGis(address.getCityNoGis());
            addressInfo.setCityNameGis(address.getCityNameGis());
            addressInfo.setCountyNoGis(address.getCountyNoGis());
            addressInfo.setCountyNameGis(address.getCountyNameGis());
            addressInfo.setTownNoGis(address.getTownNoGis());
            addressInfo.setTownNameGis(address.getTownNameGis());
            addressInfo.setAddressGis(address.getAddressGis());
            addressInfo.setPreciseGis(address.getPreciseGis());
            addressInfo.setChinaPostAddressCode(address.getChinaPostAddressCode());
            //目前只有收货地址下发嵌套地址级别
            if (StringUtils.isNotBlank(consigneeOrConsignor) && CONSIGNEE.equals(consigneeOrConsignor)) {
                addressInfo.setConflictLevel(address.getConflictLevel());
            }
            addressInfo.setAddressSource(address.getAddressSource());
            // 围栏信任标识
            addressInfo.setFenceTrusted(address.getFenceTrusted());
            // 围栏信息 目前只有传信任标识才会传入围栏信息
            if (FenceTrustEnum.TRUSTED.getCode().equals(address.getFenceTrusted())) {
                Optional.ofNullable(address.getFenceInfos()).ifPresent(fenceInfos -> {
                    List<FenceInfo> fenceInfoList = new ArrayList<>(fenceInfos.size());
                    fenceInfos.forEach(fenceInfo -> {
                        FenceInfo fence = new FenceInfo();
                        fence.setFenceId(fenceInfo.getFenceId());
                        fence.setFenceType(fenceInfo.getFenceType());
                        fenceInfoList.add(fence);
                    });
                    addressInfo.setFenceInfos(fenceInfoList);
                });
            }
            // 行政区编码
            addressInfo.setRegionNo(address.getRegionNo());
            // 行政区名称
            addressInfo.setRegionName(address.getRegionName());
            // 英文城市
            addressInfo.setEnCityName(address.getEnCityName());
            // 英文地址
            addressInfo.setEnAddress(address.getEnAddress());
        }
        return addressInfo;
    }

    /**
     * 接单货品信息转换
     *
     * @param cargoList
     * @return
     */
    private List<CargoInfo> toCargoInfoList(List<? extends ICargo> cargoList) {
        if (CollectionUtils.isEmpty(cargoList)) {
            return Collections.emptyList();
        }
        return cargoList.stream().map(iCargo -> {
            Cargo cargo = (Cargo) iCargo;
            CargoInfo cargoInfo = new CargoInfo();
            cargoInfo.setCargoName(cargo.getCargoName());
            cargoInfo.setCargoNo(cargo.getCargoNo());
            cargoInfo.setCargoType(cargo.getCargoType());
            cargoInfo.setCargoVolume(this.toVolumeInfo(cargo.getCargoVolume()));
            cargoInfo.setCargoWeight(this.toWeightInfo(cargo.getCargoWeight()));

            // TODO 后期数量可能要统一用Quantity对象，后期再改
            // Quantity innerQuantityInfo = new Quantity();
            QuantityInfo innerQuantityInfo = new QuantityInfo();
            innerQuantityInfo.setValue(cargo.getCargoInnerQuantity());
            cargoInfo.setCargoInnerQuantity(innerQuantityInfo);
            cargoInfo.setSerialInfos(SerialMapper.INSTANCE.toSerialInfoList(cargo.getSerialInfos()));
            cargoInfo.setCargoQuantity(toCargoQuantityInfo(cargo.getCargoQuantity()));
            cargoInfo.setCargoDimension(this.toDimensionInfo(cargo.getCargoDimension()));
            cargoInfo.setCargoRemark(cargo.getCargoRemark());
            Optional.ofNullable(cargo.getPolluteSign()).ifPresent(polluteSignEnum -> {
                cargoInfo.setPolluteSign(polluteSignEnum.getCode());
            });
            cargoInfo.setCargoAttachmentInfos(this.toAttachmentInfoList(cargo.getAttachments()));
            //是否易损
            if (cargo.getCargoVulnerable() != null) {
                cargoInfo.setCargoVulnerable(cargo.getCargoVulnerable().getCode());
            }
            //货品标识
            cargoInfo.setCargoSign(cargo.getCargoSign());
            cargoInfo.setExtendProps(cargo.getExtendProps());
            //货品信息增值服务
            Optional.ofNullable(cargo.getCargoProductInfos()).ifPresent(productList -> {
                List<ProductInfo> productInfoList = new ArrayList<>(productList.size());
                productList.forEach(product -> {
                    ProductInfo productInfo = new ProductInfo();
                    productInfo.setProductNo(product.getProductNo());
                    productInfo.setProductName(product.getProductName());
                    productInfo.setExtendProps(product.getExtendProps());
                    productInfo.setProductAttrs(product.getProductAttrs());
                    productInfo.setProductType(product.getProductType());
                    productInfo.setParentNo(product.getParentNo());
                    productInfoList.add(productInfo);
                });
                cargoInfo.setProductInfos(productInfoList);
            });

            //隐私货品展示信息
            cargoInfo.setPrivacyCargoName(cargo.getPrivacyCargoName());
            return cargoInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 货物数量转换
     *
     * @param quantity
     * @return
     */
    private QuantityInfo toCargoQuantityInfo(Quantity quantity) {
        if (quantity == null) {
            return null;
        }
        QuantityInfo quantityInfo = new QuantityInfo();
        quantityInfo.setUnit(quantity.getUnit());
        quantityInfo.setValue(quantity.getValue());
        return quantityInfo;
    }

    /**
     * 功能：接单商品信息转换 - 商品数量
     *
     * @param quantity 上下文商品数量信息
     * @return cn.jdl.oms.core.model.QuantityInfo
     * @description
     * <AUTHOR>
     * @date 2021/4/28 16:45
     */
    private QuantityInfo toQuantityInfo(Quantity quantity) {
        QuantityInfo quantityInfo = new QuantityInfo();
        quantityInfo.setValue(quantity.getValue());
        quantityInfo.setUnit(quantity.getUnit());
        return quantityInfo;
    }

    /**
     * 功能：接单商品信息转换 - 序列号信息
     *
     * @param serials 上下文商品序列号信息
     * @return List<cn.jdl.oms.core.model.SerialInfo>
     * @description
     * <AUTHOR>
     * @date 2021/4/28 16:45
     */
    private List<SerialInfo> toSerialInfoList(List<Serial> serials) {
        if (CollectionUtils.isEmpty(serials)) {
            return Collections.emptyList();
        }

        return serials.stream().map(serial -> {
            SerialInfo serialInfo = new SerialInfo();
            serialInfo.setSerialNo(serial.getSerialNo());
            return serialInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 附件信息转换
     *
     * @param attachments
     * @return
     */
    private List<AttachmentInfo> toAttachmentInfoList(List<Attachment> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            return Collections.emptyList();
        }
        return attachments.stream().map(attachment -> {
            AttachmentInfo attachmentInfo = new AttachmentInfo();
            attachmentInfo.setAttachmentSortNo(attachment.getAttachmentSortNo());
            attachmentInfo.setAttachmentName(attachment.getAttachmentName());
            attachmentInfo.setAttachmentType(attachment.getAttachmentType());
            attachmentInfo.setAttachmentDocType(attachment.getAttachmentDocType());
            attachmentInfo.setAttachmentUrl(attachment.getAttachmentUrl());
            attachmentInfo.setAttachmentRemark(attachment.getAttachmentRemark());
            return attachmentInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 接单下发体积信息转换
     *
     * @param volume
     * @return
     */
    private VolumeInfo toVolumeInfo(Volume volume) {//TODO 待办 需要照哥统一处理
        VolumeInfo volumeInfo = new VolumeInfo();
        if (volume != null) {
            volumeInfo.setUnit(volume.getUnit() != null ? volume.getUnit().getCode()
                    : VolumeTypeEnum.CM3.getCode());//为空默认立方厘米
            volumeInfo.setValue(volume.getValue());
        }
        return volumeInfo;
    }

    /**
     * 接单下发重量信息转换
     *
     * @param weight
     * @return
     */
    private WeightInfo toWeightInfo(Weight weight) {
        WeightInfo weightInfo = new WeightInfo();
        if (weight != null) {
            weightInfo.setUnit(weight.getUnit() != null ? weight.getUnit().getCode()
                    : WeightTypeEnum.KG.getCode());
            weightInfo.setValue(weight.getValue());
        }
        return weightInfo;
    }

    /**
     * 接单下发长宽高聚合信息转换
     *
     * @param dimension
     * @return
     */
    private DimensionInfo toDimensionInfo(Dimension dimension) {
        DimensionInfo dimensionInfo = new DimensionInfo();
        if (dimension != null) {
            dimensionInfo.setLength(dimension.getLength());
            dimensionInfo.setWidth(dimension.getWidth());
            dimensionInfo.setHeight(dimension.getHeight());
            dimensionInfo.setUnit(dimension.getUnit() != null ? dimension.getUnit().getCode()
                    : LengthTypeEnum.CM.getCode());
        }
        return dimensionInfo;
    }

    /**
     * 接单下发配送要求信息转换
     *
     * @param orderModel
     * @return
     */
    private ShipmentInfo toShipmentInfo(ExpressOrderModel orderModel) {
        Shipment shipment = orderModel.getShipment();
        ShipmentInfo shipmentInfo = new ShipmentInfo();
        if (shipment != null) {
            shipmentInfo.setPlanDeliveryTime(shipment.getPlanDeliveryTime());
            shipmentInfo.setPlanDeliveryPeriod(shipment.getPlanDeliveryPeriod());
            shipmentInfo.setExpectDeliveryStartTime(shipment.getExpectDeliveryStartTime());
            shipmentInfo.setExpectDeliveryEndTime(shipment.getExpectDeliveryEndTime());
            shipmentInfo.setExpectPickupStartTime(shipment.getExpectPickupStartTime());
            shipmentInfo.setExpectPickupEndTime(shipment.getExpectPickupEndTime());
            shipmentInfo.setPickupType(shipment.getPickupType() != null
                    ? shipment.getPickupType().getCode()
                    : null);
            shipmentInfo.setDeliveryType(shipment.getDeliveryType() != null
                    ? shipment.getDeliveryType().getCode()
                    : null);
            shipmentInfo.setTransportType(shipment.getTransportType() != null
                    ? shipment.getTransportType().getCode()
                    : null);
            shipmentInfo.setCollector(shipment.getCollector());
            shipmentInfo.setVehicleType(shipment.getVehicleType());
            if (shipment.getWarmLayer() != null) {
                shipmentInfo.setWarmLayer(shipment.getWarmLayer().getCode());
            }

            if (orderModel.getProductDelegate() != null
                    && ProductEnum.JDBS.getCode().equals(orderModel.getProductDelegate().getMajorProductNo())) {
                // 帮送订单 不下发揽收站点
                shipmentInfo.setStartStationNo(null);
                shipmentInfo.setStartStationName(null);
                shipmentInfo.setStartStationType(null);
            } else {
                shipmentInfo.setStartStationNo(shipment.getStartStationNo());
                shipmentInfo.setStartStationName(shipment.getStartStationName());
                shipmentInfo.setStartStationType(shipment.getStartStationType());
            }

            shipmentInfo.setEndStationNo(shipment.getEndStationNo());
            shipmentInfo.setEndStationName(shipment.getEndStationName());
            shipmentInfo.setEndStationType(shipment.getEndStationType());
            shipmentInfo.setTransitType(shipment.getTransitType());
            shipmentInfo.setWarehouseNo(shipment.getWarehouseNo());
            shipmentInfo.setReceiveWarehouseNo(shipment.getReceiveWarehouseNo());
            shipmentInfo.setPlanReceiveTime(shipment.getPlanReceiveTime());
            shipmentInfo.setContactlessType(shipment.getContactlessType() != null ? shipment.getContactlessType().getCode()
                    : null);
            shipmentInfo.setAssignedAddress(shipment.getAssignedAddress());
            shipmentInfo.setPickupCode(shipment.getPickupCode());
            shipmentInfo.setPickupCodeCreateType(shipment.getPickupCodeCreateType() == null ? null : shipment.getPickupCodeCreateType().getCode());
            shipmentInfo.setServiceRequirements(shipment.getServiceRequirements());
            shipmentInfo.setExtendProps(shipment.getExtendProps());
            //收货偏好
            shipmentInfo.setReceivingPreference(shipment.getReceivingPreference());
            // 始发配送中心编码
            shipmentInfo.setStartCenterNo(shipment.getStartCenterNo());
            // 目的配送中心编码
            shipmentInfo.setEndCenterNo(shipment.getEndCenterNo());
            // 承运商编码
            shipmentInfo.setShipperNo(shipment.getShipperNo());
            // 承运商名称
            shipmentInfo.setShipperName(shipment.getShipperName());
            // 承运商类型
            shipmentInfo.setShipperType(shipment.getShipperType());
            // 接驳点
            shipmentInfo.setEndTransferStationNo(shipment.getEndTransferStationNo());
            shipmentInfo.setExpectDispatchStartTime(shipment.getExpectDispatchStartTime());
            shipmentInfo.setExpectDispatchEndTime(shipment.getExpectDispatchEndTime());
            shipmentInfo.setStartStationTypeL3(shipment.getStartStationTypeL3());
            shipmentInfo.setEndStationTypeL3(shipment.getEndStationTypeL3());
        }
        return shipmentInfo;
    }

    /**
     * 接单下发财务信息转换
     *
     * @param finance
     * @return
     */
    private FinanceInfo toFinanceInfo(Finance finance) {
        FinanceInfo financeInfo = new FinanceInfo();
        if (finance != null) {
            financeInfo.setSettlementType(finance.getSettlementType().getCode());
            //只有月结才下发结算账号
            if (SettlementTypeEnum.MONTHLY_PAYMENT.getCode().equals(finance.getSettlementType().getCode())) {
                financeInfo.setSettlementAccountNo(finance.getSettlementAccountNo());
            }
            Optional.ofNullable(finance.getEnquiryType()).ifPresent(enquiryTypeEnum -> {
                financeInfo.setEnquiryType(enquiryTypeEnum.getCode());
            });
            financeInfo.setEstimateAmount(toMoneyInfo(finance.getEstimateAmount()));
            financeInfo.setPaymentAccountNo(finance.getPaymentAccountNo());
            financeInfo.setPayment(finance.getPayment() == null
                    ? null
                    : finance.getPayment().getCode());
            financeInfo.setPreemptType(finance.getPreemptType());
            financeInfo.setPaymentStage(finance.getPaymentStage() != null
                    ? finance.getPaymentStage().getCode()
                    : null);
            financeInfo.setPreAmount(toMoneyInfo(finance.getPreAmount()));
            financeInfo.setDiscountAmount(toMoneyInfo(finance.getDiscountAmount()));
            financeInfo.setTotalDiscountAmount(toMoneyInfo(finance.getTotalDiscountAmount()));
            financeInfo.setBillingType(finance.getBillingType());
            financeInfo.setBillingWeight(toWeightInfo(finance.getBillingWeight()));
            financeInfo.setBillingVolume(toVolumeInfo(finance.getBillingVolume()));
            financeInfo.setBillingMode(finance.getBillingMode());
            financeInfo.setCollectionOrgNo(finance.getCollectionOrgNo());
            financeInfo.setFinanceDetailInfos(this.toFinanceDetailInfoList(finance.getFinanceDetails()));
            financeInfo.setPaymentTime(finance.getPaymentTime());
            financeInfo.setPaymentStatus(finance.getPaymentStatus() != null ? finance.getPaymentStatus().getStatus() : null);
            financeInfo.setPaymentNo(finance.getPaymentNo());
            financeInfo.setPayDeadline(finance.getPayDeadline());
            financeInfo.setPointsInfo(toPointsInfo(finance.getPoints()));
            //抵扣信息
            financeInfo.setDeductionInfos(toDeductionInfos((List<Deduction>) finance.getDeductionDelegate().getDeductions()));
            //收费信息
            Optional.ofNullable(finance.getCostInfos()).ifPresent(costInfoList -> {
                List<CostInfo> costInfos = new ArrayList<>(costInfoList.size());
                costInfoList.forEach(cost -> {
                    if (cost != null) {
                        costInfos.add(toCostInfo(cost));
                    }
                });
                financeInfo.setCostInfos(costInfos);
            });
            //附加费用
            Optional.ofNullable(finance.getAttachFees()).ifPresent(attachFees -> {
                List<CostInfo> attachFeesList = new ArrayList<>(attachFees.size());
                attachFees.forEach(cost -> {
                    if (cost != null) {
                        attachFeesList.add(toCostInfo(cost));
                    }
                });
                financeInfo.setAttachFees(attachFeesList);
            });
            //预估税金
            financeInfo.setEstimatedTax(MoneyMapper.INSTANCE.toMoneyInfo(finance.getEstimatedTax()));
            //真实税金
            financeInfo.setActualTax(MoneyMapper.INSTANCE.toMoneyInfo(finance.getActualTax()));
            //扩展属性
            financeInfo.setExtendProps(finance.getExtendProps());
            //费用支付状态归集
            financeInfo.setPayStatusMap(finance.getPayStatusMap());
            //税金结算方式
            financeInfo.setTaxSettlementType(finance.getTaxSettlementType());
        }
        return financeInfo;
    }

    /**
     * vo转对外对象
     *
     * @param vo
     * @return
     */
    private CostInfo toCostInfo(cn.jdl.oms.express.domain.vo.CostInfo vo) {
        CostInfo costInfo = new CostInfo();
        costInfo.setCostNo(vo.getCostNo());
        costInfo.setCostName(vo.getCostName());
        costInfo.setChargingSource(vo.getChargingSource());
        costInfo.setSettlementAccountNo(vo.getSettlementAccountNo());
        costInfo.setExtendProps(vo.getExtendProps());
        return costInfo;
    }
    /**
     * 积分信息
     *
     * @param points
     * @return
     */
    private PointsInfo toPointsInfo(Points points) {
        if (points == null) {
            return null;
        }
        PointsInfo pointsInfo = new PointsInfo();
        if (points.getRedeemPointsQuantity() != null) {
            QuantityInfo redeemPointsQuantity = new QuantityInfo();
            redeemPointsQuantity.setUnit(points.getRedeemPointsQuantity().getUnit());
            redeemPointsQuantity.setValue(points.getRedeemPointsQuantity().getValue());
            pointsInfo.setRedeemPointsQuantity(redeemPointsQuantity);
        }
        if (points.getRedeemPointsAmount() != null) {
            MoneyInfo redeemPointsAmount = new MoneyInfo();
            redeemPointsAmount.setAmount(points.getRedeemPointsAmount().getAmount());
            redeemPointsAmount.setCurrencyCode(points.getRedeemPointsAmount().getCurrency() != null ? points.getRedeemPointsAmount().getCurrency().getCode() : null);
            pointsInfo.setRedeemPointsAmount(redeemPointsAmount);
        }
        return pointsInfo;
    }

    /**
     * 接单下发金额转换
     *
     * @param money
     * @return
     */
    private MoneyInfo toMoneyInfo(Money money) {
        MoneyInfo moneyInfo = new MoneyInfo();
        if (money != null) {
            moneyInfo.setAmount(money.getAmount());
            if (money.getCurrency() != null) {
                moneyInfo.setCurrencyCode(money.getCurrency().getCode());
            }
        }
        return moneyInfo;
    }

    /**
     * 接单下发财务费用明细转换
     *
     * @param financeDetails
     * @return
     */
    private List<FinanceDetailInfo> toFinanceDetailInfoList(List<FinanceDetail> financeDetails) {
        if (CollectionUtils.isEmpty(financeDetails)) {
            return Collections.emptyList();
        }
        return financeDetails.stream().map(financeDetail -> {
            FinanceDetailInfo financeDetailInfo = new FinanceDetailInfo();
            financeDetailInfo.setCostNo(financeDetail.getCostNo());
            financeDetailInfo.setCostName(financeDetail.getCostName());
            financeDetailInfo.setProductNo(financeDetail.getProductNo());
            //产品名称
            financeDetailInfo.setProductName(financeDetail.getProductName());

            if (CollectionUtils.isNotEmpty(financeDetail.getDiscounts())) {
                List<DiscountInfo> discountInfos = new ArrayList<>(financeDetail.getDiscounts().size());
                financeDetail.getDiscounts().forEach(discount -> {
                    DiscountInfo discountInfo = new DiscountInfo();
                    discountInfo.setDiscountNo(discount.getDiscountNo());
                    discountInfo.setDiscountType(discount.getDiscountType());
                    if (discount.getDiscountedAmount() != null) {
                        MoneyInfo moneyInfo = new MoneyInfo();
                        moneyInfo.setAmount(discount.getDiscountedAmount().getAmount());
                        if (discount.getDiscountedAmount().getCurrency() != null) {
                            moneyInfo.setCurrencyCode(discount.getDiscountedAmount().getCurrency().getCode());
                        }
                        discountInfo.setDiscountedAmount(moneyInfo);
                        //扩展信息
                        discountInfo.setExtendProps(discount.getExtendProps());
                    }
                    discountInfos.add(discountInfo);
                });
                financeDetailInfo.setDiscountInfos(discountInfos);
            }

            financeDetailInfo.setPreAmount(toMoneyInfo(financeDetail.getPreAmount()));
            financeDetailInfo.setDiscountAmount(toMoneyInfo(financeDetail.getDiscountAmount()));
            return financeDetailInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 接单下发营销信息转换
     *
     * @param promotion
     * @return
     */
    private PromotionInfo toPromotionInfo(Promotion promotion) {
        PromotionInfo promotionInfo = new PromotionInfo();
        if (promotion != null) {
            promotionInfo.setActivityInfos(this.toActivityInfoList(promotion.getActivities()));
            promotionInfo.setDiscountRefOrderNo(promotion.getDiscountRefOrderNo());
            promotionInfo.setTicketInfos(this.toTicketInfoList(promotion.getTickets()));
            promotionInfo.setDiscountInfos(this.toDiscountInfoList(promotion.getDiscounts()));
        }
        return promotionInfo;
    }

    /**
     * 促销信息转换
     *
     * @param activities
     * @return
     */
    private List<ActivityInfo> toActivityInfoList(List<Activity> activities) {
        if (CollectionUtils.isEmpty(activities)) {
            return Collections.emptyList();
        }
        return activities.stream().map(activity -> {
            ActivityInfo activityInfo = new ActivityInfo();
            activityInfo.setActivityNo(activity.getActivityNo());
            activityInfo.setActivityName(activity.getActivityName());
            activityInfo.setActivityStatus(activity.getActivityStatus());
            activityInfo.setActivityValue(activity.getActivityValue());
            return activityInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 接单下发优惠劵信息转换
     *
     * @return
     */
    private List<TicketInfo> toTicketInfoList(List<Ticket> ticketList) {
        if (CollectionUtils.isEmpty(ticketList)) {
            return Collections.emptyList();
        }
        return ticketList.stream().map(ticket -> {
            TicketInfo ticketInfo = new TicketInfo();
            ticketInfo.setTicketNo(ticket.getTicketNo());
            ticketInfo.setTicketCategory(ticket.getTicketCategory());
            ticketInfo.setTicketType(ticket.getTicketType());
            ticketInfo.setTicketDiscountAmount(this.toMoneyInfo(ticket.getTicketDiscountAmount()));
            ticketInfo.setTicketDiscountRate(ticket.getTicketDiscountRate());
            ticketInfo.setTicketDiscountUpperLimit(this.toMoneyInfo(
                    ticket.getTicketDiscountUpperLimit()));
            ticketInfo.setTicketDescription(ticket.getTicketDescription());
            ticketInfo.setTicketUseAmount(this.toMoneyInfo(ticket.getTicketUseAmount()));
            ticketInfo.setTicketSource(ticket.getTicketSource());
            return ticketInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 接单下发折扣卷信息转换
     *
     * @param discountList
     * @return
     */
    private List<DiscountInfo> toDiscountInfoList(List<Discount> discountList) {
        if (CollectionUtils.isEmpty(discountList)) {
            return Collections.emptyList();
        }
        return discountList.stream().map(discount -> {
            DiscountInfo discountInfo = new DiscountInfo();
            discountInfo.setDiscountNo(discount.getDiscountNo());
            discountInfo.setExtendProps(discount.getExtendProps());
            return discountInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 接单下发关联单信息转换
     *
     * @param refOrderDelegate
     * @return
     */
    private RefOrderInfo toRefOrderInfo(RefOrderDelegate refOrderDelegate) {
        RefOrderInfo refOrderInfo = new RefOrderInfo();
        if (refOrderDelegate != null) {
            refOrderInfo.setWaybillNo(refOrderDelegate.getWaybillNo());
            refOrderInfo.setReservationOrderNo(refOrderDelegate.getReservationOrderNo());
            //改址单和逆向单才有原单号
            refOrderInfo.setOriginalOrderNo(refOrderDelegate.getOriginalOrderNo());
            //询价单
            RefOrder refOrder = refOrderDelegate.getRefOrderInfoByType(RefOrderTypeEnum.ENQUIRY.getCode());
            if (refOrder != null) {
                refOrderInfo.setEnquiryOrderNo(refOrder.getRefOrderNo());
            }

            //送取同步派送运单号
            refOrder = refOrderDelegate.getRefOrderInfoByType(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY.getCode());
            if (refOrder != null) {
                refOrderInfo.setDeliveryWaybillNo(refOrder.getRefOrderNo());
            }
            //送取同步取件运单号
            refOrder = refOrderDelegate.getRefOrderInfoByType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode());
            if (refOrder != null) {
                refOrderInfo.setPickupWaybillNo(refOrder.getRefOrderNo());
            }
            //送取同步派送订单号
            refOrder = refOrderDelegate.getRefOrderInfoByType(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY_ORDER.getCode());
            if (refOrder != null) {
                refOrderInfo.setDeliveryOrderNo(refOrder.getRefOrderNo());
            }
            //送取同步取件订单号
            refOrder = refOrderDelegate.getRefOrderInfoByType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode());
            if (refOrder != null) {
                refOrderInfo.setPickupOrderNo(refOrder.getRefOrderNo());
            }

            //集单号
            refOrderInfo.setCollectionOrderNo(refOrderDelegate.getCollectionOrderNo());

            refOrderInfo.setExtendProps(refOrderDelegate.getExtendProps());
        }
        return refOrderInfo;
    }

    /**
     * 解决方案信息转换
     *
     * @param businessSolution
     * @return
     */
    private BusinessSolutionInfo toBusinessSolutionInfo(BusinessSolution businessSolution) {
        if (businessSolution == null) {
            return null;
        }
        BusinessSolutionInfo solutionInfo = new BusinessSolutionInfo();
        solutionInfo.setBusinessSolutionNo(businessSolution.getBusinessSolutionNo());
        solutionInfo.setBusinessSolutionName(businessSolution.getBusinessSolutionName());
        solutionInfo.setProductAttrs(businessSolution.getProductAttrs());
        return solutionInfo;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductions
     * @return
     */
    private List<DeductionInfo> toDeductionInfos(List<Deduction> deductions) {
        if (CollectionUtils.isEmpty(deductions)) {
            return null;
        }

        List<DeductionInfo> deductionInfos = new ArrayList<>(deductions.size());
        deductions.forEach(deduction -> {
            if (deduction != null) {
                DeductionInfo info = new DeductionInfo();
                info.setDeductionNo(deduction.getDeductionNo());
                // 抵扣金额
                info.setDeductionAmount(MoneyMapper.INSTANCE.toMoneyInfo(deduction.getDeductionAmount()));
                info.setExtendProps(deduction.getExtendProps());
                deductionInfos.add(info);
            }
        });
        return deductionInfos;

    }

    /**
     * 协议信息转换
     * @param agreementDelegate
     * @return
     */
    private List<AgreementInfo> toAgreementInfos(AgreementDelegate agreementDelegate) {
        if (null == agreementDelegate || agreementDelegate.isEmpty()) {
            return null;
        }

        return agreementDelegate.getAgreementList().stream()
                .filter(Objects::nonNull)
                .map(agreement -> {
                    AgreementInfo agreementInfo = new AgreementInfo();
                    agreementInfo.setAgreementType(agreement.getAgreementType());
                    agreementInfo.setAgreementId(agreement.getAgreementId());
                    agreementInfo.setSigner(agreement.getSigner());
                    agreementInfo.setSigningTime(agreement.getSigningTime());
                    agreementInfo.setExtendProps(agreement.getExtendProps());
                    return agreementInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 履约信息
     * @return
     */
    private FulfillmentInfo toFulfillmentInfo(Fulfillment fulfillment) {
        if(fulfillment == null) {
            return null;
        }
        FulfillmentInfo fulfillmentInfo = new FulfillmentInfo();
        fulfillmentInfo.setFulfillmentSign(fulfillment.getFulfillmentSign());
        fulfillmentInfo.setExtendProps(fulfillment.getExtendProps());
        return fulfillmentInfo;
    }

    /**
     * 跨境报关信息转换
     */
    private CustomsInfo toCustomsInfo(Customs customs) {
        if (customs == null) {
            return null;
        }
        return CustomsMapper.INSTANCE.toCustomsInfo(customs);
    }

    /**
     * 附件信息转换
     */
    private List<AttachmentInfo> toAttachmentInfos(List<Attachment> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            return null;
        }
        return AttachmentMapper.INSTANCE.toAttachmentInfos(attachments);
    }


    /**
     * 重量信息转换
     */
    private WeightInfo toOrderWeightInfo(Weight weight) {
        if (null == weight) {
            return null;
        }
        return WeightMapper.INSTANCE.toWeightInfo(weight);
    }

    /**
     * 体积信息转换
     */
    private VolumeInfo toOrderVolumeInfo(Volume volume) {
        if (null == volume) {
            return null;
        }
        return VolumeMapper.INSTANCE.toVolumeInfo(volume);
    }
}