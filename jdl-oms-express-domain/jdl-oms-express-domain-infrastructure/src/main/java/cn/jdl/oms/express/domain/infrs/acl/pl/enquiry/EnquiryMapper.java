package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.EnquiryInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.ProductInfoDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.TrustSellerUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.RefOrderDelegate;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.SystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import com.jd.lbs.product.inquiry.dto.response.StandardInquiryResult;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 询价信息对象转换工具
 * @copyright    &copy;2023 JDL.CN All Right Reserved
 * <AUTHOR>
 * @date         2023/3/23
 * @version      1.0
 * @since        1.8
 */
@Mapper(uses = {MoneyMapper.class})
public interface EnquiryMapper {

    /** 询价服务对象转换器实例 */
    EnquiryMapper INSTANCE = Mappers.getMapper(EnquiryMapper.class);

    /**
     * 计费询价防腐层出参转换
     * @param financeFacadeDto 询价计费-财务信息防腐层对象
     * @return
     */
    @Mapping(target = "preAmount", source = "preAmount")
    @Mapping(target = "discountAmount", source = "discountAmount")
    @Mapping(target = "billingWeight", source = "billingWeight")
    @Mapping(target = "billingVolume", source = "billingVolume")
    @Mapping(target = "pointsInfoDto", source = "pointsInfoDto")
    @Mapping(target = "collectionOrgNo", source = "collectionOrgNo")
    @Mapping(target = "financeDetailInfos", source = "financeDetailFacadeDtoList")
    FinanceInfoDto toFinanceInfoDto(BillingEnquiryFacadeResponse.FinanceFacadeDto financeFacadeDto);

    /**
     * 计费询价防腐层出参转换（新）
     * 计费重量赋值calWeight -- 实际计费重量
     * 计费模式赋值heavyBubbleType -- 重泡货类型
     * @param financeFacadeDto 询价计费-财务信息防腐层对象
     * @return
     */
    @Mapping(target = "preAmount", source = "preAmount")
    @Mapping(target = "discountAmount", source = "discountAmount")
    @Mapping(target = "billingVolume", source = "billingVolume")
    @Mapping(target = "billingWeight", source = "calWeight")
    @Mapping(target = "billingMode", source = "heavyBubbleType")
    @Mapping(target = "pointsInfoDto", source = "pointsInfoDto")
    @Mapping(target = "collectionOrgNo", source = "collectionOrgNo")
    @Mapping(target = "payDeadline", source = "payDeadline")
    @Mapping(target = "financeDetailInfos", source = "financeDetailFacadeDtoList")
    FinanceInfoDto toFinanceInfoDtoNEW(BillingEnquiryFacadeResponse.FinanceFacadeDto financeFacadeDto);

    BillingEnquiryFacadeResponse.FinanceFacadeDto toResponseFinanceFacadeDto(Finance finance);

    /**
     * 询价信息转换
     * @param request 询价防腐层请求对象
     * @return
     */
    @Mapping(target = "enquireTime", source = "request.financeFacadeDto.enquireTime")
    @Mapping(target = "enquiryStartProvinceNo", source = "request.consignorFacadeDto.addressFacadeDto.provinceNoGis")
    @Mapping(target = "enquiryStartCityNo", source = "request.consignorFacadeDto.addressFacadeDto.cityNoGis")
    @Mapping(target = "enquiryStartCountyNo", source = "request.consignorFacadeDto.addressFacadeDto.countyNoGis")
    @Mapping(target = "enquiryEndProvinceNo", source = "request.consigneeFacadeDto.addressFacadeDto.provinceNoGis")
    @Mapping(target = "enquiryEndCityNo", source = "request.consigneeFacadeDto.addressFacadeDto.cityNoGis")
    @Mapping(target = "enquiryEndCountyNo", source = "request.consigneeFacadeDto.addressFacadeDto.countyNoGis")
    @Mapping(target = "enquiryWeight", source = "request.financeFacadeDto.billingWeight")
    @Mapping(target = "enquiryVolume", source = "request.financeFacadeDto.billingVolume")
    @Mapping(target = "productInfos", source = "request.productFacadeDtoList")
    @Mapping(target = "extendProps", ignore = true)
    EnquiryInfoDto toEnquiryInfoDto(BillingEnquiryFacadeRequest request);

    /**
     * 费用明细列表转换
     * @param financeDetailFacadeDtos
     * @return
     */
    List<FinanceDetailInfoDto> toFinanceDetailInfoDtoList(List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> financeDetailFacadeDtos);

    /**
     * 折扣信息转换
     * @param financeDetailFacadeDto
     * @return
     */
    @Mapping(target = "discountInfoDtos", source = "discountInfoFacadeDtos")
    FinanceDetailInfoDto toFinanceDetailInfoDto(BillingEnquiryFacadeResponse.FinanceDetailFacadeDto financeDetailFacadeDto);

//    DiscountInfoDto toDiscountInfoDto(BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto);

    MoneyInfoDto toMoneyInfoDto(BillingEnquiryFacadeResponse.MoneyFacadeDto moneyFacadeDto);

    List<ProductInfoDto> toProductInfoDtos(List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos);
    ProductInfoDto toProductInfoDto(BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto);

    @Mapping(target = "account2No", source = "accountNo2")
    @Mapping(target = "account2Name", source = "accountName2")
    BillingEnquiryFacadeRequest.CustomerFacadeDto toCustomerFacadeDto(Customer customer);

    BillingEnquiryFacadeRequest.RefOrderFacadeDto toRefOrderFacadeDto(RefOrderDelegate refOrderDelegate);

    @Mapping(target = "channelNo", source = "channelNo")
    BillingEnquiryFacadeRequest.ChannelFacadeDto toChannelFacadeDto(Channel channel);

    @Mapping(target = "addressFacadeDto", source = "address")
    BillingEnquiryFacadeRequest.ConsignorFacadeDto toConsignorFacadeDto(Consignor consignor);

    @Mapping(target = "addressFacadeDto", source = "address")
    BillingEnquiryFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(Consignee consignee);

    BillingEnquiryFacadeRequest.AddressFacadeDto toAddressFacadeDto(Address address);

    @Mapping(target = "enquireTime", expression = "java(new Date())")
    BillingEnquiryFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(Finance finance);

    BillingEnquiryFacadeRequest.ProductFacadeDto toProductFacadeDto(Product product);

    List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDtos(List<Product> products);

    default String toBusinessUnit(ExpressOrderModel orderModel) {
        return orderModel.getOrderBusinessIdentity().getBusinessUnit();
    }

    default BillingEnquiryFacadeRequest.RefOrderFacadeDto toRefOrderFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getRefOrderInfoDelegate().getWaybillNo());
        return refOrderFacadeDto;
    }

    // TODO 分场景区分怎么获取
    default BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel orderModel, ExpressOrderModel snapshot) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        if (snapshot == null) {
            // 快照为空场景认为是下单询价，货品信息取当前单
            cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
            cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
            cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
            return cargoFacadeDto;
        }

        if (TrustSellerUtil.isTrustWeightVolume(snapshot)) {
            // 信任商家的货品信息取快照信息
            cargoFacadeDto.setTotalCargoWeight(snapshot.getCargoDelegate().totalCargoWeight());
            cargoFacadeDto.setTotalCargoVolume(snapshot.getCargoDelegate().totalCargoVolume());
        } else {
            // 询价场景使用当前单Enquiry对象里的
            Weight weight = Optional.ofNullable(orderModel.getEnquiry().getEnquiryWeight())
                    .orElseThrow(() -> new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价重量为空"));
            cargoFacadeDto.setTotalCargoWeight(weight.getValue());

            Volume volume = Optional.ofNullable(orderModel.getEnquiry().getEnquiryVolume())
                    .orElseThrow(() -> new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价体积为空"));
            cargoFacadeDto.setTotalCargoVolume(volume.getValue());
        }
        // 总货品数量取快照
        cargoFacadeDto.setTotalCargoQuantity(snapshot.getCargoDelegate().totalCargoQuantity());
        return cargoFacadeDto;
    }

    /**
     * 拓展信息-结算方式
     */
     default Object toSettlementType(ExpressOrderModel orderModel) {
         SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(orderModel);
         if (null == settlementType) {
             return null;
         }
         switch (settlementType) {
            case CASH_ON_PICK:
                return EnquiryConstants.SETTLEMENT_CASH_ON_PICK;
            case CASH_ON_DELIVERY:
                return EnquiryConstants.SETTLEMENT_CASH_ON_DELIVERY;
            case MONTHLY_PAYMENT:
                return EnquiryConstants.SETTLEMENT_MONTHLY_PAYMENT;
            default:
                return null;
        }
    }

    /**
     * @description 功能描述:  仓配类型(0：纯配；1：仓配)
     *  接单字段deliveryPattern若为1京仓发货 则该字段赋值1
     *  若为2纯配则该字段赋值0；
     *  接单字段deliveryPattern若为空且systemSubCaller为eclp则该字段赋值为1，
     *  其他情况全部赋值0
     * <AUTHOR>
     * @date 2021/6/29 18:04
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    default Object toDistributionType(ExpressOrderModel orderModel) {
        // 配送的拓展字段 deliveryPattern 从这里获取
        String deliveryPattern = orderModel.getShipment().getDeliveryPattern();
        if (OrderConstants.DELIVERY_PATTERN_ONE.equals(deliveryPattern)){
            return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
        }else if (OrderConstants.DELIVERY_PATTERN_TWO.equals(deliveryPattern)){
            return EnquiryConstants.DISTRIBUTION_PURE;
        }else if (deliveryPattern == null && SystemSubCallerEnum.ECLP.getCode().equals(orderModel.getChannel().getSystemSubCaller())){
            return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
        }
        return EnquiryConstants.DISTRIBUTION_PURE;

    }

    /**
     * 根据询价响应获取币种，默认人民币
     */
    default CurrencyCodeEnum getCurrencyCodeEnum(StandardInquiryResult standardInquiryResult) {
        CurrencyCodeEnum currencyCodeEnum = CurrencyCodeEnum.CNY;
        if (standardInquiryResult != null
                && standardInquiryResult.getExchangeCurrency() != null
                && CurrencyCodeEnum.ofALL(standardInquiryResult.getExchangeCurrency()) != null) {
            currencyCodeEnum = CurrencyCodeEnum.ofALL(standardInquiryResult.getExchangeCurrency());
        }
        return currencyCodeEnum;
    }

    List<DiscountInfoDto> toDiscountInfoDtos(List<BillingEnquiryFacadeResponse.DiscountInfoFacadeDto> discountInfoFacadeDtos);

    /**
     * 折扣信息转换
     * @param discountInfoFacadeDto
     * @return
     */
    default DiscountInfoDto toDiscountInfoDto(BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto) {
        if ( discountInfoFacadeDto == null ) {
            return null;
        }

        DiscountInfoDto discountInfoDto = new DiscountInfoDto();

        discountInfoDto.setDiscountNo( discountInfoFacadeDto.getDiscountNo() );
        discountInfoDto.setDiscountType( discountInfoFacadeDto.getDiscountType() );
        discountInfoDto.setDiscountedAmount( toMoney( discountInfoFacadeDto.getDiscountedAmount()) );


        Map<String, String> extendProps = discountInfoFacadeDto.getExtendProps();
        if (null == extendProps) {
            extendProps = new HashMap<>();
        }
        if(StringUtils.isNotBlank(discountInfoFacadeDto.getTicketNo())){
            //优惠券编码
            extendProps.put(AttachmentKeyEnum.TICKET_NO.getKey(), discountInfoFacadeDto.getTicketNo());
        }
        discountInfoDto.setExtendProps(extendProps);

        return discountInfoDto;
    }

    /**
     * 费用明细构建
     * @param amount 金额
     * @param currencyCode 币种
     * @return 金额对象
     */
    @Mapping(source = "currencyCode", target = "currencyCode")
    @Mapping(source =  "amount", target = "amount")
    BillingEnquiryFacadeResponse.MoneyFacadeDto toMoneyFacadeDto(BigDecimal amount, CurrencyCodeEnum currencyCode);

    /**
     * 金额信息转换
     * @return
     */
    MoneyInfo toMoneyInfo(BillingEnquiryFacadeResponse.MoneyFacadeDto moneyFacadeDto);

    /**
     * 金额信息转换
     * @return
     */
    @Mapping(target = "currency", source = "currencyCode")
    Money toMoney(BillingEnquiryFacadeResponse.MoneyFacadeDto moneyFacadeDto);

}
