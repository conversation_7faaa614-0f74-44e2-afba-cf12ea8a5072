package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing;


import cn.jdl.oms.express.domain.dto.PointsInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import com.jd.lbs.product.inquiry.dto.response.ProductInquiryFeeInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 计费询价防腐层响应数据
 */
@Data
public class BillingEnquiryFacadeResponse {

    /**
     * 财务信息
     */
    private FinanceFacadeDto financeFacadeDto;

    /**
     * 询价单号
     */
    private String enquiryOrderNo;

    /**
     * 询价结果，返回的可以使用的优惠券
     * key:优惠券编码
     * value:优惠券编码
     */
    private Map<String, String> ticketInfos;

    /**
     * 计费询价返回结果
     */
    private List<ProductInquiryFeeInfo> feeInfos;

    @Data
    public static class FinanceFacadeDto {

        /**
         * 折前金额
         */
        private MoneyFacadeDto preAmount;
        /**
         * 折后金额
         */
        private MoneyFacadeDto discountAmount;
        /**
         * 总优惠金额
         */
        private MoneyFacadeDto totalDiscountAmount;
        /**
         * 收款机构
         */
        private String collectionOrgNo;
        /**
         * 费用明细
         */
        private List<FinanceDetailFacadeDto> financeDetailFacadeDtoList;
        /**
         * 支付单号
         */
        private String paymentNo;
        /**
         * 支付截止时间
         */
        private Date payDeadline;

        /**
         * 计费重量
         */
        private WeightInfoDto billingWeight;

        /**
         * 计费体积
         */
        private VolumeInfoDto billingVolume;

        /**
         * 积分信息
         */
        private PointsInfoDto pointsInfoDto;

        /**
         * 计费模式
         */
        private String billingMode;

        /**
         * 计费重量 实际参与计费的重量
         */
        private WeightInfoDto calWeight;

        /**
         * 计费类型 - 重货/泡货
         */
        private String heavyBubbleType;
        /**
         * 换汇前-折前金额
         */
        private MoneyFacadeDto beforeExchangePreAmount;
        /**
         * 换汇前-折后金额
         */
        private MoneyFacadeDto beforeExchangeDiscountAmount;
        /**
         * 换汇汇率
         */
        private String exchangeRate;

        /**
         * 换汇后【加价后总金额】
         */
        private MoneyFacadeDto totalAdditionAmount;

        /**
         * 扩展信息
         */
        private Map<String, String> extendProps;
    }

    @Data
    public static class FinanceDetailFacadeDto {

        /**
         * 费用编号
         */
        private String costNo;

        /**
         * 费用名称
         */
        private String costName;

        /**
         * 货品编号
         */
        private String productNo;
        /**
         * 产品名称
         */
        private String productName;

        /**
         * 折扣类型
         */
        private String discountType;

        /**
         * 折扣编号
         */
        private String discountNo;

        /**
         * 折前金额
         */
        private MoneyFacadeDto preAmount;

        /**
         * 折后金额
         */
        private MoneyFacadeDto discountAmount;

        /**
         * 备注
         */
        private String remark;

        /**
         * 折扣明细
         */
        private List<DiscountInfoFacadeDto> discountInfoFacadeDtos;

        /**
         * 积分信息
         */
        private PointsInfoDto pointsInfoDto;

        /**
         * 扩展字段
         */
        private Map<String, String> extendProps;

        /**
         * 加价后金额
         */
        private MoneyFacadeDto additionAmount;
    }

    @Data
    public static class MoneyFacadeDto {

        /**
         * 金额数值
         */
        private BigDecimal amount;

        /**
         * 金额币种，CNY-人民币，USD-美元，欧元-EUR，日元-JPY，HKD-港币， GBP-英镑
         */
        private CurrencyCodeEnum currencyCode;
    }

    @Data
    public static class DiscountInfoFacadeDto {

        /**
         * 折扣编号
         */
        private String discountNo;

        /**
         * 折扣类型
         */
        private String discountType;

        /**
         * 折扣金额
         */
        private MoneyFacadeDto discountedAmount;

        /**
         * 优惠券编码
         */
        private String ticketNo;

        private MoneyFacadeDto pureDiscountAmount;

        private Map<String, String> extendProps;
    }
}
