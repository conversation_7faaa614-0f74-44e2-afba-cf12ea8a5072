package cn.jdl.oms.express.domain.infrs.acl.pl.jdorder;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @ProjectName：jdl-oms-express-shared-common
 * @Package： cn.jdl.oms.express.domain.infrs.acl.pl.jdorder
 * @ClassName: GetJdOrderFacadeResponse
 * @Description: 零售订单详情信息
 * @Author： liyong549
 * @CreateDate 2021/4/28 20:48
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Data
public class GetJdOrderFacadeResponse {
    /**
     * 订单号
     */
    private Long orderId;
    /**
     * 国际化-订单状态枚举
     */
    private Integer state;
    /**
     * 是否有效（0：代表订单已取消，1：代表订单正常）
     */
    private Integer yn;
    /**
     * sendPay
     */
    private String sendPay;
    /**
     * sendPayMap
     */
    private String sendPayMap;
    /**
     * 收货人姓名
     */
    private String customerName;
    /**
     * 收货人手机号
     */
    private String mobile;
    /**
     * 收货人座机
     */
    private String phone;

    /**
     * 支付方式
     */
    private Integer paymentType;
    /**
     * 配送方式
     */
    private Integer shipmentType;
    /**
     * idSopShipmentType
     */
    private Integer idSopShipmentType;
    /**
     * shipType
     */
    private Integer shipType;
    /**
     * 省
     */
    private Integer province;
    /**
     * 省
     */
    private String provinceName;
    /**
     * 市
     */
    private Integer city;
    /**
     * 市
     */
    private String cityName;
    /**
     * 县
     */
    private Integer county;
    /**
     * 县
     */
    private String countyName;
    /**
     * 镇
     */
    private Integer town;
    /**
     * 镇
     */
    private String townName;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 商品（sku）ID
     */
    private List<Long> detailProductIds;
    /**
     * 商品三级分类id及（sku）ID
     */
    private List<Cart> cartList;

    /**
     * x
     */
    private String venderId;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 厂直国际订单
     */
    private VcIntelOrder vcIntelOrder;

    /**
     * 英文APP-地址替换识别逻辑
     */
    private boolean needCnAddress;

    @Data
    public static class Cart{
        /**
         * 商品（sku）ID
         */
        private Long skuId;
        /**
         * 商品三级分类id
         */
        private Integer cid;
        /**
         * 商品（sku）扩展节点
         */
        private Map<String, String> extTags;
    }
    /**
     * 厂直国际订单
     */
    @Data
    public static class VcIntelOrder {
        /**
         * 省
         */
        private String province;
        /**
         * 市
         */
        private String city;
        /**
         * 县
         */
        private String county;
        /**
         * 乡｜镇
         */
        private String town;
        /**
         * 收件人详细地址
         */
        private String receiveAddress;
        /**
         * 收件人名称
         */
        private String receiveName;
        /**
         * 收件人电话
         */
        private String receiveTel;
        /**
         * 收件人手机号
         */
        private String receiveMobile;
        /**
         * 省id
         */
        private Integer provinceId;
        /**
         * 市id
         */
        private Integer cityId;
        /**
         * 县id
         */
        private Integer countyId;
        /**
         * 乡｜镇id
         */
        private Integer townId;
    }
}