package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.EnquiryInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.ProductInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingInquiryTypeEnum;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryModeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.spec.util.ReceiptCurrencyUtil;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.CargoDelegate;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.Dimension;
import cn.jdl.oms.express.domain.vo.Enquiry;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Package;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.google.common.collect.Lists;
import com.jd.lbs.product.inquiry.dto.request.AdditionPrice;
import com.jd.lbs.product.inquiry.dto.request.ProductParam;
import com.jd.lbs.product.inquiry.dto.request.StandardProductPackage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.CALC_PRICE_ITEM_LIST;

/**
 * b2c询价防腐层数据转换器（ofc）
 */
@Translator
public class B2CEnquiryFacadeTranslator {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(B2CEnquiryFacadeTranslator.class);

    //单据状态 默认传0
    private static final int DEFAULT_ORDER_STATUS = 0;
    // 交付模式
    private static final String DELIVERY_PATTERN = "deliveryPattern";
    // 交付模式 为 1京仓发货
    private static final String DELIVERY_PATTERN_ONE = "1";
    //交付模式 为 2
    private static final String DELIVERY_PATTERN_TWO = "2";
    // 拓展字段
    private static final String SHIPMENT_EXTEND_PROPS ="shipmentExtendProps";
    //eclp
    private static final String ECLP ="eclp";
    // 1代表信任商家
    private static final String TRADER_SIGN_TRUST ="1";
    /**
     * 商家基础资料信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    @Resource
    private EnquiryFacadeTranslator enquiryFacadeTranslator;

    /**
     * ducc配置
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 计费询价防腐层请求数据转换
     */
    public BillingEnquiryFacadeRequest toBillingEnquiryFacadeRequest(ExpressOrderContext expressOrderContext, boolean useSnapshotAttachFees) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        facadeRequest.setOrderNo(orderModel.orderNo());
        //青龙业主编码和青龙业主号名称 (ofc接口传的）
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(orderModel.getOrderSnapshot()));
        //运单号  (这个是从外单查询出来的)
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getOrderSnapshot().getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        //客户、渠道（ofc传递过来的)
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));
        //产品信息  用传过来的订单号查出来有都赋值进去 (这个以前的是从外单查询出来的，添加OFC的包装耗材)
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel,orderModel.getOrderSnapshot(), useSnapshotAttachFees));
        // 总重量、总体积、(ofc 传递过来的)（总数量 从外单里查出来货品数量）
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel,orderModel.getOrderSnapshot()));
        //发件人信息 (这个是从外单查询出来的 2级地址市id是ofc传过来的)
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(orderModel, orderModel.getOrderSnapshot()));
        //收件人信息  (这个是从外单查询出来的)
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel.getOrderSnapshot()));
        //财务相关信息 (这个是从ofc传递出来的 )  结算方式 时间用自己的
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel, orderModel.getOrderSnapshot()));
        //设置拓展字段
        facadeRequest.setExtendProps(toExtendProps(orderModel, orderModel.getOrderSnapshot()));
        return facadeRequest;
    }


    /**
     * 终端切百川后
     * orderSnapshot 为原始订单数据 非外单数据
     * 计费询价防腐层请求数据转换
     */
    public BillingEnquiryFacadeRequest toEnquiryFacadeRequest(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot, boolean useSnapshotAttachFees) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        facadeRequest.setOrderNo(orderModel.orderNo());
        //青龙业主编码和青龙业主号名称 (ofc接口传的）
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(orderSnapshot));
        //运单号  (这个是从外单查询出来的)
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        //客户、渠道（ofc传递过来的)
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));
        //产品信息  用传过来的订单号查出来有都赋值进去 (这个以前的是从外单查询出来的，添加OFC的包装耗材)
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel, orderSnapshot, useSnapshotAttachFees));
        // 总重量、总体积、(ofc 传递过来的)（总数量 从外单里查出来货品数量）
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel, orderSnapshot));
        //发件人信息 (这个是从外单查询出来的 2级地址市id是ofc传过来的)
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(orderModel, orderSnapshot));
        //收件人信息  (这个是从外单查询出来的)
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderSnapshot));
        //财务相关信息 (这个是从ofc传递出来的 )  结算方式 时间用自己的
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel, orderSnapshot));
        //设置拓展字段
        facadeRequest.setExtendProps(toExtendProps(orderModel, orderSnapshot));
        return facadeRequest;
    }

    /**
     * 计费询价防腐层请求数据转换
     * 接单场景
     * 转换过程全用当前单信息
     */
    public BillingEnquiryFacadeRequest toEnquiryReaddressEnquiryFacadeRequest(ExpressOrderContext expressOrderContext, List<ModifyRecord> modifyRecordList, boolean useSnapshotAttachFees) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        // 外单详情补完后的订单数据
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        facadeRequest.setOrderNo(orderModel.orderNo());
        //下单人编号
        facadeRequest.setOperator(orderModel.getOperator());
        //操作时间（订单中心服务接收到请求时的时间，接单场景是接单时间，修改是修改时间，取消是取消时间等）
        facadeRequest.setOperateTime(orderModel.getOperateTime());
        // 青龙业主编码和青龙业主号名称
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(orderSnapshot));
        // 运单号 (这个是从外单查询出来的)
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        //产品信息
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel, orderSnapshot, useSnapshotAttachFees));
        //货物信息，总重量、总体积、总数量
        facadeRequest.setCargoFacadeDto(toEnquiryCargoFacadeDto(orderModel, orderSnapshot));
        //发件人信息
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(orderModel, orderSnapshot));
        //收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderSnapshot));
        //财务相关信息
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel, orderSnapshot));
        //设置拓展字段
        facadeRequest.setExtendProps(toExtendProps(orderModel, orderSnapshot));
        // 改址一单到底信息补充
        enquiryFacadeTranslator.setReaddressRecord(facadeRequest, modifyRecordList);

        // 询价类型
        if(null != orderModel.getEnquiry() && null != orderModel.getEnquiry().getEnquiryMode()){
            facadeRequest.setInquiryType(orderModel.getEnquiry().getEnquiryMode().getPriceCode());
        }
        return facadeRequest;
    }

    /**
     * @description 功能描述: B2C 添加拓展字段
     * <AUTHOR>
     * @date 2021/6/29 12:27
     * @param orderSnapshot 终端切百川前为外单数据，切百川后为原始订单数据
     * @throws
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String, Object> toExtendProps(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(orderSnapshot));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS, DEFAULT_ORDER_STATUS);
        // 仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, toDistributionType(orderSnapshot));
        //温层
        extendParam.put(EnquiryConstants.WARM_LAYER,orderSnapshot.getShipment().getWarmLayer());
        //询价模式
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.ENQUIRY_EXTEND_INQUIRY_TYPE_SWITCH)) {
            LOGGER.info("调计费询价，传入extendParam:inquiryType,开关开启");
            extendParamToInquiryType(extendParam, orderModel);
        } else {
            LOGGER.info("调计费询价，传入extendParam:inquiryType,开关关闭");
        }
        //跨境报关信息：跨境业务类型、是否文件、币种
        extendParamToCustoms(extendParam, orderSnapshot);
        //包裹信息：取询价参数的扩展信息，传enquiryOrderModel
        extendParamToPackageInformation(extendParam, orderModel);
        return extendParam;
    }

    /**
     * @description 功能描述:  仓配类型(0：纯配；1：仓配)
     *  接单字段deliveryPattern若为1京仓发货 则该字段赋值1
     *  若为2纯配则该字段赋值0；
     *  接单字段deliveryPattern若为空且systemSubCaller为eclp则该字段赋值为1，
     *  其他情况全部赋值0
     * <AUTHOR>
     * @date 2021/6/29 18:04
     * @param orderSnapshot 终端切百川前为外单数据，切百川后为原始订单数据
     * @throws
     * @return java.lang.Object
     */
    private Object toDistributionType(ExpressOrderModel orderSnapshot) {
        // 配送的拓展字段 deliveryPattern 从这里获取
        Map<String, String> extendProps = orderSnapshot.getShipment().getExtendProps();
        if(MapUtils.isNotEmpty(extendProps)){
            String shipmentExtendProps = extendProps.get(SHIPMENT_EXTEND_PROPS);
            if (shipmentExtendProps != null){
                Map map = JSONUtils.jsonToMap(shipmentExtendProps);
                   if(MapUtils.isNotEmpty(map)){
                       String deliveryPattern = (String)map.get(DELIVERY_PATTERN);
                       if (DELIVERY_PATTERN_ONE.equals(deliveryPattern)){
                           return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                       }else if (DELIVERY_PATTERN_TWO.equals(deliveryPattern)){
                           return EnquiryConstants.DISTRIBUTION_PURE;
                       }else if (deliveryPattern == null && ECLP.equals(orderSnapshot.getChannel().getSystemSubCaller())){
                           return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                       }
                   }
            }
        }
        return EnquiryConstants.DISTRIBUTION_PURE;

    }


    /**
     * @description 功能描述: B2C拓展信息之结算方式
     * <AUTHOR>
     * @date 2021/6/29 12:33
     * @param orderSnapshot 终端切百川前为外单数据，切百川后为原始订单数据
     * @throws
     * @return java.lang.Object
     */
    private Object extendParamToSettlementType(ExpressOrderModel orderSnapshot) {
        if (SettlementTypeEnum.CASH_ON_PICK == orderSnapshot.getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_CASH_ON_PICK;
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY ==  orderSnapshot.getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_CASH_ON_DELIVERY;
        } else if (SettlementTypeEnum.MONTHLY_PAYMENT ==  orderSnapshot.getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_MONTHLY_PAYMENT;
        }
        return null;
    }
    /**
     * @description 功能描述: 补全询价记录，入参
     * <AUTHOR>
     * @date 2021/7/2 16:03
     * @param expressOrderContext
     * @param billingEnquiryFacadeRequest
     * @throws
     * @return void
     */
    public void complementEnquiry(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        // 询价记录
        EnquiryInfoDto enquiryInfoDto = new EnquiryInfoDto();
        // B2C询价时间为接单时间
        enquiryInfoDto.setEnquireTime(billingEnquiryFacadeRequest.getFinanceFacadeDto().getEnquireTime());
        // 高峰附加费时间 要从model里拿
        enquiryInfoDto.setPeakPeriodTime(expressOrderContext.getOrderModel().getEnquiry().getPeakPeriodTime());

        enquiryInfoDto.setEnquiryStartProvinceNo(billingEnquiryFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().getProvinceNoGis());
        //起始人的市id
        enquiryInfoDto.setEnquiryStartCityNo(billingEnquiryFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().getCityNoGis());
        enquiryInfoDto.setEnquiryStartCountyNo(billingEnquiryFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().getCountyNoGis());

        enquiryInfoDto.setEnquiryEndProvinceNo(billingEnquiryFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().getProvinceNoGis());
        enquiryInfoDto.setEnquiryEndCityNo(billingEnquiryFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().getCityNoGis());
        enquiryInfoDto.setEnquiryEndCountyNo(billingEnquiryFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().getCountyNoGis());

        // 这里面有可能有包装耗材
        if (CollectionUtils.isNotEmpty(billingEnquiryFacadeRequest.getProductFacadeDtoList()) ){
            //产品集合
            List<ProductInfoDto> productInfoDtos = Lists.newArrayListWithCapacity(billingEnquiryFacadeRequest.getProductFacadeDtoList().size());
            //遍历当前费用明细
            billingEnquiryFacadeRequest.getProductFacadeDtoList().forEach(productFacadeDto -> {
                //产品信息
                ProductInfoDto productInfoDto = new ProductInfoDto();
                //产品编码
                productInfoDto.setProductNo(productFacadeDto.getProductNo());
                //产品类型
                productInfoDto.setProductType(productFacadeDto.getProductType());
                //产品关系(所属主产品编码)
                productInfoDto.setParentNo(productFacadeDto.getParentNo());
                //产品要素属性
                productInfoDto.setProductAttrs(productFacadeDto.getProductAttrs());
                //扩展字段说明
                productInfoDto.setExtendProps(productFacadeDto.getExtendProps());
                //产品信息
                productInfoDtos.add(productInfoDto);
            });
            enquiryInfoDto.setProductInfos(productInfoDtos);
        }

        Optional.ofNullable(billingEnquiryFacadeRequest.getFinanceFacadeDto().getBillingWeight()).ifPresent(enquiryWeight->{
            WeightInfoDto weightInfoDto = new WeightInfoDto();
            weightInfoDto.setUnit(enquiryWeight.getUnit());
            weightInfoDto.setValue(enquiryWeight.getValue());
            enquiryInfoDto.setEnquiryWeight(weightInfoDto);
        });

        Optional.ofNullable(billingEnquiryFacadeRequest.getFinanceFacadeDto().getBillingVolume()).ifPresent(enquiryVolume->{
            VolumeInfoDto volumeInfo = new VolumeInfoDto();
            volumeInfo.setUnit(enquiryVolume.getUnit());
            volumeInfo.setValue(enquiryVolume.getValue());
            enquiryInfoDto.setEnquiryVolume(volumeInfo);
        });

        expressOrderModelCreator.setEnquiryInfo(enquiryInfoDto);
        // 补全询价对象
        expressOrderContext.getOrderModel().complement().complementEnquiry(this, expressOrderModelCreator);
    }

    /**
     * 补全计费结果信息
     */
    public void complementBillingResult(ExpressOrderModel orderModel, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();

        FinanceInfoDto financeInfoDtoByResponse = generateFinanceInfoDtoByResponse(billingEnquiryFacadeResponse, orderModel.orderNo());

        // 【询价类型=预估运费】时，将询价结果更新到预估运费模型
        if (orderModel.checkOnlyCostEstimate()) {
            FinanceInfoDto financeInfoDto = new FinanceInfoDto();
            financeInfoDto.setEstimateFinanceInfo(financeInfoDtoByResponse);
            expressOrderModelCreator.setFinanceInfo(financeInfoDto);
            orderModel.complement().complementEstimateFinanceInfo(this, expressOrderModelCreator);
        } else {
            //fixme 汇前金额用财务扩展字段承接
            Map<String,String> extendProps;
            if(null != billingEnquiryFacadeResponse.getFinanceFacadeDto().getBeforeExchangeDiscountAmount()){
                extendProps = new HashMap<>();
                extendProps.put(FinanceConstants.BEFORE_EXCHANGE_DISCOUNT_AMOUNT,JSONUtils.beanToJSONDefault(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBeforeExchangeDiscountAmount()));
                extendProps.put(FinanceConstants.EXCHANGE_RATE,billingEnquiryFacadeResponse.getFinanceFacadeDto().getExchangeRate());
                financeInfoDtoByResponse.setExtendProps(extendProps);
            }
            expressOrderModelCreator.setFinanceInfo(financeInfoDtoByResponse);
            orderModel.complement().complementFinanceInfo(this, expressOrderModelCreator);
        }
    }

    /**
     * 补全计费结果信息
     */
    public void complementBillingResultForAsyncHandler(ExpressOrderModel orderModel, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse, String orderNo) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        FinanceInfoDto financeInfoDtoByResponse = generateFinanceInfoDtoByResponse(billingEnquiryFacadeResponse, orderNo);
        financeInfoDto.setEstimateFinanceInfo(financeInfoDtoByResponse);
        financeInfoDto.setEstimateAmount(financeInfoDtoByResponse.getPreAmount());
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        orderModel.complement().complementEstimateFinanceInfo(this, expressOrderModelCreator);
    }

    private static FinanceInfoDto generateFinanceInfoDtoByResponse(BillingEnquiryFacadeResponse billingEnquiryFacadeResponse, String orderNo) {
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //折前金额
        if (Objects.nonNull(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount())) {
            MoneyInfoDto preAmount = new MoneyInfoDto();
            preAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getAmount());
            preAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getCurrencyCode());
            financeInfoDto.setPreAmount(preAmount);
        } else {
            LOGGER.error("计费结果信息缺少（PreAmount）折前金额,订单号：{}", orderNo);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价失败！计费结果信息缺少（PreAmount）折前金额，订单号:" + orderNo);
        }

        //折后金额
        if (Objects.nonNull(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount())) {
            MoneyInfoDto discountAmount = new MoneyInfoDto();
            discountAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
            discountAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
            financeInfoDto.setDiscountAmount(discountAmount);
        } else {
            LOGGER.error("计费结果信息缺少（DiscountAmount）折后金额,订单号：{}", orderNo);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价失败！计费结果信息缺少（DiscountAmount）折后金额，订单号:" + orderNo);
        }

        //加价后总金额
        if (null != billingEnquiryFacadeResponse.getFinanceFacadeDto().getTotalAdditionAmount()) {
            MoneyInfoDto totalAdditionAmount = new MoneyInfoDto();
            totalAdditionAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getTotalAdditionAmount().getAmount());
            totalAdditionAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getTotalAdditionAmount().getCurrencyCode());
            financeInfoDto.setTotalAdditionAmount(totalAdditionAmount);
        }

        //计费重量
        financeInfoDto.setBillingWeight(billingEnquiryFacadeResponse.getFinanceFacadeDto().getCalWeight());
        //计费体积
        financeInfoDto.setBillingVolume(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingVolume());

        //计费类型
        financeInfoDto.setBillingMode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingMode());

        //积分信息
        financeInfoDto.setPointsInfoDto(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPointsInfoDto());

        //费用明细
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : billingEnquiryFacadeResponse.getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
            FinanceDetailInfoDto detailInfoDto = new FinanceDetailInfoDto();
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount().getAmount());
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount().getCurrencyCode());
            detailInfoDto.setPreAmount(detailPreAmount);

            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount().getAmount());
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount().getCurrencyCode());
            detailInfoDto.setDiscountAmount(detailDiscountAmount);

            //加价后金额
            if (null != detailFacadeDto.getAdditionAmount()) {
                MoneyInfoDto additionAmount = new MoneyInfoDto();
                additionAmount.setAmount(detailFacadeDto.getAdditionAmount().getAmount());
                additionAmount.setCurrencyCode(detailFacadeDto.getAdditionAmount().getCurrencyCode());
                detailInfoDto.setAdditionAmount(additionAmount);
            }

            detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            detailInfoDto.setCostName(detailFacadeDto.getCostName());
            detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            detailInfoDto.setProductName(detailFacadeDto.getProductName());
            detailInfoDto.setRemark(detailFacadeDto.getRemark());
            //折扣明细
            if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
                for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
                    discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());
                    Money money = new Money();
                    money.setAmount(discountInfoFacadeDto.getDiscountedAmount().getAmount());
                    money.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
                    discountInfoDto.setDiscountedAmount(money);
                    discountInfoDtos.add(discountInfoDto);
                }
                detailInfoDto.setDiscountInfoDtos(discountInfoDtos);
            }

            detailInfoDto.setExtendProps(new HashMap<>());
            // 价格项明细
            if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
                detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
            }

            financeDetailInfoDtoList.add(detailInfoDto);
        }
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
        // 收款机构
        financeInfoDto.setCollectionOrgNo(billingEnquiryFacadeResponse.getFinanceFacadeDto().getCollectionOrgNo());
        return financeInfoDto;
    }

    /**
     * @param orderSnapshot 终端切百川前为外单数据，切百川后为原始订单数据
     * @return
     */
    private BillingEnquiryFacadeRequest.CustomerFacadeDto toCustomerFacadeDto(ExpressOrderModel orderSnapshot) {
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = new BillingEnquiryFacadeRequest.CustomerFacadeDto();
        Customer customer = orderSnapshot.getCustomer();
        customerFacadeDto.setAccountNo(customer.getAccountNo());
        return customerFacadeDto;
    }

    /**
     * @param orderModel
     * @param orderSnapshot 终端切百川前为外单数据，切百川后为原始订单数据
     * @param useSnapshotAttachFees 是否使用快照附加费作为计费询价入参
     * @return
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot, boolean useSnapshotAttachFees) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        // 外单最新的增值服务-终端切之前
        // 订单最新的增值服务-终端切之后
        List<? extends IProduct> products = orderSnapshot.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            for (IProduct product : products) {
                // COd的不询价
                if (!AddOnProductEnum.getCodCode().contains(product.getProductNo()) &&
                        !AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode().equals(product.getProductNo()) &&
                        !AddOnProductEnum.CC_MD_NEGOTIATION_REDELIVERY.getCode().equals(product.getProductNo())){

                    // 国补-激活校验 https://joyspace.jd.com/pages/G0hyCRMoGagEM96u9gtC
                    if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.GB_B2C_ENQUIRY_CULL_SWITCH)
                            && AddOnProductEnum.ACTIVATION_CHECK.getCode().equals(product.getProductNo())
                            && MapUtils.isNotEmpty(product.getProductAttrs())// 产品要素包含结算编码
                            && product.getProductAttrs().containsKey(AddOnProductAttrEnum.ACTIVATION_CHECK_SETTLEMENT_ACCOUNT.getCode())
                    ) {
                        continue;
                    }

                    BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                    productFacadeDto.setProductNo(product.getProductNo());
                    productFacadeDto.setProductType(product.getProductType());
                    productFacadeDto.setParentNo(product.getParentNo());
                    productFacadeDto.setProductAttrs(product.getProductAttrs());
                    //港澳：特惠送，特快送计费时传入揽派模式
                    if(ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(product.getProductType())
                            && (ProductEnum.THS.getCode().equals(product.getProductNo())
                            || ProductEnum.TKS.getCode().equals(product.getProductNo()))){
                        Map<String, String> productAttrs = product.getProductAttrs();
                        if(MapUtils.isEmpty(productAttrs)){
                            productAttrs = new HashMap<>();
                        }
                        if(null != orderSnapshot.getShipment()){
                            //揽收模式
                            if(null != orderSnapshot.getShipment().getPickupType()){
                                productAttrs.put(EnquiryConstants.PICKUP_MODEL,String.valueOf(orderSnapshot.getShipment().getPickupType().getCode()));
                            }
                            //派送模式
                            if(null != orderSnapshot.getShipment().getDeliveryType()){
                                productAttrs.put(EnquiryConstants.DELIVERY_MODEL,String.valueOf(orderSnapshot.getShipment().getDeliveryType().getCode()));
                            }
                        }
                        productFacadeDto.setProductAttrs(productAttrs);
                    }
                    productFacadeDtos.add(productFacadeDto);
                }
            }
        }
        //包装耗材、保温箱从询价入参取
        List<Product> products1 = (List<Product>) orderModel.getProductDelegate().getProducts();
        if(CollectionUtils.isNotEmpty(products1)){
            for (Product product : products1) {
                if (!AddOnProductEnum.getPackageServiceCode().contains(product.getProductNo()) &&
                        !product.getProductNo().equals(AddOnProductEnum.COOLER_BOX.getCode()) &&
                        !product.getProductNo().equals(AddOnProductEnum.CC_LL_COOLER_BOX.getCode())) {
                    continue;
                }
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }
        if (useSnapshotAttachFees) {
            //todo 补全附加服务费
            if(CollectionUtils.isNotEmpty(orderSnapshot.getFinance().getAttachFees())){
                for (CostInfo costInfo : orderSnapshot.getFinance().getAttachFees()) {
                    BillingEnquiryFacadeRequest.ProductFacadeDto attachFeeFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                    attachFeeFacadeDto.setProductNo(costInfo.getCostNo());
                    attachFeeFacadeDto.setAttachFees(Boolean.TRUE);
                    attachFeeFacadeDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                    attachFeeAddSpecialProductAttrs(costInfo, attachFeeFacadeDto);
                    productFacadeDtos.add(attachFeeFacadeDto);
                }
            }
        }
        return productFacadeDtos;
    }

    /**
     * 客户、渠道
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ChannelFacadeDto toChannelFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ChannelFacadeDto channelFacadeDto = new BillingEnquiryFacadeRequest.ChannelFacadeDto();
        Channel channel = orderModel.getChannel();
        channelFacadeDto.setChannelNo(channel.getChannelNo());
        return channelFacadeDto;
    }

    /**
     * 收件人信息 传过来的orderModel 是外单里的信息
     * @param orderSnapshot 终端切百川前为外单数据，切百川后为原始订单数据
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(ExpressOrderModel orderSnapshot) {
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();

        Consignee consignee = orderSnapshot.getConsignee();

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        Address address = consignee.getAddress();
        if (address != null) {
            //收件人省
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            // 收件人市
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            //收件人县
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     *
     * @param orderModel
     * @return
     */
    public BillingEnquiryFacadeRequest.CargoFacadeDto toEnquiryCargoFacadeDto(ExpressOrderModel orderModel,ExpressOrderModel orderSnapshot) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        CargoDelegate cargoDelegate = orderModel.getCargoDelegate();
        if (null == cargoDelegate || CollectionUtils.isEmpty(cargoDelegate.getCargoList())) {
            // 当前单货品为空 取快照
            if (null != orderSnapshot && null != orderSnapshot.getCargoDelegate()) {
                cargoDelegate = orderSnapshot.getCargoDelegate();
            }
        }

        if (null != cargoDelegate) {
            cargoFacadeDto.setTotalCargoWeight(cargoDelegate.totalCargoWeight());
        }

        Enquiry enquiry = orderModel.getEnquiry();

        // 询价场景 入参数优先 TODO 是否重量体积一起
        if (null != enquiry && null != enquiry.getEnquiryVolume() && null != enquiry.getEnquiryWeight()) {
            // 计费体积
            cargoFacadeDto.setTotalCargoVolume(enquiry.getEnquiryVolume().getValue());
            // 计费重量
            cargoFacadeDto.setTotalCargoWeight(enquiry.getEnquiryWeight().getValue());
            // 计费数量
            if (null != enquiry.getEnquiryQuantity() && null != enquiry.getEnquiryQuantity().getValue()){
                cargoFacadeDto.setTotalCargoQuantity(enquiry.getEnquiryQuantity().getValue());
            } else if (null != cargoDelegate) {
                // 计费数量(从外单里查出来的货品数量)
                cargoFacadeDto.setTotalCargoQuantity(orderSnapshot.getCargoDelegate().totalCargoQuantity());
            }
        } else {
            // 优先取复核重量体积 若没有 取货品重量体积
            Optional<BigDecimal> recheckWeight = Optional.ofNullable(orderSnapshot)
                    .map(ExpressOrderModel::getRecheckWeight)
                    .map(Weight::getValue);
            Optional<BigDecimal> recheckVolume = Optional.ofNullable(orderSnapshot)
                    .map(ExpressOrderModel::getRecheckVolume)
                    .map(Volume::getValue);

            if (recheckWeight.isPresent() && recheckVolume.isPresent()) {
                cargoFacadeDto.setTotalCargoVolume(recheckVolume.get());
                cargoFacadeDto.setTotalCargoWeight(recheckWeight.get());
            } else if (null != cargoDelegate && CollectionUtils.isNotEmpty(cargoDelegate.getCargoList())) {
                cargoFacadeDto.setTotalCargoVolume(cargoDelegate.totalCargoVolume());
                cargoFacadeDto.setTotalCargoWeight(cargoDelegate.totalCargoWeight());
            }

            if (null != cargoDelegate) {
                // 计费数量
                cargoFacadeDto.setTotalCargoQuantity(cargoDelegate.totalCargoQuantity());
            }
        }

        return cargoFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     * @param orderModel
     * @param  orderSnapshot 终端切百川前为外单数据，切百川后为原始订单数据
     * @return
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        //上游询价入参数优先
        if (null != orderModel.getEnquiry()
                && null != orderModel.getEnquiry().getEnquiryQuantity()
                && null != orderModel.getEnquiry().getEnquiryQuantity().getValue()) {
            cargoFacadeDto.setTotalCargoQuantity(orderModel.getEnquiry().getEnquiryQuantity().getValue());
        } else {
            // 计费数量(从外单里查出来的货品数量)
            BigDecimal cargoQuantity = null;
            if (orderSnapshot.getCargoDelegate() != null) {
                cargoQuantity = orderSnapshot.getCargoDelegate().totalCargoQuantity();
            }
            if (cargoQuantity == null && orderSnapshot.getOrderSnapshot() != null
                    && orderSnapshot.getOrderSnapshot().getCargoDelegate() != null) {
                cargoQuantity = orderSnapshot.getOrderSnapshot().getCargoDelegate().totalCargoQuantity();
            }
            cargoFacadeDto.setTotalCargoQuantity(cargoQuantity);
        }
        //计费体积
        cargoFacadeDto.setTotalCargoVolume(orderModel.getEnquiry().getEnquiryVolume().getValue());
        //计费重量
        cargoFacadeDto.setTotalCargoWeight(orderModel.getEnquiry().getEnquiryWeight().getValue());
        return cargoFacadeDto;
    }
    /**
     * 财务相关信息
     * @param  orderSnapshot 终端切百川前为外单数据，切百川后为原始订单数据
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        //经王维金和张勇确认，询价时间需要用当前时间，具体原因：
        // 如果高峰期附加费时间是28日到29日，询价的时候需要查询高峰期附加费再调计费接口询价，
        // 查询高峰期附加费传的是当前时间，比如28号，命中高峰期附加费时间段（比如高峰期附加费时间段时28日到29日），
        // 但询价的时候计费日期用的是下单时间，比如是27日，没有命中高峰期附加费时间，计费就会报错，
        // 因此查询高峰期的时间和询价的计费日期都需要用当前时间
        financeFacadeDto.setEnquireTime(DateUtils.now());
        //结算方式 这里在查询外单那里已经补在快照里了,传递给计费询价接口
        financeFacadeDto.setSettlementType(orderSnapshot.getFinance().getSettlementType());

        // 抵扣信息 只有逆向
        // 下了运费保，抵扣编码是OFC传过来的，所以抵扣信息在当前订单的fiance信息
        if (null != orderModel.getFinance().getDeductionDelegate()) {
            financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) orderModel.getFinance().getDeductionDelegate().getDeductions()));
        }
        return financeFacadeDto;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductions
     * @return
     */
    private List<DeductionInfoDto> toDeductionInfoDtos(List<Deduction> deductions) {
        if (CollectionUtils.isEmpty(deductions)) {
            return null;
        }
        List<DeductionInfoDto> deductionInfoDtos = new ArrayList<>(deductions.size());
        deductions.forEach(deduction -> {
            if (deduction != null) {
                DeductionInfoDto dto = new DeductionInfoDto();
                //抵扣编码
                dto.setDeductionNo(deduction.getDeductionNo());
                // 抵扣金额
                dto.setDeductionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(deduction.getDeductionAmount()));
                //扩展信息
                dto.setExtendProps(deduction.getExtendProps());
                deductionInfoDtos.add(dto);
            }
        });
        return deductionInfoDtos;
    }

    /**
     *
     * @param orderModel
     * @param  orderSnapshot 终端切百川前为外单数据，切百川后为原始订单数据
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toConsignorFacadeDto(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        Consignor consignor = orderSnapshot.getConsignor();
        Address address = consignor.getAddress();
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        if (address != null) {
            //起始省(外单查出来的)
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            // 发件人二级地址 ofc传递过来的
            if (StringUtils.isNotBlank(orderModel.getEnquiry().getEnquiryStartCityNo())){
                addressFacadeDto.setCityNoGis(orderModel.getEnquiry().getEnquiryStartCityNo());
            } else {
                //不传默认
                addressFacadeDto.setCityNoGis(address.getCityNoGis());
            }
            //起始县(外单查出来的)
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    public List<BillingEnquiryFacadeRequest.ProductFacadeDto> peakPeriodToProductFacadeDto(List<Product> products) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(products)) {
            for (IProduct product : products) {
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }
        return productFacadeDtos;
    }


    /**
     * 询价场景扩展字段：询价类型
     */
    private void extendParamToInquiryType(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        if (orderModel == null || orderModel.getEnquiry() == null || orderModel.getEnquiry().getEnquiryMode() == null) {
            return;
        }
        EnquiryModeEnum enquiryMode = orderModel.getEnquiry().getEnquiryMode();
        extendParam.put(EnquiryConstants.INQUIRY_TYPE, String.valueOf(enquiryMode.getCode()));
    }


    /**
     * 询价场景扩展字段：跨境报关信息
     * 跨境业务类型、是否文件、币种
     */
    private void extendParamToCustoms(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        Customs customs = orderModel.getCustoms();
        if (customs == null) {
            return;
        }
        // 跨境业务类型
        if (customs.getStartFlowDirection() != null && customs.getEndFlowDirection() != null) {
            // 格式：始发站点_目的站点
            String crossBorderType = customs.getStartFlowDirection().name() + "_" + customs.getEndFlowDirection().name();
            extendParam.put(EnquiryConstants.CROSS_BORDER_TYPE, crossBorderType);
        }
        // 是否文件
        if (customs.getFileTag() != null) {
            extendParam.put(EnquiryConstants.FILE_TAG, customs.getFileTag());
        }
        // 币种
        CurrencyCodeEnum currencyCodeEnum = getCurrencyCodeEnum(customs, orderModel.getFinance());
        if (currencyCodeEnum != null) {
            extendParam.put(EnquiryConstants.EXCHANGE_CURRENCY, String.valueOf(currencyCodeEnum.getCode()));
        }
    }


    /**
     * 获取币种
     */
    private CurrencyCodeEnum getCurrencyCodeEnum(Customs customs, Finance finance) {
        AdministrativeRegionEnum startFlowDirection = null;
        AdministrativeRegionEnum endFlowDirection = null;
        SettlementTypeEnum settlementType = null;
        if (customs != null) {
            startFlowDirection = customs.getStartFlowDirection();
            endFlowDirection = customs.getEndFlowDirection();
        }
        if (finance != null) {
            settlementType = finance.getSettlementType();
        }
        return ReceiptCurrencyUtil.getCurrency(startFlowDirection, endFlowDirection, settlementType);
    }


    /**
     * 询价场景扩展字段：包裹信息
     */
    private void extendParamToPackageInformation(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        if (orderModel == null
                || orderModel.getEnquiry() == null
                || MapUtils.isEmpty(orderModel.getEnquiry().getExtendProps())
                || !orderModel.getEnquiry().getExtendProps().containsKey(EnquiryConstants.PACKAGE_LIST)) {
            return;
        }

        String jsonString = orderModel.getEnquiry().getExtendProps().get(EnquiryConstants.PACKAGE_LIST);
        if (StringUtils.isBlank(jsonString)) {
            return;
        }
        List<Package> packageList = JSONUtils.jsonToList(jsonString, Package.class);
        if (CollectionUtils.isEmpty(packageList)) {
            return;
        }

        // 如果上游传递的 packageList 中只有长宽高，但没有体积，需要根据长宽高计算出体积
        // 港澳传的是体积，包裹维度附加费传的是长宽高
        if (expressUccConfigCenter.isPackageModeAttachFeesSwitch()) {
            for (Package aPackage : packageList) {
                if (null == aPackage.getPackageVolume() && null != aPackage.getPackageDimension()) {
                    Dimension dimension = aPackage.getPackageDimension();
                    BigDecimal length = dimension.getLength();
                    BigDecimal width = dimension.getWidth();
                    BigDecimal height = dimension.getHeight();
                    if (length != null && width != null && height != null) {
                        Volume volume = new Volume();
                        volume.setValue(length.multiply(width).multiply(height));
                        aPackage.setPackageVolume(volume);
                    }
                }
            }
        }

        extendParam.put(EnquiryConstants.PACKAGE_INFORMATION, PackageMapper.INSTANCE.toStandardProductPackageInformationList(packageList));
    }

    /**
     * 附加费补充特殊计费要素
     */
    private void attachFeeAddSpecialProductAttrs(CostInfo costInfo, BillingEnquiryFacadeRequest.ProductFacadeDto attachFeeFacadeDto) {
        if (costInfo == null) {
            return;
        }

        // 国际超长超重附加费
        if (EnquiryConstants.COST_NO_OVER_WEIGHT_INTL.equals(costInfo.getCostNo())) {
            String overweightNum = getCostQuantity(costInfo);
            if (StringUtils.isNotBlank(overweightNum)) {
                Map<String, String> productAttrs = attachFeeFacadeDto.getProductAttrs();
                if(MapUtils.isEmpty(productAttrs)){
                    productAttrs = new HashMap<>();
                    attachFeeFacadeDto.setProductAttrs(productAttrs);
                }
                productAttrs.put(EnquiryConstants.OVERWEIGHT_NUM, overweightNum);
            }
        }
    }

    /**
     * 获取费用项命中次数
     */
    private String getCostQuantity(CostInfo costInfo) {
        if (costInfo == null || MapUtils.isEmpty(costInfo.getExtendProps())) {
            return null;
        }
        return costInfo.getExtendProps().get(EnquiryConstants.COST_QUANTITY);
    }

    /**
     * 包裹维度附加费列表 设置到询价扩展字段
     *
     * @param billingEnquiryFacadeRequest
     * @param packageSurchargeList
     */
    public void extendParamToPackageList(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest, List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList) {
        if (billingEnquiryFacadeRequest == null || CollectionUtils.isEmpty(packageSurchargeList)) {
            return;
        }

        Map<String, Object> extendProps = billingEnquiryFacadeRequest.getExtendProps();
        if (extendProps == null) {
            extendProps = new HashMap<>();
        }

        List<StandardProductPackage> standardProductPackageList = new ArrayList<>();
        for (BillingEnquiryFacadeRequest.PackageFacadeDto packageFacadeDto : packageSurchargeList) {
            if (packageFacadeDto.isFilterBilling()) {
                LOGGER.warn("构造计费入参包裹列表时，包裹:{}被过滤", packageFacadeDto.getPackageNo());
                continue;
            }
            StandardProductPackage standardProductPackage = new StandardProductPackage();
            // 包裹号
            standardProductPackage.setCode(packageFacadeDto.getPackageNo());
            // 包裹数
            standardProductPackage.setQty(packageFacadeDto.getQuantity());
            // 重量
            standardProductPackage.setWeight(packageFacadeDto.getWeight() == null ? null : packageFacadeDto.getWeight().doubleValue());
            // 体积
            standardProductPackage.setVolume(packageFacadeDto.getVolume() == null ? null : packageFacadeDto.getVolume().doubleValue());

            // 产品列表
            List<ProductParam> productParamList = new ArrayList<>();
            List<BillingEnquiryFacadeRequest.ProductFacadeDto> productList = packageFacadeDto.getProductList();
            if (CollectionUtils.isNotEmpty(productList)) {
                for (BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto : productList) {
                    ProductParam productParam = new ProductParam();
                    productParam.setProductCode(productFacadeDto.getProductNo());
                    productParam.setProductElement(productFacadeDto.getProductAttrs());
                    productParamList.add(productParam);
                }
                standardProductPackage.setProducts(productParamList);
                standardProductPackageList.add(standardProductPackage);
            }
        }

        if (CollectionUtils.isNotEmpty(standardProductPackageList)) {
            extendProps.put(EnquiryConstants.PACKAGE_LIST, standardProductPackageList);
            billingEnquiryFacadeRequest.setExtendProps(extendProps);
        }
    }

    public void extendParamToAdditionPriceInfo(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest, Finance finance) {
        List<AdditionPrice> additionPriceList = new ArrayList<>();
        if (null != finance && CollectionUtils.isNotEmpty(finance.getCostInfos())) {
            for (CostInfo costInfo : finance.getCostInfos()) {
                AdditionPrice additionPrice = new AdditionPrice();
                additionPrice.setCostNo(costInfo.getCostNo());
                if (Objects.nonNull(costInfo.getAdditionPriceInfo())) {
                    additionPrice.setPriceItems(costInfo.getAdditionPriceInfo().getPriceItems());
                    additionPrice.setFormulaNo(costInfo.getAdditionPriceInfo().getFormulaNo());
                }
                additionPriceList.add(additionPrice);
            }
        }
        if (CollectionUtils.isNotEmpty(additionPriceList)) {
            // todo 判断ExtendProps为空
            billingEnquiryFacadeRequest.getExtendProps().put(EnquiryConstants.ADDITION_PRICE_LIST, additionPriceList);
        }
    }

    public void generateRequestForStandardProductAndDiscountInquiry(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest, ExpressOrderModel orderModel) {
        // 询价类型
        billingEnquiryFacadeRequest.setInquiryType(BillingInquiryTypeEnum.SHI_HOU_ZHE.getCode());
        //加价信息
        extendParamToAdditionPriceInfo(billingEnquiryFacadeRequest, orderModel.getFinance());
    }
}
