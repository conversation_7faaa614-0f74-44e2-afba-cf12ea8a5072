package cn.jdl.oms.express.domain.infrs.acl.facade.product;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductMappingFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductMappingFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductRpcTranslator;
import cn.jdl.oms.express.domain.infrs.acl.rpc.product.IProductService;
import cn.jdl.oms.express.domain.infrs.acl.rpc.RpcResult;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.pms.api.request.CheckProductRequest;
import cn.jdl.pms.api.response.CheckProductResponse;
import com.jdl.product.api.request.productmapping.ProductMappingQueryRequest;
import com.jdl.product.api.response.productmapping.ProductMappingResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @ProjectName：cn.jdl.oms.express.domain.infrs.acl.facade.product
 * @Package： cn.jdl.oms.express.domain.infrs.acl.facade.product
 * @ClassName: ProductFacade
 * @Description: 物流产品校验门面¬
 * @Author： zhangqi1026
 * @CreateDate 2020/3/18
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Component
public class ProductFacade {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductFacade.class);


    /**
     * 产品Rpc服务
     */
    @Resource
    private IProductService productService;
    /**
     * Rpc参数转换器
     */
    @Resource
    private ProductRpcTranslator productRpcTranslator;

    /**
     * 产品校验
     *
     * @param productFacadeRequest
     * @return
     */
    public ProductFacadeResponse checkProduct(ProductFacadeRequest productFacadeRequest) {
        try {
            RequestProfile requestProfile = productRpcTranslator.toRequestProfile(productFacadeRequest);
            CheckProductRequest checkProductRequest = productRpcTranslator.toCheckProductRequest(productFacadeRequest);
            RpcResult<CheckProductResponse> rpcResult = productService.checkProduct(requestProfile, checkProductRequest);

            if (rpcResult.isSuccess()) {
                return productRpcTranslator.toProductFacadeResponse(rpcResult.getResult());
            } else {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRODUCT_VALIDATE_FAIL)
                        .withCustom("产品校验接口调用失败")
                        .withSubCode(rpcResult.getResult().getCode())
                        .withSubMessage(rpcResult.getResult().getMessage());
            }
        } catch (BusinessDomainException e) {
            LOGGER.error("产品信息校验业务异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("产品信息校验异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.PRODUCT_VALIDATE_FAIL, e);
        }
    }

    /**
     * LAS大件产品校验不传货品体积
     *
     * @param productFacadeRequest
     * @return
     */
    public ProductFacadeResponse checkProductByDiy(ProductFacadeRequest productFacadeRequest) {
        try {
            RequestProfile requestProfile = productRpcTranslator.toRequestProfile(productFacadeRequest);
            CheckProductRequest checkProductRequest = productRpcTranslator.toCheckLASProductRequest(productFacadeRequest);
            RpcResult<CheckProductResponse> rpcResult = productService.checkProduct(requestProfile, checkProductRequest);

            if (rpcResult.isSuccess()) {
                return productRpcTranslator.toProductFacadeResponse(rpcResult.getResult());
            } else {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRODUCT_VALIDATE_FAIL)
                    .withCustom("产品校验接口调用失败").withSubCode(rpcResult.getResult().getCode())
                    .withSubMessage(rpcResult.getResult().getMessage());
            }
        } catch (BusinessDomainException e) {
            LOGGER.error("产品信息校验业务异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("产品信息校验异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.PRODUCT_VALIDATE_FAIL, e);
        }
    }

    /**
     * 产品映射
     *
     * @param productMappingFacadeRequest
     * @return
     */
    public ProductMappingFacadeResponse queryProductMappingInfo(ProductMappingFacadeRequest productMappingFacadeRequest) {
        try {
            com.jdl.product.api.core.bean.RequestProfile requestProfile = productRpcTranslator.toProductRequestProfile(productMappingFacadeRequest);
            ProductMappingQueryRequest mappingQueryRequest = productRpcTranslator.toProductMappingQueryRequest(productMappingFacadeRequest);
            RpcResult<ProductMappingResult> rpcResult = productService.queryProductMappingInfo(requestProfile, mappingQueryRequest);

            if (rpcResult.isSuccess()) {
                return productRpcTranslator.toProductMappingFacadeResponse(rpcResult.getResult());
            } else {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRODUCT_VALIDATE_FAIL)
                        .withCustom("产品映射接口调用失败");
            }
        } catch (BusinessDomainException e) {
            LOGGER.error("产品映射接口业务异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("产品映射接口异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.PRODUCT_VALIDATE_FAIL, e);
        }
    }
}
