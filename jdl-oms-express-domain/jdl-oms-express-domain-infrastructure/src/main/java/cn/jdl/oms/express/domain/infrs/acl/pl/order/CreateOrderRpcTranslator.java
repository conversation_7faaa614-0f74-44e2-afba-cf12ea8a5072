package cn.jdl.oms.express.domain.infrs.acl.pl.order;

import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.FenceInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.AttachmentFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.CustomsFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.MoneyFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ActivityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AgreementFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AttachmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessSolutionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CargoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ChannelFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsigneeFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsignorFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.DiscountFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceDetailFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FulfillmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.GoodsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.MarketEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PromotionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.RefOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ReturnInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ShipmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.StationTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.TicketFacade;
import cn.jdl.oms.express.domain.infrs.ohs.locals.security.TdeAcl;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderExtendTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderSubType;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.shared.common.constant.ABConstants;
import cn.jdl.oms.express.shared.common.dict.NodeClassIfcationEnum;
import cn.jdl.oms.express.shared.common.dict.NodeUsageEnum;
import cn.jdl.oms.express.shared.common.utils.DataMaskUtil;
import com.jdl.cp.core.spec.RequestProfile;
import com.jdl.cp.core.ts.entity.AdditionPriceInfo;
import com.jdl.cp.core.ts.entity.ConsignBaseInfo;
import com.jdl.cp.core.ts.entity.CostInfo;
import com.jdl.cp.core.ts.entity.CustomsInfo;
import com.jdl.cp.core.ts.entity.FulfillmentInfo;
import com.jdl.cp.core.ts.entity.Order;
import com.jdl.cp.op.client.dto.PersistOrderRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName CreateOrderRpcTranslator
 * @Description 创建订单rpc服务对象转换
 * <AUTHOR>
 * @Date 2021/3/21 5:08 下午
 * @ModifyDate 2021/3/21 5:08 下午
 * @Version 1.0
 */
@Translator
public class CreateOrderRpcTranslator {
    @Resource
    private TdeAcl tdeAcl;

    /**
     * 环境标识
     */
    @Value("${express.order.run.environment}")
    private String environmentCode;

    /**
     * 英文发件人
     */
    public static final String CONSIGNOR_EN_NAME = "consignorEnName";

    /**
     * 转换为订单rpc服务的profile
     *
     * @param facadeRequestProfile
     * @return
     */
    public RequestProfile toRpcRequestProfile(cn.jdl.batrix.spec.RequestProfile facadeRequestProfile) {
        RequestProfile rpcRequestProfile = new RequestProfile();
        //国别
        rpcRequestProfile.setLocale(facadeRequestProfile.getLocale());
        //租户
        rpcRequestProfile.setTenantId(facadeRequestProfile.getTenantId());
        //时区
        rpcRequestProfile.setTimezone(facadeRequestProfile.getTimeZone());
        //调用链id
        rpcRequestProfile.setTraceId(facadeRequestProfile.getTraceId());
        return rpcRequestProfile;
    }

    public PersistOrderRequest toPersistOrderRequest(cn.jdl.batrix.spec.RequestProfile facadeRequestProfile, CreateOrderFacadeRequest facadeRequest) throws Exception {
        PersistOrderRequest persistOrderRequest = new PersistOrderRequest();
        //订单主档信息转换
        Order order = toOrderMain(facadeRequestProfile, facadeRequest);
        // 订单标识
        Map<String, String> orderSign = facadeRequest.getOrderSign();
        order.setOrderSign(orderSign);
        //客户渠道信息转化
        order.setCustomerChannelInfo(this.toCustomerChannel(facadeRequest.getChannel(),facadeRequest.getCustomer()));
        //货品信息
        order.setCargoInfoList(this.toCargoList(facadeRequest.getCargos()));
        //商品信息
        order.setGoodsInfoList(this.toGoodsList(facadeRequest.getGoodsList()));
        //计费信息
        order.setTransactionCostInfo(this.toTransactionCost(facadeRequest.getFinance()));
        //主产品和增值服务
        order.setServiceProductInfoList(this.toServiceProductList(facadeRequest.getProducts()));
        //收发件人信息
        order.setConsignInfo(this.toConsign(facadeRequest.getConsignor(), facadeRequest.getConsignee(), orderSign));
        //收发件人扩展信息
        order.setConsignExtendInfo(this.toConsignExtendInfo(facadeRequest.getConsignor(), facadeRequest.getConsignee()));
        //配送信息
        order.setShipmentInfo(this.toShipment(facadeRequest.getShipment()));
        //营销信息
        order.setMarketInfoList(this.toMarketInfos(facadeRequest.getPromotion()));
        //关联单信息
        //order.setRelationInfoList(this.toOrderRelation(facadeRequest.getRefOrders()));
        order.setRefOrderInfo(this.toOrderRelationNew(facadeRequest));
        //扩展字段信息
        order.setExtendInfo(this.toOrderExtend(facadeRequest.getExtendProps()));
        //系统扩展信息
        order.setSysExt(this.toSysExt(facadeRequest.getExtendProps()));
        //隐藏标
        order.setHiddenMark(facadeRequest.getHiddenMark());
        //解决方案
        order.setSolution(toSolution(facadeRequest.getBusinessSolutionFacade()));
        //协议信息
        order.setAgreementInfoList(this.toAgreementInfos(facadeRequest.getAgreementFacades()));
        //节点信息
        //发货仓编码及收货信息转物流节点LogisticsNodes
        order.setLogisticsNodesList(this.toLogisticsNodes(facadeRequest.getConsignor(), facadeRequest.getConsignee()));
        if (order.getLogisticsNodesList().isEmpty()) {
            order.setLogisticsNodesList(null);
        }

        if (facadeRequest.getFinance() != null && CollectionUtils.isNotEmpty(facadeRequest.getFinance().getDeductionInfoDtos())) {
            //抵扣信息
            order.setDeductionInfoList(toDeductionInfoList(facadeRequest.getFinance().getDeductionInfoDtos()));
        }

        //退货信息
        order.setReturnInfo(this.toOrderReturnInfo(facadeRequest.getReturnInfoFacade()));
        //履约信息
        order.setFulfillmentInfo(this.toFulfillmentInfo(facadeRequest.getFulfillmentFacade()));
        //跨境报关信息
        order.setCustomsInfo(this.toCustomsInfo(facadeRequest.getCustomsFacade()));
        //附件列表
        order.setAttachmentInfoList(toAttachmentInfos(facadeRequest.getAttachmentFacades()));
        persistOrderRequest.setOrder(order);
        return persistOrderRequest;
    }

    /**
     * 退货信息
     * @param returnInfoFacade
     * @return
     * @throws Exception
     */
    private Order.ReturnInfo toOrderReturnInfo(ReturnInfoFacade returnInfoFacade) throws Exception {
        if (null == returnInfoFacade) {
            return null;
        }
        Order.ReturnInfo returnInfo = new Order.ReturnInfo();
        returnInfo.setReturnType(returnInfoFacade.getReturnType());
        if (null != returnInfoFacade.getConsigneeFacade()) {
            // 退货收货人信息
            ConsignBaseInfo consignBaseInfo = new ConsignBaseInfo();
            consignBaseInfo.setName(tdeAcl.encrypt(returnInfoFacade.getConsigneeFacade().getConsigneeName()));
            consignBaseInfo.setMobile(tdeAcl.encrypt(returnInfoFacade.getConsigneeFacade().getConsigneeMobile()));
            consignBaseInfo.setPhone(tdeAcl.encrypt(returnInfoFacade.getConsigneeFacade().getConsigneePhone()));
            returnInfo.setConsigneeInfo(consignBaseInfo);
            // 退货地址信息
            AddressInfo address = returnInfoFacade.getConsigneeFacade().getAddress();
            if (null != address) {
                com.jdl.cp.core.ts.entity.AddressInfo addressInfo = new com.jdl.cp.core.ts.entity.AddressInfo();
                //发货人省编码
                addressInfo.setProvinceNo(address.getProvinceNo());
                //发货人省名称
                addressInfo.setProvinceName(address.getProvinceName());
                //发货人市编码
                addressInfo.setCityNo(address.getCityNo());
                //发货人市名称
                addressInfo.setCityName(address.getCityName());
                //发货人区编码
                addressInfo.setCountyNo(address.getCountyNo());
                //发货人区名称
                addressInfo.setCountyName(address.getCountyName());
                //发货人镇编码
                addressInfo.setTownNo(address.getTownNo());
                //发货人镇名称
                addressInfo.setTownName(address.getTownName());
                //发货人镇详细地址
                addressInfo.setAddress(tdeAcl.encrypt(address.getAddress()));

                //发货人省编码
                addressInfo.setProvinceNoGis(address.getProvinceNoGis());
                //发货人省名称
                addressInfo.setProvinceNameGis(address.getProvinceNameGis());
                //发货人市编码
                addressInfo.setCityNoGis(address.getCityNoGis());
                //发货人市名称
                addressInfo.setCityNameGis(address.getCityNameGis());
                //发货人区编码
                addressInfo.setCountyNoGis(address.getCountyNoGis());
                //发货人区名称
                addressInfo.setCountyNameGis(address.getCountyNameGis());
                //发货人镇编码
                addressInfo.setTownNoGis(address.getTownNoGis());
                //发货人镇名称
                addressInfo.setTownNameGis(address.getTownNameGis());
                //发货人镇详细地址
                addressInfo.setAddressGis(tdeAcl.encrypt(address.getAddressGis()));
                // GIS解析精确度
                addressInfo.setPreciseGis(address.getPreciseGis());
                // 维度
                if (address.getLatitude() != null && NumberUtils.isParsable(address.getLatitude())) {
                    addressInfo.setLatitude(address.getLatitude());
                }
                // 经度
                if (address.getLongitude() != null && NumberUtils.isParsable(address.getLongitude())) {
                    addressInfo.setLongitude(address.getLongitude());
                }
                //坐标系类型
                addressInfo.setCoordinateType(address.getCoordinateType());

                //地址嵌套等级
                addressInfo.setConflictLevel(address.getConflictLevel());
                //gis打标地址来源
                addressInfo.setAddressSource(address.getAddressSource());
                // 围栏信任标识
                addressInfo.setFenceTrusted(address.getFenceTrusted());
                // 围栏信息
                addressInfo.setFenceInfos(this.toFenceInfos(address.getFenceInfos()));
                returnInfo.setAddressInfo(addressInfo);
            }
        }
        return returnInfo;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductionInfoDtos
     * @return
     */
    private List<Order.DeductionInfo> toDeductionInfoList(List<DeductionInfoDto> deductionInfoDtos) {
        if (CollectionUtils.isEmpty(deductionInfoDtos)) {
            return null;
        }
        List<Order.DeductionInfo> deductionInfoList = new ArrayList<>(deductionInfoDtos.size());
        deductionInfoDtos.forEach(dto -> {
            if (dto != null) {
                Order.DeductionInfo deductionInfo = new Order.DeductionInfo();
                //抵扣编码
                deductionInfo.setDeductionNo(dto.getDeductionNo());
                // 抵扣金额
                if (null != dto.getDeductionAmount()) {
                    deductionInfo.setCurrency(null == dto.getDeductionAmount().getCurrencyCode() ? null : dto.getDeductionAmount().getCurrencyCode().getCode());
                    deductionInfo.setDeductionAmount(dto.getDeductionAmount().getAmount());
                }
                //扩展信息
                deductionInfo.setExt(dto.getExtendProps());

                deductionInfoList.add(deductionInfo);
            }
        });
        return deductionInfoList;
    }

    /**
     * 订单主档
     */
    private Order toOrderMain(cn.jdl.batrix.spec.RequestProfile facadeRequestProfile, CreateOrderFacadeRequest facadeRequest) {
        Order order = new Order();
        //租户id
        order.setTenantId(facadeRequestProfile.getTenantId());
        //订单号
        order.setOrderNo(facadeRequest.getOrderNo());
        //父订单号
        order.setParentOrderNo(facadeRequest.getParentOrderNo());
        //业务单号
        order.setCustomOrderNo(facadeRequest.getCustomOrderNo());
        //业务身份
        order.setBusinessUnit(facadeRequest.getBusinessIdentity().getBusinessUnit());
        //业务类型
        order.setBusinessType(facadeRequest.getBusinessIdentity().getBusinessType());
        //业务策略
        order.setBusinessStrategy(facadeRequest.getBusinessIdentity().getBusinessStrategy());
        //履行单元
        order.setFulfillmentUnit(facadeRequest.getBusinessIdentity().getFulfillmentUnit());
        //订单类型
        order.setOrderType(Integer.valueOf(facadeRequest.getOrderType()));
        //订单子类型
        order.setOrderSubType(facadeRequest.getOrderSubType());
        //订单用途
        order.setOrderUsage(facadeRequest.getOrderUsage());
        //下单人编号
        order.setOrderCreatorNo(facadeRequest.getOperator());
        //订单主状态
        order.setOrderStatus(facadeRequest.getOrderStatus());
        //订单状态
        order.setOrderStatusCustom(facadeRequest.getOrderStatusCustom());
        //订单扩展状态
        order.setOrderExtendStatus(facadeRequest.getOrderExtendStatus());
        //OMS侧的接单时间
        order.setReceivedTime(facadeRequest.getOperateTime());
        //渠道下单时间
        order.setChannelCreateTime(facadeRequest.getChannel().getChannelOperateTime());
        //履约账号1
        order.setFulfillmentAccountNo(facadeRequest.getCustomer().getAccountNo());
        // 履约名称1
        order.setFulfillmentAccountName(facadeRequest.getCustomer().getAccountName());
        order.setFulfillmentAccountId(facadeRequest.getCustomer().getAccountId());
        //履约账号2
        order.setAccountExtend1(facadeRequest.getCustomer().getAccount2No());
        //履约名称2
        order.setAccountExtendName1(facadeRequest.getCustomer().getAccount2Name());
        //履约账号3
        order.setAccountExtend2(facadeRequest.getCustomer().getAccount3No());
        //履约名称3
        order.setAccountExtendName2(facadeRequest.getCustomer().getAccount3Name());

        //客户订单号
        order.setCustomerOrderNo(facadeRequest.getChannel().getCustomerOrderNo());
        //备注
        order.setRemark(facadeRequest.getRemark());
        //订单创建人
        order.setCreateUser(facadeRequest.getOperator());
        //下单人类型
        order.setOrderCreatorNoType(facadeRequest.getInitiatorType());
        //订单用途
        order.setOrderUsage(facadeRequest.getOrderUsage());
        //隐藏标
        order.setHiddenMark(facadeRequest.getHiddenMark());

        if(null != facadeRequest.getOrderNetWeight()){
            //订单总净重
            order.setOrderNetWeight(facadeRequest.getOrderNetWeight().getValue());
            if(null != facadeRequest.getOrderNetWeight().getUnit()){
                order.setWeightUnit(facadeRequest.getOrderNetWeight().getUnit().getCode());
            }
        }

        if(null != facadeRequest.getOrderWeight()){
            //订单总毛重
            order.setOrderWeight(facadeRequest.getOrderWeight().getValue());
            if(null != facadeRequest.getOrderWeight().getUnit()){
                order.setWeightUnit(facadeRequest.getOrderWeight().getUnit().getCode());
            }
        }

        if(null != facadeRequest.getOrderVolume()){
            //订单总题体积
            order.setOrderVolume(facadeRequest.getOrderVolume().getValue());
            if(null != facadeRequest.getOrderVolume().getUnit()){
                order.setVolumeUnit(facadeRequest.getOrderVolume().getUnit().getCode());
            }
        }

        //弃货状态
        order.setDiscardStatus(facadeRequest.getDiscardStatus());

        return order;
    }


    /**
     * 客户渠道信息
     */
    private Order.CustomerChannelInfo toCustomerChannel(ChannelFacade channelFacade, CustomerFacade customerFacade) {
        Order.CustomerChannelInfo customerChannel = new Order.CustomerChannelInfo();
        //渠道来源
        customerChannel.setChannelReceiveSource(channelFacade.getSystemCaller());
        //渠道子来源
        customerChannel.setChannelReceiveSubSource(channelFacade.getSystemSubCaller());
        //客户编码
        customerChannel.setCustomerNo(channelFacade.getChannelCustomerNo());
        //渠道单号
        customerChannel.setChannelOrderNo(channelFacade.getChannelOrderNo());
        //渠道客户编码
        customerChannel.setChannelCustomerNo(channelFacade.getChannelCustomerNo());
        //渠道编码
        customerChannel.setChannelNo(channelFacade.getChannelNo());
        //客户的二级业务渠道客户编码(clothesCode，马甲公司)
        customerChannel.setSecondLevelChannelCustomerNo(channelFacade.getSecondLevelChannelCustomerNo());
        //客户的二级业务渠道
        customerChannel.setSecondLevelChannel(channelFacade.getSecondLevelChannel());
        //客户的二级业务渠道订单号
        customerChannel.setSecondLevelChannelOrderNo(channelFacade.getSecondLevelChannelOrderNo());
        //客户的三级业务渠道订单号
        customerChannel.setThirdLevelChannelOrderNo(channelFacade.getThirdLevelChannelOrderNo());
        //扩展属性
        customerChannel.setExt(channelFacade.getExtendProps());
        //客户类型
        if(null != customerFacade.getCustomerType()){
            customerChannel.setCustomerType(customerFacade.getCustomerType().byteValue());
        }
        //客户编码(C码-超级账号)
        customerChannel.setCustomerNo(customerFacade.getCustomerNo());
        return customerChannel;
    }

    /**
     * 商品信息
     *
     * @param goodsList
     * @return
     */
    private List<Order.GoodsInfo> toGoodsList(List<GoodsFacade> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }
        List<Order.GoodsInfo> goodsInfoList = new ArrayList<>(goodsList.size());
        goodsList.forEach(goodsFacade -> {
            Order.GoodsInfo goodsInfo = new Order.GoodsInfo();
            goodsInfo.setGoodsNo(goodsFacade.getGoodsNo());
            // 商品唯一编码
            goodsInfo.setUniqueMark(goodsFacade.getGoodsUniqueCode());
            if (goodsFacade.getGoodsAmount() != null) {
                goodsInfo.setGoodsAmount(goodsFacade.getGoodsAmount().getAmount());
                if (goodsFacade.getGoodsAmount().getCurrency() != null) {
                    goodsInfo.setCurrency(goodsFacade.getGoodsAmount().getCurrency().getCode());
                }
            }
            goodsInfo.setGoodsName(goodsFacade.getGoodsName());
            goodsInfo.setChannelGoodsNo(goodsFacade.getChannelGoodsNo());
            if (goodsFacade.getGoodsType() != null) {
                goodsInfo.setGoodsType(goodsFacade.getGoodsType());
            }
            if (goodsFacade.getGoodsQuantity() != null && goodsFacade.getGoodsQuantity().getValue() != null) {
                goodsInfo.setGoodsQuantity(goodsFacade.getGoodsQuantity().getValue().intValue());
            }

            if (goodsFacade.getGoodsQuantity() != null && StringUtils.isNotBlank(goodsFacade.getGoodsQuantity().getUnit())) {
                goodsInfo.setQuantityUnit(goodsFacade.getGoodsQuantity().getUnit());
            }
            // 重量
            if (null != goodsFacade.getGoodsWeight() && null != goodsFacade.getGoodsWeight().getValue()) {
                goodsInfo.setGoodsWeight(goodsFacade.getGoodsWeight().getValue());
            }

            if (null != goodsFacade.getGoodsWeight() && null != goodsFacade.getGoodsWeight().getUnit()) {
                goodsInfo.setWeightUnit(goodsFacade.getGoodsWeight().getUnit().getCode());
            }

            // 体积
            if (null != goodsFacade.getGoodsVolume() && null != goodsFacade.getGoodsVolume().getValue()) {
                goodsInfo.setGoodsVolume(goodsFacade.getGoodsVolume().getValue());
            }

            if (null != goodsFacade.getGoodsVolume() && null != goodsFacade.getGoodsVolume().getUnit()) {
                goodsInfo.setVolumeUnit(goodsFacade.getGoodsVolume().getUnit().getCode());
            }

            // 三维
            if (null != goodsFacade.getGoodsDimension() && null != goodsFacade.getGoodsDimension().getHeight()) {
                goodsInfo.setGoodsHeight(goodsFacade.getGoodsDimension().getHeight());
            }

            if (null != goodsFacade.getGoodsDimension()  && null != goodsFacade.getGoodsDimension().getLength()) {
                goodsInfo.setGoodsLength(goodsFacade.getGoodsDimension().getLength());
            }

            if (null != goodsFacade.getGoodsDimension() && null != goodsFacade.getGoodsDimension().getWidth()) {
                goodsInfo.setGoodsWidth(goodsFacade.getGoodsDimension().getWidth());
            }

            if (null != goodsFacade.getGoodsDimension()  && null != goodsFacade.getGoodsDimension().getUnit()) {
                goodsInfo.setDimensionUnit(goodsFacade.getGoodsDimension().getUnit().getCode());
            }

            // 促销信息
            goodsInfo.setSalesInfos(goodsFacade.getSalesInfos());

            goodsInfo.setCombinationGoodsVersion(goodsFacade.getCombinationGoodsVersion());
            if (goodsFacade.getGoodsPrice() != null) {
                goodsInfo.setGoodsUnitPrice(goodsFacade.getGoodsPrice().getAmount());
                if (goodsFacade.getGoodsPrice().getCurrency() != null) {
                    goodsInfo.setCurrency(goodsFacade.getGoodsPrice().getCurrency().getCode());
                }
            }
            if (CollectionUtils.isNotEmpty(goodsFacade.getGoodsSerialInfos())) {
                List<Order.GoodsCodesInfo> goodsCodesInfos = goodsFacade.getGoodsSerialInfos().stream().filter(Objects::nonNull).map(goodsSerial -> {
                    Order.GoodsCodesInfo goodsCodesInfo = new Order.GoodsCodesInfo();
                    //识别码类型 1-PP码， 2-SN码，和维金确认，B2C只有SN码 TODO 枚举待确认
                    int serialType = 2;
                    if (null != goodsSerial.getSerialType()) {
                        serialType = goodsSerial.getSerialType();
                    }
                    goodsCodesInfo.setGoodsIdType(serialType);
                    goodsCodesInfo.setGoodsIdCode(goodsSerial.getSerialNo());
                    goodsCodesInfo.setGoodsIdQuantity(goodsSerial.getSerialQuantity());
                    return goodsCodesInfo;
                }).collect(Collectors.toList());
                goodsInfo.setGoodsCodesInfoList((ArrayList<Order.GoodsCodesInfo>) goodsCodesInfos);
            }
            if (CollectionUtils.isNotEmpty(goodsFacade.getGoodsProductInfos())) {
                List<Order.ServiceProductInfo> serviceProductInfoList = goodsFacade.getGoodsProductInfos().stream().map(goodsProduct -> {
                    Order.ServiceProductInfo serviceProductInfo = new Order.ServiceProductInfo();
                    serviceProductInfo.setProductNo(goodsProduct.getProductNo());
                    serviceProductInfo.setProductName(goodsProduct.getProductName());
                    serviceProductInfo.setParentNo(goodsProduct.getParentNo());
                    serviceProductInfo.setProductType(goodsProduct.getProductType().byteValue());
                    serviceProductInfo.setExt((HashMap<String, String>) goodsProduct.getExtendProps());
                    serviceProductInfo.setAttrs(goodsProduct.getProductAttrs());
                    return serviceProductInfo;
                }).collect(Collectors.toList());
                goodsInfo.setServiceProductInfoList(serviceProductInfoList);
            }
            if(MapUtils.isNotEmpty(goodsFacade.getExtendProps())){
                goodsInfo.setExt(goodsFacade.getExtendProps());
            }
            // 货品附件信息
            if (CollectionUtils.isNotEmpty(goodsFacade.getAttachmentInfos())) {
                List<Order.AttachmentInfo> attachmentList = goodsFacade.getAttachmentInfos().stream().map(attachmentInfo -> {
                    Order.AttachmentInfo attachment = new Order.AttachmentInfo();
                    attachment.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                    //附件名称
                    attachment.setAttachmentName(attachmentInfo.getAttachmentName());
                    //附件业务类型
                    attachment.setAttachmentType(attachmentInfo.getAttachmentType());
                    //附件文档类型
                    attachment.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                    //附件路径
                    attachment.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                    //附件备注
                    attachment.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                    return attachment;
                }).collect(Collectors.toList());
                goodsInfo.setGoodsAttachmentInfos(attachmentList);
            }

            // 商品净重
            if (null != goodsFacade.getNetWeight() && null != goodsFacade.getNetWeight().getValue()) {
                goodsInfo.setNetWeight(goodsFacade.getNetWeight().getValue());
            }

            if (null != goodsFacade.getNetWeight() && null != goodsFacade.getNetWeight().getUnit()) {
                goodsInfo.setWeightUnit(goodsFacade.getNetWeight().getUnit().getCode());
            }

            goodsInfoList.add(goodsInfo);
        });
        return goodsInfoList;
    }

    /**
     * 货品信息
     */
    private List<Order.CargoInfo> toCargoList(List<CargoFacade> cargoFacades) {
        List<Order.CargoInfo> cargoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(cargoFacades)) {
            return cargoList;
        }
        for (CargoFacade cargoFacade : cargoFacades) {
            Order.CargoInfo orderCargo = new Order.CargoInfo();
            //货品名称
            orderCargo.setCargoName(cargoFacade.getCargoName());
            //货品编码
            orderCargo.setCargoNo(cargoFacade.getCargoNo());
            //货品类型
            orderCargo.setCargoType(cargoFacade.getCargoType());

            //货品体积
            if (cargoFacade.getCargoVolume() != null) {
                orderCargo.setCargoVolume(cargoFacade.getCargoVolume().getValue());
                orderCargo.setVolumeUnit(cargoFacade.getCargoVolume().getUnit().getCode());
            }
            //货品重量
            if (cargoFacade.getCargoWeight() != null) {
                orderCargo.setCargoWeight(cargoFacade.getCargoWeight().getValue());
                orderCargo.setWeightUnit(cargoFacade.getCargoWeight().getUnit().getCode());
            }
            //货品尺寸
            if (cargoFacade.getCargoDimension() != null) {
                //长
                orderCargo.setCargoLength(cargoFacade.getCargoDimension().getLength());
                //宽
                orderCargo.setCargoWidth(cargoFacade.getCargoDimension().getWidth());
                //高
                orderCargo.setCargoHeight(cargoFacade.getCargoDimension().getHeight());
                //单位
                if (cargoFacade.getCargoDimension().getUnit() != null) {
                    orderCargo.setDimensionUnit(cargoFacade.getCargoDimension().getUnit().getCode());
                }

            }
            //易污染标识
            if (cargoFacade.getPolluteSign() != null) {
                orderCargo.setPolluteSign(cargoFacade.getPolluteSign().byteValue());
            }
            //货品数量
            if (cargoFacade.getCargoQuantity() != null && cargoFacade.getCargoQuantity().getValue() != null) {
                orderCargo.setCargoQuantity(cargoFacade.getCargoQuantity().getValue().intValue());
                orderCargo.setCargoUnit(cargoFacade.getCargoQuantity().getUnit());
            }
            // 货品内数量
            if (cargoFacade.getCargoInnerQuantity() != null) {
                orderCargo.setCargoInnerQuantity(cargoFacade.getCargoInnerQuantity().intValue());
            }
            // 货品附件信息
            if (CollectionUtils.isNotEmpty(cargoFacade.getAttachmentInfos())) {
                List<Order.AttachmentInfo> attachmentList = cargoFacade.getAttachmentInfos().stream().map(attachmentInfo -> {
                    Order.AttachmentInfo attachment = new Order.AttachmentInfo();
                    attachment.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                    //附件名称
                    attachment.setAttachmentName(attachmentInfo.getAttachmentName());
                    //附件业务类型
                    attachment.setAttachmentType(attachmentInfo.getAttachmentType());
                    //附件文档类型
                    attachment.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                    //附件路径
                    attachment.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                    //附件备注
                    attachment.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                    return attachment;
                }).collect(Collectors.toList());
                orderCargo.setCargoAttachmentInfos(attachmentList);
            }
            if (CollectionUtils.isNotEmpty(cargoFacade.getSerialInfos())){
                List<Order.CargoCodesInfo> cargoCodesInfos = cargoFacade.getSerialInfos().stream().map(cargoSerial -> {
                    Order.CargoCodesInfo cargoCodesInfo = new Order.CargoCodesInfo();
                    if (cargoSerial.getSerialType() != null) {
                        cargoCodesInfo.setCargoIdType(cargoSerial.getSerialType().byteValue());
                    }
                    cargoCodesInfo.setCargoIdCode(cargoSerial.getSerialNo());
                    return cargoCodesInfo;
                }).collect(Collectors.toList());
                orderCargo.setCargoCodesInfos( cargoCodesInfos);
            }
            //货品备注
            orderCargo.setCargoRemark(cargoFacade.getCargoRemark());
            //货品拓展信息
            orderCargo.setExt(cargoFacade.getExtendProps());
            //是否易损
            orderCargo.setCargoVulnerable(cargoFacade.getCargoVulnerable());
            //货品标识
            orderCargo.setCargoSign(cargoFacade.getCargoSign());
            //货品维度增值服务列表
            if (CollectionUtils.isNotEmpty(cargoFacade.getCargoProductInfos())) {
                List<Order.ServiceProductInfo> serviceProductInfoList = cargoFacade.getCargoProductInfos().stream().map(cargoProduct -> {
                    Order.ServiceProductInfo serviceProductInfo = new Order.ServiceProductInfo();
                    serviceProductInfo.setProductNo(cargoProduct.getProductNo());
                    serviceProductInfo.setProductName(cargoProduct.getProductName());
                    serviceProductInfo.setParentNo(cargoProduct.getParentNo());
                    serviceProductInfo.setProductType(cargoProduct.getProductType().byteValue());
                    serviceProductInfo.setExt((HashMap<String, String>) cargoProduct.getExtendProps());
                    serviceProductInfo.setAttrs(cargoProduct.getProductAttrs());
                    return serviceProductInfo;
                }).collect(Collectors.toList());
                orderCargo.setServiceProductInfoList(serviceProductInfoList);
            }
            //货品维度扩展信息
            orderCargo.setExt(cargoFacade.getExtendProps());
            //隐私货品展示信息
            orderCargo.setPrivacyCargoName(cargoFacade.getPrivacyCargoName());
            cargoList.add(orderCargo);
        }
        return cargoList;
    }

    /**
     * 发货信息
     */
    private Order.ShipmentInfo toShipment(ShipmentFacade shipmentFacade) {
        if (shipmentFacade == null) {
            return null;
        }
        Order.ShipmentInfo shipmentInfo = new Order.ShipmentInfo();
        //预计送达时间
        shipmentInfo.setPlanDeliveryTime(shipmentFacade.getPlanDeliveryTime());
        //预计送达时间段
        shipmentInfo.setPlanDeliveryPeriod(shipmentFacade.getPlanDeliveryPeriod());
        //期望配送开始时间
        shipmentInfo.setExpectDeliveryStartTime(shipmentFacade.getExpectDeliveryStartTime());
        //期望配送结束时间
        shipmentInfo.setExpectDeliveryEndTime(shipmentFacade.getExpectDeliveryEndTime());
        //期望取件开始时间
        shipmentInfo.setExpectPickupStartTime(shipmentFacade.getExpectPickupStartTime());
        //期望取件结束时间
        shipmentInfo.setExpectPickupEndTime(shipmentFacade.getExpectPickupEndTime());
        //取件类型
        if (shipmentFacade.getPickupType() != null) {
            shipmentInfo.setPickupType(shipmentFacade.getPickupType().byteValue());
        }
        //派送类型
        if (shipmentFacade.getDeliveryType() != null) {
            shipmentInfo.setDeliveryType(shipmentFacade.getDeliveryType().byteValue());
        }
        if (shipmentFacade.getTransportType() != null) {
            shipmentInfo.setTransportType(shipmentFacade.getTransportType().byteValue());
        }
        if (shipmentFacade.getContactlessType() != null) {
            shipmentInfo.setContactlessType(shipmentFacade.getContactlessType().byteValue());
        }

        //温层
        shipmentInfo.setDeliveryTempLayer(shipmentFacade.getWarmLayer());
        // 始发配送中心
        if (StringUtils.isNotBlank(shipmentFacade.getStartCenterNo())) {
            //始发配送中心编码
            shipmentInfo.setStartCenterNo(shipmentFacade.getStartCenterNo());
        }
        // 目的配送中心
        if (StringUtils.isNotBlank(shipmentFacade.getEndCenterNo())) {
            //目的配送中心编码
            shipmentInfo.setEndCenterNo(shipmentFacade.getEndCenterNo());
        }

        if (StringUtils.isNotBlank(shipmentFacade.getStartStationNo())) {
            //揽收站点编码
            shipmentInfo.setFirstStationNo(shipmentFacade.getStartStationNo());
            //揽收站点名称
            shipmentInfo.setFirstStationName(shipmentFacade.getStartStationName());
            //重点关注：数据存储专用--写死：1
            shipmentInfo.setFirstStationType(StationTypeEnum.STATION.getType());
            //起始站点类型
            shipmentInfo.setStartStationType(shipmentFacade.getStartStationType());
        }

        if (StringUtils.isNotBlank(shipmentFacade.getEndStationNo())) {
            //派送站点编码
            shipmentInfo.setLastStationNo(shipmentFacade.getEndStationNo());
            //派送站点名称
            shipmentInfo.setLastStationName(shipmentFacade.getEndStationName());
            ///重点关注：数据存储专用--写死：1
            shipmentInfo.setLastStationType(StationTypeEnum.STATION.getType());
            //目的站点类型
            shipmentInfo.setEndStationType(shipmentFacade.getEndStationType());
        }

        //物流中转类型
        shipmentInfo.setTransitType(shipmentFacade.getTransitType());
        //发货仓库编码
        shipmentInfo.setWarehouseNo(shipmentFacade.getWarehouseNo());
        //收货仓库编码
        shipmentInfo.setReceiveWarehouseNo(shipmentFacade.getReceiveWarehouseNo());
        //车型
        shipmentInfo.setVehicleType(shipmentFacade.getVehicleType());
        //预计接单时间
        shipmentInfo.setPlanReceiveTime(shipmentFacade.getPlanReceiveTime());
        //取件码
        shipmentInfo.setPickupCode(shipmentFacade.getPickupCode());
        //取件码生产方式
        shipmentInfo.setPickupCodeCreateType(shipmentFacade.getPickupCodeCreateType() != null ? shipmentFacade.getPickupCodeCreateType().getCode() : null);
        //服务要求
        shipmentInfo.setServiceRequirements(shipmentFacade.getServiceRequirements());
        //取件员
        shipmentInfo.setCollector(shipmentFacade.getCollector());
        //指定签收地址
        shipmentInfo.setAssignedAddress(shipmentFacade.getAssignedAddress());
        //无接触收货类型
        if (shipmentFacade.getContactlessType() != null) {
            shipmentInfo.setContactlessType(shipmentFacade.getContactlessType().byteValue());
        }
        //扩展属性
        shipmentInfo.setExt(shipmentFacade.getExtendProps());
        //收货偏好
        shipmentInfo.setReceivingPreference(shipmentFacade.getReceivingPreference());
        shipmentInfo.setShipperNo(shipmentFacade.getShipperNo());
        shipmentInfo.setShipperName(shipmentFacade.getShipperName());
        shipmentInfo.setShipperType(shipmentFacade.getShipperType() != null ? shipmentFacade.getShipperType().toString() : null);

        //接驳站点
        shipmentInfo.setEndTransferStationNo(shipmentFacade.getEndTransferStationNo());
        // 期望派货时间
        shipmentInfo.setExpectDispatchStartTime(shipmentFacade.getExpectDispatchStartTime());
        shipmentInfo.setExpectDispatchEndTime(shipmentFacade.getExpectDispatchEndTime());
        shipmentInfo.setStartStationTypeL3(shipmentFacade.getStartStationTypeL3());
        shipmentInfo.setEndStationTypeL3(shipmentFacade.getEndStationTypeL3());
        return shipmentInfo;
    }

    /**
     * 交易费用
     */
    private Order.TransactionCostInfo toTransactionCost(FinanceFacade financeFacade) {

        Order.TransactionCostInfo transactionCostInfo = new Order.TransactionCostInfo();
        if (financeFacade.getSettlementType() != null) {
            transactionCostInfo.setSettlementType(financeFacade.getSettlementType().byteValue());
        }
        //预估费用字段没改
        if (financeFacade.getEstimateAmount() != null) {
            transactionCostInfo.setFreightAmount(financeFacade.getEstimateAmount().getAmount());
            transactionCostInfo.setCurrency(financeFacade.getEstimateAmount().getCurrency() != null
                    ? financeFacade.getEstimateAmount().getCurrency().getCode() : null);
        }
        // 预占金额
        transactionCostInfo.setOccupyAmount(MoneyFacadeMapper.INSTANCE.toMoneyInfo(financeFacade.getOccupyAmount()));
        // 预占模式
        transactionCostInfo.setOccupyMode(financeFacade.getOccupyMode());

        //预估财务信息
        Optional.ofNullable(financeFacade.getEstimateFinanceInfo()).ifPresent(estimateFinanceInfo -> {
            Order.TransactionCostInfo transactionEstimatedFinanceInfo = new Order.TransactionCostInfo();
            // 预估-折前金额
            Optional.ofNullable(estimateFinanceInfo.getPreAmount()).ifPresent(estimatePreAmount -> {
                transactionEstimatedFinanceInfo.setPreAmount(estimatePreAmount.getAmount());
                transactionEstimatedFinanceInfo.setPreAmountCurrency(estimatePreAmount.getCurrency() != null ? estimatePreAmount.getCurrency().getCode() : null);
            });
            // 预估-折后金额
            Optional.ofNullable(estimateFinanceInfo.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
                transactionEstimatedFinanceInfo.setDiscountAmount(estimateDiscountAmount.getAmount());
                transactionEstimatedFinanceInfo.setDiscountAmountCurrency(estimateDiscountAmount.getCurrency() != null ? estimateDiscountAmount.getCurrency().getCode() : null);
            });
            // 预估-计费重量
            Optional.ofNullable(estimateFinanceInfo.getBillingWeight()).ifPresent(estimateBillingWeight -> {
                transactionEstimatedFinanceInfo.setBillingWeight(estimateBillingWeight.getValue());
                transactionEstimatedFinanceInfo.setWeightUnit(estimateBillingWeight.getUnit() != null ? estimateBillingWeight.getUnit().getCode() : null);
            });
            // 预估-计费体积
            Optional.ofNullable(estimateFinanceInfo.getBillingVolume()).ifPresent(estimateBillingVolume -> {
                transactionEstimatedFinanceInfo.setBillingVolume(estimateBillingVolume.getValue());
                transactionEstimatedFinanceInfo.setVolumeUnit(estimateBillingVolume.getUnit() != null ? estimateBillingVolume.getUnit().getCode() : null);
            });
            // 预估-加价后总金额
            Optional.ofNullable(estimateFinanceInfo.getTotalAdditionAmount()).ifPresent(totalAdditionAmount -> {
                transactionEstimatedFinanceInfo.setTotalAdditionAmount(MoneyFacadeMapper.INSTANCE.toMoneyInfo(totalAdditionAmount));
            });
            // 预估-费用明细
            Optional.ofNullable(estimateFinanceInfo.getFinanceDetails()).ifPresent(estimateFinanceDetails -> {
                List<Order.FinanceDetailInfo> estimateFinanceDetailInfoList = new ArrayList<>(estimateFinanceDetails.size());
                estimateFinanceDetails.forEach(estimateFinanceDetail -> {
                    // 预估-费用明细对象
                    Order.FinanceDetailInfo estimateFinanceDetailInfo = new Order.FinanceDetailInfo();
                    // 预估-费用编号
                    estimateFinanceDetailInfo.setCostNo(estimateFinanceDetail.getCostNo());
                    // 预估-费用名称
                    estimateFinanceDetailInfo.setCostName(estimateFinanceDetail.getCostName());
                    // 预估-费用产品编码
                    estimateFinanceDetailInfo.setProductNo(estimateFinanceDetail.getProductNo());
                    // 预估-费用产品名称
                    estimateFinanceDetailInfo.setProductName(estimateFinanceDetail.getProductName());
                    // 预估-折扣信息
                    if (CollectionUtils.isNotEmpty(estimateFinanceDetail.getDiscountFacades())) {
                        List<Order.MarketInfo> marketInfoList = new ArrayList<>(estimateFinanceDetail.getDiscountFacades().size());
                        estimateFinanceDetail.getDiscountFacades().forEach(estimateDiscountFacade -> {
                            Order.MarketInfo estimateMarketInfo = new Order.MarketInfo();
                            // 预估-折扣编号
                            estimateMarketInfo.setCouponNo(estimateDiscountFacade.getDiscountNo());
                            // 预估-折扣类型
                            estimateMarketInfo.setCouponType(estimateDiscountFacade.getDiscountType());
                            if (estimateDiscountFacade.getDiscountedAmount() != null) {
                                // 预估-折扣金额
                                estimateMarketInfo.setDiscountAmount(estimateDiscountFacade.getDiscountedAmount().getAmount());
                                if (estimateDiscountFacade.getDiscountedAmount().getCurrency() != null) {
                                    estimateMarketInfo.setCurrency(estimateDiscountFacade.getDiscountedAmount().getCurrency().getCode());
                                }
                            }
                            marketInfoList.add(estimateMarketInfo);
                        });

                        estimateFinanceDetailInfo.setMarketInfoList(marketInfoList);
                    }

                    // 预估-折前金额
                    if (estimateFinanceDetail.getPreAmount() != null) {
                        estimateFinanceDetailInfo.setPreAmount(estimateFinanceDetail.getPreAmount().getAmount());
                        estimateFinanceDetailInfo.setPreAmountCurrency(estimateFinanceDetail.getPreAmount().getCurrency() != null ?
                                estimateFinanceDetail.getPreAmount().getCurrency().getCode() : null);
                    }

                    // 预估-折后金额
                    if (estimateFinanceDetail.getDiscountAmount() != null) {
                        estimateFinanceDetailInfo.setDiscountAmount(estimateFinanceDetail.getDiscountAmount().getAmount());
                        estimateFinanceDetailInfo.setDiscountAmountCurrency(estimateFinanceDetail.getDiscountAmount().getCurrency() != null ?
                                estimateFinanceDetail.getDiscountAmount().getCurrency().getCode() : null);
                    }

                    // 预估-加价后金额
                    if (estimateFinanceDetail.getAdditionAmount() != null) {
                        estimateFinanceDetailInfo.setAdditionAmount(MoneyFacadeMapper.INSTANCE.toMoneyInfo(estimateFinanceDetail.getAdditionAmount()));
                    }

                    // 预估-扩展字段
                    estimateFinanceDetailInfo.setExt(estimateFinanceDetail.getExtendProps());

                    estimateFinanceDetailInfoList.add(estimateFinanceDetailInfo);
                });

                transactionEstimatedFinanceInfo.setFinanceDetailInfoList(estimateFinanceDetailInfoList);

            });

            transactionCostInfo.setEstimatedFinanceInfo(transactionEstimatedFinanceInfo);
        });

        //折扣金额
        if (financeFacade.getDiscountAmount() != null) {
            transactionCostInfo.setDiscountAmount(financeFacade.getDiscountAmount().getAmount());
            transactionCostInfo.setCurrency(financeFacade.getDiscountAmount().getCurrency() != null
                    ? financeFacade.getDiscountAmount().getCurrency().getCode() : null);
        }
        transactionCostInfo.setSettlementAccountNo(financeFacade.getSettlementAccountNo());
        //支付账号
        transactionCostInfo.setCustomerDeductionAccountNo(financeFacade.getPaymentAccountNo());
        //财务预占标识, 1：白条预授权
        transactionCostInfo.setPreemptType(financeFacade.getPreemptType());
        //支付方式
        if (financeFacade.getPayment() != null) {
            transactionCostInfo.setPaymentType(financeFacade.getPayment().byteValue());
        }
        //支付状态
        transactionCostInfo.setPaymentStatus(financeFacade.getPaymentStatus());
        //是否询价
        if (financeFacade.getEnquiryType() != null) {
            transactionCostInfo.setEnquiry(financeFacade.getEnquiryType().byteValue());
        }
        //询价状态
        transactionCostInfo.setEnquiryStatus(financeFacade.getEnquiryStatus());
        //支付截止时间
        transactionCostInfo.setPayDeadline(financeFacade.getPayDeadline());
        //退款状态
        transactionCostInfo.setRefundStatus(financeFacade.getRefundStatus());
        //折前金额
        if (financeFacade.getPreAmount() != null) {
            transactionCostInfo.setPreAmount(financeFacade.getPreAmount().getAmount());
            transactionCostInfo.setCurrency(financeFacade.getPreAmount().getCurrency() != null
                    ? financeFacade.getPreAmount().getCurrency().getCode() : null);
        }
        //总优惠金额
        if (financeFacade.getTotalDiscountAmount() != null) {
            transactionCostInfo.setTotalDiscountAmount(financeFacade.getTotalDiscountAmount().getAmount());
            transactionCostInfo.setCurrency(financeFacade.getTotalDiscountAmount().getCurrency() != null
                    ? financeFacade.getTotalDiscountAmount().getCurrency().getCode() : null);
        }
        //计费重量
        if (financeFacade.getBillingWeight() != null) {
            transactionCostInfo.setBillingWeight(financeFacade.getBillingWeight().getValue());
            transactionCostInfo.setWeightUnit(financeFacade.getBillingWeight().getUnit() != null
                    ? financeFacade.getBillingWeight().getUnit().getCode() : null);
        }
        //计费体积
        if (financeFacade.getBillingVolume() != null) {
            transactionCostInfo.setBillingVolume(financeFacade.getBillingVolume().getValue());
            transactionCostInfo.setVolumeUnit(financeFacade.getBillingVolume().getUnit() != null
                    ? financeFacade.getBillingVolume().getUnit().getCode() : null);
        }
        //计费模式
        if (StringUtils.isNotBlank(financeFacade.getBillingMode())) {
            transactionCostInfo.setBillingMode(financeFacade.getBillingMode());
        }
        //收款机构
        transactionCostInfo.setCollectionOrgNo(financeFacade.getCollectionOrgNo());
        //付款环节
        transactionCostInfo.setPaymentStage(financeFacade.getPaymentStage());
        //费用明细
        if (CollectionUtils.isNotEmpty(financeFacade.getFinanceDetails())) {
            List<Order.FinanceDetailInfo> financeDetailInfoList = new ArrayList<>(financeFacade.getFinanceDetails().size());
            for (FinanceDetailFacade financeDetailFacade : financeFacade.getFinanceDetails()) {
                Order.FinanceDetailInfo financeDetailInfo = new Order.FinanceDetailInfo();
                //费用编码
                financeDetailInfo.setCostNo(financeDetailFacade.getCostNo());
                //费用名称
                financeDetailInfo.setCostName(financeDetailFacade.getCostName());
                //产品编码
                financeDetailInfo.setProductNo(financeDetailFacade.getProductNo());
                //产品名称
                financeDetailInfo.setProductName(financeDetailFacade.getProductName());
                if (financeDetailFacade.getPreAmount() != null) {
                    //折前金额
                    financeDetailInfo.setPreAmount(financeDetailFacade.getPreAmount().getAmount());
                }
                //折扣信息存储转换
                if (CollectionUtils.isNotEmpty(financeDetailFacade.getDiscountFacades())) {
                    List<Order.MarketInfo> marketInfoList = new ArrayList<>(financeDetailFacade.getDiscountFacades().size());
                    financeDetailFacade.getDiscountFacades().forEach(discountFacade -> {
                        Order.MarketInfo marketInfo = new Order.MarketInfo();
                        marketInfo.setCouponNo(discountFacade.getDiscountNo());
                        marketInfo.setCouponType(discountFacade.getDiscountType());
                        marketInfo.setCouponSource(MarketEnum.DISCOUNT_FINANCE.getCode());
                        if (discountFacade.getDiscountedAmount() != null) {
                            marketInfo.setDiscountAmount(discountFacade.getDiscountedAmount().getAmount());
                            if (discountFacade.getDiscountedAmount().getCurrency() != null) {
                                marketInfo.setCurrency(discountFacade.getDiscountedAmount().getCurrency().getCode());
                            }
                        }
                        //扩展字段
                        marketInfo.setExt(discountFacade.getExtendProps());
                        marketInfoList.add(marketInfo);
                    });
                    financeDetailInfo.setMarketInfoList(marketInfoList);
                }

                if (financeDetailFacade.getDiscountAmount() != null) {
                    //折扣金额
                    financeDetailInfo.setDiscountAmount(financeDetailFacade.getDiscountAmount().getAmount());
                }
                //备注
                financeDetailInfo.setRemark(financeDetailFacade.getRemark());

                financeDetailInfoList.add(financeDetailInfo);
            }
            transactionCostInfo.setFinanceDetailInfoList(financeDetailInfoList);
        }
        //积分信息
        if (financeFacade.getPointsFacade() != null && financeFacade.getPointsFacade().getRedeemPointsAmount() != null) {
            transactionCostInfo.setRedeemPointsAmount(financeFacade.getPointsFacade().getRedeemPointsAmount().getAmount());
            transactionCostInfo.setCurrency(financeFacade.getPointsFacade().getRedeemPointsAmount().getCurrency() != null ? financeFacade.getPointsFacade().getRedeemPointsAmount().getCurrency().getCode() : null);
        }
        if (financeFacade.getPointsFacade() != null && financeFacade.getPointsFacade().getRedeemPointsQuantity() != null) {
            transactionCostInfo.setRedeemPointsQuantity(financeFacade.getPointsFacade().getRedeemPointsQuantity().getValue());
            transactionCostInfo.setQuantityUnit(financeFacade.getPointsFacade().getRedeemPointsQuantity().getUnit());
        }
        //支付类型billType补齐
        transactionCostInfo.setBillingType(financeFacade.getBillingType());

        //收费要求信息
        if(CollectionUtils.isNotEmpty(financeFacade.getCostInfos())){
            List<Order.TransactionCostInfo> costInfos = financeFacade.getCostInfos().stream().map(costInfo ->{
                Order.TransactionCostInfo tranCostInfo = new Order.TransactionCostInfo();
                tranCostInfo.setCostNo(costInfo.getCostNo());
                tranCostInfo.setCostName(costInfo.getCostName());
                tranCostInfo.setChargingSource(costInfo.getChargingSource());
                tranCostInfo.setSettlementAccountNo(costInfo.getSettlementAccountNo());
                tranCostInfo.setExt(costInfo.getExtendProps());
                tranCostInfo.setAdditionPriceInfo(toAdditionPriceInfo(costInfo.getAdditionPriceInfo()));
                return tranCostInfo;
            }).collect(Collectors.toList());
            transactionCostInfo.setCostInfos(costInfos);
        }

        //附加费用
        if(CollectionUtils.isNotEmpty(financeFacade.getAttachFees())){
            List<CostInfo> attachFees = financeFacade.getAttachFees().stream().map(attachFee ->{
                CostInfo costInfo = new CostInfo();
                costInfo.setCostNo(attachFee.getCostNo());
                costInfo.setCostName(attachFee.getCostName());
                costInfo.setChargingSource(attachFee.getChargingSource());
                costInfo.setSettlementAccountNo(attachFee.getSettlementAccountNo());
                costInfo.setExt(attachFee.getExtendProps());
                return costInfo;
            }).collect(Collectors.toList());
            transactionCostInfo.setAttachFees(attachFees);
        }
        //预估税金
        transactionCostInfo.setEstimatedTax(MoneyFacadeMapper.INSTANCE.toMoneyInfo(financeFacade.getEstimatedTax()));

        //真实税金
        transactionCostInfo.setActualTax(MoneyFacadeMapper.INSTANCE.toMoneyInfo(financeFacade.getActualTax()));

        //费用支付状态归集
        Map<String, String> payStatusMap = financeFacade.getPayStatusMap();
        if (MapUtils.isNotEmpty(payStatusMap)) {
            transactionCostInfo.setPayStatusMap(payStatusMap);
        }
        //扩展属性
        transactionCostInfo.setExt(financeFacade.getExtendProps());
        //税金结算方式
        if (financeFacade.getTaxSettlementType() != null) {
            transactionCostInfo.setTaxSettlementType(financeFacade.getTaxSettlementType());
        }
        return transactionCostInfo;
    }

    private AdditionPriceInfo toAdditionPriceInfo(cn.jdl.oms.express.domain.vo.AdditionPriceInfo additionPriceInfo) {
        AdditionPriceInfo info = new AdditionPriceInfo();
        if(Objects.isNull(additionPriceInfo)) {
            return info;
        }
        info.setFormulaNo(additionPriceInfo.getFormulaNo());
        info.setPriceItems(additionPriceInfo.getPriceItems());
        return info;
    }

    /**
     * 收发件信息
     */
    private Order.ConsignInfo toConsign(ConsignorFacade consignorFacade, ConsigneeFacade consigneeFacade, Map<String, String> orderSign) throws Exception {
        Order.ConsignInfo orderConsign = new Order.ConsignInfo();
        //发货人信息
        if (consignorFacade != null) {
            //发货人名称
            orderConsign.setConsignorName(tdeAcl.encrypt(consignorFacade.getConsignorName()));
            orderConsign.setConsignorNameQuery(tdeAcl.obtainKeyWordIndex(consignorFacade.getConsignorName()));
            //发货人手机号
            orderConsign.setConsignorMobile(tdeAcl.encrypt(consignorFacade.getConsignorMobile()));
            orderConsign.setConsignorMobileMask(DataMaskUtil.blurPhone(consignorFacade.getConsignorMobile()));
            orderConsign.setConsignorMobileQuery(tdeAcl.obtainWildCardKeyWordIndex(consignorFacade.getConsignorMobile()));
            //发货人电话
            orderConsign.setConsignorPhone(tdeAcl.encrypt(consignorFacade.getConsignorPhone()));
            orderConsign.setConsignorPhoneQuery(tdeAcl.obtainWildCardKeyWordIndex(consignorFacade.getConsignorPhone()));
            //发货人公司
            orderConsign.setConsignorCompany(consignorFacade.getConsignorCompany());
            //发货人邮编
            orderConsign.setConsignorZipCode(consignorFacade.getConsignorZipCode());
            //发货人国家编码
            orderConsign.setConsignorNationNo(consignorFacade.getConsignorNationNo());
            //发货人国家
            orderConsign.setConsignorNation(consignorFacade.getConsignorNation());
            //发货人证件类型
            if (consignorFacade.getConsignorIdType() != null) {
                orderConsign.setConsignorIdType(consignorFacade.getConsignorIdType().byteValue());
            }
            //发货人证件号
            orderConsign.setConsignorIdNo(tdeAcl.encrypt(consignorFacade.getConsignorIdNo()));
            orderConsign.setConsignorIdNoQuery(tdeAcl.obtainWildCardKeyWordIndex(consignorFacade.getConsignorIdNo()));
            //发货人证件姓名
            orderConsign.setConsignorIdName(tdeAcl.encrypt(consignorFacade.getConsignorIdName()));
            //地址简称
            orderConsign.setConsignorAddressAbbreviation(consignorFacade.getConsignorAddressAbbreviation());
            //英文发货人姓名
            orderConsign.setConsignorEnName(tdeAcl.encrypt(consignorFacade.getConsignorEnName()));

            //发货人地址信息
            AddressInfo consignorAddress = consignorFacade.getAddress();
            if (consignorAddress != null) {
                //发货人省编码
                orderConsign.setConsignorProvinceNo(consignorAddress.getProvinceNo());
                //发货人省名称
                orderConsign.setConsignorProvinceName(consignorAddress.getProvinceName());
                //发货人市编码
                orderConsign.setConsignorCityNo(consignorAddress.getCityNo());
                //发货人市名称
                orderConsign.setConsignorCityName(consignorAddress.getCityName());
                //发货人区编码
                orderConsign.setConsignorCountyNo(consignorAddress.getCountyNo());
                //发货人区名称
                orderConsign.setConsignorCountyName(consignorAddress.getCountyName());
                //发货人镇编码
                orderConsign.setConsignorTownNo(consignorAddress.getTownNo());
                //发货人镇名称
                orderConsign.setConsignorTownName(consignorAddress.getTownName());
                //发货人镇详细地址
                orderConsign.setConsignorAddress(tdeAcl.encrypt(consignorAddress.getAddress()));
                orderConsign.setConsignorAddressQuery(tdeAcl.obtainKeyWordIndex(consignorAddress.getAddress()));

                //GIS解析后的发货人省编码
                orderConsign.setConsignorProvinceNoGis(consignorAddress.getProvinceNoGis());
                //GIS解析后的发货人省名称
                orderConsign.setConsignorProvinceNameGis(consignorAddress.getProvinceNameGis());
                //GIS解析后的发货人市编码
                orderConsign.setConsignorCityNoGis(consignorAddress.getCityNoGis());
                //GIS解析后的发货人市名称
                orderConsign.setConsignorCityNameGis(consignorAddress.getCityNameGis());
                //GIS解析后的发货人区编码
                orderConsign.setConsignorCountyNoGis(consignorAddress.getCountyNoGis());
                //GIS解析后的发货人区名称
                orderConsign.setConsignorCountyNameGis(consignorAddress.getCountyNameGis());
                //GIS解析后的发货人镇编码
                orderConsign.setConsignorTownNoGis(consignorAddress.getTownNoGis());
                //GIS解析后的发货人镇名称
                orderConsign.setConsignorTownNameGis(consignorAddress.getTownNameGis());
                // 纬度
                if (consignorAddress.getLatitude() != null && NumberUtils.isParsable(consignorAddress.getLatitude())) {
                    orderConsign.setConsignorLatitude(new BigDecimal(consignorAddress.getLatitude()));
                }
                // 经度
                if (consignorAddress.getLongitude() != null && NumberUtils.isParsable(consignorAddress.getLongitude())) {
                    orderConsign.setConsignorLongitude(new BigDecimal(consignorAddress.getLongitude()));
                }
                //坐标系类型
                orderConsign.setConsignorCoordinateType(consignorAddress.getCoordinateType());
                // 围栏信息
                orderConsign.setConsignorFenceList(toFenceInfos(consignorAddress.getFenceInfos()));
                //地址嵌套等级
                orderConsign.setConsignorConflictLevel(consignorAddress.getConflictLevel());
                //gis打标地址来源
                orderConsign.setConsignorAddressSource(consignorAddress.getAddressSource());
                //围栏信任标识
                if (null != consignorAddress.getFenceTrusted()) {
                    orderSign.put(OrderSignEnum.CONSIGNOR_FENCE_TRUSTED.getCode(), consignorAddress.getFenceTrusted().toString());
                }
                //行政区编码
                orderConsign.setConsignorRegionNo(consignorAddress.getRegionNo());
                //行政区名称
                orderConsign.setConsignorRegionName(consignorAddress.getRegionName());
                //英文城市
                orderConsign.setConsignorEnCity(consignorAddress.getEnCityName());
                //英文地址
                orderConsign.setConsignorEnAddress(tdeAcl.encrypt(consignorAddress.getEnAddress()));
                orderConsign.setConsignorPoiCode(consignorAddress.getPoiCode());
                orderConsign.setConsignorPoiName(tdeAcl.encrypt(consignorAddress.getPoiName()));
                orderConsign.setConsignorHouseNumber(tdeAcl.encrypt(consignorAddress.getHouseNumber()));
                //地址扩展信息
                orderConsign.setConsignorAddressExt(consignorAddress.getExtendProps());
            }
            //发件人扩展信息
            orderConsign.setConsignorExt(consignorFacade.getExtendProps());

        }
        //收货人人信息
        if (consigneeFacade != null) {
            //收货人名称
            orderConsign.setConsigneeName(tdeAcl.encrypt(consigneeFacade.getConsigneeName()));
            orderConsign.setConsigneeNameQuery(tdeAcl.obtainKeyWordIndex(consigneeFacade.getConsigneeName()));
            //收货人手机号
            orderConsign.setConsigneeMobile(tdeAcl.encrypt(consigneeFacade.getConsigneeMobile()));
            orderConsign.setConsigneeMobileQuery(tdeAcl.obtainWildCardKeyWordIndex(consigneeFacade.getConsigneeMobile()));
            orderConsign.setConsigneeMobileMask(DataMaskUtil.blurPhone(consigneeFacade.getConsigneeMobile()));
            //收货人电话
            orderConsign.setConsigneePhone(tdeAcl.encrypt(consigneeFacade.getConsigneePhone()));
            orderConsign.setConsigneePhoneQuery(tdeAcl.obtainWildCardKeyWordIndex(consigneeFacade.getConsigneePhone()));
            //收货人公司
            orderConsign.setConsigneeCompany(consigneeFacade.getConsigneeCompany());
            //收货人邮编
            orderConsign.setConsigneeZipCode(consigneeFacade.getConsigneeZipCode());
            //收货人国家编码
            orderConsign.setConsigneeNationNo(consigneeFacade.getConsigneeNationNo());
            //收货人国家
            orderConsign.setConsigneeNation(consigneeFacade.getConsigneeNation());
            //收货人证件类型
            if (consigneeFacade.getConsigneeIdType() != null) {
                orderConsign.setConsigneeIdType(consigneeFacade.getConsigneeIdType().byteValue());
            }
            //收货人证件号
            orderConsign.setConsigneeIdNo(tdeAcl.encrypt(consigneeFacade.getConsigneeIdNo()));
            //收货人证件姓名
            orderConsign.setConsigneeIdName(tdeAcl.encrypt(consigneeFacade.getConsigneeIdName()));
            orderConsign.setConsigneeIdNoQuery(tdeAcl.obtainWildCardKeyWordIndex(consigneeFacade.getConsigneeIdNo()));
            //地址简称
            orderConsign.setConsigneeAddressAbbreviation(consignorFacade.getConsignorAddressAbbreviation());
            //收货人地址
            AddressInfo consigneeAddress = consigneeFacade.getAddress();
            if (consigneeAddress != null) {
                //收货人省编码
                orderConsign.setConsigneeProvinceNo(consigneeAddress.getProvinceNo());
                //收货人省名称
                orderConsign.setConsigneeProvinceName(consigneeAddress.getProvinceName());
                //收货人市编码
                orderConsign.setConsigneeCityNo(consigneeAddress.getCityNo());
                //收货人市名称
                orderConsign.setConsigneeCityName(consigneeAddress.getCityName());
                //收货人区编码
                orderConsign.setConsigneeCountyNo(consigneeAddress.getCountyNo());
                //收货人区名称
                orderConsign.setConsigneeCountyName(consigneeAddress.getCountyName());
                //收货人镇编码
                orderConsign.setConsigneeTownNo(consigneeAddress.getTownNo());
                //收货人镇名称
                orderConsign.setConsigneeTownName(consigneeAddress.getTownName());
                //收货人详细地址
                orderConsign.setConsigneeAddress(tdeAcl.encrypt(consigneeAddress.getAddress()));
                orderConsign.setConsigneeAddressQuery(tdeAcl.obtainKeyWordIndex(consigneeAddress.getAddress()));
                //GIS解析后的收货人省编码
                orderConsign.setConsigneeProvinceNoGis(consigneeAddress.getProvinceNoGis());
                //GIS解析后的收货人省名称
                orderConsign.setConsigneeProvinceNameGis(consigneeAddress.getProvinceNameGis());
                //GIS解析后的收货人市编码
                orderConsign.setConsigneeCityNoGis(consigneeAddress.getCityNoGis());
                //GIS解析后的收货人市名称
                orderConsign.setConsigneeCityNameGis(consigneeAddress.getCityNameGis());
                //GIS解析后的收货人区编码
                orderConsign.setConsigneeCountyNoGis(consigneeAddress.getCountyNoGis());
                //GIS解析后的收货人区名称
                orderConsign.setConsigneeCountyNameGis(consigneeAddress.getCountyNameGis());
                //GIS解析后的收货人镇编码
                orderConsign.setConsigneeTownNoGis(consigneeAddress.getTownNoGis());
                //GIS解析后的收货人镇名称
                orderConsign.setConsigneeTownNameGis(consigneeAddress.getTownNameGis());
                // 纬度
                if (consigneeAddress.getLatitude() != null && NumberUtils.isParsable(consigneeAddress.getLatitude())) {
                    orderConsign.setConsigneeLatitude(new BigDecimal(consigneeAddress.getLatitude()));
                }
                // 经度
                if (consigneeAddress.getLongitude() != null && NumberUtils.isParsable(consigneeAddress.getLongitude())) {
                    orderConsign.setConsigneeLongitude(new BigDecimal(consigneeAddress.getLongitude()));
                }
                //坐标系类型
                orderConsign.setConsigneeCoordinateType(consigneeAddress.getCoordinateType());
                // 围栏信息
                orderConsign.setConsigneeFenceList(toFenceInfos(consigneeAddress.getFenceInfos()));
                //地址嵌套等级
                orderConsign.setConsigneeConflictLevel(consigneeAddress.getConflictLevel());
                //gis打标地址来源
                orderConsign.setConsigneeAddressSource(consigneeAddress.getAddressSource());
                // 围栏信任标识
                if (null != consigneeAddress.getFenceTrusted()) {
                    orderSign.put(OrderSignEnum.CONSIGNEE_FENCE_TRUSTED.getCode(), consigneeAddress.getFenceTrusted().toString());
                }
                //行政区编码
                orderConsign.setConsigneeRegionNo(consigneeAddress.getRegionNo());
                //行政区名称
                orderConsign.setConsigneeRegionName(consigneeAddress.getRegionName());
                //英文城市
                orderConsign.setConsigneeEnCity(consigneeAddress.getEnCityName());
                //英文地址
                orderConsign.setConsigneeEnAddress(tdeAcl.encrypt(consigneeAddress.getEnAddress()));
                orderConsign.setConsigneePoiCode(consigneeAddress.getPoiCode());
                orderConsign.setConsigneePoiName(tdeAcl.encrypt(consigneeAddress.getPoiName()));
                orderConsign.setConsigneeHouseNumber(tdeAcl.encrypt(consigneeAddress.getHouseNumber()));
                orderConsign.setConsigneeAddressExt(consigneeAddress.getExtendProps());
            }
            //收件人扩展信息
            if(MapUtils.isNotEmpty(consigneeFacade.getExtendProps())){
                orderConsign.setConsigneeExt(consigneeFacade.getExtendProps());
                // ext里特殊字段加密
                if (consigneeFacade.getExtendProps().containsKey(AttachmentKeyEnum.APPLIER_NAME.getKey())) {
                    String applierName = consigneeFacade.getExtendProps().get(AttachmentKeyEnum.APPLIER_NAME.getKey());
                    orderConsign.getConsigneeExt().put(AttachmentKeyEnum.APPLIER_NAME.getKey(), tdeAcl.encrypt(applierName));
                }
                if (consigneeFacade.getExtendProps().containsKey(AttachmentKeyEnum.APPLIER_PHONE.getKey())) {
                    String applierPhone = consigneeFacade.getExtendProps().get(AttachmentKeyEnum.APPLIER_PHONE.getKey());
                    orderConsign.getConsigneeExt().put(AttachmentKeyEnum.APPLIER_PHONE.getKey(), tdeAcl.encrypt(applierPhone));
                }
                if (consigneeFacade.getExtendProps().containsKey(AttachmentKeyEnum.APPLY_ID_NO.getKey())) {
                    String applyIdNo = consigneeFacade.getExtendProps().get(AttachmentKeyEnum.APPLY_ID_NO.getKey());
                    orderConsign.getConsigneeExt().put(AttachmentKeyEnum.APPLY_ID_NO.getKey(), tdeAcl.encrypt(applyIdNo));
                }
            }
        }
        //TODO GIS解析地址类型
        //orderConsign.setAddressType((byte) (addressType != null ? addressType.getCode() : AddressTypeEnum.JD_STANDARD.getCode()));
        return orderConsign;
    }

    /**
     * 围栏信息转换
     * @param fenceInfos
     * @return
     */
    private List<Order.FenceInfo> toFenceInfos(List<FenceInfo> fenceInfos) {
        if (CollectionUtils.isEmpty(fenceInfos)) {
            return null;
        }
        List<Order.FenceInfo> fenceInfoList = new ArrayList<>(fenceInfos.size());
        for (FenceInfo fenceInfo : fenceInfos) {
            Order.FenceInfo fence = new Order.FenceInfo();
            fence.setFenceId(fenceInfo.getFenceId());
            fence.setFenceType(fenceInfo.getFenceType());
            fenceInfoList.add(fence);
        }
        return fenceInfoList;
    }

    /**
     * 收发件人扩展信息
     *
     * @param consignorFacade
     * @param consigneeFacade
     * @return
     * @throws Exception
     */
    private Order.ConsignExtendInfo toConsignExtendInfo(ConsignorFacade consignorFacade, ConsigneeFacade consigneeFacade) throws Exception {
        Order.ConsignExtendInfo consignExtendInfo = new Order.ConsignExtendInfo();
        //发货人信息
        if (consignorFacade != null && consignorFacade.getAddress() != null) {
            //发货人地址
            AddressInfo consignorAddress = consignorFacade.getAddress();
            //GIS解析后的发货人详细地址
            consignExtendInfo.setConsignorAddressGis(tdeAcl.encrypt(consignorAddress.getAddressGis()));
            consignExtendInfo.setConsignorAddressGisQuery(tdeAcl.obtainKeyWordIndex(consignorAddress.getAddressGis()));
            consignExtendInfo.setConsignorPreciseGis(consignorAddress.getPreciseGis());
            consignExtendInfo.setConsignorChinaPostAddressCode(consignorAddress.getChinaPostAddressCode());
            consignExtendInfo.setPickupPlaceCode(consignorFacade.getPickupPlaceCode());
        }
        //收货人人信息
        if (consigneeFacade != null && consigneeFacade.getAddress() != null) {
            //收货人地址
            AddressInfo consigneeAddress = consigneeFacade.getAddress();
            consignExtendInfo.setConsigneeAddressGis(tdeAcl.encrypt(consigneeAddress.getAddressGis()));
            consignExtendInfo.setConsigneeAddressGisQuery(tdeAcl.obtainKeyWordIndex(consigneeAddress.getAddressGis()));
            consignExtendInfo.setConsigneeEmail(consigneeFacade.getConsigneeEmail());
            consignExtendInfo.setConsigneePreciseGis(consigneeAddress.getPreciseGis());
            consignExtendInfo.setConsigneeChinaPostAddressCode(consigneeAddress.getChinaPostAddressCode());
            consignExtendInfo.setDeliveryPlaceCode(consigneeFacade.getDeliveryPlaceCode());
        }
        return consignExtendInfo;
    }

    /**
     * 关联单-已经不用
     */
//    private List<Order.RelationInfo> toOrderRelation(List<RefOrderFacade> refOrderFacades) {
//        if (CollectionUtils.isEmpty(refOrderFacades)) {
//            return null;
//        }
//
//        List<Order.RelationInfo> list = new ArrayList<>(refOrderFacades.size());
//        refOrderFacades.forEach(refOrderInfo -> {
//            Order.RelationInfo relationInfo = new Order.RelationInfo();
//            //关联单号
//            relationInfo.setRefOrderNo(refOrderInfo.getRefOrderNo());
//            //关联单类型
//            relationInfo.setRefOrderType(refOrderInfo.getRefOrderType());
//            //关联单子类型
//            if (refOrderInfo.getRefOrderSubType() != null) {
//                relationInfo.setRefOrderSubType(refOrderInfo.getRefOrderSubType().byteValue());
//            }
//            relationInfo.setRemark(refOrderInfo.getRemark());
//            list.add(relationInfo);
//        });
//        return list;
//    }

    /**
     * 新字段持久化-关联单
     */
    private Order.RefOrderInfo toOrderRelationNew(CreateOrderFacadeRequest facadeRequest) {
        Order.RefOrderInfo refOrderInfo = new Order.RefOrderInfo();
        refOrderInfo.setExt(facadeRequest.getExtendRefOrder());

        if (CollectionUtils.isEmpty(facadeRequest.getRefOrders())) {
            return refOrderInfo;
        }

        List<RefOrderFacade> refOrderFacades = facadeRequest.getRefOrders();
        List<String> waybillNos = new ArrayList<>();
        List<String> reservationOrderNos = new ArrayList<>();
        List<String> purchaseOrderNos = new ArrayList<>();
        List<String> originalOrderNos = new ArrayList<>();
        List<String> discountRefOrderNos = new ArrayList<>();
        List<String> enquiryOrderNos = new ArrayList<>();

        //送取同步派送运单号
        List<String> deliveryWaybillNos = new ArrayList<>();
        //送取同步取件运单号
        List<String> pickupWaybillNos = new ArrayList<>();
        //送取同步派取件订单号
        List<String> pickupOrderNos = new ArrayList<>();
        //送取同步派派送订单号
        List<String> deliveryOrderNos = new ArrayList<>();
        //子单
        List<String> childOrderNos = new ArrayList<>();
        //集单号
        List<String> collectionOrderNos = new ArrayList<>();

        /**
         * 折扣关联单
         */
        if (facadeRequest.getPromotion() != null && StringUtils.isNotBlank(facadeRequest.getPromotion().getDiscountRefOrderNo())) {
            discountRefOrderNos.add(facadeRequest.getPromotion().getDiscountRefOrderNo());
        }
        /**
         * 如果有原订单号，绑定原运单号
         */
        if (facadeRequest.getOriginalNo() != null) {
            originalOrderNos.add(facadeRequest.getOriginalNo());
        }
        //扩展单
        Map<String, String> ext = refOrderInfo.getExt();
        if (null == ext) {
            ext = new HashMap<>();
        }
        /**
         * 遍历取出关联单
         */
        Map<String, String> finalExt = ext;
        refOrderFacades.forEach(refOrderFacade -> {

            //运单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY.getCode()) && refOrderFacade.getRefOrderSubType().equals(RefOrderSubType.DeliveryEnum.FORWARD_DELIVERY.getCode())) {
                waybillNos.add(refOrderFacade.getRefOrderNo());
            }

            //预约单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.WORK_ORDER.getCode()) && refOrderFacade.getRefOrderSubType().equals(RefOrderSubType.WorkOrderEnum.RESERVATION.getCode())) {
                reservationOrderNos.add(refOrderFacade.getRefOrderNo());
            }

            //采购单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.WORK_ORDER.getCode()) && refOrderFacade.getRefOrderSubType().equals(RefOrderSubType.WorkOrderEnum.INBOUND.getCode())) {
                purchaseOrderNos.add(refOrderFacade.getRefOrderNo());
            }
            //询价单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.ENQUIRY.getCode())) {
                enquiryOrderNos.add(refOrderFacade.getRefOrderNo());
            }

            //送取同步派送运单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY.getCode())) {
                deliveryWaybillNos.add(refOrderFacade.getRefOrderNo());
            }
            //送取同步取件运单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode())) {
                pickupWaybillNos.add(refOrderFacade.getRefOrderNo());
            }

            //送取同步-配送单订单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY_ORDER.getCode())) {
                deliveryOrderNos.add(refOrderFacade.getRefOrderNo());
            }

            //送取同步-取件单订单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode())) {
                pickupOrderNos.add(refOrderFacade.getRefOrderNo());
            }

            //子单
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.CHILD.getCode())) {
                childOrderNos.add(refOrderFacade.getRefOrderNo());
            }
            //集单号
            if (refOrderFacade.getRefOrderType().equals(RefOrderTypeEnum.COLLECTION_ORDER.getCode())) {
                collectionOrderNos.add(refOrderFacade.getRefOrderNo());
            }

            //扩展单据-支付单号
            if (RefOrderTypeEnum.PAY_ORDER.getCode().equals(refOrderFacade.getRefOrderType())) {
                finalExt.put(RefOrderExtendTypeEnum.PAY_ORDER_NO.getCode(), refOrderFacade.getRefOrderNo());
            }
        });

        refOrderInfo.setOriginalOrderNos(originalOrderNos);
        refOrderInfo.setWaybillNos(waybillNos);
        refOrderInfo.setReservationOrderNos(reservationOrderNos);
        refOrderInfo.setPurchaseOrderNos(purchaseOrderNos);
        refOrderInfo.setDiscountRefOrderNos(discountRefOrderNos);
        refOrderInfo.setEnquiryOrderNos(enquiryOrderNos);

        //送取同步派送运单号
        refOrderInfo.setDeliveryWaybillNos(deliveryWaybillNos);
        //送取同步取件运单号
        refOrderInfo.setPickupWaybillNos(pickupWaybillNos);
        refOrderInfo.setPickupOrderNos(pickupOrderNos);
        refOrderInfo.setDeliveryOrderNos(deliveryOrderNos);

        refOrderInfo.setChildOrderNos(childOrderNos);
        refOrderInfo.setCollectionOrderNos(collectionOrderNos);

        refOrderInfo.setExt(ext);
        return refOrderInfo;
    }


    /**
     * 物流服务产品信息
     */
    private List<Order.ServiceProductInfo> toServiceProductList(List<ProductFacade> productFacades) {
        if (CollectionUtils.isEmpty(productFacades)) {
            return null;
        }

        List<Order.ServiceProductInfo> list = new ArrayList<>(productFacades.size());

        for (ProductFacade productFacade : productFacades) {
            Order.ServiceProductInfo serviceProduct = new Order.ServiceProductInfo();
            serviceProduct.setProductNo(productFacade.getProductNo());
            serviceProduct.setProductName(productFacade.getProductName());
            serviceProduct.setProductType(productFacade.getProductType().byteValue());
            //降级前的产品编码
            serviceProduct.setPreDegradeProductNo(productFacade.getOriginalProductNo());
            //降级前的产品名称
            serviceProduct.setPreDegradeProductName(productFacade.getOriginalProductName());
            // 主产品编码
            serviceProduct.setParentNo(productFacade.getParentNo());
            //产品要素
            serviceProduct.setAttrs(productFacade.getProductAttrs());
            //TODO 扩展字段统一用MAP
            if (productFacade.getExtendProps() != null) {
                HashMap<String, String> ext = new HashMap<>();
                productFacade.getExtendProps().forEach((key, value) -> ext.put(key, value));
                serviceProduct.setExt(ext);
            }

            list.add(serviceProduct);
        }
        return list;
    }

    /**
     * 营销信息
     *
     * @param promotionFacade
     * @return
     */
    public List<Order.MarketInfo> toMarketInfos(PromotionFacade promotionFacade) {
        if (promotionFacade == null) {
            return null;
        }

        List<Order.MarketInfo> marketInfos = new ArrayList<>();
        //优惠券信息
        List<TicketFacade> ticketFacades = promotionFacade.getTickets();
        if (CollectionUtils.isNotEmpty(ticketFacades)) {
            for (TicketFacade ticketFacade : ticketFacades) {
                Order.MarketInfo marketInfo = new Order.MarketInfo();
                //营销类型：券
                marketInfo.setCouponSource(MarketEnum.COUPON.getCode());
                //优惠券编码
                marketInfo.setCouponNo(ticketFacade.getTicketNo());
                //优惠券类型
                marketInfo.setCouponType(String.valueOf(ticketFacade.getTicketType()));
                //优惠券类别
                marketInfo.setCouponCategory(ticketFacade.getTicketCategory());
                //优惠券文案描述
                marketInfo.setDescription(ticketFacade.getTicketDescription());
                //优惠金额
                if (ticketFacade.getTicketDiscountAmount() != null) {
                    marketInfo.setDiscountAmount(ticketFacade.getTicketDiscountAmount().getAmount());
                    if (ticketFacade.getTicketDiscountAmount().getCurrency() != null) {
                        marketInfo.setCurrency(ticketFacade.getTicketDiscountAmount().getCurrency().getCode());
                    }
                }
                //折扣率
                marketInfo.setDiscountRate(ticketFacade.getTicketDiscountRate());
                //折扣上限
                if (ticketFacade.getTicketDiscountUpperLimit() != null) {
                    marketInfo.setDiscountUpperLimit(ticketFacade.getTicketDiscountUpperLimit().getAmount());
                    if (ticketFacade.getTicketDiscountUpperLimit().getCurrency() != null) {
                        marketInfo.setCurrency(ticketFacade.getTicketDiscountUpperLimit().getCurrency().getCode());
                    }
                }
                //使用金额
                if (ticketFacade.getTicketUseAmount() != null) {
                    marketInfo.setUseAmount(ticketFacade.getTicketUseAmount().getAmount());
                    if (ticketFacade.getTicketUseAmount().getCurrency() != null) {
                        marketInfo.setCurrency(ticketFacade.getTicketUseAmount().getCurrency().getCode());
                    }
                }

                marketInfo.setCouponStatus(ticketFacade.getCouponStatus());
                //营销标识
                Map<String, String> promotionSign = new HashMap<>();
                if (ticketFacade.getTicketSource() != null) {
                    //优惠券来源
                    promotionSign.put(PromotionSignEnum.TICKET_SOURCE.getKey(), String.valueOf(ticketFacade.getTicketSource()));
                }
                marketInfo.setPromotionSign(promotionSign);
                //批次号
                marketInfo.setCouponBatchNo(ticketFacade.getTicketBatchNo());
                marketInfos.add(marketInfo);
            }
        }
        //折扣码信息
        List<DiscountFacade> discountFacades = promotionFacade.getDiscounts();
        if (CollectionUtils.isNotEmpty(discountFacades)) {
            for (DiscountFacade discountFacade : discountFacades) {
                Order.MarketInfo marketInfo = new Order.MarketInfo();
                //营销类型：折扣码
                marketInfo.setCouponSource(MarketEnum.DISCOUNT.getCode());
                //折扣码
                marketInfo.setCouponNo(discountFacade.getDiscountNo());
                //扩展信息
                marketInfo.setExt(discountFacade.getExtendProps());

                marketInfos.add(marketInfo);
            }
        }
        //营销活动信息
        List<ActivityFacade> activityFacades = promotionFacade.getActivities();
        if (CollectionUtils.isNotEmpty(activityFacades)) {
            for (ActivityFacade activityFacade : activityFacades) {
                Order.MarketInfo marketInfo = new Order.MarketInfo();
                //营销类型：活动信息
                marketInfo.setCouponSource(MarketEnum.ACTIVITY.getCode());
                //活动编码
                marketInfo.setCouponNo(activityFacade.getActivityNo());
                //活动名称
                marketInfo.setCouponName(activityFacade.getActivityName());
                //活动状态
                marketInfo.setCouponStatus(activityFacade.getActivityStatus());
                //活动内容
                marketInfo.setCouponValue(activityFacade.getActivityValue());

                marketInfos.add(marketInfo);
            }
        }

        return marketInfos;
    }

    /**
     * 主档扩展信息
     *
     * @param extendProps
     * @return
     */
    public Order.ExtendInfo toOrderExtend(Map<String, String> extendProps) throws Exception {
        Order.ExtendInfo orderExtend = new Order.ExtendInfo();
        orderExtend.setEnvMark(environmentCode);
        if (extendProps != null && !extendProps.isEmpty()) {
            HashMap<String, String> extMap = new HashMap<>(extendProps);
            orderExtend.setExt(extMap);
            // ext里特殊字段加密
            if (extMap.containsKey(AttachmentKeyEnum.CREATOR_PHONE.getKey())) {
                String creatorPhone = extMap.get(AttachmentKeyEnum.CREATOR_PHONE.getKey());
                extMap.put(AttachmentKeyEnum.CREATOR_PHONE.getKey(), tdeAcl.encrypt(creatorPhone));
            }
        }
        return orderExtend;
    }

    /**
     * 系统环境扩展信息
     *
     * @param extendProps
     * @return
     */
    private HashMap<String, String> toSysExt(Map<String, String> extendProps) {
        if (extendProps == null || extendProps.isEmpty()) {
            return null;
        }
        HashMap<String, String> sysExt = new HashMap<>();
        // 环境标识
        sysExt.put(ABConstants.ABENVIRONMENT_FLAG, extendProps.get(ABConstants.ABENVIRONMENT_FLAG));
        return sysExt;
    }

    /**
     * 解决方案信息
     *
     * @param facade
     * @return
     */
    public Order.Solution toSolution(BusinessSolutionFacade facade) {
        if (facade == null) {
            return null;
        }
        Order.Solution solution = new Order.Solution();
        //编码
        solution.setBusinessSolutionNo(facade.getBusinessSolutionNo());
        //名称
        solution.setBusinessSolutionName(facade.getBusinessSolutionName());
        //要素
        solution.setProductAttrs(facade.getProductAttrs());
        return solution;
    }

    /**
     * 接收信息转换节点信息
     *
     * @param consigneeFacade
     * @return
     */
    private List<Order.LogisticsNodes> toReceiveLogisticsNodes(ConsigneeFacade consigneeFacade) {
        List<Order.LogisticsNodes> list = null;
        if (consigneeFacade != null && consigneeFacade.getReceiveWarehouse() != null) {
            list = new ArrayList<>();
            Order.LogisticsNodes logisticsNodes = new Order.LogisticsNodes();
            logisticsNodes.setNodeNo(consigneeFacade.getReceiveWarehouse().getWarehouseNo());
            logisticsNodes.setNodeName(consigneeFacade.getReceiveWarehouse().getWarehouseName());
            logisticsNodes.setNodeType(consigneeFacade.getReceiveWarehouse().getWarehouseSource());
            //节点分类,1-仓库
            logisticsNodes.setNodeClassification(NodeClassIfcationEnum.WAREHOUSE.getCode());
            logisticsNodes.setNodeUsage(NodeUsageEnum.RECEIVE.getCode());//1、发货，2、收货
            list.add(logisticsNodes);
        }
        return list;
    }

    /**
     * @description: 发货仓编码及收货信息转物流节点
     * @Param
     * @return:
     * @author: JDL-技术与数据智能部-中台技术部-交易平台组
     * @date: 2022/4/13 11:10 上午
     */
    private List<Order.LogisticsNodes> toLogisticsNodes(ConsignorFacade consignorFacade, ConsigneeFacade consigneeFacade) {
        List<Order.LogisticsNodes> list = new ArrayList<>();
        if (consignorFacade != null && consignorFacade.getCustomerWarehouse() != null) {
            Order.LogisticsNodes logisticsNodes = new Order.LogisticsNodes();
            logisticsNodes.setNodeNo(consignorFacade.getCustomerWarehouse().getWarehouseNo());
            logisticsNodes.setNodeName(consignorFacade.getCustomerWarehouse().getWarehouseName());
            logisticsNodes.setNodeType(consignorFacade.getCustomerWarehouse().getWarehouseSource());
            // 京东仓库编码
            logisticsNodes.setActualNodeNo(consignorFacade.getCustomerWarehouse().getActualWarehouseNo());
            //节点分类,1-仓库
            logisticsNodes.setNodeClassification(NodeClassIfcationEnum.WAREHOUSE.getCode());
            logisticsNodes.setNodeUsage(NodeUsageEnum.SEND.getCode());//1、发货，2、收货
            list.add(logisticsNodes);
        }
        if (consigneeFacade != null && consigneeFacade.getReceiveWarehouse() != null) {
            Order.LogisticsNodes logisticsNodes = new Order.LogisticsNodes();
            logisticsNodes.setNodeNo(consigneeFacade.getReceiveWarehouse().getWarehouseNo());
            logisticsNodes.setNodeName(consigneeFacade.getReceiveWarehouse().getWarehouseName());
            logisticsNodes.setNodeType(consigneeFacade.getReceiveWarehouse().getWarehouseSource());
            // 京东仓库编码
            logisticsNodes.setActualNodeNo(consigneeFacade.getReceiveWarehouse().getActualWarehouseNo());
            //节点分类,1-仓库
            logisticsNodes.setNodeClassification(NodeClassIfcationEnum.WAREHOUSE.getCode());
            logisticsNodes.setNodeUsage(NodeUsageEnum.RECEIVE.getCode());//1、发货，2、收货
            list.add(logisticsNodes);
        }
        return list;
    }

    /**
     * 协议信息列表转换
     * @param agreementFacades
     * @return
     */
    private List<Order.AgreementInfo> toAgreementInfos(List<AgreementFacade> agreementFacades) {
        if (CollectionUtils.isEmpty(agreementFacades)) {
            return null;
        }

        return agreementFacades.stream().filter(Objects::nonNull).map(agreementFacade -> {
            Order.AgreementInfo agreementInfo = new Order.AgreementInfo();
            agreementInfo.setAgreementType(agreementFacade.getAgreementType());
            agreementInfo.setAgreementId(agreementFacade.getAgreementId());
            agreementInfo.setSigner(agreementFacade.getSigner());
            agreementInfo.setSigningTime(agreementFacade.getSigningTime());
            agreementInfo.setExtendProps(agreementFacade.getExtendProps());
            return agreementInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 履约信息转换
     * @param fulfillmentFacade
     * @return
     */
    private FulfillmentInfo toFulfillmentInfo(FulfillmentFacade fulfillmentFacade) {
        if(fulfillmentFacade == null) {
            return null;
        }
        FulfillmentInfo fulfillmentInfo = new FulfillmentInfo();
        fulfillmentInfo.setFulfillmentSign(fulfillmentFacade.getFulfillmentSign());
        fulfillmentInfo.setExtendProps(fulfillmentFacade.getExtendProps());
        return fulfillmentInfo;
    }

    /**
     * 跨境报关信息转换
     */
    private CustomsInfo toCustomsInfo(CustomsFacade customsFacade) {
        if (customsFacade == null) {
            return null;
        }
        return CustomsFacadeMapper.INSTANCE.toCustomsInfo(customsFacade);
    }

    /**
     * 附件列表转换
     */
    private List<Order.AttachmentInfo> toAttachmentInfos(List<AttachmentFacade> attachmentFacades) {
        if (CollectionUtils.isEmpty(attachmentFacades)) {
            return null;
        }
        return AttachmentFacadeMapper.INSTANCE.toAttachmentInfosOrder(attachmentFacades);
    }
}
