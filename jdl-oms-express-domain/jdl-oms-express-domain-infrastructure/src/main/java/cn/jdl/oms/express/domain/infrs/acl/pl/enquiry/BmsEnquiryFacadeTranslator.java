package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryModeEnum;
import cn.jdl.oms.express.domain.spec.dict.FenceTrustEnum;
import cn.jdl.oms.express.domain.spec.dict.FenceTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Fence;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.DEFAULT_ORDER_STATUS;

@Translator
public class BmsEnquiryFacadeTranslator {
    private static final Logger LOGGER = LoggerFactory.getLogger(BmsEnquiryFacadeTranslator.class);

    /**
     * MTD计费询价防腐层请求数据转换
     */
    public BillingEnquiryFacadeRequest toEnquiryFacadeRequest(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        // 业务身份
        facadeRequest.setBusinessUnit(EnquiryMapper.INSTANCE.toBusinessUnit(orderModel));
        // 青龙业主编码和青龙业主号名称
        facadeRequest.setCustomerFacadeDto(EnquiryMapper.INSTANCE.toCustomerFacadeDto(orderModel.getCustomer()));
        // 运单号
        facadeRequest.setRefOrderFacadeDto(EnquiryMapper.INSTANCE.toRefOrderFacadeDto(orderModel));
        // 产品信息  用传过来的订单号查出来有都赋值进去 (这个以前的是从外单查询出来的，添加OFC的包装耗材)
        List<Product> productList = orderModel.getProductDelegate().getProductList();
        facadeRequest.setProductFacadeDtoList(EnquiryMapper.INSTANCE.toProductFacadeDtos(productList));
        // 产品信息额外处理 FIXME: jiangwai 后续考虑将特殊逻辑下线
        Product mainProduct = (Product) orderModel.getProductDelegate().getMainProduct();
        String majorProductNo = mainProduct.getProductNo();
        // 判断是否为 特快重货 or 特惠重货
        boolean changeInsuredValue = ProductEnum.TKZH.getCode().equals(majorProductNo)
                                        || ProductEnum.THZH.getCode().equals(majorProductNo);
        // 获取包装耗材产品编码
        List<String> packageServiceCode = AddOnProductEnum.getPackageServiceCode();
        Iterator<BillingEnquiryFacadeRequest.ProductFacadeDto> iterator = facadeRequest.getProductFacadeDtoList().iterator();
        while (iterator.hasNext()) {
            BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = iterator.next();
            // 过滤掉产品要素为空的包装服务
            if (packageServiceCode.contains(productFacadeDto.getProductNo())) {
                if (MapUtils.isEmpty(productFacadeDto.getProductAttrs())) {
                    iterator.remove();
                }
            }
            /*
              必要时将保价产品替换成特快重货-保价 fr-a-0016
              2023-05-31 与快运产品王传宇、林恒波确认，主产品是fr-m-0004（特快重货）+fr-m-0002（特惠重货）时，调计费标准产品询价接口fr-a-0002（普通保价），需要转化成fr-a-0016（特快重货保价）
              背景：业务及运营系统，目前保价只识别了fr-a-0002 （普通保价），下单也只会下一个码，但计费是根据两个保价码分别计费的。短期方案，订单中心调计费根据主产品去做码的转换，长期方案，需要王传宇和林恒波 推动业务及运营系统按标准的两个码执行
             */
            if (changeInsuredValue && AddOnProductEnum.INSURED_VALUE_TOB.getCode().equals(productFacadeDto.getProductNo())) {
                productFacadeDto.setProductNo(AddOnProductEnum.FR_A_0016.getCode());
            }

            // 主产品如果=特惠专配fr-m-0017，则需要给计费传fencetype=F20对应的围栏fenceId，放在产品要素里 @haoxiaoxiao、@huokaichao
            if (ProductEnum.THPH.getCode().equals(productFacadeDto.getProductNo())) {
                Address consignorAddr = orderModel.getConsignor().getAddress();
                if (null != consignorAddr && CollectionUtils.isNotEmpty(consignorAddr.getFenceInfos())) {
                    for (Fence fenceInfo : consignorAddr.getFenceInfos()) {
                        if (FenceTypeEnum.F20.name().equals(fenceInfo.getFenceType())) {
                            if (null == productFacadeDto.getProductAttrs()) {
                                productFacadeDto.setProductAttrs(new HashMap<>());
                            }
                            productFacadeDto.getProductAttrs().put(EnquiryConstants.FENCE_ID, fenceInfo.getFenceId());
                            break;
                        }
                    }
                    // TODO liujiangwai 业务告警
                }
            }
        }
        // 总重量、总体积
        facadeRequest.setCargoFacadeDto(EnquiryMapper.INSTANCE.toCargoFacadeDto(orderModel, null));
        // 发件人信息
        facadeRequest.setConsignorFacadeDto(EnquiryMapper.INSTANCE.toConsignorFacadeDto(orderModel.getConsignor()));
        // 收件人信息
        facadeRequest.setConsigneeFacadeDto(EnquiryMapper.INSTANCE.toConsigneeFacadeDto(orderModel.getConsignee()));
        // 财务相关信息  结算方式 时间用自己的
        facadeRequest.setFinanceFacadeDto(EnquiryMapper.INSTANCE.toFinanceFacadeDto(orderModel.getFinance()));
        // 设置拓展字段
        facadeRequest.setExtendProps(toExtendProps(orderModel));
        // 询价类型
        facadeRequest.setInquiryType(4);
        return facadeRequest;
    }

    /**
     * 计费询价扩展字段
     * @param orderModel
     * @return
     */
    private Map<String, Object> toExtendProps(ExpressOrderModel orderModel) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, EnquiryMapper.INSTANCE.toSettlementType(orderModel));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS, DEFAULT_ORDER_STATUS);
        // 仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, EnquiryMapper.INSTANCE.toDistributionType(orderModel));
        //温层
        if (null != orderModel.getShipment().getWarmLayer()) {
            extendParam.put(EnquiryConstants.WARM_LAYER, orderModel.getShipment().getWarmLayer());
        }
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.ENQUIRY_EXTEND_INQUIRY_TYPE_SWITCH)) {
            LOGGER.info("调计费询价，传入extendParam:inquiryType,开关开启");
            extendParamToInquiryType(extendParam, orderModel);
        } else {
            LOGGER.info("调计费询价，传入extendParam:inquiryType,开关关闭");
        }
        //跨境报关信息：跨境业务类型、是否文件、币种（收发管家不涉及）
        extendParamToCustoms(extendParam, orderModel);
        //包裹信息：取询价参数的扩展信息，传enquiryOrderModel （收发管家不涉及）
        // extendParamToPackageInformation(extendParam, orderModel);
        return extendParam;
    }

    /**
     * 询价场景扩展字段：询价类型
     */
    private void extendParamToInquiryType(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        if (orderModel == null || orderModel.getEnquiry() == null || orderModel.getEnquiry().getEnquiryMode() == null) {
            return;
        }
        EnquiryModeEnum enquiryMode = orderModel.getEnquiry().getEnquiryMode();
        extendParam.put(EnquiryConstants.INQUIRY_TYPE, String.valueOf(enquiryMode.getCode()));
    }


    /**
     * 询价场景扩展字段：跨境报关信息
     * 跨境业务类型、是否文件、币种
     */
    private void extendParamToCustoms(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        Customs customs = orderModel.getCustoms();
        if (customs == null) {
            return;
        }
        // 跨境业务类型
        if (customs.getStartFlowDirection() != null && customs.getEndFlowDirection() != null) {
            // 格式：始发站点_目的站点
            String crossBorderType = customs.getStartFlowDirection().name() + "_" + customs.getEndFlowDirection().name();
            extendParam.put(EnquiryConstants.CROSS_BORDER_TYPE, crossBorderType);
        }
        // 是否文件
        if (customs.getFileTag() != null) {
            extendParam.put(EnquiryConstants.FILE_TAG, customs.getFileTag());
        }
        // 币种
        CurrencyCodeEnum currencyCodeEnum = GetFieldUtils.getCurrencyCodeEnum(customs, GetFieldUtils.getSettlementType(orderModel));
        if (currencyCodeEnum != null) {
            extendParam.put(EnquiryConstants.EXCHANGE_CURRENCY, String.valueOf(currencyCodeEnum.getCode()));
        }
    }
}
