package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.address.AddressBasicPrimaryWSFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ModifyRecordTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.spec.util.ReceiptCurrencyUtil;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.lbs.product.inquiry.dto.request.ForwardInquiryRequest;
import com.jd.lbs.product.inquiry.dto.request.ProductParam;
import com.jd.lbs.product.inquiry.dto.request.StandardProductDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.CALC_PRICE_ITEM_LIST;

/**
 * @ProjectName：jdl-oms-express-shared-common
 * @Package： cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.o2o
 * @ClassName: B2CReverseEnquiryFacadeTranslator
 * @Description: B2C 接单时 逆向单询价facade转换器
 * @Author： xinghuanjie
 * @CreateDate 2021-06-30 16:41
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Translator
public class B2CCreateEnquiryFacadeTranslator {
    private static final Logger LOGGER = LoggerFactory.getLogger(B2CCreateEnquiryFacadeTranslator.class);
    //单据状态 默认传0
    private static final int DEFAULT_ORDER_STATUS = 0;
    // 交付模式
    private static final String DELIVERY_PATTERN = "deliveryPattern";
    // 交付模式 为 1京仓发货
    private static final String DELIVERY_PATTERN_ONE = "1";
    //交付模式 为 2
    private static final String DELIVERY_PATTERN_TWO = "2";
    // 拓展字段
    private static final String SHIPMENT_EXTEND_PROPS ="shipmentExtendProps";
    //eclp
    private static final String ECLP ="eclp";

    /**
     * ucc配置
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * @description 功能描述: B2C 逆向单询价查询青龙基础资料获取起始省市县facade
     * <AUTHOR>
     * @date 2021/7/1 9:33
     * @param
     * @throws
     * @return
     */
    @Resource
    private AddressBasicPrimaryWSFacade addressBasicPrimaryWSFacade;

    /**
     * @description 功能描述: B2C 逆向单询价查询青龙基础资料获取起始省市县facade转换器
     * <AUTHOR>
     * @date 2021/7/1 9:33
     * @param
     * @throws
     * @return
     */
    @Resource
    private AddressBasicPrimaryWSFacadeTranslator addressBasicPrimaryWSFacadeTranslator;

    @Resource
    private EnquiryFacadeTranslator enquiryFacadeTranslator;

    /**
     * @description 功能描述: 逆向单询价转换器
     * <AUTHOR>
     * @date 2021/6/30 20:15
     * @param expressOrderContext 原单, reverseOrderModel 当前单
     * @throws
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest
     */
    public BillingEnquiryFacadeRequest toReverseBillingEnquiryFacadeRequest(ExpressOrderContext expressOrderContext, ExpressOrderModel reverseOrderModel) {
        //经胡智阳与计费确认，单次逆向和多次逆向给计费的扩展信息中的订单状态orderStatus都传0（单据状态 默认传0），不再做特殊区分处理
        BillingEnquiryFacadeRequest billingEnquiryFacadeRequest = toBillingEnquiryFacadeRequest(expressOrderContext);

        // 后款的（paymentStage=2）且结算方式为现结的改址新单下发成功后，对改址新单进行询价
        // 结算方式非月结：计费重量和计费体积优先取原单复核重量体积，若原单没有复核重量体积，从下单重量、体积获取后调计费询价接口；

        // 改址单、后款订单、到付现结
        if (this.changeAddressAndCashonDelivery(expressOrderContext.getOrderModel())) {
            // 计费重量和计费体积优先取原单复核重量体积，若原单没有复核重量体积，从下单重量、体积获取后调计费询价接口
            // 此处与孙志宇确认 只有原单复核重量体积值都有值的情况下才使用原单的值
            if (null != reverseOrderModel && null != reverseOrderModel.getRecheckWeight() && null != reverseOrderModel.getRecheckVolume() &&
                null != reverseOrderModel.getRecheckWeight().getValue() && null != reverseOrderModel.getRecheckVolume().getValue()) {
                BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = billingEnquiryFacadeRequest.getCargoFacadeDto();
                // 原单复核体积
                cargoFacadeDto.setTotalCargoVolume(reverseOrderModel.getRecheckVolume().getValue());
                // 原单复核重量
                cargoFacadeDto.setTotalCargoWeight(reverseOrderModel.getRecheckWeight().getValue());
            }
        }


        return billingEnquiryFacadeRequest;
    }

    /**
     * 计费询价防腐层请求数据转换
     */
    public BillingEnquiryFacadeRequest toBillingEnquiryFacadeRequest(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        return generateBillingEnquiryFacadeRequest(orderModel);
    }

    public BillingEnquiryFacadeRequest generateBillingEnquiryFacadeRequest(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        facadeRequest.setOrderNo(orderModel.orderNo());
        //青龙业主编码和青龙业主号名称
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(orderModel));
        //关联单号，正向运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        //客户、渠道
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));
        if (OrderTypeEnum.READDRESS == orderModel.getOrderType()) {
            // 改址单只询白名单中的增值产品 目前为改址增值服务
            facadeRequest.setProductFacadeDtoList(toReaddressProdcutFacadeDto(orderModel));
            // 货品信息 总重量、总体积、
            facadeRequest.setCargoFacadeDto(toReaddressCargoFacadeDto(orderModel));
        } else {
            // 产品信息  用传过来的订单号查出来有都赋值进去
            facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel));
            // 货品信息 总重量、总体积、
            facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel));
        }
        if (OrderTypeEnum.DELIVERY == orderModel.getOrderType()) {
            //发件人信息
            facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(orderModel));
        } else {
            //发件人信息
            facadeRequest.setConsignorFacadeDto(toReverseConsignorFacadeDto(orderModel));
        }

        //收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel));
        //财务相关信息
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel));
        //设置拓展字段
        facadeRequest.setExtendProps(toExtendProps(orderModel));

        // 跨城急送 询价参数转换
        Map<String, Object> extendParam = facadeRequest.getExtendProps();
        if (null == extendParam) {
            extendParam = new HashMap<>();
            facadeRequest.setExtendProps(extendParam);
        }
        String majorProductNo = orderModel.getProductDelegate().getMajorProductNo();
        if (ProductEnum.KCJS.getCode().equals(majorProductNo) || ProductEnum.TSSTC.getCode().equals(majorProductNo)) {
            facadeRequest.setWaiFa(true);
            ForwardInquiryRequest forwardInquiryFacadeRequest = enquiryFacadeTranslator.toForwardInquiryFacadeRequest(orderModel);
            extendParam.put(EnquiryConstants.WAI_FA, JSONUtils.beanToJSONDefault(forwardInquiryFacadeRequest));
        }
        facadeRequest.setWaiFa(true);
        return facadeRequest;
    }

    /**
     * @description 功能描述: B2C 添加拓展字段
     * <AUTHOR>
     * @date 2021/6/29 12:27
     * @param orderModel
     * @throws
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String, Object> toExtendProps(ExpressOrderModel orderModel) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(orderModel));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS,DEFAULT_ORDER_STATUS);
        // 仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, toDistributionType(orderModel));
        //跨境出口
        if(orderModel.isHKMO() || orderModel.isIntl()){
            if(null != orderModel.getConsignor()
                    && null != orderModel.getConsignor().getAddress()
                    && StringUtils.isNotBlank(orderModel.getConsignor().getAddress().getRegionNo())
                    && null != orderModel.getConsignee()
                    && null != orderModel.getConsignee().getAddress()
                    && StringUtils.isNotBlank(orderModel.getConsignee().getAddress().getRegionNo())
            ){
                String startRegionNo = orderModel.getConsignor().getAddress().getRegionNo();
                String endRegionNo = orderModel.getConsignee().getAddress().getRegionNo();
                //跨境类型
                extendParam.put(EnquiryConstants.CROSS_BORDER_TYPE,startRegionNo + "_" + endRegionNo);
                if(null != orderModel.getFinance() && null != orderModel.getFinance().getSettlementType()){
                    //换汇币种
                    extendParam.put(EnquiryConstants.EXCHANGE_CURRENCY,
                            ReceiptCurrencyUtil.getCurrency(startRegionNo,endRegionNo,orderModel.getFinance().getSettlementType().getCode()).getCode());
                }
            }
            if(null != orderModel.getCustoms()){
                //是否是文件
                extendParam.put(EnquiryConstants.FILE_TAG,orderModel.getCustoms().getFileTag());
            }
        }

        // 改址拦截模式要素 todo 后续C2C一单到底需要关注，改址模式后续是否需要调整
        List<ModifyRecord> enabledThroughOrderModifyRecords = orderModel.getModifyRecordDelegate().getEnabledThroughOrderModifyRecords();
        // [正向单]存在改址记录则按拦截地模式询价
        if(OrderTypeEnum.DELIVERY == orderModel.getOrderType() && CollectionUtils.isNotEmpty(enabledThroughOrderModifyRecords)){
            LOGGER.info("存在有效改址记录:{}，按了拦截地模式询价",enabledThroughOrderModifyRecords.size());
            // 改址模式
            // intercept1order2end
            // 改址模式
            String readdressMode;
            int size = enabledThroughOrderModifyRecords.size();
            List<ModifyRecord> interceptModifyRecords = enabledThroughOrderModifyRecords.stream().filter(modifyRecord -> {
                return ModifyRecordTypeEnum.INTERCEPT.getCode().equals(modifyRecord.getModifyRecordType());
            }).collect(Collectors.toList());
            // ModifyRecordTypeEnum.REJECT 不处理：拒收一单到底不涉及接单询价

            if(2 == size && CollectionUtils.isNotEmpty(interceptModifyRecords)){
                //数量为2且有拦截-仅拦截
                readdressMode = EnquiryConstants.INTERCEPT_THROUGH_ORDER;
            } else if(2 < size && CollectionUtils.isNotEmpty(interceptModifyRecords)){
                //数量>2且有拦截，改址后拦截
                readdressMode = EnquiryConstants.THROUGH_ORDER;
            } else {
                //仅改址
                readdressMode = EnquiryConstants.READDRESS_THROUGH_ORDER;
            }
            // 改址模式
            extendParam.put(EnquiryConstants.READDRESS_MODE, readdressMode);
            // DETAIL_LIST
            List<StandardProductDetail> productDetails = (List<StandardProductDetail>) extendParam.get(EnquiryConstants.DETAIL_LIST);
            if (null == productDetails) {
                productDetails = new ArrayList<>();
            }
            // 遍历改址修改记录，获取改址详情
            for (ModifyRecord modifyRecord : enabledThroughOrderModifyRecords) {
                if(0 != modifyRecord.getModifyRecordSequence()){
                    StandardProductDetail standardProductDetail = new StandardProductDetail();
                    ReaddressRecordDetailInfo modifyRecordDetail = (ReaddressRecordDetailInfo) modifyRecord.getModifyRecordDetail();
                    standardProductDetail.setDetailNo(String.valueOf(modifyRecordDetail.getRecordSequence()));
                    standardProductDetail.setProductParams(Collections.singletonList(toReaddressProductParam(modifyRecordDetail, modifyRecord.getModifyRecordType())));
                    productDetails.add(standardProductDetail);
                }
            }
            extendParam.put(EnquiryConstants.DETAIL_LIST, productDetails);
        }
        return extendParam;
    }


    /**
     * 根据改址修改记录，转化为计费系统的 ProductParam
     * @param modifyRecordDetail
     * @return ProductParam
     */
    private ProductParam toReaddressProductParam(ReaddressRecordDetailInfo modifyRecordDetail, String modifyRecordType) {
        ProductParam productParam = new ProductParam();
        modifyRecordDetail.getProductInfos()
                .stream()
                .filter(product -> AddOnProductEnum.READDRESS.getCode().equals(product.getProductNo()))
                .findFirst()
                .ifPresent(productInfo -> {
                    productParam.setProductCode(productInfo.getProductNo());
                    Map<String, String> productElement = new HashMap<>();
                    if (MapUtils.isNotEmpty(productInfo.getProductAttrs())) {
                        productElement.putAll(productInfo.getProductAttrs());
                    }
                    productElement.put(EnquiryConstants.RECORD_TYPE, modifyRecordType);
                    productParam.setProductElement(productElement);
                });
        return productParam;
    }


    /**
     * @description 功能描述:  仓配类型(0：纯配；1：仓配)
     *  接单字段deliveryPattern若为1京仓发货 则该字段赋值1
     *  若为2纯配则该字段赋值0；
     *  接单字段deliveryPattern若为空且systemSubCaller为eclp则该字段赋值为1，
     *  其他情况全部赋值0
     * <AUTHOR>
     * @date 2021/6/29 18:04
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    private Object toDistributionType(ExpressOrderModel orderModel) {
        // 配送的拓展字段 deliveryPattern 从这里获取
        Map<String, String> extendProps = orderModel.getShipment().getExtendProps();
        if(MapUtils.isNotEmpty(extendProps)){
            String shipmentExtendProps = extendProps.get(SHIPMENT_EXTEND_PROPS);
            if (shipmentExtendProps != null){
                Map map = JSONUtils.jsonToMap(shipmentExtendProps);
                if(MapUtils.isNotEmpty(map)){
                    String deliveryPattern = (String)map.get(DELIVERY_PATTERN);
                    if (DELIVERY_PATTERN_ONE.equals(deliveryPattern)){
                        return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                    }else if (DELIVERY_PATTERN_TWO.equals(deliveryPattern)){
                        return EnquiryConstants.DISTRIBUTION_PURE;
                    }else if (deliveryPattern == null && ECLP.equals(orderModel.getChannel().getSystemSubCaller())){
                        return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                    }
                }
            }
        }
        return EnquiryConstants.DISTRIBUTION_PURE;

    }


    /**
     * @description 功能描述: B2C拓展信息之结算方式
     * <AUTHOR>
     * @date 2021/6/29 12:33
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    private Object extendParamToSettlementType(ExpressOrderModel orderModel) {
        if (SettlementTypeEnum.CASH_ON_PICK == orderModel.getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_CASH_ON_PICK;
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY ==  orderModel.getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_CASH_ON_DELIVERY;
        } else if (SettlementTypeEnum.MONTHLY_PAYMENT ==  orderModel.getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_MONTHLY_PAYMENT;
        }
        return null;
    }

    /**
     * 补全计费结果信息
     */
    public void complementBillingResult(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();

        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getAmount());
        preAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getCurrencyCode());
        financeInfoDto.setPreAmount(preAmount);

        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
        discountAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
        financeInfoDto.setDiscountAmount(discountAmount);

        //计费重量
        financeInfoDto.setBillingWeight(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingVolume());

        //计费类型
        financeInfoDto.setBillingMode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingMode());

        //积分信息
        financeInfoDto.setPointsInfoDto(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPointsInfoDto());

        //费用明细
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : billingEnquiryFacadeResponse.getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
            FinanceDetailInfoDto detailInfoDto = new FinanceDetailInfoDto();
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount().getAmount());
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount().getCurrencyCode());
            detailInfoDto.setPreAmount(detailPreAmount);
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount().getAmount());
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount().getCurrencyCode());
            detailInfoDto.setDiscountAmount(detailDiscountAmount);
            detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            detailInfoDto.setCostName(detailFacadeDto.getCostName());
            detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            detailInfoDto.setProductName(detailFacadeDto.getProductName());
            detailInfoDto.setRemark(detailFacadeDto.getRemark());
            //折扣明细
            if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
                for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
                    discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());
                    Money money = new Money();
                    money.setAmount(discountInfoFacadeDto.getDiscountedAmount().getAmount());
                    money.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
                    discountInfoDto.setDiscountedAmount(money);
                    discountInfoDtos.add(discountInfoDto);
                }
                detailInfoDto.setDiscountInfoDtos(discountInfoDtos);
            }
            detailInfoDto.setExtendProps(new HashMap<>());
            // 价格项明细
            if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
                detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
            }
            financeDetailInfoDtoList.add(detailInfoDto);
        }
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
        // 收款机构
        financeInfoDto.setCollectionOrgNo(billingEnquiryFacadeResponse.getFinanceFacadeDto().getCollectionOrgNo());
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

    private BillingEnquiryFacadeRequest.CustomerFacadeDto toCustomerFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = new BillingEnquiryFacadeRequest.CustomerFacadeDto();
        Customer customer = orderModel.getCustomer();
        customerFacadeDto.setAccountNo(customer.getAccountNo());
        return customerFacadeDto;
    }

    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toReaddressProdcutFacadeDto(ExpressOrderModel orderModel) {
        //产品信息
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        // 改址单只询白名单中的增值产品 目前为改址增值服务
        List<? extends IProduct> products = orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isEmpty(products)) {
            return productFacadeDtos;
        }
        for (IProduct product : products) {
            if (ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode().equals(product.getProductType())
                    && !expressUccConfigCenter.isReaddressEnquiryAddOnProductWhite(product.getProductNo())) {
                //如果是增值服务，且不在白名单中，则不询价
                continue;
            }

            // 国补-激活校验 https://joyspace.jd.com/pages/G0hyCRMoGagEM96u9gtC
            if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.GB_B2C_ENQUIRY_CULL_SWITCH)
                    && AddOnProductEnum.ACTIVATION_CHECK.getCode().equals(product.getProductNo())
                    && MapUtils.isNotEmpty(product.getProductAttrs())// 产品要素包含结算编码
                    && product.getProductAttrs().containsKey(AddOnProductAttrEnum.ACTIVATION_CHECK_SETTLEMENT_ACCOUNT.getCode())
            ) {
                continue;
            }

            //如果产品编码在配置中，则需要计费
            BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
            productFacadeDto.setProductNo(product.getProductNo());
            productFacadeDto.setProductType(product.getProductType());
            productFacadeDto.setParentNo(product.getParentNo());
            productFacadeDto.setProductAttrs(product.getProductAttrs());
            productFacadeDtos.add(productFacadeDto);
        }
        return productFacadeDtos;
    }

    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel orderModel) {
        //冷链B2C逆向单合并支付原单增值服务只计算保价和包装耗材
        if(BusinessUnitEnum.CN_JDL_CC_B2C.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessUnit()) &&
                OrderTypeEnum.DELIVERY == orderModel.getOrderType()){
            List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
            // 原来单的增值服务
            List<? extends IProduct> products = orderModel.getProductDelegate().getProducts();
            if (CollectionUtils.isNotEmpty(products)) {
                for (IProduct product : products) {
                    //保价的
                    if (AddOnProductEnum.getInsuredValueCode().contains(product.getProductNo()) ||
                            (AddOnProductEnum.getPackageServiceCode().contains(product.getProductNo()) &&
                                    MapUtils.isNotEmpty(product.getProductAttrs())) ||
                            ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(product.getProductType())){
                        BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                        productFacadeDto.setProductNo(product.getProductNo());
                        productFacadeDto.setProductType(product.getProductType());
                        productFacadeDto.setParentNo(product.getParentNo());
                        productFacadeDto.setProductAttrs(product.getProductAttrs());
                        productFacadeDtos.add(productFacadeDto);
                    }
                }
            }
            return productFacadeDtos;
        }
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        // 原来单的增值服务
        List<Product> products = orderModel.getProductDelegate().getProductList();
        if (CollectionUtils.isNotEmpty(products)) {
            for (Product product : products) {
                if (AddOnProductEnum.b2cSkipEnquiry(product.getProductNo())) {
                    // 代收货款/协商再投/微笑面单 不询价
                    continue;
                }

                // 国补-激活校验 https://joyspace.jd.com/pages/G0hyCRMoGagEM96u9gtC
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.GB_B2C_ENQUIRY_CULL_SWITCH)
                        && AddOnProductEnum.ACTIVATION_CHECK.getCode().equals(product.getProductNo())
                        && MapUtils.isNotEmpty(product.getProductAttrs())// 产品要素包含结算编码
                        && product.getProductAttrs().containsKey(AddOnProductAttrEnum.ACTIVATION_CHECK_SETTLEMENT_ACCOUNT.getCode())
                ) {
                    continue;
                }

                //包装耗材增值产品如果不存在产品要素,询价过滤掉
                if (AddOnProductEnum.getPackageServiceCode().contains(product.getProductNo())) {
                    if (MapUtils.isEmpty(product.getProductAttrs())) {
                        continue;
                    }
                }
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }
        //todo 补全附加服务费
        if(CollectionUtils.isNotEmpty(orderModel.getFinance().getAttachFees())){
            for (CostInfo costInfo : orderModel.getFinance().getAttachFees()) {
                BillingEnquiryFacadeRequest.ProductFacadeDto attachFeeFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                attachFeeFacadeDto.setProductNo(costInfo.getCostNo());
                attachFeeFacadeDto.setAttachFees(Boolean.TRUE);
                attachFeeFacadeDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                attachFeeAddSpecialProductAttrs(costInfo, attachFeeFacadeDto);
                productFacadeDtos.add(attachFeeFacadeDto);
            }
        }

        return productFacadeDtos;
    }

    /**
     * 客户、渠道
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ChannelFacadeDto toChannelFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ChannelFacadeDto channelFacadeDto = new BillingEnquiryFacadeRequest.ChannelFacadeDto();
        Channel channel = orderModel.getChannel();
        channelFacadeDto.setChannelNo(channel.getChannelNo());
        return channelFacadeDto;
    }



    /**
     * 收件人信息 传过来的orderModel
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();

        Consignee consignee = orderModel.getConsignee();

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        Address address = consignee.getAddress();
        if (address != null) {
            //收件人省
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setProvinceNo(address.getProvinceNo());
            // 收件人市
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCityNo(address.getCityNo());
            //收件人县
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
            addressFacadeDto.setCountyNo(address.getCountyNo());

        }
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toReaddressCargoFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();

        // 原单信息
        ExpressOrderModel originOrderModel = orderModel.getOrderSnapshot();
        Optional<BigDecimal> recheckWeight = Optional.ofNullable(originOrderModel)
                .map(ExpressOrderModel::getRecheckWeight)
                .map(Weight::getValue);
        Optional<BigDecimal> recheckVolume = Optional.ofNullable(originOrderModel)
                .map(ExpressOrderModel::getRecheckVolume)
                .map(Volume::getValue);
        if (recheckWeight.isPresent() && recheckVolume.isPresent()) {
            // 原单复核体积
            cargoFacadeDto.setTotalCargoVolume(recheckVolume.get());
            // 原单复核重量
            cargoFacadeDto.setTotalCargoWeight(recheckWeight.get());
        } else {
            // 货品信息里的计算总体积
            cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
            // 货品信息里的计算总重量
            cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
        }

        // 计费数量
        cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());

        return cargoFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        // 计费数量
        cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());

        // 货品信息里的计算总体积
        if (null != orderModel.getRecheckVolume() && null != orderModel.getRecheckVolume().getValue()) {
            cargoFacadeDto.setTotalCargoVolume(orderModel.getRecheckVolume().getValue());
        } else {
            cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
        }

        // 货品信息里的计算总重量
        if (null != orderModel.getRecheckWeight() && null != orderModel.getRecheckWeight().getValue()) {
            cargoFacadeDto.setTotalCargoWeight(orderModel.getRecheckWeight().getValue());
        } else {
            cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
        }

        return cargoFacadeDto;
    }
    /**
     * 财务相关信息
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        // b2C的是用接单时间
        financeFacadeDto.setEnquireTime(orderModel.getOperateTime());
        //结算方式
        financeFacadeDto.setSettlementType(orderModel.getFinance().getSettlementType());

        // 抵扣信息 只有逆向
        // 下了运费保，抵扣编码是OFC传过来的，所以抵扣信息在当前订单的fiance信息
        if (null != orderModel.getFinance().getDeductionDelegate()) {
            financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) orderModel.getFinance().getDeductionDelegate().getDeductions()));
        }
        return financeFacadeDto;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductions
     * @return
     */
    private List<DeductionInfoDto> toDeductionInfoDtos(List<Deduction> deductions) {
        if (CollectionUtils.isEmpty(deductions)) {
            return null;
        }
        List<DeductionInfoDto> deductionInfoDtos = new ArrayList<>(deductions.size());
        deductions.forEach(deduction -> {
            if (deduction != null) {
                DeductionInfoDto dto = new DeductionInfoDto();
                //抵扣编码
                dto.setDeductionNo(deduction.getDeductionNo());
                // 抵扣金额
                dto.setDeductionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(deduction.getDeductionAmount()));
                //扩展信息
                dto.setExtendProps(deduction.getExtendProps());
                deductionInfoDtos.add(dto);
            }
        });
        return deductionInfoDtos;
    }

    /**
     * 通用收件人信息转换逻辑
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toConsignorFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        // 1. 优先用揽收站点获取四级地址
        String stationNo = null;
        if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()) {
            stationNo = getReturnEndStationNo(orderModel);
        } else {
            stationNo = orderModel.getShipment().getStartStationNo();
        }
        Address address = null;
        if (StringUtils.isNotEmpty(stationNo) && StringUtils.isNumeric(stationNo)) {
            Integer stationId = Integer.valueOf(stationNo);
            address = addressBasicPrimaryWSFacade.getAddressBySiteId(stationId);
        }
        // 2. 如果揽收站点获取不到，则用发件人地址
        if (null == address) {
            if (null == orderModel.getConsignor() || null == orderModel.getConsignor().getAddress()) {
                return consignorFacadeDto;
            }
            address = orderModel.getConsignor().getAddress();
        }

//        // 补齐询价场景能力
//        if (StringUtils.isNotBlank(orderModel.getEnquiry().getEnquiryStartCityNo())){
//            address.setCityNoGis(orderModel.getEnquiry().getEnquiryStartCityNo());
//        }

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        // 发件人一级地址
        addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
        addressFacadeDto.setProvinceNo(address.getProvinceNo());
        // 发件人二级地址
        addressFacadeDto.setCityNoGis(address.getCityNoGis());
        addressFacadeDto.setCityNo(address.getCityNo());
        // 起始县
        addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        addressFacadeDto.setCountyNo(address.getCountyNo());
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 逆向单询价，根据终端揽收时传的站点ID，调青龙基础资料 获取cityId countryId provinceId
     * 如果是改址单，不需要查青龙基础资料，因此不用此方法
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toReverseConsignorFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();

        // 逆向单询价和寄付现结一样 根据终端揽收时传的站点ID，调青龙基础资料 获取cityId countryId provinceId
        // 输入揽收站点id
        String stationNo = getReturnEndStationNo(orderModel);

        AddressBasicPrimaryWSFacadeRequest addressBasicPrimaryWSFacadeRequest = addressBasicPrimaryWSFacadeTranslator.toAddressBasicPrimaryWSFacadeRequest(stationNo);

        AddressBasicPrimaryWSFacadeResponse addressBasicPrimaryWSFacadeResponse = addressBasicPrimaryWSFacade.getBaseSiteBySiteId(addressBasicPrimaryWSFacadeRequest.getSiteId());
        //起始省
        addressFacadeDto.setProvinceNoGis(addressBasicPrimaryWSFacadeResponse.getProvinceId());
        addressFacadeDto.setProvinceNo(addressBasicPrimaryWSFacadeResponse.getProvinceId());
        // 起始市
        addressFacadeDto.setCityNoGis(addressBasicPrimaryWSFacadeResponse.getCityId());
        addressFacadeDto.setCityNo(addressBasicPrimaryWSFacadeResponse.getCityId());
        //起始县
        addressFacadeDto.setCountyNoGis(addressBasicPrimaryWSFacadeResponse.getCountryId());
        addressFacadeDto.setCountyNo(addressBasicPrimaryWSFacadeResponse.getCountryId());

        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 逆向单新单询价时，起始省市县赋值逻辑为：
     * 1、逆向单询价取逆向新单传入的揽收站点ID，调青龙基础资料获取省市县id
     * 2、若未传入站点ID则取该逆向单原单的末端站点endstationno调青龙基础资料查询获取省市县id
     *
     * @param orderModel
     * @return
     */
    private String getReturnEndStationNo(ExpressOrderModel orderModel){
        //逆向单揽收站点非必填，因此需要判断是否为空
        if (StringUtils.isNotBlank(orderModel.getShipment().getStartStationNo())){
            //若逆向新单传入了揽收站点ID，则用传入的揽收站点ID，调青龙基础资料获取省市县id；
            return orderModel.getShipment().getStartStationNo();
        } else if (null != orderModel.getOrderSnapshot()) {
            //若未传入揽收站点ID则取该逆向单原单的末端站点endstationNo调青龙基础资料查询获取省市县id；
            return orderModel.getOrderSnapshot().getShipment().getEndStationNo();
        }
        return null;
    }

    /**
     * 改址单后款订单到付现结 TODO 是否和到付 后款没有关系
     * @param orderModel
     * @return
     */
    private boolean changeAddressAndCashonDelivery(ExpressOrderModel orderModel) {
        return OrderTypeEnum.READDRESS == orderModel.getOrderType()
            && PaymentStageEnum.CASHONDELIVERY == orderModel.getFinance().getPaymentStage()
            && SettlementTypeEnum.CASH_ON_DELIVERY == orderModel.getFinance().getSettlementType();
    }

    /**
     * 附加费补充特殊计费要素
     */
    private void attachFeeAddSpecialProductAttrs(CostInfo costInfo, BillingEnquiryFacadeRequest.ProductFacadeDto attachFeeFacadeDto) {
        if (costInfo == null) {
            return;
        }

        // 国际超长超重附加费
        if (EnquiryConstants.COST_NO_OVER_WEIGHT_INTL.equals(costInfo.getCostNo())) {
            String overweightNum = getCostQuantity(costInfo);
            if (StringUtils.isNotBlank(overweightNum)) {
                Map<String, String> productAttrs = attachFeeFacadeDto.getProductAttrs();
                if(MapUtils.isEmpty(productAttrs)){
                    productAttrs = new HashMap<>();
                    attachFeeFacadeDto.setProductAttrs(productAttrs);
                }
                productAttrs.put(EnquiryConstants.OVERWEIGHT_NUM, overweightNum);
            }
        }
    }

    /**
     * 获取费用项命中次数
     */
    private String getCostQuantity(CostInfo costInfo) {
        if (costInfo == null || MapUtils.isEmpty(costInfo.getExtendProps())) {
            return null;
        }
        return costInfo.getExtendProps().get(EnquiryConstants.COST_QUANTITY);
    }
}
