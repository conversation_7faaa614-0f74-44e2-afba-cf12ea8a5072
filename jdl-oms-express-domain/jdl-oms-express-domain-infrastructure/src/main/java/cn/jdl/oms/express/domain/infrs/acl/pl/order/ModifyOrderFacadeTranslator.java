package cn.jdl.oms.express.domain.infrs.acl.pl.order;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.AttachmentInfo;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.FenceInfo;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.core.model.WarehouseInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.AddressExtend;
import cn.jdl.oms.express.domain.bo.ManualPresortExtend;
import cn.jdl.oms.express.domain.converter.AddressMapper;
import cn.jdl.oms.express.domain.converter.ModifyRecordMapper;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.record.ModifyRecordDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.AttachmentFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.ConsigneeFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.CustomsFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.FinanceDetailFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.FinanceFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.ShipmentFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ActivityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AgreementFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AttachmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessIdentityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessSolutionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CargoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ChannelFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsigneeFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsignorFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.DiscountFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceDetailFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FulfillmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.GoodsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.InterceptFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.MoneyFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PointsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PromotionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.QuantityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.RefOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ReturnInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ShipmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.TicketFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.RefundRequest;
import cn.jdl.oms.express.domain.infrs.acl.util.FinanceUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.TdeAclUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.TrustSellerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.annotation.ConvertMode;
import cn.jdl.oms.express.domain.infrs.annotation.UnitedBusinessIdentityConverter;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.DeleteRepositoryMessageDto;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.CancelStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.DiscardStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryModeEnum;
import cn.jdl.oms.express.domain.spec.dict.FenceTrustEnum;
import cn.jdl.oms.express.domain.spec.dict.InterceptTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ReaddressStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderSubType;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefundStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.utils.ContextInfoUtil;
import cn.jdl.oms.express.domain.vo.Activity;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Agreement;
import cn.jdl.oms.express.domain.vo.AgreementDelegate;
import cn.jdl.oms.express.domain.vo.Attachment;
import cn.jdl.oms.express.domain.vo.BusinessSolution;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.DeductionDelegate;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Fence;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Fulfillment;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.Intercept;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.Points;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.domain.vo.Promotion;
import cn.jdl.oms.express.domain.vo.Quantity;
import cn.jdl.oms.express.domain.vo.RefOrder;
import cn.jdl.oms.express.domain.vo.RefOrderDelegate;
import cn.jdl.oms.express.domain.vo.ReturnInfoVo;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Warehouse;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.B2BOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.B2CExtendStatusEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessTypeEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.CCB2BOrderStatusExtendEnum;
import cn.jdl.oms.express.shared.common.dict.CancelInterceptTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ContractOrderStatusExtendEnum;
import cn.jdl.oms.express.shared.common.dict.DeliveryPickupSyncBindOperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusExtendEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyMarkEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyRecordListUpdateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyRecordUpdateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ReaddressMarkEnum;
import cn.jdl.oms.express.shared.common.dict.SignReturnEnumForElectronicStatus;
import cn.jdl.oms.express.shared.common.dict.SignReturnEnumForPaperStatus;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.ModifyMarkUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName CreateOrderFacadeTranslator
 * @Description 修改防腐层信息转换
 * <AUTHOR>
 * @Date 2021/3/26 5:00 下午
 * @ModifyDate 2021/3/26 5:00 下午
 * @Version 1.0
 */
@Translator
public class ModifyOrderFacadeTranslator {
    /** Log */
    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyOrderFacadeTranslator.class);

    /**
     * 是否换单
     */
    private static final String NEW_ORDER = "newOrder";
    /**
     * 已换单
     */
    private static final String NEW_ORDER_VALUE = "1";
    /**
     * 是否修改
     */
    private static final String MODIFY_TIMES = "modifyTimes";
    /**
     * 已修改
     */
    private static final String MODIFY_TIMES_VALUE = "1";

    /**
     * 运单状态-取消
     */
    private static final String EXECUTED_STATUS_CANCEL = "-790";

    /** 运单状态-拦截成功 */
    private static final String EXECUTED_STATUS_INTERCEPT = "-860";

    /**
     * 是否超区 -1：超区  1：非超区  0：待人工预分拣
     */
    private static final String BEYOND_YES = "-1";

    public static final String REMARK_ALIPAY_ORDER = "芝麻代扣支付单";

    /**
     * 加密解密工具类
     */
    @Resource
    private TdeAclUtil tdeAclUtil;
    /**
     * 签单类型：纸质
     */
    private static final Integer WRITTEN = 1;

    /**
     * 签单类型：电子
     */
    private static final Integer ELECTRONIC = 3;

    /**
     * 签单类型：纸质+电子
     */
    private static final Integer COMBINE = 4;

    /**
     * 纸质签单返还状态码
     */
    private static final String RERECEIVE_PAPER_STATUS = "reReceivePaperStatus";

    /**
     * 电子签单返还状态码
     */
    private static final String RERECEIVE_ELECTRONIC_STATUS = "reReceiveElectronicStatus";

    /**
     * 0 删除 1 正常
     */
    private static final Byte YN_DELETE = 0;

    /**
     * ducc配置
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 修改入参
     *
     * @param context
     * @return
     * @throws ParseException
     */
    public ModifyOrderFacadeRequest toModifyOrderFacadeRequest(ExpressOrderContext context){
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel model = context.getOrderModel();
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //业务身份信息
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(model.getOrderBusinessIdentity()));
        //业务身份和业务类型和业务策略 修改类型
        if (ContextInfoUtil.getModifyBusinessIdentityTypes(context) != null) {
            facadeRequest.setModifyBusinessIdentityTypes(ContextInfoUtil.getModifyBusinessIdentityTypes(context));
        }
        //订单号
        facadeRequest.setOrderNo(model.orderNo());
        //订单主状态
        if (model.getOrderStatus() != null && model.getOrderStatus().getOrderStatus() != null) {
            facadeRequest.setOrderStatus(model.getOrderStatus().getOrderStatus().getCode());
        }
        //订单状态
        facadeRequest.setOrderStatusCustom(model.getCustomStatus());
        //下单人类型
        //facadeRequest.setInitiatorType(model.getInitiatorType() != null ? model.getInitiatorType().getCode() : null);
        //下单人
        facadeRequest.setOperator(model.getOperator());
        //oms修改时间
        facadeRequest.setOperateTime(model.getOperateTime());
        //订单备注
        facadeRequest.setRemark(model.getRemark());
        //扩展信息
        setExtendProps(model, facadeRequest);
        //todo 门店pin
        facadeRequest.setCustomerFacade(toCustomerFacade(model.getCustomer()));
        //渠道信息
        facadeRequest.setChannel(toChannelFacade(model.getChannel()));
        //产品信息
        if (model.getProductDelegate() != null) {
            List<Product> list = (List<Product>) model.getProductDelegate().getProducts();
            facadeRequest.setProducts(toProductFacades(list));
        }
        //发货信息
        facadeRequest.setConsignor(toConsignorFacade(model.getConsignor(),context.getAddressExtend()));
        //收货信息
        facadeRequest.setConsignee(toConsigneeFacade(model.getConsignee(),context.getAddressExtend()));

        //货品信息
        if (model.getCargoDelegate() != null) {
            List<Cargo> cargoList = (List<Cargo>) model.getCargoDelegate().getCargoList();
            facadeRequest.setCargos(this.toCargoFacades(cargoList));
        }
        //商品信息
        if(null != model.getGoodsDelegate() && CollectionUtils.isNotEmpty(model.getGoodsDelegate().getGoodsList())){
            facadeRequest.setGoodsList(this.toGoodsFacades((List<Goods>) model.getGoodsDelegate().getGoodsList()));
        }
        //配送信息
        facadeRequest.setShipment(toShipmentFacade(model.getShipment()));

        //财务信息
        facadeRequest.setFinance(toFinanceFacade(model));
        //营销信息
        facadeRequest.setPromotion(toPromotionFacade(model.getPromotion()));
        //关联单信息
        facadeRequest.setRefOrders(toRefOrderFacades(context));
        // 协议信息
        facadeRequest.setAgreementFacades(this.toAgreementFacades(model.getAgreementDelegate()));
        // 订单标识
        if (MapUtils.isNotEmpty(model.getOrderSign())) {
            facadeRequest.setOrderSign(model.getOrderSign());
        }
        // 改址状态
        if (model.getReaddressStatus() != null) {
            facadeRequest.setReaddressStatus(model.getReaddressStatus().getCode());
        }
        // 关联单扩展单据类型
        Optional.ofNullable(model.getRefOrderInfoDelegate()).ifPresent(refOrderDelegate ->
                facadeRequest.setExtendRefOrder(refOrderDelegate.getExtendProps())
        );

        //跨境报关信息
        facadeRequest.setCustomsFacade(this.toCustomsFacade(model.getCustoms()));
        //附件列表
        facadeRequest.setAttachmentFacades(toAttachmentFacades(model.getAttachments()));
        //订单总净重
        facadeRequest.setOrderNetWeight(model.getOrderNetWeight());
        //订单总毛重
        facadeRequest.setOrderWeight(model.getOrderWeight());
        //订单总体积
        facadeRequest.setOrderVolume(model.getOrderVolume());
        //复核重量
        facadeRequest.setRecheckWeight(model.getRecheckWeight());
        //复核体积
        facadeRequest.setRecheckVolume(model.getRecheckVolume());

        //修改场景-清空的字段
        if (model.getClearFields() != null && model.getClearFields().size() > 0) {
            facadeRequest.setClearFields(model.getClearFields());
        }
        //修改场景-集合字段操作模式
        if (model.getModifiedFields() != null && model.getModifiedFields().size() > 0) {
            facadeRequest.setModifiedFields(model.getModifiedFields());
        }
        facadeRequest.setReturnInfoFacade(this.toReturnInfoFacade(model.getReturnInfoVo()));
        //履约信息
        facadeRequest.setFulfillment(this.toFulfillmentFacade(model.getFulfillment()));
        //变更信息列表
        if(null != context.getModifyRecordDelegate() && CollectionUtils.isNotEmpty(context.getModifyRecordDelegate().getModifyRecords())){
            facadeRequest.setModifyRecordListOperateType(ModifyRecordListUpdateTypeEnum.ALL_COVER.getCode());
            facadeRequest.setModifyRecordDtos(toAllCoverRecordDtos(context.getModifyRecordDelegate().getModifyRecords()));
        }

        if (model.isReaddress1Order2End() || model.isKKInterceptionThroughOrderRecord()) {
            //原单信息
            String modifyMark = GetFieldUtils.getModifyMark(context.getOrderModel());
            if (PaymentStageEnum.CASHONDELIVERY == model.getFinance().getPaymentStage()) {
                modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition(), ReaddressMarkEnum.RESULT_SUCCESS.getCode());
                Map<String, String> extendProps = facadeRequest.getExtendProps();
                if (null == extendProps) {
                    extendProps = new HashMap<>();
                }
                extendProps.put(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);
                facadeRequest.setExtendProps(extendProps);
            }
        }

        //自定义单号
        if(StringUtils.isNotBlank(model.getCustomOrderNo())){
            facadeRequest.setCustomOrderNo(model.getCustomOrderNo());
            // 简易服务自定义单号即为运单号 修改接口获取
            if(BusinessTypeEnum.SIMPLE_SERVICE.getCode().equals(model.getBusinessIdentity().getBusinessType())){
                RefOrderFacade refOrder = new RefOrderFacade();
                refOrder.setRefOrderNo(model.getCustomOrderNo());
                refOrder.setRefOrderType(RefOrderTypeEnum.DELIVERY.getCode());
                refOrder.setRefOrderSubType(RefOrderSubType.DeliveryEnum.FORWARD_DELIVERY.getCode());
                List<RefOrderFacade> refOrders = facadeRequest.getRefOrders();
                if(null == refOrders){
                    refOrders = new ArrayList<>();
                }
                refOrders.add(refOrder);
                facadeRequest.setRefOrders(refOrders);
            }
        }
        facadeRequest.setDeleteRefOrders(toDeleteRefOrders(context));

        //解决方案信息
        facadeRequest.setBusinessSolutionFacade(toBusinessSolutionFacade(model.getBusinessSolution()));

        return facadeRequest;
    }

    /**
     * 解决方案信息防腐层转换
     *
     * @param businessSolution
     * @return
     */
    private BusinessSolutionFacade toBusinessSolutionFacade(BusinessSolution businessSolution) {
        if (businessSolution == null) {
            return null;
        }
        BusinessSolutionFacade facade = new BusinessSolutionFacade();
        //编码
        facade.setBusinessSolutionNo(businessSolution.getBusinessSolutionNo());
        //名称
        facade.setBusinessSolutionName(businessSolution.getBusinessSolutionName());
        //要素
        facade.setProductAttrs(businessSolution.getProductAttrs());
        return facade;
    }

    /**
     * 渠道信息转换
     *
     * @param channel
     * @return
     */
    private ChannelFacade toChannelFacade(Channel channel) {
        if (channel == null) {
            return null;
        }
        ChannelFacade channelFacade = new ChannelFacade();

        Map<String, String> channelFacadeExt = channel.getExtendProps();
        if (MapUtils.isNotEmpty(channelFacadeExt)) {
            String modifySystemSubCaller = channelFacadeExt.get(OrderConstants.MODIFY_SYSTEM_SUB_CALLER);
            // 仅供融合修改使用，订单做透传。modifySystemSubCaller传值（非空）时同步修改systemSubCaller。
            if (StringUtils.isNotBlank(modifySystemSubCaller)) {
                // 修改接单渠道来源
                channelFacade.setSystemSubCaller(channel.getSystemSubCaller());
            }
        }

        channelFacade.setExtendProps(channelFacadeExt);

        return channelFacade;
    }

    /**
     * 修改变更记录入参
     *
     * @param context
     * @return
     * @throws ParseException
     */
    public ModifyOrderFacadeRequest toModifyRecordFacadeRequest(ExpressOrderContext context, ModifyRecord readdressModifyRecord) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel model = context.getOrderModel();
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //业务身份信息
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(model.getOrderBusinessIdentity()));
        //订单号
        facadeRequest.setOrderNo(model.orderNo());

        facadeRequest = toIncrementalUpdateModifyRecordInfo(facadeRequest, readdressModifyRecord);

        return facadeRequest;
    }

    /**
     *
     * @param modifyRecordList
     * @return
     */
    private List<ModifyRecordDto> toAllCoverRecordDtos(List<ModifyRecord> modifyRecordList) {
        if (CollectionUtils.isEmpty(modifyRecordList)) {
            return null;
        }
        List<ModifyRecordDto> modifyRecordDtoList = new ArrayList<>(modifyRecordList.size());
        modifyRecordList.forEach(modifyRecord -> {
            ModifyRecordDto modifyRecordDto = ModifyRecordMapper.INSTANCE.toModifyRecordDto(modifyRecord);
            modifyRecordDto.setModifyRecordMsg(JSONUtils.beanToJSONDisableCircularReferenceDetect(modifyRecord.getModifyRecordDetail()));
            modifyRecordDto.setModifyInfoUpdateType(ModifyRecordUpdateTypeEnum.INSERT.getCode());
            modifyRecordDtoList.add(modifyRecordDto);
        });
        return modifyRecordDtoList;
    }

    /**
     *
     * @param modifyRecordList
     * @return
     */
    private List<ModifyRecordDto> toModifyRecordDtos(List<ModifyRecord> modifyRecordList, ModifyRecordUpdateTypeEnum modifyRecordUpdateTypeEnum) {
        if (CollectionUtils.isEmpty(modifyRecordList)) {
            return null;
        }
        List<ModifyRecordDto> modifyRecordDtoList = new ArrayList<>(modifyRecordList.size());
        modifyRecordList.forEach(modifyRecord -> {
            ModifyRecordDto modifyRecordDto = ModifyRecordMapper.INSTANCE.toModifyRecordDto(modifyRecord);
            modifyRecordDto.setModifyRecordMsg(JSONUtils.beanToJSONDisableCircularReferenceDetect(modifyRecord.getModifyRecordDetail()));
            modifyRecordDto.setModifyInfoUpdateType(modifyRecordUpdateTypeEnum.getCode());
            modifyRecordDtoList.add(modifyRecordDto);
        });
        return modifyRecordDtoList;
    }

    /**
     * 退货信息
     * @param returnInfoVo
     * @return
     */
    private ReturnInfoFacade toReturnInfoFacade(ReturnInfoVo returnInfoVo){
        ReturnInfoFacade returnInfoFacade = new ReturnInfoFacade();
        returnInfoFacade.setReturnType(returnInfoVo.getReturnType());
        returnInfoFacade.setConsigneeFacade(toConsigneeFacade(returnInfoVo.getConsignee(),null));
        return returnInfoFacade;
    }

    /**
     * 货品追加持久化请求构建
     * @param context 订单上下文
     * @return ModifyOrderFacadeRequest
     * @throws ParseException
     */
    public ModifyOrderFacadeRequest toModifyOrderCargoFacadeRequest(ExpressOrderContext context) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel model = context.getOrderModel();
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //业务身份信息
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(model.getOrderBusinessIdentity()));
        //订单id
        //facadeRequest.setOrderId(model.getOrderSnapshot() != null ? model.getOrderSnapshot().getOrderId() : null);
        //订单号
        facadeRequest.setOrderNo(model.orderNo());
        //下单人类型
        //facadeRequest.setInitiatorType(model.getInitiatorType() != null ? model.getInitiatorType().getCode() : null);
        //下单人
        facadeRequest.setOperator(model.getOperator());
        //oms修改时间
        facadeRequest.setOperateTime(model.getOperateTime());
        //扩展信息
        if (model.getExtendProps() != null && model.getExtendProps().size() > 0) {
            facadeRequest.setExtendProps(model.getExtendProps());
        }
        //渠道信息
        //facadeRequest.setChannel(toChannelFacade(model.getChannel()));
        //货品信息
        if (model.getCargoDelegate() != null) {
            List<Cargo> cargoList = (List<Cargo>) model.getCargoDelegate().getCargoList();
            facadeRequest.setCargos(this.toCargoFacades(cargoList));
        }
        //修改场景-清空的字段
        if (model.getClearFields() != null && model.getClearFields().size() > 0) {
            facadeRequest.setClearFields(model.getClearFields());
        }
        //修改场景-集合字段操作模式
        if (model.getModifiedFields() != null && model.getModifiedFields().size() > 0) {
            facadeRequest.setModifiedFields(model.getModifiedFields());
        }
        return facadeRequest;
    }

    /**
     * 设置扩展信息
     * @param model
     * @param facadeRequest
     */
    private void setExtendProps(ExpressOrderModel model, ModifyOrderFacadeRequest facadeRequest) {
        if (BusinessUnitEnum.CN_JDL_B2C.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_O2O_B.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_C2B.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                //新增冷链B2C身份识别
                || BusinessUnitEnum.CN_JDL_CC_B2C.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())) {
            //B2C需要判断是否是揽收后修改，如果是，则更新是否修改标识
            Map<String, String> map = model.getExtendProps();
            if (model.getExtendProps() == null) {
                map = new HashMap<>();
            }
            if (null!= model.getOrderSnapshot() && model.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {//揽收后修改
                if (!ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(model)
                        && !ModifySceneRuleUtil.isModifyContactInformation(model)) {
                    //非仅修改收件人联系方式（姓名、电话、手机）
                    this.setModifyTimes(map);
                }
            }
            if (map.size() > 0) {
                facadeRequest.setExtendProps(map);
            }
        } else {
            if (model.getExtendProps() != null && model.getExtendProps().size() > 0) {
                facadeRequest.setExtendProps(model.getExtendProps());
            }
        }
    }

    /**
     * 设置修改次数
     * @param map
     */
    private void setModifyTimes(Map<String, String> map){
        if (StringUtils.isNotBlank(map.get(OrderConstants.IGNORE_MODIFY_FLAG))){
            //由于终端不切订单中心，终端揽收完成时称重量方通过外单修改（揽收完成是揽收前的动作--揽收前修改），不经过订单中心；
            //外单修改调运单修改，此时修改请求经过运单处理后会发出运单变化消息，订单中心监听运单变更消息调外单信息更新到订单中心；
            //在处理运单消息变更的过程中，订单的状态可能已经回传成揽收后，
            //因此如果只判断订单状态是揽收后，会导致揽收后修改的标识记录有误（因为终端的修改是揽收前，订单中心的状态被回传更新为揽收后）
            //与外单及订单中心产品（严飞、王维金确认后），同步外单数据更新时，
            // 判断运单标位waybillSign（第8位和第103位为0表示揽收前修改）的第8位和第103位是否为0，
            //如果为0，则为揽收前修改，如果不是，则为揽收后修改
            String ignoreModifyFlag = map.get(OrderConstants.IGNORE_MODIFY_FLAG);
            LOGGER.info("运单数据变化发起修改，ignoreModifyFlag:{}",ignoreModifyFlag);
            if (OrderConstants.NOT_IGNORE_MODIFY_FLAG_VALUE.equals(ignoreModifyFlag)){
                map.put(MODIFY_TIMES, MODIFY_TIMES_VALUE);
            }
        }else {
            map.put(MODIFY_TIMES, MODIFY_TIMES_VALUE);
        }
    }

    /**
     * 是否为空对象
     * @param obj
     * @param clazz
     * @return
     * @param <T>
     */
    private <T> boolean isNullObject(Object obj, Class<T> clazz) {
        if (obj == null) {
            return true;
        }
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if ("serialVersionUID".equals(field.getName())) {
                continue;
            }
            if (Modifier.isStatic(field.getModifiers())) {
                continue;
            }
            field.setAccessible(true);
            try {
                if (field.get(obj) != null) {
                    field.setAccessible(false);
                    return false;
                }
            } catch (IllegalAccessException e) {
                //不会有这个问题
            }
            field.setAccessible(false);
        }

        return true;
    }


    /**
     * 业务身份转换
     *
     * @param businessIdentity
     * @return
     */
    public BusinessIdentityFacade toBusinessIdentityFacade(OrderBusinessIdentity businessIdentity) {
        BusinessIdentityFacade businessIdentityFacade = new BusinessIdentityFacade();
        businessIdentityFacade.setBusinessUnit(businessIdentity.getBusinessUnit());
        businessIdentityFacade.setBusinessType(businessIdentity.getBusinessType());
        businessIdentityFacade.setBusinessScene(businessIdentity.getBusinessScene());
        businessIdentityFacade.setBusinessStrategy(businessIdentity.getBusinessStrategy());
        businessIdentityFacade.setFulfillmentUnit(businessIdentity.getFulfillmentUnit());
        return businessIdentityFacade;
    }


    /**
     * 客户信息转换
     *
     * @param customer
     * @return
     */
    public CustomerFacade toCustomerFacade(Customer customer) {
        if (customer == null || isNullObject(customer, Customer.class)) {
            return null;
        }
        CustomerFacade customerFacade = new CustomerFacade();
        //履约账号
        customerFacade.setAccountNo(customer.getAccountNo());
        //履约账号名称
        customerFacade.setAccountName(customer.getAccountName());
        //履约账号2
        customerFacade.setAccount2No(customer.getAccountNo2());
        //履约账号名称2
        customerFacade.setAccount2Name(customer.getAccountName2());
        //履约账号名称3
        customerFacade.setAccount3No(customer.getAccountNo3());
        //履约账号名称3
        customerFacade.setAccount3Name(customer.getAccountName3());
        //todo 门店pin
        customerFacade.setStorePin(customer.getStorePin());
        return customerFacade;
    }

    /**
     * 产品信息转换
     *
     * @param products
     * @return
     */
    public List<ProductFacade> toProductFacades(List<Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        List<ProductFacade> productFacades = new ArrayList<>(products.size());
        for (Product product : products) {
            if (product.getOperateType() == null) {
                continue;
            }
            ProductFacade productFacade = new ProductFacade();
            //产品编码
            productFacade.setProductNo(product.getProductNo());
            //产品名称
            productFacade.setProductName(product.getProductName());
            //产品类型
            productFacade.setProductType(product.getProductType());
            //降级前产品编码
            productFacade.setOriginalProductNo(product.getOriginalProductNo());
            //降级前产品名称
            productFacade.setOriginalProductName(product.getOriginalProductName());
            //主产品编码
            productFacade.setParentNo(product.getParentNo());
            //扩展信息
            productFacade.setExtendProps(product.getExtendProps());
            //产品属性
            productFacade.setProductAttrs(product.getProductAttrs());
            //操作类型
            productFacade.setOperateType(product.getOperateType());
            productFacades.add(productFacade);
        }
        if (productFacades.size() == 0) {
            return null;
        }
        return productFacades;
    }

    /**
     * 回传产品处理-第一个参数是历史产品，第二个参数是回传产品
     *
     * @param snapshotModel
     * @param products
     * @return
     */
    public List<ProductFacade> toB2CCallBackProductFacades(ExpressOrderModel snapshotModel, List<Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        LOGGER.info("回传增值服务转换：snapshotModel:{},products:{}", JSONUtils.beanToJSONDefault(snapshotModel), JSONUtils.beanToJSONDefault(products));
        List<ProductFacade> productFacades = new ArrayList<>();
        for (Product product : products) {
            Product snapshotProduct = snapshotModel.getProductDelegate().ofProductNo(product.getProductNo());

            ProductFacade productFacade = new ProductFacade();
            //签单返还
            if (AddOnProductEnum.getSignReturnCode().contains(product.getProductNo()) && product.getProductAttrs() != null) {
                //纸质类型-运单号
                if (null != product.getProductAttrs().get(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode())) {
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode()));
                }
                //电子类型-照片和拍照时间
                if (null != product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode())) {
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode()));
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_PHOTO_TIME.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO_TIME.getCode()));
                }
            }
            //协商再投
            if (AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode().equals(product.getProductNo()) && product.getProductAttrs() != null) {
                snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.REAL_REJECT_AUDIT_NUMBERS.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.REAL_REJECT_AUDIT_NUMBERS.getCode()));
            }
            //冷链协商再投
            if (AddOnProductEnum.CC_MD_NEGOTIATION_REDELIVERY.getCode().equals(product.getProductNo()) && product.getProductAttrs() != null) {
                snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.REAL_REJECT_AUDIT_NUMBERS.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.REAL_REJECT_AUDIT_NUMBERS.getCode()));
            }
            //产品编码
            productFacade.setProductNo(snapshotProduct.getProductNo());
            //产品名称
            productFacade.setProductName(snapshotProduct.getProductName());
            //产品类型
            productFacade.setProductType(snapshotProduct.getProductType());
            //降级前产品编码
            productFacade.setOriginalProductNo(snapshotProduct.getOriginalProductNo());
            //降级前产品名称
            productFacade.setOriginalProductName(snapshotProduct.getOriginalProductName());
            //主产品编码
            productFacade.setParentNo(snapshotProduct.getParentNo());
            //扩展信息
            productFacade.setExtendProps(snapshotProduct.getExtendProps());
            //产品属性
            productFacade.setProductAttrs(snapshotProduct.getProductAttrs());
            //操作类型
            productFacade.setOperateType(OperateTypeEnum.UPDATE);
            productFacades.add(productFacade);
        }
        return productFacades;
    }

    /**
     * 发货信息对象转换
     *
     * @param consignor
     * @return
     */
    public ConsignorFacade toConsignorFacade(Consignor consignor, AddressExtend addressExtend) {
        if (consignor == null || isNullObject(consignor, Consignor.class)) {
            return null;
        }
        ConsignorFacade consignorFacade = new ConsignorFacade();
        //发货人名称
        consignorFacade.setConsignorName(consignor.getConsignorName());
        //发货人手机号
        consignorFacade.setConsignorMobile(consignor.getConsignorMobile());
        //发货人电话
        consignorFacade.setConsignorPhone(consignor.getConsignorPhone());
        //发货人公司
        consignorFacade.setConsignorCompany(consignor.getConsignorCompany());
        //发货人邮编
        consignorFacade.setConsignorZipCode(consignor.getConsignorZipCode());
        //发货人国家编码
        consignorFacade.setConsignorNationNo(consignor.getConsignorNationNo());
        //发货人国家
        consignorFacade.setConsignorNation(consignor.getConsignorNation());
        //发货人证件类型
        if (consignor.getConsignorIdType() != null) {
            consignorFacade.setConsignorIdType(consignor.getConsignorIdType().getCode());
        }
        //发货人证件号
        consignorFacade.setConsignorIdNo(consignor.getConsignorIdNo());
        //发货人证件姓名
        consignorFacade.setConsignorIdName(consignor.getConsignorIdName());
        //发货仓信息
        Warehouse customerWarehouse = consignor.getCustomerWarehouse();
        if (customerWarehouse != null && !isNullObject(customerWarehouse, Warehouse.class)) {
            WarehouseInfo warehouseInfo = new WarehouseInfo();
            warehouseInfo.setWarehouseNo(customerWarehouse.getWarehouseNo());
            warehouseInfo.setWarehouseName(customerWarehouse.getWarehouseName());
            warehouseInfo.setWarehouseSource(customerWarehouse.getWarehouseSource());
            consignorFacade.setCustomerWarehouse(warehouseInfo);
        }

        //英文发货人姓名
        consignorFacade.setConsignorEnName(consignor.getConsignorEnName());
        //发货人扩展信息
        consignorFacade.setExtendProps(consignor.getExtendProps());
        Address consignorAddress = consignor.getAddress();
        if (consignorAddress != null && !isNullObject(consignorAddress, Address.class)) {
            AddressInfo addressInfo = new AddressInfo();
            //发货人省编码
            addressInfo.setProvinceNo(consignorAddress.getProvinceNo());
            //发货人省名称
            addressInfo.setProvinceName(consignorAddress.getProvinceName());
            //发货人市编码
            addressInfo.setCityNo(consignorAddress.getCityNo());
            //发货人市名称
            addressInfo.setCityName(consignorAddress.getCityName());
            //发货人区编码
            addressInfo.setCountyNo(consignorAddress.getCountyNo());
            //发货人区名称
            addressInfo.setCountyName(consignorAddress.getCountyName());
            //发货人镇编码
            addressInfo.setTownNo(consignorAddress.getTownNo());
            //发货人镇名称
            addressInfo.setTownName(consignorAddress.getTownName());
            //发货人镇详细地址
            addressInfo.setAddress(consignorAddress.getAddress());
            //坐标系
            if (null != consignorAddress.getCoordinateType()) {
                addressInfo.setCoordinateType(consignorAddress.getCoordinateType().getCode());
            }
            //经度
            addressInfo.setLongitude(consignorAddress.getLongitude());
            //纬度
            addressInfo.setLatitude(consignorAddress.getLatitude());
            //解析后的发货人省编码
            addressInfo.setProvinceNoGis(consignorAddress.getProvinceNoGis());
            //解析后的发货人省名称
            addressInfo.setProvinceNameGis(consignorAddress.getProvinceNameGis());
            //解析后的发货人市编码
            addressInfo.setCityNoGis(consignorAddress.getCityNoGis());
            //解析后的发货人市名称
            addressInfo.setCityNameGis(consignorAddress.getCityNameGis());
            //解析后的发货人区编码
            addressInfo.setCountyNoGis(consignorAddress.getCountyNoGis());
            //解析后的发货人区名称
            addressInfo.setCountyNameGis(consignorAddress.getCountyNameGis());
            //解析后的发货人镇编码
            addressInfo.setTownNoGis(consignorAddress.getTownNoGis());
            //解析后的发货人镇名称
            addressInfo.setTownNameGis(consignorAddress.getTownNameGis());
            //解析后的详细地址
            addressInfo.setAddressGis(consignorAddress.getAddressGis());
            //gis解析精准度
            addressInfo.setPreciseGis(consignorAddress.getPreciseGis());
            //邮政编码
            addressInfo.setChinaPostAddressCode(consignorAddress.getChinaPostAddressCode());
            // 地址嵌套等级
            // addressInfo.setConflictLevel(consignorAddress.getConflictLevel());
            //上下文取出gis打标 地址来源
            if(null != addressExtend && null != addressExtend.getConsignorAddressSource()) {
                addressInfo.setAddressSource(addressExtend.getConsignorAddressSource());
            }
            // 围栏信息
            addressInfo.setFenceTrusted(consignorAddress.getFenceTrusted());
            if (FenceTrustEnum.TRUSTED.getCode().equals(consignorAddress.getFenceTrusted())) {
                addressInfo.setFenceInfos(toFenceInfos(consignorAddress.getFenceInfos()));
            }
            //行政区编码
            addressInfo.setRegionNo(consignorAddress.getRegionNo());
            //行政区名称
            addressInfo.setRegionName(consignorAddress.getRegionName());
            //英文城市
            addressInfo.setEnCityName(consignorAddress.getEnCityName());
            //英文地址
            addressInfo.setEnAddress(consignorAddress.getEnAddress());
            addressInfo.setPoiCode(consignorAddress.getPoiCode());
            addressInfo.setPoiName(consignorAddress.getPoiName());
            addressInfo.setHouseNumber(consignorAddress.getHouseNumber());
            addressInfo.setExtendProps(consignorAddress.getExtendProps());
            consignorFacade.setAddress(addressInfo);
        }
        if (isNullObject(consignorFacade, ConsignorFacade.class)) {
            return null;
        }
        return consignorFacade;
    }

    /**
     * 收货人对象转换
     *
     * @param consignee
     * @return
     */
    public ConsigneeInfo toConsigneeInfo(Consignee consignee) {
        if (consignee == null) {
            return null;
        }
        ConsigneeInfo consigneeInfo = new ConsigneeInfo();
        //收货人名称
        consigneeInfo.setConsigneeName(consignee.getConsigneeName());
        //收货人手机号
        consigneeInfo.setConsigneeMobile(consignee.getConsigneeMobile());
        //收货人电话
        consigneeInfo.setConsigneePhone(consignee.getConsigneePhone());
        //收货人公司
        consigneeInfo.setConsigneeCompany(consignee.getConsigneeCompany());
        //收货人邮编
        consigneeInfo.setConsigneeZipCode(consignee.getConsigneeZipCode());
        //收货人国家编码
        consigneeInfo.setConsigneeNationNo(consignee.getConsigneeNationNo());
        //收货人国家
        consigneeInfo.setConsigneeNation(consignee.getConsigneeNation());
        //收货人证件类型
        if (consignee.getConsigneeIdType() != null) {
            consigneeInfo.setConsigneeIdType(consignee.getConsigneeIdType().getCode());
        }
        //收货人证件号
        consigneeInfo.setConsigneeIdNo(consignee.getConsigneeIdNo());
        //收货人证件姓名
        consigneeInfo.setConsigneeIdName(consignee.getConsigneeIdName());
        //收货仓信息
        Warehouse receiveWarehouse = consignee.getReceiveWarehouse();
        if (receiveWarehouse != null) {
            WarehouseInfo warehouseInfo = new WarehouseInfo();
            //收货仓库编号
            warehouseInfo.setWarehouseNo(receiveWarehouse.getWarehouseNo());
            //收货仓库名称
            warehouseInfo.setWarehouseName(receiveWarehouse.getWarehouseName());
            //收货仓库类型
            warehouseInfo.setWarehouseSource(receiveWarehouse.getWarehouseSource());
            //收货仓信息
            consigneeInfo.setReceiveWarehouse(warehouseInfo);
        }

        consigneeInfo.setAddressInfo(toAddressInfo(consignee.getAddress()));

        return consigneeInfo;
    }

    /**
     * 地址信息转换
     * @param address
     * @return
     */
    public AddressInfo toAddressInfo(Address address) {
        if (address == null) {
            return null;
        }
        AddressInfo addressInfo = new AddressInfo();
        //发货人省编码
        addressInfo.setProvinceNo(address.getProvinceNo());
        //发货人省名称
        addressInfo.setProvinceName(address.getProvinceName());
        //发货人市编码
        addressInfo.setCityNo(address.getCityNo());
        //发货人市名称
        addressInfo.setCityName(address.getCityName());
        //发货人区编码
        addressInfo.setCountyNo(address.getCountyNo());
        //发货人区名称
        addressInfo.setCountyName(address.getCountyName());
        //发货人镇编码
        addressInfo.setTownNo(address.getTownNo());
        //发货人镇名称
        addressInfo.setTownName(address.getTownName());
        //发货人镇详细地址
        addressInfo.setAddress(address.getAddress());
        //解析后的发货人省编码
        addressInfo.setProvinceNoGis(address.getProvinceNoGis());
        //解析后的发货人省名称
        addressInfo.setProvinceNameGis(address.getProvinceNameGis());
        //解析后的发货人市编码
        addressInfo.setCityNoGis(address.getCityNoGis());
        //解析后的发货人市名称
        addressInfo.setCityNameGis(address.getCityNameGis());
        //解析后的发货人区编码
        addressInfo.setCountyNoGis(address.getCountyNoGis());
        //解析后的发货人区名称
        addressInfo.setCountyNameGis(address.getCountyNameGis());
        //解析后的发货人镇编码
        addressInfo.setTownNoGis(address.getTownNoGis());
        //解析后的发货人镇名称
        addressInfo.setTownNameGis(address.getTownNameGis());
        //解析后的详细地址
        addressInfo.setAddressGis(address.getAddressGis());
        //gis解析精准度
        addressInfo.setPreciseGis(address.getPreciseGis());
        //邮政编码
        addressInfo.setChinaPostAddressCode(address.getChinaPostAddressCode());
        //地址来源
        addressInfo.setAddressSource(address.getAddressSource());
        // 围栏信息
        addressInfo.setFenceTrusted(address.getFenceTrusted());
        if (FenceTrustEnum.TRUSTED.getCode().equals(address.getFenceTrusted())) {
            addressInfo.setFenceInfos(toFenceInfos(address.getFenceInfos()));
        }
        //行政区编码
        addressInfo.setRegionNo(address.getRegionNo());
        //行政区名称
        addressInfo.setRegionName(address.getRegionName());
        return addressInfo;
    }
    /**
     * 收货人对象转换
     *
     * @param consignee
     * @return
     */
    public ConsigneeFacade toConsigneeFacade(Consignee consignee, AddressExtend addressExtend) {
        if (consignee == null || isNullObject(consignee, Consignee.class)) {
            return null;
        }
        ConsigneeFacade consigneeFacade = new ConsigneeFacade();
        //收货人名称
        consigneeFacade.setConsigneeName(consignee.getConsigneeName());
        //收货人手机号
        consigneeFacade.setConsigneeMobile(consignee.getConsigneeMobile());
        //收货人电话
        consigneeFacade.setConsigneePhone(consignee.getConsigneePhone());
        //收货人公司
        consigneeFacade.setConsigneeCompany(consignee.getConsigneeCompany());
        //收货人邮编
        consigneeFacade.setConsigneeZipCode(consignee.getConsigneeZipCode());
        //收货人国家编码
        consigneeFacade.setConsigneeNationNo(consignee.getConsigneeNationNo());
        //收货人国家
        consigneeFacade.setConsigneeNation(consignee.getConsigneeNation());
        //收货人证件类型
        if (consignee.getConsigneeIdType() != null) {
            consigneeFacade.setConsigneeIdType(consignee.getConsigneeIdType().getCode());
        }
        //收货人证件号
        consigneeFacade.setConsigneeIdNo(consignee.getConsigneeIdNo());
        //收货人证件姓名
        consigneeFacade.setConsigneeIdName(consignee.getConsigneeIdName());
        //收货仓信息
        Warehouse receiveWarehouse = consignee.getReceiveWarehouse();
        if(receiveWarehouse != null){
            WarehouseInfo warehouseInfo = new WarehouseInfo();
            //收货仓库编号
            warehouseInfo.setWarehouseNo(receiveWarehouse.getWarehouseNo());
            //收货仓库名称
            warehouseInfo.setWarehouseName(receiveWarehouse.getWarehouseName());
            //收货仓库类型
            warehouseInfo.setWarehouseSource(receiveWarehouse.getWarehouseSource());
            //收货仓信息
            consigneeFacade.setReceiveWarehouse(warehouseInfo);
        }
        Address consigneeAddress = consignee.getAddress();
        if (consigneeAddress != null && !isNullObject(consigneeAddress, Address.class)) {
            AddressInfo addressInfo = new AddressInfo();
            //收货人省编码
            addressInfo.setProvinceNo(consigneeAddress.getProvinceNo());
            //收货人省名称
            addressInfo.setProvinceName(consigneeAddress.getProvinceName());
            //收货人市编码
            addressInfo.setCityNo(consigneeAddress.getCityNo());
            //收货人市名称
            addressInfo.setCityName(consigneeAddress.getCityName());
            //收货人区编码
            addressInfo.setCountyNo(consigneeAddress.getCountyNo());
            //收货人区名称
            addressInfo.setCountyName(consigneeAddress.getCountyName());
            //收货人镇编码
            addressInfo.setTownNo(consigneeAddress.getTownNo());
            //收货人镇名称
            addressInfo.setTownName(consigneeAddress.getTownName());
            //收货人镇详细地址
            addressInfo.setAddress(consigneeAddress.getAddress());
            //坐标系
            if (null != consigneeAddress.getCoordinateType()) {
                addressInfo.setCoordinateType(consigneeAddress.getCoordinateType().getCode());
            }
            //经度
            addressInfo.setLongitude(consigneeAddress.getLongitude());
            //纬度
            addressInfo.setLatitude(consigneeAddress.getLatitude());
            //解析后的收货人省编码
            addressInfo.setProvinceNoGis(consigneeAddress.getProvinceNoGis());
            //解析后的收货人省名称
            addressInfo.setProvinceNameGis(consigneeAddress.getProvinceNameGis());
            //解析后的收货人市编码
            addressInfo.setCityNoGis(consigneeAddress.getCityNoGis());
            //解析后的收货人市名称
            addressInfo.setCityNameGis(consigneeAddress.getCityNameGis());
            //解析后的收货人区编码
            addressInfo.setCountyNoGis(consigneeAddress.getCountyNoGis());
            //解析后的收货人区名称
            addressInfo.setCountyNameGis(consigneeAddress.getCountyNameGis());
            //解析后的收货人镇编码
            addressInfo.setTownNoGis(consigneeAddress.getTownNoGis());
            //解析后的收货人镇名称
            addressInfo.setTownNameGis(consigneeAddress.getTownNameGis());
            //解析后的详细地址
            addressInfo.setAddressGis(consigneeAddress.getAddressGis());
            //gis解析精准度
            addressInfo.setPreciseGis(consigneeAddress.getPreciseGis());
            //邮政编码
            addressInfo.setChinaPostAddressCode(consigneeAddress.getChinaPostAddressCode());
            //地址嵌套等级
            addressInfo.setConflictLevel(consigneeAddress.getConflictLevel());
            //上下文取出gis打标 地址来源
            if(null != addressExtend && null != addressExtend.getConsigneeAddressSource()) {
                addressInfo.setAddressSource(addressExtend.getConsigneeAddressSource());
            }
            // 围栏信息
            addressInfo.setFenceTrusted(consigneeAddress.getFenceTrusted());
            if (FenceTrustEnum.TRUSTED.getCode().equals(consigneeAddress.getFenceTrusted())) {
                addressInfo.setFenceInfos(toFenceInfos(consigneeAddress.getFenceInfos()));
            }

            //行政区编码
            addressInfo.setRegionNo(consigneeAddress.getRegionNo());
            //行政区名称
            addressInfo.setRegionName(consigneeAddress.getRegionName());
            //英文城市
            addressInfo.setEnCityName(consigneeAddress.getEnCityName());
            //英文地址
            addressInfo.setEnAddress(consigneeAddress.getEnAddress());
            addressInfo.setPoiCode(consigneeAddress.getPoiCode());
            addressInfo.setPoiName(consigneeAddress.getPoiName());
            addressInfo.setHouseNumber(consigneeAddress.getHouseNumber());
            addressInfo.setExtendProps(consigneeAddress.getExtendProps());
            consigneeFacade.setAddress(addressInfo);
        }
        //收件人邮箱
        consigneeFacade.setConsigneeEmail(consignee.getConsigneeEmail());

        if(null != consignee.getExtendProps()){
            consigneeFacade.setExtendProps(consignee.getExtendProps());

        }
        if (isNullObject(consigneeFacade, ConsigneeFacade.class)) {
            return null;
        }
        return consigneeFacade;
    }
    /**
     * 围栏信息转换
     * @param fenceInfoList
     * @return
     */
    private List<FenceInfo> toFenceInfos(List<Fence> fenceInfoList) {
        if (CollectionUtils.isEmpty(fenceInfoList)) {
            return Collections.emptyList();
        }
        List<FenceInfo> fenceInfos = new ArrayList<>(fenceInfoList.size());
        for (Fence fence : fenceInfoList) {
            FenceInfo fenceInfo = new FenceInfo();
            fenceInfo.setFenceId(fence.getFenceId());
            fenceInfo.setFenceType(fence.getFenceType());
            fenceInfos.add(fenceInfo);
        }
        return fenceInfos;
    }

    /**
     * 货品信息转换
     *
     * @param cargos
     * @return
     */
    public List<CargoFacade> toCargoFacades(List<Cargo> cargos) {
        if (CollectionUtils.isEmpty(cargos)) {
            return null;
        }
        List<CargoFacade> cargoFacades = new ArrayList<>(cargos.size());
        for (Cargo cargo : cargos) {
            CargoFacade cargoFacade = new CargoFacade();
            //货品编码
            cargoFacade.setCargoNo(cargo.getCargoNo());
            //货品名称
            cargoFacade.setCargoName(cargo.getCargoName());
            //货品类型
            cargoFacade.setCargoType(cargo.getCargoType());
            //货品重量
            cargoFacade.setCargoWeight(cargo.getCargoWeight());
            //货品体积
            cargoFacade.setCargoVolume(cargo.getCargoVolume());
            //货品尺寸
            cargoFacade.setCargoDimension(cargo.getCargoDimension());
            //货品数量
            cargoFacade.setCargoQuantity(cargo.getCargoQuantity());
            //货品内件数量
            cargoFacade.setCargoInnerQuantity(cargo.getCargoInnerQuantity());
            cargoFacade.setSerialInfos(cargo.getSerialInfos());
            //货品拓展信息
            cargoFacade.setExtendProps(cargo.getExtendProps());
            if (cargo.getPolluteSign() != null) {
                //清真易污染标识
                cargoFacade.setPolluteSign(cargo.getPolluteSign().getCode());
            }

            if (CollectionUtils.isNotEmpty(cargo.getAttachments())) {
                List<AttachmentInfo> attachmentList = cargo.getAttachments().stream().map(attachmentInfo -> {
                    AttachmentInfo attachment = new AttachmentInfo();
                    attachment.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                    //附件名称
                    attachment.setAttachmentName(attachmentInfo.getAttachmentName());
                    //附件业务类型
                    attachment.setAttachmentType(attachmentInfo.getAttachmentType());
                    //附件文档类型
                    attachment.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                    //附件路径
                    attachment.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                    //附件备注
                    attachment.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                    return attachment;
                }).collect(Collectors.toList());

                cargoFacade.setAttachmentInfos(attachmentList);
            }

            //货品备注
            cargoFacade.setCargoRemark(cargo.getCargoRemark());
            //是否易损
            if (cargo.getCargoVulnerable() != null) {
                cargoFacade.setCargoVulnerable(cargo.getCargoVulnerable().getCode());
            }
            //货品标识
            cargoFacade.setCargoSign(cargo.getCargoSign());
            //货品信息增值服务
            cargoFacade.setCargoProductInfos(cargo.getCargoProductInfos());
            //隐私货品展示信息
            cargoFacade.setPrivacyCargoName(cargo.getPrivacyCargoName());
            cargoFacades.add(cargoFacade);
        }
        return cargoFacades;
    }

    /**
     * 商品信息
     *
     * @param goodsList
     * @return
     */
    private List<GoodsFacade> toGoodsFacades(List<Goods> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }
        List<GoodsFacade> goodsFacadeList = new ArrayList<>(goodsList.size());
        goodsList.forEach(goods -> {
            GoodsFacade goodsFacade = new GoodsFacade();
            goodsFacade.setGoodsQuantity(goods.getGoodsQuantity());
            goodsFacade.setGoodsNo(goods.getGoodsNo());
            goodsFacade.setGoodsUniqueCode(goods.getGoodsUniqueCode());
            goodsFacade.setChannelGoodsNo(goods.getChannelGoodsNo());
            goodsFacade.setGoodsName(goods.getGoodsName());
            goodsFacade.setGoodsAmount(goods.getGoodsAmount());
            goodsFacade.setGoodsType(goods.getGoodsType());
            goodsFacade.setGoodsPrice(goods.getGoodsPrice());
            goodsFacade.setGoodsSerialInfos(goods.getGoodsSerialInfos());
            goodsFacade.setCombinationGoodsVersion(goods.getCombinationGoodsVersion());
            goodsFacade.setExtendProps(goods.getExtendProps());
            goodsFacade.setGoodsProductInfos(goods.getGoodsProductInfos());
            if (CollectionUtils.isNotEmpty(goods.getAttachments())) {
                List<AttachmentInfo> attachmentList = goods.getAttachments().stream().map(attachmentInfo -> {
                    AttachmentInfo attachment = new AttachmentInfo();
                    attachment.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                    //附件名称
                    attachment.setAttachmentName(attachmentInfo.getAttachmentName());
                    //附件业务类型
                    attachment.setAttachmentType(attachmentInfo.getAttachmentType());
                    //附件文档类型
                    attachment.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                    //附件路径
                    attachment.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                    //附件备注
                    attachment.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                    return attachment;
                }).collect(Collectors.toList());
                goodsFacade.setAttachmentInfos(attachmentList);
            }
            // 商品重量
            goodsFacade.setGoodsWeight(goods.getGoodsWeight());
            // 商品体积
            goodsFacade.setGoodsVolume(goods.getGoodsVolume());
            // 商品三维
            goodsFacade.setGoodsDimension(goods.getGoodsDimension());
            // 商品促销信息
            goodsFacade.setSalesInfos(goods.getSalesInfos());
            // 商品净重
            goodsFacade.setNetWeight(goods.getNetWeight());
            goodsFacadeList.add(goodsFacade);
        });
        return goodsFacadeList;
    }

    /**
     * 配送信息对象转换
     *
     * @param shipment
     * @return
     */
    public ShipmentFacade toShipmentFacade(Shipment shipment) {
        if (shipment == null || isNullObject(shipment, Shipment.class)) {
            return null;
        }
        ShipmentFacade shipmentFacade = new ShipmentFacade();
        //预计送达时间
        shipmentFacade.setPlanDeliveryTime(shipment.getPlanDeliveryTime());
        //预计送达时间段
        shipmentFacade.setPlanDeliveryPeriod(shipment.getPlanDeliveryPeriod());
        //期望配送开始时间
        shipmentFacade.setExpectDeliveryStartTime(shipment.getExpectDeliveryStartTime());
        //期望配送结束时间
        shipmentFacade.setExpectDeliveryEndTime(shipment.getExpectDeliveryEndTime());
        //期望取件开始时间
        shipmentFacade.setExpectPickupStartTime(shipment.getExpectPickupStartTime());
        //期望取件结束时间
        shipmentFacade.setExpectPickupEndTime(shipment.getExpectPickupEndTime());
        //取件类型
        if (shipment.getPickupType() != null) {
            shipmentFacade.setPickupType(shipment.getPickupType().getCode());
        }
        //派送类型
        if (shipment.getDeliveryType() != null) {
            shipmentFacade.setDeliveryType(shipment.getDeliveryType().getCode());
        }
        //运输方式
        if (shipment.getTransportType() != null) {
            shipmentFacade.setTransportType(shipment.getTransportType().getCode());
        }
        //温层
        shipmentFacade.setWarmLayer(shipment.getWarmLayer() == null ? null : shipment.getWarmLayer().getCode());
        //揽收站点编码
        shipmentFacade.setStartStationNo(shipment.getStartStationNo());
        //揽收站点名称
        shipmentFacade.setStartStationName(shipment.getStartStationName());
        //揽收站点类型
        shipmentFacade.setStartStationType(shipment.getStartStationType());
        //派送站点名称
        shipmentFacade.setEndStationNo(shipment.getEndStationNo());
        //派送站点编码
        shipmentFacade.setEndStationName(shipment.getEndStationName());
        //目的站点类型
        shipmentFacade.setEndStationType(shipment.getEndStationType());
        //发货仓库编码
        shipmentFacade.setWarehouseNo(shipment.getWarehouseNo());
        //收货仓库编码
        shipmentFacade.setReceiveWarehouseNo(shipment.getReceiveWarehouseNo());
        //车型
        shipmentFacade.setVehicleType(shipment.getVehicleType());
        //预计修改时间
        shipmentFacade.setPlanReceiveTime(shipment.getPlanReceiveTime());
        //取件码
        shipmentFacade.setPickupCode(shipment.getPickupCode());
        //取件码生成方式
        shipmentFacade.setPickupCodeCreateType(shipment.getPickupCodeCreateType());
        //服务要求
        shipmentFacade.setServiceRequirements(shipment.getServiceRequirements());
        //指定地点
        shipmentFacade.setAssignedAddress(shipment.getAssignedAddress());
        //无接触收货方式
        shipmentFacade.setContactlessType(shipment.getContactlessType() == null ? null : shipment.getContactlessType().getCode());
        //收货偏好
        shipmentFacade.setReceivingPreference(shipment.getReceivingPreference());
        //扩展信息
        shipmentFacade.setExtendProps(shipment.getExtendProps());
        //实际预计送达时间
        shipmentFacade.setActualPlanDeliveryTime(shipment.getActualPlanDeliveryTime());
        //起始配送中心
        shipmentFacade.setStartCenterNo(shipment.getStartCenterNo());
        //目的配送中心
        shipmentFacade.setEndCenterNo(shipment.getEndCenterNo());
        //承运商编码
        shipmentFacade.setShipperNo(shipment.getShipperNo());
        //承运商名称
        shipmentFacade.setShipperName(shipment.getShipperName());
        //承运商类型
        shipmentFacade.setShipperType(shipment.getShipperType());
        //接驳站点
        shipmentFacade.setEndTransferStationNo(shipment.getEndTransferStationNo());
        //期望派货开始时间
        shipmentFacade.setExpectDispatchStartTime(shipment.getExpectDispatchStartTime());
        //期望派货结束时间
        shipmentFacade.setExpectDispatchEndTime(shipment.getExpectDispatchEndTime());
        //揽收网点三级类型
        shipmentFacade.setStartStationTypeL3(shipment.getStartStationTypeL3());
        //派送网点三级类型
        shipmentFacade.setEndStationTypeL3(shipment.getEndStationTypeL3());
        return shipmentFacade;
    }

    /**
     * 财务信息转换
     *
     * @param model
     * @return
     */
    public FinanceFacade toFinanceFacade(ExpressOrderModel model) {
        Finance finance = model.getFinance();
        if (finance == null || isNullObject(finance, Finance.class)) {
            return null;
        }
        FinanceFacade financeFacade = new FinanceFacade();
        if (model.isReaddress1Order2End() || model.isKKInterceptionThroughOrderRecord()) {
            Map<String, String> financeExt = model.getFinance().getExtendProps();//新单财务域扩展信息
            // 原单的结算方式
            String orderSettlementType = MapUtils.isNotEmpty(financeExt) ? financeExt.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey()) : null;
            if (StringUtils.isNotBlank(orderSettlementType)) {
                financeFacade.setSettlementType(Integer.valueOf(orderSettlementType));
            }
            // 原单的支付环节
            String orderPaymentStage = MapUtils.isNotEmpty(financeExt) ? financeExt.get(AttachmentKeyEnum.ORIGIN_PAYMENT_STAGE.getKey()) : null;
            if (StringUtils.isNotBlank(orderPaymentStage)) {
                financeFacade.setPaymentStage(Integer.valueOf(orderPaymentStage));
            }
        } else {
            if (finance.getSettlementType() != null) {
                financeFacade.setSettlementType(finance.getSettlementType().getCode());
            }
            //付款环节
            financeFacade.setPaymentStage(finance.getPaymentStage() != null ? finance.getPaymentStage().getCode() : null);
        }

        //预估费用
        financeFacade.setEstimateAmount(finance.getEstimateAmount());

        //预估财务信息
        Optional.ofNullable(finance.getEstimateFinanceInfo()).ifPresent(estimateFinanceInfo -> {
            // 非改址请求持久化更新预估财务信息（改址请求的预估财务信息持久化到改址记录）
            if (!model.isReaddress()) {

                FinanceFacade estimateFinanceFacade = new FinanceFacade();
                // 预估-折前金额
                Optional.ofNullable(estimateFinanceInfo.getPreAmount()).ifPresent(estimateFinanceFacade::setPreAmount);
                // 预估-折后金额
                Optional.ofNullable(estimateFinanceInfo.getDiscountAmount()).ifPresent(estimateFinanceFacade::setDiscountAmount);
                // 预估-计费重量
                Optional.ofNullable(estimateFinanceInfo.getBillingWeight()).ifPresent(estimateFinanceFacade::setBillingWeight);
                // 预估-计费体积
                Optional.ofNullable(estimateFinanceInfo.getBillingVolume()).ifPresent(estimateFinanceFacade::setBillingVolume);
                // 预估-加价后总金额
                Optional.ofNullable(estimateFinanceInfo.getTotalAdditionAmount()).ifPresent(estimateFinanceFacade::setTotalAdditionAmount);
                // 预估-费用明细
                Optional.ofNullable(estimateFinanceInfo.getFinanceDetails()).ifPresent(estimateFinanceDetails -> {
                    // 预估-费用明细信息
                    List<FinanceDetailFacade> financeDetailFacades = Lists.newArrayListWithCapacity(estimateFinanceDetails.size());
                    estimateFinanceDetails.forEach(estimateFinanceDetail -> {
                        // 预估-费用明细对象
                        FinanceDetailFacade estimateFinanceDetailFacade = new FinanceDetailFacade();
                        // 预估-费用编号
                        estimateFinanceDetailFacade.setCostNo(estimateFinanceDetail.getCostNo());
                        // 预估-费用名称
                        estimateFinanceDetailFacade.setCostName(estimateFinanceDetail.getCostName());
                        // 预估-费用产品编码
                        estimateFinanceDetailFacade.setProductNo(estimateFinanceDetail.getProductNo());
                        // 预估-费用产品名称
                        estimateFinanceDetailFacade.setProductName(estimateFinanceDetail.getProductName());
                        // 预估-折扣信息
                        if (CollectionUtils.isNotEmpty(estimateFinanceDetail.getDiscounts())) {
                            List<DiscountFacade> estimateDiscountFacades = new ArrayList<>(estimateFinanceDetail.getDiscounts().size());
                            estimateFinanceDetail.getDiscounts().forEach(estimateDiscount -> {
                                // 预估-折扣信息对象
                                DiscountFacade estimateDiscountFacade = new DiscountFacade();
                                // 预估-折扣码
                                estimateDiscountFacade.setDiscountNo(estimateDiscount.getDiscountNo());
                                // 预估-折扣类型
                                estimateDiscountFacade.setDiscountType(estimateDiscount.getDiscountType());
                                // 预估-折扣金额
                                estimateDiscountFacade.setDiscountedAmount(estimateDiscount.getDiscountedAmount());
                                estimateDiscountFacades.add(estimateDiscountFacade);
                            });
                            estimateFinanceDetailFacade.setDiscountFacades(estimateDiscountFacades);
                        }
                        // 预估-折前金额
                        estimateFinanceDetailFacade.setPreAmount(estimateFinanceDetail.getPreAmount());
                        // 预估-折后金额
                        estimateFinanceDetailFacade.setDiscountAmount(estimateFinanceDetail.getDiscountAmount());
                        // 预估-加价后金额
                        estimateFinanceDetailFacade.setAdditionAmount(estimateFinanceDetail.getAdditionAmount());
                        // 预估-扩展字段
                        estimateFinanceDetailFacade.setExtendProps(estimateFinanceDetail.getExtendProps());
                        financeDetailFacades.add(estimateFinanceDetailFacade);
                    });

                    estimateFinanceFacade.setFinanceDetails(financeDetailFacades);
                });

                financeFacade.setEstimateFinanceInfo(estimateFinanceFacade);
            }
        });

        //折后金额
        financeFacade.setDiscountAmount(finance.getDiscountAmount());
        //结算账号
        financeFacade.setSettlementAccountNo(finance.getSettlementAccountNo());
        //财务预占标识, 1：白条预授权
        financeFacade.setPreemptType(finance.getPreemptType());
        //支付账号
        financeFacade.setPaymentAccountNo(finance.getPaymentAccountNo());
        //支付方式
        if (finance.getPayment() != null) {
            financeFacade.setPayment(finance.getPayment().getCode());
        }
        //支付状态
        financeFacade.setPaymentStatus(finance.getPaymentStatus() != null ? finance.getPaymentStatus().getStatus() : null);
        //询价
        if (finance.getEnquiryType() != null) {
            financeFacade.setEnquiryType(finance.getEnquiryType().getCode());
        }
        //支付截止时间
        financeFacade.setPayDeadline(finance.getPayDeadline());
        //退款状态
        financeFacade.setRefundStatus(finance.getRefundStatus() != null ? finance.getRefundStatus().getStatus() : null);
        //折前金额
        financeFacade.setPreAmount(finance.getPreAmount());
        //总优惠金额
        financeFacade.setTotalDiscountAmount(finance.getTotalDiscountAmount());
        //计费重量
        financeFacade.setBillingWeight(finance.getBillingWeight());
        //计费体积
        financeFacade.setBillingVolume(finance.getBillingVolume());
        //计费模式
        financeFacade.setBillingMode(finance.getBillingMode());
        //计费类型
        financeFacade.setBillingType(finance.getBillingType());
        //收款机构
        financeFacade.setCollectionOrgNo(finance.getCollectionOrgNo());

        //费用明细
        if (CollectionUtils.isNotEmpty(finance.getFinanceDetails())) {
            List<FinanceDetailFacade> financeDetailFacades = new ArrayList<>(finance.getFinanceDetails().size());
            for (FinanceDetail financeDetail : finance.getFinanceDetails()) {
                FinanceDetailFacade financeDetailFacade = new FinanceDetailFacade();
                //费用编码
                financeDetailFacade.setCostNo(financeDetail.getCostNo());
                //费用名称
                financeDetailFacade.setCostName(financeDetail.getCostName());
                //产品编码
                financeDetailFacade.setProductNo(financeDetail.getProductNo());
                //产品名称
                financeDetailFacade.setProductName(financeDetail.getProductName());
                //折前金额
                financeDetailFacade.setPreAmount(financeDetail.getPreAmount());

                if (CollectionUtils.isNotEmpty(financeDetail.getDiscounts())) {
                    List<DiscountFacade> discountFacades = new ArrayList<>(financeDetail.getDiscounts().size());
                    financeDetail.getDiscounts().forEach(discount -> {
                        DiscountFacade discountFacade = new DiscountFacade();
                        discountFacade.setDiscountNo(discount.getDiscountNo());
                        discountFacade.setDiscountType(discount.getDiscountType());
                        discountFacade.setDiscountedAmount(discount.getDiscountedAmount());
                        discountFacade.setOperateType(discount.getOperateType());
                        discountFacades.add(discountFacade);
                    });
                    financeDetailFacade.setDiscountFacades(discountFacades);
                }

                //折扣金额
                financeDetailFacade.setDiscountAmount(financeDetail.getDiscountAmount());
                //备注
                financeDetailFacade.setRemark(financeDetail.getRemark());
                //积分
                financeDetailFacade.setPointsFacade(toPointsFacade(financeDetail.getPoints()));
                // 扩展信息
                financeDetailFacade.setExtendProps(financeFacade.getExtendProps());

                financeDetailFacades.add(financeDetailFacade);
            }
            financeFacade.setFinanceDetails(financeDetailFacades);
        }
        // 支付单号
        financeFacade.setPaymentNo(finance.getPaymentNo());
        //积分信息
        financeFacade.setPointsFacade(toPointsFacade(finance.getPoints()));
        //收费要求信息
        financeFacade.setCostInfos(finance.getCostInfos());
        //询价状态
        if (finance.getEnquiryStatus() != null) {
            financeFacade.setEnquiryStatus(finance.getEnquiryStatus().getCode());
        }
        //财务备注
        financeFacade.setRemark(finance.getRemark());
        //抵扣信息
        if (finance.getDeductionDelegate() != null && finance.getDeductionDelegate().getDeductions() != null) {
            financeFacade.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) finance.getDeductionDelegate().getDeductions()));
        }
        financeFacade.setExtendProps(finance.getExtendProps());

        //附加费用
        financeFacade.setAttachFees(finance.getAttachFees());
        //预估税金
        financeFacade.setEstimatedTax(finance.getEstimatedTax());
        //真实税金
        financeFacade.setActualTax(finance.getActualTax());
        //费用支付状态归集
        financeFacade.setPayStatusMap(finance.getPayStatusMap());
        //税金结算方式
        if (finance.getTaxSettlementType() != null) {
            financeFacade.setTaxSettlementType(finance.getTaxSettlementType());
        }
        //扩展字段
        if(MapUtils.isNotEmpty(finance.getExtendProps())){
            financeFacade.setExtendProps(finance.getExtendProps());
        }
        //多方计费收费总额
        financeFacade.setMultiPartiesTotalAmounts(finance.getMultiPartiesTotalAmounts());
        return financeFacade;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductions
     * @return
     */
    private List<DeductionInfoDto> toDeductionInfoDtos(List<Deduction> deductions) {
        if (CollectionUtils.isEmpty(deductions)) {
            return null;
        }
        List<DeductionInfoDto> deductionInfoDtos = new ArrayList<>(deductions.size());
        deductions.forEach(deduction -> {
            if (deduction != null) {
                DeductionInfoDto dto = new DeductionInfoDto();
                // 抵扣编码
                dto.setDeductionNo(deduction.getDeductionNo());
                // 抵扣金额
                dto.setDeductionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(deduction.getDeductionAmount()));
                // 抵扣方
                dto.setDeductionOrg(deduction.getDeductionOrg());
                // 扩展信息
                dto.setExtendProps(deduction.getExtendProps());
                // 收费方
                dto.setChargingSource(deduction.getChargingSource());
                deductionInfoDtos.add(dto);
            }
        });
        return deductionInfoDtos;
    }

    /**
     * 积分
     *
     * @param points
     * @return
     */
    private PointsFacade toPointsFacade(Points points) {
        if (points == null) {
            return null;
        }
        PointsFacade pointsFacade = new PointsFacade();
        if (points.getRedeemPointsQuantity() != null) {
            QuantityFacade quantityFacade = new QuantityFacade();
            quantityFacade.setUnit(points.getRedeemPointsQuantity().getUnit());
            quantityFacade.setValue(points.getRedeemPointsQuantity().getValue());
            pointsFacade.setRedeemPointsQuantity(quantityFacade);
        }
        if (points.getRedeemPointsAmount() != null) {
            MoneyFacade moneyFacade = new MoneyFacade();
            moneyFacade.setAmount(points.getRedeemPointsAmount().getAmount());
            moneyFacade.setCurrency(points.getRedeemPointsAmount().getCurrency());
            pointsFacade.setRedeemPointsAmount(moneyFacade);
        }
        return pointsFacade;
    }

    /**
     * 营销信息
     *
     * @param promotion
     * @return
     */
    public PromotionFacade toPromotionFacade(Promotion promotion) {
        if (promotion == null || isNullObject(promotion, Promotion.class)) {
            return null;
        }
        PromotionFacade promotionFacade = new PromotionFacade();
        //优惠券信息
        if (CollectionUtils.isNotEmpty(promotion.getTickets())) {
            List<TicketFacade> ticketFacades = new ArrayList<>(promotion.getTickets().size());
            for (Ticket ticket : promotion.getTickets()) {
                if (ticket.getOperateType() == null) {
                    continue;
                }
                TicketFacade ticketFacade = new TicketFacade();
                //优惠券编码
                ticketFacade.setTicketNo(ticket.getTicketNo());
                ticketFacade.setTicketCategory(ticket.getTicketCategory());
                ticketFacade.setTicketType(ticket.getTicketType());
                ticketFacade.setTicketDescription(ticket.getTicketDescription());
                ticketFacade.setTicketDiscountAmount(ticket.getTicketDiscountAmount());
                ticketFacade.setTicketDiscountRate(ticket.getTicketDiscountRate());
                ticketFacade.setTicketDiscountUpperLimit(ticket.getTicketDiscountUpperLimit());
                ticketFacade.setCouponStatus(ticket.getCouponStatus());
                ticketFacade.setTicketUseAmount(ticket.getTicketUseAmount());
                ticketFacade.setOperateType(ticket.getOperateType());
                //优惠券来源
                ticketFacade.setTicketSource(ticket.getTicketSource());
                ticketFacade.setTicketBatchNo(ticket.getTicketBatchNo());
                ticketFacades.add(ticketFacade);
            }
            if (ticketFacades.size() > 0) {
                promotionFacade.setTickets(ticketFacades);
            }
        }
        //折扣信息
        if (CollectionUtils.isNotEmpty(promotion.getDiscounts())) {
            List<DiscountFacade> discountFacades = new ArrayList<>(promotion.getDiscounts().size());
            for (Discount discount : promotion.getDiscounts()) {
                if (discount.getOperateType() == null) {
                    continue;
                }
                DiscountFacade discountFacade = new DiscountFacade();
                //折扣码
                discountFacade.setDiscountNo(discount.getDiscountNo());
                discountFacade.setDiscountType(discount.getDiscountType());
                discountFacade.setOperateType(discount.getOperateType());
                discountFacade.setExtendProps(discount.getExtendProps());
                discountFacades.add(discountFacade);
            }
            if (discountFacades.size() > 0) {
                promotionFacade.setDiscounts(discountFacades);
            }
        }
        //营销标识
        if (CollectionUtils.isNotEmpty(promotion.getActivities())) {
            List<ActivityFacade> activityFacades = new ArrayList<>(promotion.getActivities().size());
            for (Activity activity : promotion.getActivities()) {
                if (activity.getOperateType() == null) {
                    continue;
                }
                ActivityFacade activityFacade = new ActivityFacade();
                //活动编码
                activityFacade.setActivityNo(activity.getActivityNo());
                //活动名称
                activityFacade.setActivityName(activity.getActivityName());
                //参加状态
                activityFacade.setActivityStatus(activity.getActivityStatus());
                //活动内容
                activityFacade.setActivityValue(activity.getActivityValue());
                activityFacade.setOperateType(activity.getOperateType());
                activityFacades.add(activityFacade);
            }
            if (activityFacades.size() > 0) {
                promotionFacade.setActivities(activityFacades);
            }
        }
        if (isNullObject(promotionFacade, PromotionFacade.class)) {
            return null;
        }
        return promotionFacade;
    }

    /**
     * 关联单信息转换
     * 送取同步需要注意 toRefOrderFacades 和 toDeleteRefOrders 方法，要解除绑定的单号不能在 toRefOrderFacades 出现
     *
     * @param context
     * @return
     */
    public List<RefOrderFacade> toRefOrderFacades(ExpressOrderContext context) {
        ExpressOrderModel model = context.getOrderModel();
        List<RefOrderFacade> refOrderFacades = new ArrayList<>();
        if (!model.getRefOrderInfoDelegate().isEmpty()) {
            //关联单信息转换
            for (RefOrder refOrder : model.getRefOrderInfoDelegate().getRefOrders()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(refOrder.getRefOrderNo());
                refOrderFacade.setRefOrderType(refOrder.getRefOrderType() != null ? refOrder.getRefOrderType().getCode() : null);
                refOrderFacade.setRefOrderSubType(refOrder.getRefOrderSubType());
                refOrderFacade.setRemark(refOrder.getRemark());
                refOrderFacades.add(refOrderFacade);
            }
        }
        //TODO 折扣关联单,逻辑上提
        if (model.getPromotion() != null && StringUtils.isNotBlank(model.getPromotion().getDiscountRefOrderNo())) {
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderNo(model.getPromotion().getDiscountRefOrderNo());
            refOrderFacade.setRefOrderType(RefOrderTypeEnum.WORK_ORDER.getCode());
            refOrderFacade.setRefOrderSubType(RefOrderSubType.WorkOrderEnum.DISCOUNT.getCode());
            refOrderFacades.add(refOrderFacade);
        }

        // 送取同步，解除绑定场景
        if (context.containsKeyExtInfo(ContextInfoEnum.DELIVERY_PICKUP_SYNC_BIND_OPERATE_TYPE.getCode())) {
            Integer bindOperateType = (Integer) context.getExtInfo(ContextInfoEnum.DELIVERY_PICKUP_SYNC_BIND_OPERATE_TYPE.getCode());
            DeliveryPickupSyncBindOperateTypeEnum deliveryPickupSyncOperateType = DeliveryPickupSyncBindOperateTypeEnum.fromCode(bindOperateType);
            if (deliveryPickupSyncOperateType != null) {
                if (DeliveryPickupSyncBindOperateTypeEnum.DELIVERY_UNBIND_PICKUP == deliveryPickupSyncOperateType) {
                    // 派送单解除绑定取件单，删除：送取同步-取件单-订单号、送取同步-取件单-运单号
                    refOrderFacades.removeIf(refOrderFacade ->
                            RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode().equals(refOrderFacade.getRefOrderType())
                            || RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode().equals(refOrderFacade.getRefOrderType()));
                } else if (DeliveryPickupSyncBindOperateTypeEnum.PICKUP_UNBIND_DELIVERY == deliveryPickupSyncOperateType) {
                    // 取件单解除绑定派送单，删除：送取同步-派送单-订单号、送取同步-派送单-运单号
                    refOrderFacades.removeIf(refOrderFacade -> RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY_ORDER.getCode().equals(refOrderFacade.getRefOrderType())
                            || RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY.getCode().equals(refOrderFacade.getRefOrderType()));
                }
            }
        }

        if (CollectionUtils.isEmpty(refOrderFacades)) {
            return null;
        }
        return refOrderFacades;
    }

    /**
     * 逆向单:关联单信息转换
     *
     * @param model
     * @return
     */
    public List<RefOrderFacade> toReturnRefOrderFacades(ExpressOrderModel model) {
        List<RefOrderFacade> refOrderFacades = new ArrayList<>();
        /*新增，逆向单正向订单号和逆向订单号绑定新增一条数据，绑定关系为500与50000-1*/
        if (model.getOrderType().getCode().equals(OrderTypeEnum.RETURN_ORDER.getCode())) {
            //逆向单订单号
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderNo(model.orderNo());
            refOrderFacade.setRefOrderType(RefOrderTypeEnum.RETURN_ORDER.getCode());
            refOrderFacades.add(refOrderFacade);

            //逆向单运单号
            RefOrderFacade waybillRefOrderFacade = new RefOrderFacade();
            waybillRefOrderFacade.setRefOrderNo(model.getCustomOrderNo());
            waybillRefOrderFacade.setRefOrderType(RefOrderTypeEnum.REVERSE_WAYBILL.getCode());
            refOrderFacades.add(waybillRefOrderFacade);

        }
        if (refOrderFacades.isEmpty()) {
            return null;
        }
        return refOrderFacades;
    }

    /**
     * 关联单信息转换
     * @param refOrder
     * @return
     */
    public RefOrderFacade toRefOrderFacade(RefOrder refOrder){
        if (refOrder == null) {
            return null;
        }
        RefOrderFacade facade = new RefOrderFacade();
        facade.setRefOrderNo(refOrder.getRefOrderNo());
        if (refOrder.getRefOrderType() != null) {
            facade.setRefOrderType(refOrder.getRefOrderType().getCode());
        }

        facade.setRefOrderSubType(refOrder.getRefOrderSubType());
        facade.setRemark(refOrder.getRemark());
        return facade;
    }
    /**
     * 改址单：信息转换
     *
     * @param model
     * @return
     */
    public List<RefOrderFacade> toChangeAddressOrderFacades(ExpressOrderModel model) {
        List<RefOrderFacade> refOrderFacades = new ArrayList<>();
        //原单绑定改址单的信息
        if (model.getOrderType().getCode().equals(OrderTypeEnum.READDRESS.getCode())) {
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderNo(model.orderNo());
            refOrderFacade.setRefOrderType(RefOrderTypeEnum.READDRESS.getCode());
            refOrderFacades.add(refOrderFacade);
        }
        if (refOrderFacades.size() == 0) {
            return null;
        }
        return refOrderFacades;
    }

    /**
     * 功能：转换隐藏标识请求类
     *
     * @param requestProfile 1
     * @param context        2
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/27 20:38
     */
    public ModifyOrderFacadeRequest toModifyOrderSomeDataFacadeRequest(RequestProfile requestProfile, ExpressOrderContext context) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        //modifyOrderFacadeRequest.setOrderId(context.getOrderModel().getOrderId());
        modifyOrderFacadeRequest.setOrderNo(context.getOrderModel().orderNo());
        modifyOrderFacadeRequest.setHiddenMark(context.getOrderModel().getHiddenMark());
        modifyOrderFacadeRequest.setTenantId(requestProfile.getTenantId());
        modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(context.getOrderModel().getOrderBusinessIdentity()));
        modifyOrderFacadeRequest.setOperator(context.getOrderModel().getOperator());
        modifyOrderFacadeRequest.setOperateTime(context.getOrderModel().getOperateTime());
        return modifyOrderFacadeRequest;
    }

    /**
     * 功能：重试转换隐藏标识请求类
     *
     * @param requestProfile 1
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/27 20:38
     */
    public ModifyOrderFacadeRequest toModifyOrderSomeDataFacadeRetryRequest(RequestProfile requestProfile, DeleteRepositoryMessageDto messageDto) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        //modifyOrderFacadeRequest.setOrderId(messageDto.getOrderId());
        modifyOrderFacadeRequest.setOrderNo(messageDto.getOrderNo());
        modifyOrderFacadeRequest.setHiddenMark(messageDto.getHiddenMark());
        modifyOrderFacadeRequest.setTenantId(requestProfile.getTenantId());
        modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(messageDto.getBusinessIdentity()));
        modifyOrderFacadeRequest.setOperator(messageDto.getOperator());
        modifyOrderFacadeRequest.setOperateTime(messageDto.getOperateTime());
        return modifyOrderFacadeRequest;
    }

    /**
     * MTD费用预估结果（预占金额）持久化请求
     * @param requestProfile 请求头信息
     * @param occupyAmount 预占金额
     * @return ModifyOrderFacadeRequest
     */
    public ModifyOrderFacadeRequest toMtdModifyFacadeRequest(RequestProfile requestProfile, ExpressOrderModel orderModel, Money occupyAmount) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        // 租户
        modifyOrderFacadeRequest.setTenantId(requestProfile.getTenantId());
        // 订单号
        modifyOrderFacadeRequest.setOrderNo(orderModel.orderNo());
        // 预占金额
        FinanceFacade finance = new FinanceFacade();
        finance.setOccupyAmount(occupyAmount);
        modifyOrderFacadeRequest.setFinance(finance);
        return modifyOrderFacadeRequest;
    }

    /**
     * 是否需要更新订单状态
     *
     * @param context
     * @return
     */
    private boolean shouldUpdateStatus(ExpressOrderContext context) {
        if (OrderConstants.YES_VAL.equals(context.getExtInfo(OrderConstants.NOT_UPDATE_STATUS))) {
            return false;
        }

        return true;
    }
    /**
     * 功能：转换回传状态请求类
     *
     * @param context
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/27 20:38
     */
    public ModifyOrderFacadeRequest toCallbackOrderSomeDataFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            if (orderModel.getOrderBusinessIdentity() != null) {
                OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
                businessIdentity.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
                businessIdentity.setBusinessType(orderModel.getOrderBusinessIdentity().getBusinessType());
                businessIdentity.setBusinessScene(orderModel.getOrderBusinessIdentity().getBusinessScene());
                modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(businessIdentity));
            }
            modifyOrderFacadeRequest.setOrderNo(orderModel.orderNo());

            if (shouldUpdateStatus(context)) {
                if (null != orderModel.getOrderStatus()
                        && null != orderModel.getOrderStatus().getOrderStatus()) {
                    modifyOrderFacadeRequest.setOrderStatus(orderModel.getOrderStatus().getOrderStatus().getCode());
                }
                modifyOrderFacadeRequest.setOrderStatusCustom(orderModel.getCustomStatus());
                modifyOrderFacadeRequest.setExecutedStatus(orderModel.getExecutedStatus());
            }

            // 拦截状态
            if (null != orderModel.getInterceptType()) {
                modifyOrderFacadeRequest.setInterceptType(orderModel.getInterceptType().getCode());
            }

            // 回传接口回传的拦截成功需要更新订单取消状态为拦截成功，同时更新订单自定义状态为已拦截
            if (ExpressOrderStatusExtendEnum.LAN_JIE_CHENG_GONG.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_SUCCESS.getCode());
            } else if (null != orderModel.getCancelStatus()) {
                modifyOrderFacadeRequest.setCancelStatus(orderModel.getCancelStatus().getCode());
            }

            if (orderModel.requestProfile() != null) {
                modifyOrderFacadeRequest.setTenantId(orderModel.requestProfile().getTenantId());
            }
            //modifyOrderFacadeRequest.setChannel(toChannelFacade(orderModel.getChannel()));
            modifyOrderFacadeRequest.setFinance(toFinanceFacade(orderModel));
            modifyOrderFacadeRequest.setShipment(toShipmentFacade(orderModel.getShipment()));
            modifyOrderFacadeRequest.setOperator(orderModel.getOperator());
            modifyOrderFacadeRequest.setRemark(orderModel.getRemark());
            modifyOrderFacadeRequest.setExtendProps(handleCallBackExtendProps(context));
            // 扩展单据
            modifyOrderFacadeRequest.setRefOrders(toRefOrderFacades(context));
            // 关联单扩展单据类型
            Optional.ofNullable(orderModel.getRefOrderInfoDelegate()).ifPresent(refOrderDelegate ->
                    modifyOrderFacadeRequest.setExtendRefOrder(refOrderDelegate.getExtendProps())
            );
            // 商品信息
            modifyOrderFacadeRequest.setGoodsList(toGoodsFacades((List<Goods>) orderModel.getGoodsDelegate().getGoodsList()));

            modifyOrderFacadeRequest.setRecheckWeight(orderModel.getRecheckWeight());
            modifyOrderFacadeRequest.setRecheckVolume(orderModel.getRecheckVolume());

            // 履约信息
            modifyOrderFacadeRequest.setFulfillment(toFulfillmentFacade(orderModel.getFulfillment()));

            if (expressUccConfigCenter.isPackageModeAttachFeesSwitch()) {
                String callbackIssueFlag = orderModel.getAttachment(OrderConstants.CALLBACK_ISSUE_FLAG);
                if (OrderConstants.YES_VAL.equals(callbackIssueFlag)) {
                    // 附加费清空
                    if (CollectionUtils.isEmpty(orderModel.getFinance().getAttachFees())) {
                        List<String> clearFields = new ArrayList<>();
                        clearFields.add(ModifyItemConfigEnum.ATTACH_FEES.getCode());
                        modifyOrderFacadeRequest.setClearFields(clearFields);
                    }
                }
            }
        }
        return modifyOrderFacadeRequest;
    }

    /**
     * 功能: B2C转换回传
     *
     * @param:
     * @return:
     * @throw:
     * @description: 状态回传的时候，如果订单状态为已取消，订单中心只更新个性化状态，不更新主状态，主状态在取消下发的时候进行更新
     * @author: liufarui
     * @date: 2021/7/5 14:16
     */
    public ModifyOrderFacadeRequest toB2CCallbackOrderSomeDataFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

            if (orderModel.getOrderBusinessIdentity() != null) {
                OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
                businessIdentity.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
                businessIdentity.setBusinessType(orderModel.getOrderBusinessIdentity().getBusinessType());
                businessIdentity.setBusinessScene(orderModel.getOrderBusinessIdentity().getBusinessScene());
                modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(businessIdentity));
            }

            modifyOrderFacadeRequest.setOrderNo(orderModel.orderNo());
            if (shouldUpdateStatus(context)) {
                // 回传的时候订单主状态不可以被更新为取消，但是扩展状态是可以被更新的
                if (null != orderModel.getOrderStatus()
                        && null != orderModel.getOrderStatus().getOrderStatus()
                        && OrderStatusEnum.CANCELED != orderModel.getOrderStatus().getOrderStatus()) {
                    modifyOrderFacadeRequest.setOrderStatus(orderModel.getOrderStatus().getOrderStatus().getCode());
                }
                modifyOrderFacadeRequest.setOrderStatusCustom(orderModel.getCustomStatus());
                modifyOrderFacadeRequest.setExecutedStatus(orderModel.getExecutedStatus());
            }

            if (orderModel.requestProfile() != null) {
                modifyOrderFacadeRequest.setTenantId(orderModel.requestProfile().getTenantId());
            }
            //modifyOrderFacadeRequest.setChannel(toChannelFacade(orderModel.getChannel()));
            modifyOrderFacadeRequest.setFinance(toFinanceFacade(orderModel));
            modifyOrderFacadeRequest.setShipment(toShipmentFacade(orderModel.getShipment()));
            modifyOrderFacadeRequest.setOperator(orderModel.getOperator());
            modifyOrderFacadeRequest.setRemark(orderModel.getRemark());
            modifyOrderFacadeRequest.setExtendProps(handleCallBackExtendProps(context));

            // 拦截回传时需要更新取消状态和拦截类型，
            // 拦截类型只有在仅拦截时才会更新，通过判断拦截类型是否有值来决定是否需要更新拦截类型
            if (null != orderModel.getOrderStatus()
                    && null != orderModel.getOrderStatus().getOrderStatus()) {
                //拦截状态处理
                callbackInterceptStatusPro(orderModel,orderModel.getOrderStatus().getOrderStatus(),modifyOrderFacadeRequest);
            }

            // 回传传入拦截失败-100000，更新订单的取消和拦截状态为拦截失败
            if (ExpressOrderStatusExtendEnum.INTERCEPT_FAILED.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_FAIL.getCode());
                modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.INTERCEPT_FAIL.getCode());
                setInterceptFacade(modifyOrderFacadeRequest);
            }

            // 回传接口回传的拦截成功需要更新订单取消状态为拦截成功，同时更新订单自定义状态为已拦截
            if (ExpressOrderStatusExtendEnum.LAN_JIE_CHENG_GONG.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                if ((DiscardStatusEnum.DISCARD_DOING == orderSnapshot.getDiscardStatus()
                        || DiscardStatusEnum.SCRAP_DOING == orderSnapshot.getDiscardStatus()) && !UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
                    LOGGER.info("回传拦截成功(-860)，并且是弃货中或报废中，不更新拦截相关状态");
                } else {
                    modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_SUCCESS.getCode());
                    modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.INTERCEPT_SUCCESS.getCode());
                    setInterceptFacade(modifyOrderFacadeRequest);
                }
            }

            // 回传传入解除拦截成功-2970，更新订单的拦截状态为解除拦截成功
            if (ExpressOrderStatusExtendEnum.LIFTED_INTERCEPT_SUCCESS.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                if (null != orderModel.getOrderSnapshot().getInterceptType()) {
                    modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.LIFTED_INTERCEPT_SUCCESS.getCode());
                    setInterceptFacade(modifyOrderFacadeRequest);
                }
            }

            // 回传传入解除拦截失败-100100，更新订单的拦截状态为解除拦截失败
            if (ExpressOrderStatusExtendEnum.LIFTED_INTERCEPT_FAIL.getExtendStatus().equals(orderModel.getExecutedStatus()) && !UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
                if (null != orderModel.getOrderSnapshot().getInterceptType()) {
                    modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.LIFTED_INTERCEPT_FAIL.getCode());
                    setInterceptFacade(modifyOrderFacadeRequest);
                }
            }

            //预分拣结果处理
            handleCallBackPresortExtend(orderModel);
            if (orderModel.getOrderSnapshot().getProductDelegate() != null) {
                List<Product> list1 = (List<Product>) orderModel.getProductDelegate().getProducts();
                modifyOrderFacadeRequest.setProducts(toB2CCallBackProductFacades(orderModel.getOrderSnapshot(), list1));
            }
            modifyOrderFacadeRequest.setRecheckWeight(orderModel.getRecheckWeight());
            modifyOrderFacadeRequest.setRecheckVolume(orderModel.getRecheckVolume());

            // 商品信息
            modifyOrderFacadeRequest.setGoodsList(toGoodsFacades((List<Goods>) orderModel.getGoodsDelegate().getGoodsList()));
            // 履约信息
            modifyOrderFacadeRequest.setFulfillment(toFulfillmentFacade(orderModel.getFulfillment()));

            if (expressUccConfigCenter.isPackageModeAttachFeesSwitch()) {
                String callbackIssueFlag = orderModel.getAttachment(OrderConstants.CALLBACK_ISSUE_FLAG);
                if (OrderConstants.YES_VAL.equals(callbackIssueFlag)) {
                    // 附加费清空
                    if (CollectionUtils.isEmpty(orderModel.getFinance().getAttachFees())) {
                        List<String> clearFields = new ArrayList<>();
                        clearFields.add(ModifyItemConfigEnum.ATTACH_FEES.getCode());
                        modifyOrderFacadeRequest.setClearFields(clearFields);
                    }
                }
            }

            // 弃货状态
            if (orderModel.getDiscardStatus() != null) {
                modifyOrderFacadeRequest.setDiscardStatus(orderModel.getDiscardStatus().getCode());
            }

            // 关联单扩展单据类型
            Optional.ofNullable(orderModel.getRefOrderInfoDelegate()).ifPresent(refOrderDelegate ->
                    modifyOrderFacadeRequest.setExtendRefOrder(refOrderDelegate.getExtendProps())
            );
        }
        return modifyOrderFacadeRequest;
    }

    private void setInterceptFacade(ModifyOrderFacadeRequest modifyOrderFacadeRequest) {
        InterceptFacade interceptFacade = new InterceptFacade();
        interceptFacade.setInterceptResultTime(new Date());
        modifyOrderFacadeRequest.setInterceptFacade(interceptFacade);
    }

    /**
     * 回传扩展字段处理：目前只处理extendProps.customerInfoExtendProps的增量更新
     * 处理完后当前单的extendProps保持不变，原因：有的逻辑处理依赖当前单数据，不能直接补全
     *
     * @param context 上下文
     * @return Map 新的extendProps
     */
    private Map<String, String> handleCallBackExtendProps(ExpressOrderContext context) {
        ExpressOrderModel model = context.getOrderModel();
        if (model == null || model.getExtendProps() == null) {
            return null;
        }
        Map<String, String> extendProps = new HashMap<>(model.getExtendProps());
        if (!shouldUpdateStatus(context)) {
            //不更新订单状态，商家扩展状态也不更新
            if (extendProps.containsKey(OrderConstants.SELLER_EXTEND_STATUS)) {
                extendProps.remove(OrderConstants.SELLER_EXTEND_STATUS);
            }
        }
        // 目前只处理extendProps.customerInfoExtendProps的增量更新，当前单没有直接返回
        if (!model.getExtendProps().containsKey(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS)) {
            return extendProps;
        }
        Map<String, String> customerInfoExtendProps = getCustomerInfoExtendProps(model);
        Map<String, String> snapCustomerInfoExtendProps = getCustomerInfoExtendProps(model.getOrderSnapshot());
        if (snapCustomerInfoExtendProps == null) {
            snapCustomerInfoExtendProps = new HashMap<>();
        }
        if (customerInfoExtendProps != null) {
            snapCustomerInfoExtendProps.putAll(customerInfoExtendProps);
        }
        extendProps.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS, JSONUtils.mapToJson(snapCustomerInfoExtendProps));

        return extendProps;
    }

    /**
     * 回传扩展字段处理（预分拣结果处理）
     * 将当前单的 extendProps-customerInfoExtendProps-beyondMessagePick
     * 或者 extendProps-customerInfoExtendProps-beyondMessageDeliver
     * 赋值到 extendProps-presortExtend
     *
     * @param model
     * @return
     */
    private void handleCallBackPresortExtend(ExpressOrderModel model) {
        ManualPresortExtend presortExtend = new ManualPresortExtend();
        //获取快照中预分拣结果
        if (StringUtils.isNotBlank(model.getOrderSnapshot().getExtendProps().get(OrderConstants.PRESORT_EXTEND))) {
            presortExtend = JSONUtils.jsonToBean(model.getOrderSnapshot().getExtendProps().get(OrderConstants.PRESORT_EXTEND), ManualPresortExtend.class);
        }
        //揽收预分拣超区处理
        if (BEYOND_YES.equals(model.getAttachment(OrderConstants.PICKUP_PRESORT))) {
            if (StringUtils.isNotBlank(model.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS))) {
                Map<String, String> customerInfoExtendProps = JSONUtils.jsonToMap(model.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                if (StringUtils.isNotBlank(customerInfoExtendProps.get(OrderConstants.BEYOND_MESSAGE_PICK))) {
                    presortExtend.setStartStation(JSONUtils.jsonToBean(customerInfoExtendProps.get(OrderConstants.BEYOND_MESSAGE_PICK), ManualPresortExtend.ManualPresortDto.class));
//                    customerInfoExtendProps.put(OrderConstants.BEYOND_MESSAGE_PICK, presortExtend.getStartStation().getSecondLevelOverAreaValue());
//                    model.putAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS, JSONUtils.mapToJson(customerInfoExtendProps));
                    model.putAttachment(OrderConstants.PRESORT_EXTEND, JSONUtils.beanToJSONDefault(presortExtend));
                }
            }
        }
        //派送预分拣超区处理
        if (BEYOND_YES.equals(model.getAttachment(OrderConstants.DELIVERY_PRESORT))) {
            if (StringUtils.isNotBlank(model.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS))) {
                Map<String, String> customerInfoExtendProps = JSONUtils.jsonToMap(model.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                if (StringUtils.isNotBlank(customerInfoExtendProps.get(OrderConstants.BEYOND_MESSAGE_DELIVER))) {
                    presortExtend.setEndStation(JSONUtils.jsonToBean(customerInfoExtendProps.get(OrderConstants.BEYOND_MESSAGE_DELIVER), ManualPresortExtend.ManualPresortDto.class));
//                    customerInfoExtendProps.put(OrderConstants.BEYOND_MESSAGE_DELIVER, presortExtend.getStartStation().getSecondLevelOverAreaValue());
//                    model.putAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS, JSONUtils.mapToJson(customerInfoExtendProps));
                    model.putAttachment(OrderConstants.PRESORT_EXTEND, JSONUtils.beanToJSONDefault(presortExtend));
                }
            }
        }
    }

    /**
     * 功能：耗材售卖转换回传状态请求类
     *
     * @param context 上下文
     * @Return cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest
     * @author: sunjingkai5
     * @date: 2022/3/31 10:30
     */
    public ModifyOrderFacadeRequest toPackingCallbackOrderSomeDataFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            if (context.getOrderModel().getOrderBusinessIdentity() != null) {
                OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
                businessIdentity.setBusinessUnit(context.getOrderModel().getOrderBusinessIdentity().getBusinessUnit());
                businessIdentity.setBusinessType(context.getOrderModel().getOrderBusinessIdentity().getBusinessType());
                businessIdentity.setBusinessScene(context.getOrderModel().getOrderBusinessIdentity().getBusinessScene());
                modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(businessIdentity));
            }
            if (context.getOrderModel().getOrderSnapshot() != null) {
                // 判断详情是否有运单号，没有的话把回传的运单号转换
                if (null == context.getOrderModel().getOrderSnapshot().getRefOrderInfoDelegate() ||
                        StringUtils.isBlank(context.getOrderModel().getOrderSnapshot().getRefOrderInfoDelegate().getWaybillNo())) {
                    RefOrderFacade refOrderFacade = new RefOrderFacade();
                    refOrderFacade.setRefOrderNo(context.getOrderModel().getRefOrderInfoDelegate().getWaybillNo());
                    refOrderFacade.setRefOrderType(RefOrderTypeEnum.DELIVERY.getCode());
                    refOrderFacade.setRefOrderSubType(RefOrderSubType.DeliveryEnum.FORWARD_DELIVERY.getCode());
                    modifyOrderFacadeRequest.setRefOrders(Lists.newArrayList(refOrderFacade));
                }
            }
            modifyOrderFacadeRequest.setOrderNo(context.getOrderModel().orderNo());
            if (shouldUpdateStatus(context)) {
                if (context.getOrderModel().getOrderStatus() != null && context.getOrderModel().getOrderStatus().getOrderStatus() != null) {
                    modifyOrderFacadeRequest.setOrderStatus(context.getOrderModel().getOrderStatus().getOrderStatus().getCode());
                }
                modifyOrderFacadeRequest.setOrderStatusCustom(context.getOrderModel().getCustomStatus());
                modifyOrderFacadeRequest.setExecutedStatus(context.getOrderModel().getExecutedStatus());
            }

            if (context.getOrderModel().requestProfile() != null) {
                modifyOrderFacadeRequest.setTenantId(context.getOrderModel().requestProfile().getTenantId());
            }
            modifyOrderFacadeRequest.setOperator(context.getOrderModel().getOperator());
            modifyOrderFacadeRequest.setRemark(context.getOrderModel().getRemark());
        }
        return modifyOrderFacadeRequest;
    }

    /**
     * 逆向单持久化转换，仅有财务信息以及必要信息
     *
     * @param context
     * @return
     * @throws ParseException
     */
    public ModifyOrderFacadeRequest toReverseOrChangeAddressOrderFacadeRequest(ExpressOrderContext context) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel model = context.getOrderModel();
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //业务身份信息
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(model.getOrderBusinessIdentity()));
        //订单id
        //facadeRequest.setOrderId(model.getOrderId());
        //订单号
        facadeRequest.setOrderNo(model.orderNo());
        //财务信息
        facadeRequest.setFinance(toFinanceFacade(model));
        //改址记录
        /*if(null != context.getOrderModel().getModifyRecordDelegate() && CollectionUtils.isNotEmpty(context.getOrderModel().getModifyRecordDelegate().getModifyRecords()) && isModifyRecord){
            //最新改址记录
            facadeRequest.setModifyRecordListOperateType(ModifyRecordListUpdateTypeEnum.ALL_COVER.getCode());
            facadeRequest.setModifyRecordDtos(toAllCoverRecordDtos(context.getOrderModel().getModifyRecordDelegate().getModifyRecords()));
        }*/
        facadeRequest.setOrderSign(model.getOrderSign());
        facadeRequest.setModifiedFields(model.getModifiedFields());
        return facadeRequest;
    }

    /**
     * 功能：转换逆向单 request
     *
     * @param context 1
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/4/12 11:06
     */
    public ModifyOrderFacadeRequest toModifyReturnOrderFacadeRequest(ExpressOrderContext context) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel model = context.getOrderModel();
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //订单号,todo 原单号 临时承接字段
        facadeRequest.setOrderNo(model.getRefOrderInfoDelegate().getOriginalNo());
        //facadeRequest.setOrderId(model.getOrderSnapshot().getOrderId());
        //oms修改时间
        facadeRequest.setOperateTime(model.getOperateTime());
        //关联单信息
        facadeRequest.setRefOrders(toReturnRefOrderFacades(model));

        //b2c c2b需要记录订单是否换单（是否有改址单和逆向单）
        if (BusinessUnitEnum.CN_JDL_B2C.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_O2O_B.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_C2B.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_CC_B2C.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())) {
            // 数据层通过putAll操作，没有传的字段不会覆盖为空
            // 同时可以解决并发场景数据不一致，信息覆盖问题
            Map<String, String> extendMap = new HashMap<>(2);
            extendMap.put(NEW_ORDER, NEW_ORDER_VALUE);//已换单
            extendMap.put(MODIFY_TIMES, MODIFY_TIMES_VALUE);
            facadeRequest.setExtendProps(extendMap);
        }
        return facadeRequest;
    }

    /**
     * 接单场景改址单持久化绑定原单与改址单关系
     *
     * @param context
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest
     * @throws
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/4/14 23:27
     */
    public ModifyOrderFacadeRequest toChangeAddressOrderFacadeRequest(ExpressOrderContext context) {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel model = context.getOrderModel();
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //订单号
        facadeRequest.setOrderNo(model.getRefOrderInfoDelegate().getOriginalOrderNo());
        //oms修改时间todoxuezhiguo5
        facadeRequest.setOperateTime(model.getOperateTime());
        //关联单信息
        facadeRequest.setRefOrders(toChangeAddressOrderFacades(model));

        Map<String, String> extendMap = new HashMap<>(model.getOrderSnapshot().getExtendProps());

        //b2c需要记录订单是否换单（是否有改址单和逆向单）
        if (BusinessUnitEnum.CN_JDL_B2C.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_O2O_B.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_C2B.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_CC_B2C.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())) {
            extendMap.put(NEW_ORDER, NEW_ORDER_VALUE);//已换单
            extendMap.put(MODIFY_TIMES, MODIFY_TIMES_VALUE);
        }

        // 修改原单打标信息--先款改址单打改址申请成功，后款改址单打改址成功
        //原单信息
        ExpressOrderModel originOrderModel = context.getOrderModel().getOrderSnapshot();
        String modifyMark = null;
        if (originOrderModel.getExtendProps() != null) {
            modifyMark = originOrderModel.getExtendProps().get(AttachmentKeyEnum.MODIFY_MARK.getKey());
        }
        if (StringUtils.isBlank(modifyMark)) {
            modifyMark = ModifyMarkUtil.getInitMark();
        }

        if (PaymentStageEnum.ONLINEPAYMENT == model.getFinance().getPaymentStage()) {
            modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition(), ReaddressMarkEnum.APPLY_SUCCESS.getCode());
        } else if (PaymentStageEnum.CASHONDELIVERY == model.getFinance().getPaymentStage()) {
            modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition(), ReaddressMarkEnum.RESULT_SUCCESS.getCode());
        }

        extendMap.put(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);
        facadeRequest.setExtendProps(extendMap);
        return facadeRequest;
    }

    /**
     * 接单场景下发成功修改当前单
     *
     * @param context
     * @return
     */
    public ModifyOrderFacadeRequest toCreateIssueOrderSomeDataFacadeRequest(ExpressOrderContext context) {
        ExpressOrderModel expressOrderModel = context.getOrderModel();
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (expressOrderModel.getOrderBusinessIdentity() != null) {
            OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
            businessIdentity.setBusinessUnit(expressOrderModel.getOrderBusinessIdentity().getBusinessUnit());
            businessIdentity.setBusinessType(expressOrderModel.getOrderBusinessIdentity().getBusinessType());
            modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(businessIdentity));
        }
        //modifyOrderFacadeRequest.setOrderId(expressOrderModel.getOrderId());
        modifyOrderFacadeRequest.setOrderNo(expressOrderModel.orderNo());
        // 主状态
        if (expressOrderModel.getOrderStatus() != null && expressOrderModel.getOrderStatus().getOrderStatus() != null) {
            modifyOrderFacadeRequest.setOrderStatus(expressOrderModel.getOrderStatus().getOrderStatus().getCode());
        }
        // 履约执行状态
        if(StringUtils.isNotBlank(expressOrderModel.getExecutedStatus())){
            modifyOrderFacadeRequest.setExecutedStatus(expressOrderModel.getExecutedStatus());
        }
        // 自定义状态
        modifyOrderFacadeRequest.setOrderStatusCustom(expressOrderModel.getCustomStatus());
        if (expressOrderModel.requestProfile() != null) {
            modifyOrderFacadeRequest.setTenantId(expressOrderModel.requestProfile().getTenantId());
        }
        return modifyOrderFacadeRequest;
    }

    /**
     * 重处理场景下发成功批量修改状态
     *
     * @param expressOrderModel
     * @return
     */
    public ModifyOrderFacadeRequest toReacceptIssueOrderSomeDataFacadeRequest(ExpressOrderModel expressOrderModel) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (expressOrderModel.getOrderBusinessIdentity() != null) {
            OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
            businessIdentity.setBusinessUnit(expressOrderModel.getOrderBusinessIdentity().getBusinessUnit());
            businessIdentity.setBusinessType(expressOrderModel.getOrderBusinessIdentity().getBusinessType());
            modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(businessIdentity));
        }
//        modifyOrderFacadeRequest.setOrderId(expressOrderModel.getOrderId());
        modifyOrderFacadeRequest.setOrderNo(expressOrderModel.orderNo());
        if (expressOrderModel.getOrderStatus() != null && expressOrderModel.getOrderStatus().getOrderStatus() != null) {
            modifyOrderFacadeRequest.setOrderStatus(expressOrderModel.getOrderStatus().getOrderStatus().getCode());
        }
        if(StringUtils.isNotBlank(expressOrderModel.getExecutedStatus())){
            modifyOrderFacadeRequest.setExecutedStatus(expressOrderModel.getExecutedStatus());
        }
        modifyOrderFacadeRequest.setOrderStatusCustom(expressOrderModel.getCustomStatus());
        if (expressOrderModel.requestProfile() != null) {
            modifyOrderFacadeRequest.setTenantId(expressOrderModel.requestProfile().getTenantId());
        }
        return modifyOrderFacadeRequest;
    }

    /**
     * 重处理场景下发成功批量修改状态
     *
     * @param orderModelList
     * @return
     */
    public List<ModifyOrderFacadeRequest> toReacceptIssueOrderSomeDataFacadeRequestList(List<ExpressOrderModel> orderModelList) {
        List<ModifyOrderFacadeRequest> facadeRequestList = new ArrayList<>();
        for(ExpressOrderModel orderModel : orderModelList) {
            facadeRequestList.add(toReacceptIssueOrderSomeDataFacadeRequest(orderModel));
        }
        return facadeRequestList;
    }

    /**
     * 改址单/退货单接单场景下发成功修改原单信息
     *
     * @param newOrderTypeEnum 新单的订单类型，改址单或者退货单
     * @param originalOrder 原单
     * @return
     */
    public ModifyOrderFacadeRequest toCreateIssueSnapOrderSomeDataFacadeRequest(OrderTypeEnum newOrderTypeEnum, ExpressOrderModel originalOrder) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (originalOrder.getOrderBusinessIdentity() != null) {
            OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
            businessIdentity.setBusinessUnit(originalOrder.getOrderBusinessIdentity().getBusinessUnit());
            businessIdentity.setBusinessType(originalOrder.getOrderBusinessIdentity().getBusinessType());
            modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(businessIdentity));
        }
        modifyOrderFacadeRequest.setOrderNo(originalOrder.orderNo());

        // 操作类型不为空（实际只有删除）的原单增值产品，需要下发
        if (!originalOrder.getProductDelegate().isEmpty()) {
            List<Product> products = (List<Product>) originalOrder.getProductDelegate().getProducts();

            List<ProductFacade> productFacades = new ArrayList<>();
            for (Product product : products) {
                if (product.getOperateType() != null) {
                    ProductFacade productFacade = new ProductFacade();
                    productFacade.setProductNo(product.getProductNo());
                    productFacade.setProductType(product.getProductType());
                    productFacade.setProductName(product.getProductName());
                    productFacade.setProductAttrs(product.getProductAttrs());
                    productFacade.setOperateType(product.getOperateType());
                    productFacades.add(productFacade);
                }
            }
            modifyOrderFacadeRequest.setProducts(productFacades);
        }

        // 改址单需要下发财务信息，退货单不需要
        if (OrderTypeEnum.READDRESS == newOrderTypeEnum) {
            FinanceFacade financeFacade = new FinanceFacade();
            financeFacade.setSettlementType(originalOrder.getFinance().getSettlementType().getCode());
            financeFacade.setPaymentStage(originalOrder.getFinance().getPaymentStage().getCode());
            //原单支付成功--修改为支付结果监听
            //financeFacade.setPaymentStatus(originalOrder.getFinance().getPaymentStatus().getStatus());
            modifyOrderFacadeRequest.setFinance(financeFacade);
        }

        String modifyMark = null;

        if (originalOrder.getExtendProps() != null) {
            modifyMark = originalOrder.getExtendProps().get(AttachmentKeyEnum.MODIFY_MARK.getKey());
        }
        if (StringUtils.isBlank(modifyMark)) {
            modifyMark = ModifyMarkUtil.getInitMark();
        }

        modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition(), ReaddressMarkEnum.RESULT_SUCCESS.getCode());

        Map<String, String> extendProps = new HashMap<>();
        extendProps.put(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);
        modifyOrderFacadeRequest.setExtendProps(extendProps);

        return modifyOrderFacadeRequest;
    }

    /**
     * 取消修改转换
     *
     * @return
     */
    public ModifyOrderFacadeRequest toCancelModifyOrderFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            //订单ID
            //request.setOrderId(context.getOrderModel().getOrderSnapshot().getOrderId());
            //订单号
            request.setOrderNo(context.getOrderModel().getOrderSnapshot().orderNo());
            //租户
            request.setTenantId(context.getOrderModel().requestProfile() != null
                    ? context.getOrderModel().requestProfile().getTenantId() : null);

            // 取当前单主档扩展字段
            Map<String, String> extendMap = context.getOrderModel().getExtendProps();
            if (extendMap == null) {
                extendMap = new HashMap<>();
            }
            //取消状态
            if (context.getOrderModel().getCancelStatus() != null) {
                request.setCancelStatus(context.getOrderModel().getCancelStatus().getCode());
                Map<String, String> customerInfoExtendProps = JSONUtils.jsonToMap(context.getOrderModel().getOrderSnapshot()
                        .getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                if (customerInfoExtendProps == null){
                    customerInfoExtendProps = new HashMap<>();
                }
                // customerInfoExtendProps的取消原因等，仅快递B2C使用 todo 废弃放customerInfoExtendProps，统一放到extendMap
                customerInfoExtendProps.put("cancelReasonCode",context.getOrderModel().getCancelReasonCode());
                customerInfoExtendProps.put("cancelReason",context.getOrderModel().getCancelReason());
                customerInfoExtendProps.put("cancelRemark",context.getOrderModel().getRemark());
                extendMap.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS,JSONUtils.beanToJSONDefault(customerInfoExtendProps));
                extendMap.put("cancelReasonCode",context.getOrderModel().getCancelReasonCode());
                extendMap.put("cancelReason",context.getOrderModel().getCancelReason());
                extendMap.put("cancelRemark",context.getOrderModel().getRemark());
                if (CancelStatusEnum.CANCEL_SUCCESS.equals(context.getOrderModel().getCancelStatus())) {
                    request.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
                    request.setOrderStatusCustom(ExpressOrderStatusCustomEnum.CANCELED.customOrderStatus());
                    //OFC取消不再补扩展状态和商家扩展状态
                    if (SystemCallerEnum.EXPRESS_OFC != context.getOrderModel().getChannel().getSystemCaller()) {
                        request.setExecutedStatus(EXECUTED_STATUS_CANCEL);
                        extendMap.put(OrderConstants.SELLER_EXTEND_STATUS, B2CExtendStatusEnum.XIA_DAN_QU_XIAO.getExtendStatus());
                    }
                }
                if (CancelStatusEnum.INTERCEPTING.equals(context.getOrderModel().getCancelStatus())) {
                    request.setExecutedStatus(ExpressOrderStatusExtendEnum.FA_QI_LAN_JIE.getExtendStatus());
                    extendMap.put(OrderConstants.SELLER_EXTEND_STATUS, B2CExtendStatusEnum.FA_QI_LANG_JIE_110.getExtendStatus());
                }
            }

            if (null != context.getOrderModel().getInterceptType()) {
                InterceptFacade interceptFacade = new InterceptFacade();
                interceptFacade.setInterceptResultTime(context.getOrderModel().getIntercept().getInterceptResultTime());
                request.setInterceptTime(context.getOrderModel().getIntercept().getInterceptTime());
                request.setInterceptFacade(interceptFacade);
                request.setInterceptType(context.getOrderModel().getInterceptType().getCode());
                // 只有仅拦截的情况下订单主状态才会更新为已拦截
                if (CancelInterceptTypeEnum.ONLY_INTERCEPT == context.getOrderModel().getCancelInterceptType()
                    && InterceptTypeEnum.INTERCEPT_SUCCESS.equals(context.getOrderModel().getInterceptType())) {
                    request.setOrderStatus(OrderStatusEnum.INTERCEPT.getCode());
                    request.setOrderStatusCustom(ExpressOrderStatusCustomEnum.INTERCEPT.customOrderStatus());
                    //OFC取消不再补扩展状态和商家扩展状态
                    if (SystemCallerEnum.EXPRESS_OFC != context.getOrderModel().getChannel().getSystemCaller()) {
                        request.setExecutedStatus(EXECUTED_STATUS_INTERCEPT);
                        extendMap.put(OrderConstants.SELLER_EXTEND_STATUS, B2CExtendStatusEnum.FA_QI_LANG_JIE_860.getExtendStatus());
                    }
                }
            }

            // 询价状态 快运整车存在【取消订单+取消询价单】【仅取消询价单】场景，会更新询价状态
            if (context.getOrderModel().getFinance() != null && context.getOrderModel().getFinance().getEnquiryStatus() != null) {
                FinanceFacade financeFacade = new FinanceFacade();
                financeFacade.setEnquiryStatus(context.getOrderModel().getFinance().getEnquiryStatus().getCode());
                request.setFinance(financeFacade);
            }

            request.setExtendProps(extendMap);
            //修改时间
            request.setUpdateTime(new Date());
            //修改人
            request.setUpdateUser(context.getOrderModel().getOperator());
            request.setOperator(context.getOrderModel().getOperator());
            //拦截信息
            if (null != context.getOrderModel().getIntercept()){
                InterceptFacade interceptFacade = request.getInterceptFacade();
                if (null == interceptFacade) {
                    interceptFacade = new InterceptFacade();
                }
                Intercept intercept = context.getOrderModel().getIntercept();
                interceptFacade.setInterceptHandlingMode(intercept.getInterceptHandlingMode());
                interceptFacade.setInterceptStationNo(intercept.getInterceptStationNo());
                interceptFacade.setInterceptAddress(AddressMapper.INSTANCE.toAddressInfo(intercept.getInterceptAddress()));
                interceptFacade.setInterceptRouteNode(intercept.getInterceptRouteNode());
                interceptFacade.setInterceptPayAccountNo(intercept.getInterceptPayAccountNo());
                request.setInterceptFacade(interceptFacade);
            }
            //弃货状态
            if (context.getOrderModel().getDiscardStatus() != null) {
                request.setDiscardStatus(context.getOrderModel().getDiscardStatus().getCode());
            }

            // 只加渠道扩展字段
            if (context.getOrderModel().getChannel() != null) {

                if (context.getOrderModel().getChannel().getExtendProps() != null) {
                    ChannelFacade channelFacade = new ChannelFacade();
                    channelFacade.setExtendProps(context.getOrderModel().getChannel().getExtendProps());
                    request.setChannel(channelFacade);
                }

            }

        }
        return request;
    }

    /**
     * 询价修改转换
     *
     * @return
     */
    public ModifyOrderFacadeRequest toEnquiryModifyOrderFacadeRequest(ExpressOrderContext context) {
        if (null == context || null == context.getOrderModel()) {
            return new ModifyOrderFacadeRequest();
        }
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        ExpressOrderModel orderModel = context.getOrderModel();
        //订单号
        request.setOrderNo(orderModel.getOrderSnapshot().orderNo());
        //租户
        request.setTenantId(orderModel.requestProfile() != null ? orderModel.requestProfile().getTenantId() : null);
        //渠道信息
        request.setChannel(toChannelFacade(orderModel.getChannel()));
        //财务信息、始发市no、高峰期附加费
        request.setFinance(this.toEnquiryFinanceFacade(context));
        //核算体积、核算重量
        if (expressUccConfigCenter.isFreightNewEnquiryProcessSwitch()) {
            if ((orderModel.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(orderModel))
                    && (TrustSellerUtil.isTrustWeightVolume(orderModel) || TrustSellerUtil.isTrustWeightVolume(orderModel.getOrderSnapshot()))) {
                LOGGER.info("信任商家，快运询价后不更新核算体积、核算重量");
            } else {
                request.setRecheckVolume(orderModel.getEnquiry().getEnquiryVolume());
                request.setRecheckWeight(orderModel.getEnquiry().getEnquiryWeight());
            }
        } else {
            request.setRecheckVolume(orderModel.getEnquiry().getEnquiryVolume());
            request.setRecheckWeight(orderModel.getEnquiry().getEnquiryWeight());
        }

        //产品信息(包装耗材)
        if (orderModel.getProductDelegate() != null) {
            request.setProducts(toEnquiryProductFacades(orderModel));
        }

        //修改时间
        request.setUpdateTime(new Date());
        //修改人
        request.setUpdateUser(orderModel.getOperator());
        //扩展字段
        Map<String, String> extendProps = orderModel.getExtendProps();
        if(MapUtils.isEmpty(extendProps)){
            extendProps = new HashMap<>();
        }
        //复重量方（询价）包裹数
        if(null != orderModel.getEnquiry()
                && null != orderModel.getEnquiry().getEnquiryQuantity()
                && null != orderModel.getEnquiry().getEnquiryQuantity().getValue()){
            extendProps.put(AttachmentKeyEnum.ENQUIRY_QUANTITY.getKey(),String.valueOf(orderModel.getEnquiry().getEnquiryQuantity().getValue()));
        }
        request.setExtendProps(extendProps);

        //修改记录
        if(null != context.getModifyRecordDelegate() && CollectionUtils.isNotEmpty(context.getModifyRecordDelegate().getModifyRecords())){
            //最新负重量方询价记录
            request = toIncrementalInsertModifyRecordInfo(request,context.getModifyRecordDelegate().getLastEnabledModifySortRecord());
        }

        List<String> clearFields = new ArrayList<>();
        if (orderModel.isUEPC2B()) {
            // 需要排除仅写帐场景
            if (EnquiryModeEnum.ONLY_CREATE_ORDER_BANK != orderModel.getEnquiry().getEnquiryMode()) {
                DeductionDelegate deductionDelegate = orderModel.getFinance().getDeductionDelegate();
                if (null == deductionDelegate || deductionDelegate.isEmpty()) {
                    LOGGER.info("POP售后询价场景，如果当前单没有抵扣信息需要传清空抵扣信息"); // 上线验证关键日志
                    clearFields.add(ModifyItemConfigEnum.DEDUCTION_INFOS.getCode());
                }
            }
        }

        if (OrderConstants.YES_VAL.equals(orderModel.getAttachment(OrderConstants.RESERVE_SNAPSHOT_ATTACH_FEES))) {
            LOGGER.info("使用原单附加费询价，询价后保留原单附加费");
        } else {
            if (expressUccConfigCenter.isPackageModeAttachFeesSwitch()) {
                // 附加费清空
                if (CollectionUtils.isEmpty(orderModel.getFinance().getAttachFees())) {
                    clearFields.add(ModifyItemConfigEnum.ATTACH_FEES.getCode());
                    request.setClearFields(clearFields);
                }
            }
        }
        if (MapUtils.isNotEmpty(orderModel.getModifiedFields()) && orderModel.getModifiedFields().get(ModifiedFieldEnum.OPERATION_DISCOUNT_INFOS.getCode()) != null) {
            //营销信息
            request.setPromotion(toEnquiryPromotionFacade(orderModel));
            // 写一个标识，标识营销信息是否切到订单中心
            extendProps.put(AttachmentKeyEnum.OPERATION_DISCOUNT.getKey(),OrderConstants.YES_VAL);
        }
        request.setClearFields(clearFields);
        return request;
    }
    /**
     * 修改询价状态
     * @param model
     * @return
     */
    public ModifyOrderFacadeRequest toModifyEnquiryStatus(ExpressOrderModel model){
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        facadeRequest.setOrderNo(model.getOrderSnapshot().orderNo());
        FinanceFacade finance = new FinanceFacade();
        finance.setEnquiryStatus(model.getFinance().getEnquiryStatus().getCode());
        facadeRequest.setFinance(finance);
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(model.getOrderBusinessIdentity()));
        return facadeRequest;
    }


    /**
     * 询价财务信息转换
     *
     * @param context
     * @return
     */
    private FinanceFacade toEnquiryFinanceFacade(ExpressOrderContext context) {
        Finance finance = context.getOrderModel().getFinance();
        if (finance == null || isNullObject(finance, Finance.class)) {
            return null;
        }
        FinanceFacade financeFacade = new FinanceFacade();
        if (finance.getSettlementType() != null) {
            financeFacade.setSettlementType(finance.getSettlementType().getCode());
        }
        //预估费用
        financeFacade.setEstimateAmount(finance.getEstimateAmount());
        //折扣金额
        financeFacade.setDiscountAmount(finance.getDiscountAmount());
        //结算账号
        financeFacade.setSettlementAccountNo(finance.getSettlementAccountNo());
        //财务预占标识, 1：白条预授权
        financeFacade.setPreemptType(finance.getPreemptType());
        //支付账号
        financeFacade.setPaymentAccountNo(finance.getPaymentAccountNo());
        //支付方式
        if (finance.getPayment() != null) {
            if (PaymentTypeEnum.PICKUP_BEFORE_PAY == finance.getPayment()) {
                LOGGER.info("在线支付-先揽后付,不在询价仅写账流程中更新支付方式，在finishXX中更新");
            } else {
                financeFacade.setPayment(finance.getPayment().getCode());
            }
        }
        //支付状态
        financeFacade.setPaymentStatus(finance.getPaymentStatus() != null ? finance.getPaymentStatus().getStatus() : null);
        //询价
        if (finance.getEnquiryType() != null) {
            financeFacade.setEnquiryType(finance.getEnquiryType().getCode());
        }
        //支付截止时间
        financeFacade.setPayDeadline(finance.getPayDeadline());
        //退款状态
        financeFacade.setRefundStatus(finance.getRefundStatus() != null ? finance.getRefundStatus().getStatus() : null);
        //折前金额
        financeFacade.setPreAmount(finance.getPreAmount());
        //总优惠金额
        financeFacade.setTotalDiscountAmount(finance.getTotalDiscountAmount());
        //计费重量
        financeFacade.setBillingWeight(finance.getBillingWeight());
        //计费体积
        financeFacade.setBillingVolume(finance.getBillingVolume());
        //计费模式
        financeFacade.setBillingMode(finance.getBillingMode());

        //收款机构
        financeFacade.setCollectionOrgNo(finance.getCollectionOrgNo());
        //付款环节
        financeFacade.setPaymentStage(finance.getPaymentStage() != null ? finance.getPaymentStage().getCode() : null);
        //费用明细
        if (CollectionUtils.isNotEmpty(finance.getFinanceDetails())) {
            List<FinanceDetailFacade> financeDetailFacades = new ArrayList<>(finance.getFinanceDetails().size());
            for (FinanceDetail financeDetail : finance.getFinanceDetails()) {
                FinanceDetailFacade financeDetailFacade = new FinanceDetailFacade();
                //费用编码
                financeDetailFacade.setCostNo(financeDetail.getCostNo());
                //费用名称
                financeDetailFacade.setCostName(financeDetail.getCostName());
                //产品编码
                financeDetailFacade.setProductNo(financeDetail.getProductNo());
                //产品名称
                financeDetailFacade.setProductName(financeDetail.getProductName());
                //折前金额
                financeDetailFacade.setPreAmount(financeDetail.getPreAmount());

                if (CollectionUtils.isNotEmpty(financeDetail.getDiscounts())) {
                    List<DiscountFacade> discountFacades = new ArrayList<>(financeDetail.getDiscounts().size());
                    financeDetail.getDiscounts().forEach(discount -> {
                        DiscountFacade discountFacade = new DiscountFacade();
                        discountFacade.setDiscountNo(discount.getDiscountNo());
                        discountFacade.setDiscountType(discount.getDiscountType());
                        discountFacade.setDiscountedAmount(discount.getDiscountedAmount());
                        discountFacade.setOperateType(discount.getOperateType());
                        discountFacade.setExtendProps(discount.getExtendProps());
                        discountFacades.add(discountFacade);
                    });
                    financeDetailFacade.setDiscountFacades(discountFacades);
                }

                //折扣金额
                financeDetailFacade.setDiscountAmount(financeDetail.getDiscountAmount());
                //备注
                financeDetailFacade.setRemark(financeDetail.getRemark());
                //积分
                financeDetailFacade.setPointsFacade(toPointsFacade(financeDetail.getPoints()));
                // 向谁收
                financeDetailFacade.setChargingSource(financeDetail.getChargingSource());
                // 扩展信息
                financeDetailFacade.setExtendProps(financeDetail.getExtendProps());

                financeDetailFacades.add(financeDetailFacade);
            }
            financeFacade.setFinanceDetails(financeDetailFacades);
            financeFacade.setPaymentNo(finance.getPaymentNo());
        }
        if (expressUccConfigCenter.isPackageModeAttachFeesSwitch()) {
            //附加费用
            Optional.ofNullable(finance.getAttachFees()).ifPresent(attachFees -> {
                List<CostInfo> attachFeesList = new ArrayList<>(attachFees.size());
                attachFees.forEach(cost -> {
                    CostInfo costInfo = new CostInfo();
                    costInfo.setCostNo(cost.getCostNo());
                    costInfo.setCostName(cost.getCostName());
                    costInfo.setChargingSource(cost.getChargingSource());
                    costInfo.setSettlementAccountNo(cost.getSettlementAccountNo());
                    costInfo.setExtendProps(cost.getExtendProps());
                    attachFeesList.add(costInfo);
                });
                financeFacade.setAttachFees(attachFeesList);
            });
        }
        //积分信息
        financeFacade.setPointsFacade(toPointsFacade(finance.getPoints()));
        //询价状态
        if (finance.getEnquiryStatus() != null) {
            financeFacade.setEnquiryStatus(finance.getEnquiryStatus().getCode());
        }
        //抵扣信息
        if (finance.getDeductionDelegate() != null && finance.getDeductionDelegate().getDeductions() != null) {
            financeFacade.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) finance.getDeductionDelegate().getDeductions()));
        }
        //财务备注
        financeFacade.setRemark(finance.getRemark());
        financeFacade.setEnquiryStartCityNo(context.getOrderModel().getEnquiry().getEnquiryStartCityNo());
        financeFacade.setPeakPeriodTime(context.getOrderModel().getEnquiry().getPeakPeriodTime());

        Map<String, String> extendProps = new HashMap<>();
        if (MapUtils.isNotEmpty(context.getOrderModel().getEnquiry().getExtendProps())) {
            extendProps.putAll(context.getOrderModel().getEnquiry().getExtendProps());
        }
        if (MapUtils.isNotEmpty(finance.getExtendProps())) {
            extendProps.putAll(finance.getExtendProps());
        }
        //最后一次询价成功时间
        extendProps.put(AttachmentKeyEnum.LAST_ENQUIRY_SUCCESS_TIME.getKey(), JSONUtils.beanToJSONDefault(context.getOrderModel().getOperateTime()));
        if (MapUtils.isNotEmpty(extendProps)) {
            financeFacade.setExtendProps(extendProps);
        }
        //多方计费收费总额
        financeFacade.setMultiPartiesTotalAmounts(finance.getMultiPartiesTotalAmounts());
        return financeFacade;
    }

    /**
     * 询价产品信息转换
     * 包装耗材落库
     *
     * @param model
     * @return
     */
    public List<ProductFacade> toEnquiryProductFacades(ExpressOrderModel model) {
        if (CollectionUtils.isEmpty(model.getProductDelegate().getProducts())) {
            return null;
        }
        List<ProductFacade> productFacades = new ArrayList<>();
        List<Product> products = (List<Product>) model.getProductDelegate().getProducts();
        for (Product product : products) {
            if (!AddOnProductEnum.getPackageServiceCode().contains(product.getProductNo()) &&
                    !product.getProductNo().equals(AddOnProductEnum.COOLER_BOX.getCode()) &&
                    !product.getProductNo().equals(AddOnProductEnum.CC_LL_COOLER_BOX.getCode())) {
                continue;
            }
            OperateTypeEnum operateTypeEnum = OperateTypeEnum.INSERT;
            if (model.getOrderSnapshot().getOrderSnapshot() != null
                    && model.getOrderSnapshot().getOrderSnapshot().orderNo() != null) {
                if (null != model.getOrderSnapshot().getOrderSnapshot().getProductDelegate().ofProductNo(product.getProductNo())) {
                    operateTypeEnum = OperateTypeEnum.UPDATE;
                }
            } else {
                if (null != model.getOrderSnapshot().getProductDelegate().ofProductNo(product.getProductNo())) {
                    operateTypeEnum = OperateTypeEnum.UPDATE;
                }
            }


            ProductFacade productFacade = new ProductFacade();
            //产品编码
            productFacade.setProductNo(product.getProductNo());
            //产品名称
            productFacade.setProductName(product.getProductName());
            //产品类型
            productFacade.setProductType(product.getProductType());
            //降级前产品编码
            productFacade.setOriginalProductNo(product.getOriginalProductNo());
            //降级前产品名称
            productFacade.setOriginalProductName(product.getOriginalProductName());

            if (model.getOrderSnapshot().getOrderSnapshot() != null
                    && model.getOrderSnapshot().getOrderSnapshot().orderNo() != null) {
                //主产品编码
                productFacade.setParentNo(StringUtils.isNotBlank(product.getParentNo()) ? product.getParentNo() : model.getOrderSnapshot().getOrderSnapshot().getProductDelegate().getMajorProductNo());
            } else {
                //主产品编码
                productFacade.setParentNo(StringUtils.isNotBlank(product.getParentNo()) ? product.getParentNo() : model.getOrderSnapshot().getProductDelegate().getMajorProductNo());
            }

            //扩展信息
            productFacade.setExtendProps(product.getExtendProps());
            //产品属性
            productFacade.setProductAttrs(product.getProductAttrs());
            //操作类型
            productFacade.setOperateType(operateTypeEnum);
            productFacades.add(productFacade);
        }
        return productFacades;
    }

    /**
     * 协议信息集合转换
     * @param agreementDelegate
     * @return
     */
    private List<AgreementFacade> toAgreementFacades(AgreementDelegate agreementDelegate) {
        if (null == agreementDelegate || agreementDelegate.isEmpty()) {
            return null;
        }

        return agreementDelegate.getAgreementList().stream()
                .filter(Objects::nonNull)
                .map(this::toAgreementFacade)
                .collect(Collectors.toList());
    }

    /**
     * 协议信息转换
     * @param agreement
     * @return
     */
    private AgreementFacade toAgreementFacade(Agreement agreement) {
        AgreementFacade agreementFacade = new AgreementFacade();
        agreementFacade.setAgreementType(agreement.getAgreementType());
        agreementFacade.setAgreementId(agreement.getAgreementId());
        agreementFacade.setSigner(agreement.getSigner());
        agreementFacade.setSigningTime(agreement.getSigningTime());
        agreementFacade.setExtendProps(agreement.getExtendProps());
        return agreementFacade;
    }



    /**
     * 功能: LAS转换回传
     *
     * @param:
     * @return:
     * @throw:
     * @description: 状态回传的时候，如果订单状态为已取消，订单中心只更新个性化状态，不更新主状态，主状态在取消下发的时候进行更新
     * @author: liufarui
     * @date: 2021/7/5 14:16
     */
    public ModifyOrderFacadeRequest toLASCallbackOrderSomeDataFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (context == null || context.getOrderModel() == null) {
            return modifyOrderFacadeRequest;
        }
        //1 状态更新
        //2 信息更新
        //2.1 逆向运单号
        //2.2 预估费用
        //2.3 三方运单号
        //2.4 打印状态
        ExpressOrderModel orderModel = context.getOrderModel();
        if (orderModel.getOrderBusinessIdentity() != null) {
            modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
        }
        modifyOrderFacadeRequest.setOrderNo(orderModel.orderNo());

        if (orderModel.requestProfile() != null) {
            modifyOrderFacadeRequest.setTenantId(orderModel.requestProfile().getTenantId());
        }
        //预估费用
        modifyOrderFacadeRequest.setFinance(toFinanceFacade(orderModel));
        // 配送信息
        modifyOrderFacadeRequest.setShipment(toShipmentFacade(orderModel.getShipment()));
        modifyOrderFacadeRequest.setOperator(orderModel.getOperator());
        modifyOrderFacadeRequest.setRemark(orderModel.getRemark());
        //打印状态
        modifyOrderFacadeRequest.setExtendProps(handleCallBackExtendProps(context));
        //逆向运单号 三方运单号
        modifyOrderFacadeRequest.setExtendRefOrder(orderModel.getRefOrderInfoDelegate().getExtendProps());
        // 回传 - 状态更新
        modifyOrderFacadeRequest.setOrderStatusCustom(orderModel.getCustomStatus());
        modifyOrderFacadeRequest.setExecutedStatus(orderModel.getExecutedStatus());
        // 主状态
        if (null != orderModel.getOrderStatus() && null != orderModel.getOrderStatus().getOrderStatus()) {
            modifyOrderFacadeRequest.setOrderStatus(orderModel.getOrderStatus().getOrderStatus().getCode());
        }
        // 取消状态
        if (null != orderModel.getCancelStatus() && null != orderModel.getCancelStatus().getCode()) {
            modifyOrderFacadeRequest.setCancelStatus(orderModel.getCancelStatus().getCode());
        }
        // 复核重量体积
        modifyOrderFacadeRequest.setRecheckWeight(orderModel.getRecheckWeight());
        modifyOrderFacadeRequest.setRecheckVolume(orderModel.getRecheckVolume());
        return modifyOrderFacadeRequest;
    }

    /**
     * Las取消修改转换
     *
     * @return
     */
    public ModifyOrderFacadeRequest toLasCancelOrderFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            //订单ID
            //request.setOrderId(context.getOrderModel().getOrderSnapshot().getOrderId());
            //订单号
            request.setOrderNo(context.getOrderModel().getOrderSnapshot().orderNo());
            //租户
            request.setTenantId(context.getOrderModel().requestProfile() != null
                    ? context.getOrderModel().requestProfile().getTenantId() : null);
            Map<String, String> extendMap = new HashMap<>();
            //取消类型
            if (context.getOrderModel().getCancelStatus() != null) {
                request.setCancelStatus(context.getOrderModel().getCancelStatus().getCode());
                Map<String, String> customerInfoExtendProps = JSONUtils.jsonToMap(context.getOrderModel().getOrderSnapshot()
                        .getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                if (customerInfoExtendProps == null){
                    customerInfoExtendProps = new HashMap<>();
                }
                customerInfoExtendProps.put("cancelReasonCode",context.getOrderModel().getCancelReasonCode());
                customerInfoExtendProps.put("cancelReason",context.getOrderModel().getCancelReason());
                customerInfoExtendProps.put("cancelRemark",context.getOrderModel().getRemark());
                extendMap.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS,JSONUtils.beanToJSONDefault(customerInfoExtendProps));
                if (CancelStatusEnum.CANCEL_SUCCESS.equals(context.getOrderModel().getCancelStatus())) {
                    request.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
                }
            }
            request.setExtendProps(extendMap);
            //修改时间
            request.setUpdateTime(new Date());
            //修改人
            request.setUpdateUser(context.getOrderModel().getOperator());
            request.setOperator(context.getOrderModel().getOperator());
        }
        return request;
    }

    /**
     * 快运回传转换
     * 状态回传的时候，如果订单状态为已取消，订单中心只更新个性化状态，不更新主状态，主状态在取消下发的时候进行更新
     */
    public ModifyOrderFacadeRequest toFreightCallbackOrderSomeDataFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            if (orderModel.getOrderBusinessIdentity() != null) {
                OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
                businessIdentity.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
                businessIdentity.setBusinessType(orderModel.getOrderBusinessIdentity().getBusinessType());
                businessIdentity.setBusinessScene(orderModel.getOrderBusinessIdentity().getBusinessScene());
                modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(businessIdentity));
            }
            modifyOrderFacadeRequest.setOrderNo(orderModel.orderNo());

            if (shouldUpdateStatus(context)) {
                // 回传的时候订单主状态不可以被更新为取消，但是扩展状态是可以被更新的
                if (null != orderModel.getOrderStatus()
                        && null != orderModel.getOrderStatus().getOrderStatus()
                        && OrderStatusEnum.CANCELED != orderModel.getOrderStatus().getOrderStatus()) {
                    modifyOrderFacadeRequest.setOrderStatus(orderModel.getOrderStatus().getOrderStatus().getCode());
                }
                modifyOrderFacadeRequest.setOrderStatusCustom(orderModel.getCustomStatus());
                modifyOrderFacadeRequest.setExecutedStatus(orderModel.getExecutedStatus());
            }

            if (orderModel.requestProfile() != null) {
                modifyOrderFacadeRequest.setTenantId(orderModel.requestProfile().getTenantId());
            }
            //modifyOrderFacadeRequest.setChannel(toChannelFacade(orderModel.getChannel()));
            modifyOrderFacadeRequest.setFinance(toFinanceFacade(orderModel));
            modifyOrderFacadeRequest.setShipment(toShipmentFacade(orderModel.getShipment()));
            modifyOrderFacadeRequest.setOperator(orderModel.getOperator());
            modifyOrderFacadeRequest.setRemark(orderModel.getRemark());
            modifyOrderFacadeRequest.setExtendProps(handleCallBackExtendProps(context));


            // 拦截回传时需要更新取消状态和拦截类型，
            // 拦截类型只有在仅拦截时才会更新，通过判断拦截类型是否有值来决定是否需要更新拦截类型
            if (null != orderModel.getOrderStatus()
                    && null != orderModel.getOrderStatus().getOrderStatus()) {
                //拦截状态处理
                callbackInterceptStatusPro(orderModel,orderModel.getOrderStatus().getOrderStatus(),modifyOrderFacadeRequest);
            }

            // 回传传入拦截失败-100000，更新订单的取消和拦截状态为拦截失败
            if (ExpressOrderStatusExtendEnum.INTERCEPT_FAILED.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_FAIL.getCode());
                modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.INTERCEPT_FAIL.getCode());
            }

            // 回传接口回传的拦截成功需要更新订单取消状态为拦截成功，同时更新订单自定义状态为已拦截
            if (ExpressOrderStatusExtendEnum.LAN_JIE_CHENG_GONG.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_SUCCESS.getCode());
                modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.INTERCEPT_SUCCESS.getCode());
            }

            //预分拣结果处理
            handleCallBackPresortExtend(orderModel);

            // 处理产品
            if (orderModel.getOrderSnapshot().getProductDelegate() != null) {
                List<Product> list1 = (List<Product>) orderModel.getProductDelegate().getProducts();
                modifyOrderFacadeRequest.setProducts(toFreightCallBackProductFacades(orderModel.getOrderSnapshot(), list1));
            }
            //复核重量
            modifyOrderFacadeRequest.setRecheckWeight(orderModel.getRecheckWeight());
            //复核体积
            modifyOrderFacadeRequest.setRecheckVolume(orderModel.getRecheckVolume());
            //履约信息
            modifyOrderFacadeRequest.setFulfillment(toFulfillmentFacade(orderModel.getFulfillment()));
        }
        return modifyOrderFacadeRequest;
    }

    /**
     * 合同物流回传转换
     * 状态回传的时候，如果订单状态为已取消，订单中心只更新个性化状态，不更新主状态，主状态在取消下发的时候进行更新
     */
    public ModifyOrderFacadeRequest toContractCallbackOrderSomeDataFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            if (orderModel.getOrderBusinessIdentity() != null) {
                OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
                businessIdentity.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
                businessIdentity.setBusinessType(orderModel.getOrderBusinessIdentity().getBusinessType());
                businessIdentity.setBusinessScene(orderModel.getOrderBusinessIdentity().getBusinessScene());
                modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(businessIdentity));
            }

            modifyOrderFacadeRequest.setOrderNo(orderModel.orderNo());

            // 回传的时候订单主状态不可以被更新为取消，但是扩展状态是可以被更新的
            if(shouldUpdateStatus(context)){
                if (null != orderModel.getOrderStatus()
                        && null != orderModel.getOrderStatus().getOrderStatus()
                        && OrderStatusEnum.CANCELED != orderModel.getOrderStatus().getOrderStatus()) {
                    modifyOrderFacadeRequest.setOrderStatus(orderModel.getOrderStatus().getOrderStatus().getCode());
                }
                modifyOrderFacadeRequest.setOrderStatusCustom(orderModel.getCustomStatus());
                modifyOrderFacadeRequest.setExecutedStatus(orderModel.getExecutedStatus());
            }

            if (orderModel.requestProfile() != null) {
                modifyOrderFacadeRequest.setTenantId(orderModel.requestProfile().getTenantId());
            }
            //modifyOrderFacadeRequest.setChannel(toChannelFacade(orderModel.getChannel()));
            modifyOrderFacadeRequest.setFinance(toFinanceFacade(orderModel));
            modifyOrderFacadeRequest.setShipment(toShipmentFacade(orderModel.getShipment()));
            modifyOrderFacadeRequest.setOperator(orderModel.getOperator());
            modifyOrderFacadeRequest.setRemark(orderModel.getRemark());
            modifyOrderFacadeRequest.setExtendProps(handleCallBackExtendProps(context));

            // 拦截回传时需要更新取消状态和拦截类型，
            // 拦截类型只有在仅拦截时才会更新，通过判断拦截类型是否有值来决定是否需要更新拦截类型
            if (null != orderModel.getOrderStatus()
                    && null != orderModel.getOrderStatus().getOrderStatus()) {
                //拦截状态处理
                callbackInterceptStatusPro(orderModel,orderModel.getOrderStatus().getOrderStatus(),modifyOrderFacadeRequest);
            }

            // 回传传入拦截失败-100000，更新订单的取消和拦截状态为拦截失败
            if (ContractOrderStatusExtendEnum.INTERCEPT_FAILED.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_FAIL.getCode());
                modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.INTERCEPT_FAIL.getCode());
            }

            //预分拣结果处理
            //toCallBackExtendProps(orderModel);
            if (orderModel.getOrderSnapshot().getProductDelegate() != null) {
                List<Product> list1 = (List<Product>) orderModel.getProductDelegate().getProducts();
                modifyOrderFacadeRequest.setProducts(toContractCallBackProductFacades(orderModel.getOrderSnapshot(), list1));
            }
            //持久化复重复量方和包裹数
            if (orderModel.getRecheckVolume() != null) {
                modifyOrderFacadeRequest.setRecheckVolume(orderModel.getRecheckVolume());
            }
            if (orderModel.getRecheckWeight() != null) {
                modifyOrderFacadeRequest.setRecheckWeight(orderModel.getRecheckWeight());
            }
            if (orderModel.getFulfillment() != null) {
                modifyOrderFacadeRequest.setFulfillment(toFulfillmentFacade(orderModel.getFulfillment()));
            }
        }
        return modifyOrderFacadeRequest;
    }

    private List<ProductFacade> toContractCallBackProductFacades(ExpressOrderModel snapshotModel, List<Product> products) {
        //todo 和ofc确认是否涉及到产品信息的回传  注意产品编码不一致需要核对
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        LOGGER.info("回传增值服务转换：snapshotModel:{},products:{}", JSONUtils.beanToJSONDefault(snapshotModel), JSONUtils.beanToJSONDefault(products));
        List<ProductFacade> productFacades = new ArrayList<>();
        for (Product product : products) {
            Product snapshotProduct = snapshotModel.getProductDelegate().ofProductNo(product.getProductNo());

            ProductFacade productFacade = new ProductFacade();
            //签单返还
            if (AddOnProductEnum.getSignReturnCode().contains(product.getProductNo()) && product.getProductAttrs() != null) {
                //纸质类型-运单号
                if (null != product.getProductAttrs().get(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode())) {
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode()));
                }
                //电子类型-照片和拍照时间
                if (null != product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode())) {
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode()));
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_PHOTO_TIME.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO_TIME.getCode()));
                }
            }
            //产品编码
            productFacade.setProductNo(snapshotProduct.getProductNo());
            //产品名称
            productFacade.setProductName(snapshotProduct.getProductName());
            //产品类型
            productFacade.setProductType(snapshotProduct.getProductType());
            //降级前产品编码
            productFacade.setOriginalProductNo(snapshotProduct.getOriginalProductNo());
            //降级前产品名称
            productFacade.setOriginalProductName(snapshotProduct.getOriginalProductName());
            //主产品编码
            productFacade.setParentNo(snapshotProduct.getParentNo());
            //扩展信息
            productFacade.setExtendProps(snapshotProduct.getExtendProps());
            //产品属性
            productFacade.setProductAttrs(snapshotProduct.getProductAttrs());
            //操作类型
            productFacade.setOperateType(OperateTypeEnum.UPDATE);
            productFacades.add(productFacade);
        }
        return productFacades;
    }

    private List<ProductFacade> toFreightCallBackProductFacades(ExpressOrderModel snapshotModel, List<Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        LOGGER.info("回传增值服务转换：snapshotModel:{},products:{}", JSONUtils.beanToJSONDefault(snapshotModel), JSONUtils.beanToJSONDefault(products));
        List<ProductFacade> productFacades = new ArrayList<>();
        for (Product product : products) {
            Product snapshotProduct = snapshotModel.getProductDelegate().ofProductNo(product.getProductNo());

            ProductFacade productFacade = new ProductFacade();

            // 特殊处理：通过回传更新产品要素。此外实时数据同步需要配置DUCC expressUccConfigCenter.syncWaybillDataSpecialHandlerAddedProduct
            //签单返还
            if (AddOnProductEnum.getSignReturnCode().contains(product.getProductNo()) && product.getProductAttrs() != null) {
                //纸质类型-运单号
                if (null != product.getProductAttrs().get(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode())) {
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode()));
                }
                //电子类型-照片和拍照时间
                if (null != product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode())) {
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode()));
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_PHOTO_TIME.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO_TIME.getCode()));
                }
            }
            // 揽收拍照-快运、派送拍照-快运
            if ((AddOnProductEnum.PICTURE_PICKUP.getCode().equals(product.getProductNo()) || AddOnProductEnum.PICTURE_DELIVERY.getCode().equals(product.getProductNo()))
                    && MapUtils.isNotEmpty(product.getProductAttrs())) {
                snapshotProduct.setProductAttrs(product.getProductAttrs());
            }

            //产品编码
            productFacade.setProductNo(snapshotProduct.getProductNo());
            //产品名称
            productFacade.setProductName(snapshotProduct.getProductName());
            //产品类型
            productFacade.setProductType(snapshotProduct.getProductType());
            //降级前产品编码
            productFacade.setOriginalProductNo(snapshotProduct.getOriginalProductNo());
            //降级前产品名称
            productFacade.setOriginalProductName(snapshotProduct.getOriginalProductName());
            //主产品编码
            productFacade.setParentNo(snapshotProduct.getParentNo());
            //扩展信息
            productFacade.setExtendProps(snapshotProduct.getExtendProps());
            //产品属性
            productFacade.setProductAttrs(snapshotProduct.getProductAttrs());
            //操作类型
            productFacade.setOperateType(OperateTypeEnum.UPDATE);
            productFacades.add(productFacade);
        }
        return productFacades;
    }

    /**
     * 快运改址修改请求转换
     */
    public ModifyOrderFacadeRequest toFreightReaddressModifyOrderFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单id
            //request.setOrderId(orderModel.getOrderSnapshot() != null ? orderModel.getOrderSnapshot().getOrderId() : null);
            // 订单号
            request.setOrderNo(orderModel.orderNo());
            // 修改人
            request.setOperator(orderModel.getOperator());
            // oms修改时间
            request.setOperateTime(orderModel.getOperateTime());

            // 改址状态
            if (orderModel.getReaddressStatus() != null) {
                request.setReaddressStatus(orderModel.getReaddressStatus().getCode());
            }

            // 财务信息-支付状态
            if (orderModel.getFinance() != null) {
                FinanceFacade financeFacade = new FinanceFacade();
                financeFacade.setPaymentStatus(orderModel.getFinance().getPaymentStatus().getStatus());
                // 实际支付方式
                financeFacade.setActualPaymentType(orderModel.getFinance().getActualPaymentType());
                request.setFinance(financeFacade);
            }

            // 扩展信息-修改信息标记
            if (orderModel.getExtendProps() != null && orderModel.getExtendProps().size() > 0) {
                request.setExtendProps(orderModel.getExtendProps());
            }
        }
        return request;
    }

    /**
     * 快运取消改址修改请求转换
     */
    public ModifyOrderFacadeRequest toFreightCancelReaddressRequest(ExpressOrderContext context) throws Exception {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单id
            //request.setOrderId(orderModel.getOrderSnapshot() != null ? orderModel.getOrderSnapshot().getOrderId() : null);
            // 订单号
            request.setOrderNo(orderModel.orderNo());
            // 修改人
            request.setOperator(orderModel.getOperator());
            // oms修改时间
            request.setOperateTime(orderModel.getOperateTime());

            // 回滚已修改的部分信息
            rollbackToOriginInfo(request, orderSnapshot);

            // 改址状态：改址失败
            request.setReaddressStatus(ReaddressStatusEnum.MODIFY_FAIL.getCode());

            // 修改信息标记：改址失败
            Map<String, String> extendProps = orderSnapshot.getExtendProps();
            if (extendProps == null) {
                extendProps = new HashMap<>();
            }
            String modifyMark = extendProps.get(AttachmentKeyEnum.MODIFY_MARK.getKey());
            if (StringUtils.isBlank(modifyMark)) {
                modifyMark = ModifyMarkUtil.getInitMark();
            }
            modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition(), ReaddressMarkEnum.RESULT_FAIL.getCode());
            extendProps.put(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);
            request.setExtendProps(extendProps);
            //京东转德邦，计算多方计费总金额
            if (orderSnapshot.isJDLToDPDelivery()) {
                //获取原单改址记录，并剔除需要删除的改址记录
                List<CostInfo> multiPartiesTotalAmounts = FinanceUtil.computeMPTotalAmountsGroupSettleAndStage(orderSnapshot, null, null, false, false);
                //财务信息多方计费
                if (CollectionUtils.isNotEmpty(multiPartiesTotalAmounts)) {
                    FinanceFacade financeFacade = new FinanceFacade();
                    financeFacade.setMultiPartiesTotalAmounts(multiPartiesTotalAmounts);
                    request.setFinance(financeFacade);
                }
            }

        }
        return request;
    }

    /**
     * 快运改址取消，回滚信息
     */
    private void rollbackToOriginInfo(ModifyOrderFacadeRequest request, ExpressOrderModel orderSnapshot) throws Exception {
        // 回滚收货信息
        rollbackConsignee(request, orderSnapshot);
        // 回滚配送信息
        rollbackShipment(request, orderSnapshot);
        // 回滚财务信息（部分）
        rollbackFinance(request, orderSnapshot);
        // 回滚产品信息
        rollbackProduct(request, orderSnapshot);
    }

    /**
     * 快运改址取消，回滚收货信息
     */
    private void rollbackConsignee(ModifyOrderFacadeRequest request, ExpressOrderModel orderSnapshot) throws Exception {
        String json = orderSnapshot.getConsignee().getExtendProps().get(AttachmentKeyEnum.ORIGIN_CONSIGNEE_INFO.getKey());
        ConsigneeInfo consigneeInfo = JSONUtils.jsonToBean(json, ConsigneeInfo.class);
        consigneeInfo = tdeAclUtil.decryptConsigneeInfo(consigneeInfo);
        ConsigneeFacade consigneeFacade = ConsigneeFacadeMapper.INSTANCE.toConsigneeFacade(consigneeInfo);
        request.setConsignee(consigneeFacade);
    }

    /**
     * 快运改址取消，回滚配送信息
     */
    private void rollbackShipment(ModifyOrderFacadeRequest request, ExpressOrderModel orderSnapshot) {
        String json = orderSnapshot.getShipment().getExtendProps().get(AttachmentKeyEnum.ORIGIN_SHIPMENT_INFO.getKey());
        ShipmentInfo shipmentInfo = JSONUtils.jsonToBean(json, ShipmentInfo.class);
        ShipmentFacade shipmentFacade = ShipmentFacadeMapper.INSTANCE.toShipmentFacade(shipmentInfo);
        request.setShipment(shipmentFacade);
    }

    /**
     * 快运改址取消，回滚财务信息（部分）
     */
    private void rollbackFinance(ModifyOrderFacadeRequest request, ExpressOrderModel orderSnapshot) {
        String json = orderSnapshot.getFinance().getExtendProps().get(AttachmentKeyEnum.ORIGIN_FINANCE_INFO.getKey());
        FinanceInfo financeInfo = JSONUtils.jsonToBean(json, FinanceInfo.class);
        FinanceFacade rollbackData = FinanceFacadeMapper.INSTANCE.toFinanceFacade(financeInfo);

        // 只回滚部分信息
        FinanceFacade financeFacade = toFinanceFacade(orderSnapshot);
        // 折前金额
        financeFacade.setPreAmount(rollbackMoney(rollbackData.getPreAmount()));
        // 折后金额
        financeFacade.setDiscountAmount(rollbackMoney(rollbackData.getDiscountAmount()));
        // 总优惠金额
        financeFacade.setTotalDiscountAmount(rollbackMoney(rollbackData.getTotalDiscountAmount()));
        // 计费重量
        financeFacade.setBillingWeight(rollbackWeight(rollbackData.getBillingWeight()));
        // 计费体积
        financeFacade.setBillingVolume(rollbackVolume(rollbackData.getBillingVolume()));
        // 费用明细
        if (CollectionUtils.isEmpty(rollbackData.getFinanceDetails())) {
            LOGGER.info("快运取消改址，清空费用明细");
            // 费用明细为空则需要清空
            List<String> clearFields = request.getClearFields();
            if (clearFields == null) {
                clearFields = new ArrayList<>();
            }
            clearFields.add(ModifyItemConfigEnum.FINANCE_DETAILS.getCode());
            request.setClearFields(clearFields);
            financeFacade.setFinanceDetails(null);
        } else {
            financeFacade.setFinanceDetails(rollbackData.getFinanceDetails());
        }
        // 改址补款金额
        Map<String, String> extendPropsSnapshot = orderSnapshot.getFinance().getExtendProps();
        if (MapUtils.isNotEmpty(extendPropsSnapshot)
                && StringUtils.isNotBlank(extendPropsSnapshot.get(OrderConstants.READDRESS_SUPPLEMENT_AMOUNT))) {
            LOGGER.info("快运取消改址，清空改址补款金额，readdressSupplementAmount={}", extendPropsSnapshot.get(OrderConstants.READDRESS_SUPPLEMENT_AMOUNT));
            Map<String, String> extendProps = new HashMap<>();
            extendProps.put(OrderConstants.READDRESS_SUPPLEMENT_AMOUNT, "");
            financeFacade.setExtendProps(extendProps);
        }
        request.setFinance(financeFacade);
    }

    /**
     * 回滚金额，原始为null则回滚为0
     */
    private Money rollbackMoney(Money money) {
        if (money == null || money.getAmount() == null) {
            Money zero = new Money();
            zero.setAmount(new BigDecimal(0));
            return zero;
        }
        return money;
    }

    /**
     * 回滚重量，原始为null则回滚为0
     */
    private Weight rollbackWeight(Weight weight) {
        if (weight == null || weight.getValue() == null) {
            Weight zero = new Weight();
            zero.setValue(new BigDecimal(0));
            return zero;
        }
        return weight;
    }

    /**
     * 回滚体积，原始为null则回滚为0
     */
    private Volume rollbackVolume(Volume volume) {
        if (volume == null || volume.getValue() == null) {
            Volume zero = new Volume();
            zero.setValue(new BigDecimal(0));
            return zero;
        }
        return volume;
    }

    /**
     * 快运改址取消，回滚产品信息
     */
    private void rollbackProduct(ModifyOrderFacadeRequest request, ExpressOrderModel orderSnapshot) {
        List<ProductFacade> productFacades = new ArrayList<>();
        ProductDelegate productDelegate = orderSnapshot.getProductDelegate();
        // 删除改址操作新增的增值产品
        removeFreightReaddress(productFacades, productDelegate);
        // 增加改址操作删除的增值产品
        insertDeletedProduct(productFacades, productDelegate, orderSnapshot);
        if (CollectionUtils.isNotEmpty(productFacades)) {
            request.setProducts(productFacades);
        }
    }

    /**
     * 快运改址取消，删除改址操作新增的增值产品
     */
    private void removeFreightReaddress(List<ProductFacade> productFacades, ProductDelegate productDelegate) {
        if (productDelegate == null || productDelegate.isEmpty()) {
            return;
        }
        List<Product> products = (List<Product>) productDelegate.getProducts();
        for (Product product : products) {
            if (AddOnProductEnum.READDRESS_FREIGHT.getCode().equals(product.getProductNo())) {
                ProductFacade productFacade = toDeleteProductFacade(product);
                productFacades.add(productFacade);
            }
        }
    }

    /**
     * 快运改址取消，增加改址操作删除的增值产品
     */
    private void insertDeletedProduct(List<ProductFacade> productFacades, ProductDelegate productDelegate, ExpressOrderModel orderSnapshot) {
        // 获取备份的产品
        String json = orderSnapshot.getExtendProps().get(AttachmentKeyEnum.ORIGIN_PRODUCT_INFO_LIST.getKey());
        if (StringUtils.isEmpty(json)) {
            return;
        }
        List<ProductInfo> originProductInfoList = JSONUtils.jsonToList(json, ProductInfo.class);
        if (CollectionUtils.isEmpty(originProductInfoList)) {
            return;
        }

        // 统计已有的产品
        Set<String> productCodeSet = new HashSet<>();
        if (productDelegate != null && !productDelegate.isEmpty()) {
            List<Product> products = (List<Product>) productDelegate.getProducts();
            for (Product product : products) {
                productCodeSet.add(product.getProductNo());
            }
        }

        // 补充被删除的产品
        for (ProductInfo productInfo : originProductInfoList) {
            if (!productCodeSet.contains(productInfo.getProductNo())) {
                ProductFacade productFacade = new ProductFacade();
                //产品编码
                productFacade.setProductNo(productInfo.getProductNo());
                //产品名称
                productFacade.setProductName(productInfo.getProductName());
                //产品类型
                productFacade.setProductType(productInfo.getProductType());
                //主产品编码
                productFacade.setParentNo(productInfo.getParentNo());
                //扩展信息
                productFacade.setExtendProps(productInfo.getExtendProps());
                //产品属性
                productFacade.setProductAttrs(productInfo.getProductAttrs());
                //操作类型：新增
                productFacade.setOperateType(OperateTypeEnum.INSERT);
                productFacades.add(productFacade);
            }
        }
    }

    /**
     * 履约信息
     * @param fulfillment
     * @return
     */
    public FulfillmentFacade toFulfillmentFacade(Fulfillment fulfillment) {
        FulfillmentFacade fulfillmentFacade = new FulfillmentFacade();
        if (fulfillment == null || isNullObject(fulfillment, Fulfillment.class)) {
            return fulfillmentFacade;
        }
        if(fulfillment.getActualReceivedQuantity() != null) {
            Quantity actualReceivedQuantity = new Quantity();
            actualReceivedQuantity.setUnit(fulfillment.getActualReceivedQuantity().getUnit());
            actualReceivedQuantity.setValue(fulfillment.getActualReceivedQuantity().getValue());
            fulfillmentFacade.setActualReceivedQuantity(actualReceivedQuantity);
        }
        if(fulfillment.getActualSignedQuantity() != null) {
            Quantity actualSignedQuantity = new Quantity();
            actualSignedQuantity.setUnit(fulfillment.getActualSignedQuantity().getUnit());
            actualSignedQuantity.setValue(fulfillment.getActualSignedQuantity().getValue());
            fulfillmentFacade.setActualSignedQuantity(actualSignedQuantity);
        }
        fulfillmentFacade.setFulfillmentSign(fulfillment.getFulfillmentSign());
        fulfillmentFacade.setExtendProps(fulfillment.getExtendProps());

        fulfillmentFacade.setActualPickupTime(fulfillment.getActualPickupTime());
        fulfillmentFacade.setActualSignedTime(fulfillment.getActualSignedTime());
        fulfillmentFacade.setPackageMaxLen(fulfillment.getPackageMaxLen());
        return fulfillmentFacade;
    }

    /**
     * 取消修改转换
     *
     * @return
     */
    public ModifyOrderFacadeRequest toCCB2BCancelModifyOrderFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            //订单号
            request.setOrderNo(context.getOrderModel().getOrderSnapshot().orderNo());
            //租户
            request.setTenantId(context.getOrderModel().requestProfile() != null
                    ? context.getOrderModel().requestProfile().getTenantId() : null);

            Map<String, String> extendMap = new HashMap<>();
            //取消状态
            if (context.getOrderModel().getCancelStatus() != null) {
                request.setCancelStatus(context.getOrderModel().getCancelStatus().getCode());
                Map<String, String> customerInfoExtendProps = JSONUtils.jsonToMap(context.getOrderModel().getOrderSnapshot()
                        .getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                if (customerInfoExtendProps == null){
                    customerInfoExtendProps = new HashMap<>();
                }
                customerInfoExtendProps.put("cancelReasonCode",context.getOrderModel().getCancelReasonCode());
                customerInfoExtendProps.put("cancelReason",context.getOrderModel().getCancelReason());
                customerInfoExtendProps.put("cancelRemark",context.getOrderModel().getRemark());
                extendMap.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS,JSONUtils.beanToJSONDefault(customerInfoExtendProps));
                if (MapUtils.isNotEmpty(context.getOrderModel().getExtendProps())) {
                    extendMap.put(AttachmentKeyEnum.BUSINESS_EXPANSION_STATUS.getKey(), context.getOrderModel().getExtendProps().get(AttachmentKeyEnum.BUSINESS_EXPANSION_STATUS.getKey()));
                    extendMap.put(AttachmentKeyEnum.CANCEL_REASON_CODE.getKey(), context.getOrderModel().getExtendProps().get(AttachmentKeyEnum.CANCEL_REASON_CODE.getKey()));
                }
                if (CancelStatusEnum.CANCEL_SUCCESS.equals(context.getOrderModel().getCancelStatus())) {
                    request.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
                    request.setOrderStatusCustom(ExpressOrderStatusCustomEnum.CANCELED.customOrderStatus());
                    //OFC取消不再补扩展状态和商家扩展状态
                    if (SystemCallerEnum.EXPRESS_OFC != context.getOrderModel().getChannel().getSystemCaller()) {
                        request.setExecutedStatus(EXECUTED_STATUS_CANCEL);
                        extendMap.put(OrderConstants.SELLER_EXTEND_STATUS, B2CExtendStatusEnum.XIA_DAN_QU_XIAO.getExtendStatus());
                    }
                }
                if (CancelStatusEnum.INTERCEPTING.equals(context.getOrderModel().getCancelStatus())) {
//                    request.setExecutedStatus(B2CExtendStatusEnum.FA_QI_LANG_JIE_110.getExtendStatus());
                    extendMap.put(OrderConstants.SELLER_EXTEND_STATUS, B2CExtendStatusEnum.FA_QI_LANG_JIE_110.getExtendStatus());
                }
            }

            if (null != context.getOrderModel().getInterceptType()) {
                InterceptFacade interceptFacade = new InterceptFacade();
                interceptFacade.setInterceptResultTime(context.getOrderModel().getIntercept().getInterceptResultTime());
                request.setInterceptTime(context.getOrderModel().getIntercept().getInterceptTime());
                request.setInterceptFacade(interceptFacade);
                request.setInterceptType(context.getOrderModel().getInterceptType().getCode());
                // 只有仅拦截的情况下订单主状态才会更新为已拦截
                if (CancelInterceptTypeEnum.ONLY_INTERCEPT == context.getOrderModel().getCancelInterceptType()
                        && InterceptTypeEnum.INTERCEPT_SUCCESS.equals(context.getOrderModel().getInterceptType())) {
                    request.setOrderStatus(OrderStatusEnum.INTERCEPT.getCode());
                    request.setOrderStatusCustom(ExpressOrderStatusCustomEnum.INTERCEPT.customOrderStatus());
                    //OFC取消不再补扩展状态和商家扩展状态
                    if (SystemCallerEnum.EXPRESS_OFC != context.getOrderModel().getChannel().getSystemCaller()) {
                        request.setExecutedStatus(EXECUTED_STATUS_INTERCEPT);
                        extendMap.put(OrderConstants.SELLER_EXTEND_STATUS, B2CExtendStatusEnum.FA_QI_LANG_JIE_860.getExtendStatus());
                    }
                }
            }

            request.setExtendProps(extendMap);
            //修改时间
            request.setUpdateTime(new Date());
            //修改人
            request.setUpdateUser(context.getOrderModel().getOperator());
            request.setOperator(context.getOrderModel().getOperator());
        }
        return request;
    }


    /**
     * 功能: CCB2B转换回传
     *
     * @param:
     * @return:
     * @throw:
     * @description: 状态回传的时候，如果订单状态为已取消，订单中心只更新个性化状态，不更新主状态，主状态在取消下发的时候进行更新
     * @author: wangsong16
     * @date: 2023/5/25 8:57 上午
     */
    public ModifyOrderFacadeRequest toCCB2BCallbackOrderSomeDataFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            if (orderModel.getOrderBusinessIdentity() != null) {
                OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
                businessIdentity.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
                businessIdentity.setBusinessType(orderModel.getOrderBusinessIdentity().getBusinessType());
                businessIdentity.setBusinessScene(orderModel.getOrderBusinessIdentity().getBusinessScene());
                modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(businessIdentity));
            }
            modifyOrderFacadeRequest.setOrderNo(orderModel.orderNo());
            CCB2BOrderStatusExtendEnum ccb2BOrderStatusExtendEnum = CCB2BOrderStatusExtendEnum.ofExtendStatus(orderModel.getExecutedStatus());
            if (ccb2BOrderStatusExtendEnum != null) {
                modifyOrderFacadeRequest.setExecutedStatus(ccb2BOrderStatusExtendEnum.getExtendStatus());
            }
            //拦截相关状态不持久化
            if (CCB2BOrderStatusExtendEnum.INTERCEPT_FAILED == ccb2BOrderStatusExtendEnum
                    || CCB2BOrderStatusExtendEnum.LIFTED_INTERCEPT_SUCCESS == ccb2BOrderStatusExtendEnum
                    || CCB2BOrderStatusExtendEnum.LIFTED_INTERCEPT_FAIL == ccb2BOrderStatusExtendEnum) {
                modifyOrderFacadeRequest.setExecutedStatus(null);
            }
            if (orderModel.requestProfile() != null) {
                modifyOrderFacadeRequest.setTenantId(orderModel.requestProfile().getTenantId());
            }

            // 回传的时候订单主状态不可以被更新为取消，但是扩展状态是可以被更新的
            if (null != orderModel.getOrderStatus()
                    && null != orderModel.getOrderStatus().getOrderStatus()
                    && OrderStatusEnum.CANCELED != orderModel.getOrderStatus().getOrderStatus()) {
                modifyOrderFacadeRequest.setOrderStatus(orderModel.getOrderStatus().getOrderStatus().getCode());
            }
            modifyOrderFacadeRequest.setOrderStatusCustom(orderModel.getCustomStatus());
            modifyOrderFacadeRequest.setShipment(toShipmentFacade(orderModel.getShipment()));
            modifyOrderFacadeRequest.setRecheckVolume(orderModel.getRecheckVolume());
            modifyOrderFacadeRequest.setRecheckWeight(orderModel.getRecheckWeight());
            modifyOrderFacadeRequest.setFulfillment(toFulfillmentFacade(orderModel.getFulfillment()));
            modifyOrderFacadeRequest.setOperator(orderModel.getOperator());
            modifyOrderFacadeRequest.setRemark(orderModel.getRemark());

            // 拦截回传时需要更新取消状态和拦截类型，
            // 拦截类型只有在仅拦截时才会更新，通过判断拦截类型是否有值来决定是否需要更新拦截类型
            if (null != orderModel.getOrderStatus() && null != orderModel.getOrderStatus().getOrderStatus()) {
                //拦截状态处理
                callbackInterceptStatusPro(orderModel,orderModel.getOrderStatus().getOrderStatus(),modifyOrderFacadeRequest);
            }

            // 回传传入拦截失败-100000，更新订单的取消和拦截状态为拦截失败
            if (CCB2BOrderStatusExtendEnum.INTERCEPT_FAILED.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_FAIL.getCode());
                modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.INTERCEPT_FAIL.getCode());
                setInterceptFacade(modifyOrderFacadeRequest);
            }

            // 回传传入解除拦截成功-2970，更新订单的拦截状态为解除拦截成功
            if (CCB2BOrderStatusExtendEnum.LIFTED_INTERCEPT_SUCCESS.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                if (null != orderModel.getOrderSnapshot().getInterceptType()) {
                    modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.LIFTED_INTERCEPT_SUCCESS.getCode());
                    setInterceptFacade(modifyOrderFacadeRequest);
                }
            }

            // 回传传入解除拦截失败-100100，更新订单的拦截状态为解除拦截失败
            if (CCB2BOrderStatusExtendEnum.LIFTED_INTERCEPT_FAIL.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                if (null != orderModel.getOrderSnapshot().getInterceptType()) {
                    modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.LIFTED_INTERCEPT_FAIL.getCode());
                    setInterceptFacade(modifyOrderFacadeRequest);
                }
            }
            if (orderModel.getOrderSnapshot().getProductDelegate() != null) {
                List<Product> list1 = (List<Product>) orderModel.getProductDelegate().getProducts();
                modifyOrderFacadeRequest.setProducts(toCCB2BCallBackProductFacades(orderModel.getOrderSnapshot(), list1));
            }
            modifyOrderFacadeRequest.setExtendProps(toCCB2BCallBackExtendProps(orderModel));

            //订单状态为妥投、取消成功、拦截成功、部分妥投均不允许订单状态再发生变更
            if (orderModel.getOrderSnapshot().getOrderStatus().getOrderStatus() == OrderStatusEnum.CUSTOMER_SIGNED
                    || orderModel.getOrderSnapshot().getOrderStatus().getOrderStatus() == OrderStatusEnum.CANCELED
                    || orderModel.getOrderSnapshot().getOrderStatus().getOrderStatus() == OrderStatusEnum.INTERCEPT
                    || orderModel.getOrderSnapshot().getOrderStatus().getOrderStatus() == OrderStatusEnum.PARTIAL_SIGNED) {
                LOGGER.info("订单状态为妥投、取消成功、拦截成功、部分妥投均不允许订单状态再发生变更");
                modifyOrderFacadeRequest.setOrderStatus(null);
                modifyOrderFacadeRequest.setOrderStatusCustom(null);
                modifyOrderFacadeRequest.setExecutedStatus(null);
            }
        }
        return modifyOrderFacadeRequest;
    }

    private Map<String, String> toCCB2BCallBackExtendProps(ExpressOrderModel orderModel) {
        Map<String, String> extendProps = new HashMap<>();

        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        Map<String, String> snapshotExtendProps = orderSnapshot.getExtendProps();
        boolean reReceivePaperStatusIsFinal = false;
        boolean reReceiveElectronicStatusIsFinal = false;
        if (MapUtils.isEmpty(snapshotExtendProps)) {
            if(MapUtils.isEmpty(orderModel.getExtendProps())) {
                extendProps = new HashMap<>();
            }else {
                extendProps = orderModel.getExtendProps();
            }
        } else {
            if (MapUtils.isEmpty(orderModel.getExtendProps())) {
                //extendProps.putAll(snapshotExtendProps);
            } else {
                //取快照的
                String snapshotCustomerInfoExtendPropsJsonStr = snapshotExtendProps.get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS);
                Map<String, String> snapshotCustomerInfoExtendPro = new HashMap<>();
                if (StringUtils.isNotBlank(snapshotCustomerInfoExtendPropsJsonStr)) {
                    snapshotCustomerInfoExtendPro = JSONUtils.jsonToMap(snapshotCustomerInfoExtendPropsJsonStr);
                }
                //取原单的
                String customerInfoExtendPropsJsonStr = orderModel.getExtendProps().get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS);
                Map<String, String> customerInfoExtendPro = new HashMap<>();
                if (StringUtils.isNotBlank(customerInfoExtendPropsJsonStr)) {
                    customerInfoExtendPro = JSONUtils.jsonToMap(customerInfoExtendPropsJsonStr);
                }
                snapshotCustomerInfoExtendPro.putAll(customerInfoExtendPro);
                String combineCustomerInfoExtendProStr = JSONUtils.mapToJson(snapshotCustomerInfoExtendPro);

                //extendProps.putAll(snapshotExtendProps);
                extendProps.putAll(orderModel.getExtendProps());
                extendProps.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS, combineCustomerInfoExtendProStr);
            }
        }
        //预分拣结果处理
        extendProps.putAll(toCallBackExtendPropsForCCB2B(orderModel));

        // 当前状态不为空时的签单返还信息结果的处理逻辑
        if(orderModel.getCustomStatus() == null) {
            return extendProps;
        }
        if(extendProps.containsKey(RERECEIVE_PAPER_STATUS)) {
            if(SignReturnEnumForPaperStatus.YI_WAN_CHENG.getCode().equals(extendProps.get(RERECEIVE_PAPER_STATUS))
                    || SignReturnEnumForPaperStatus.YI_QU_XIAO.getCode().equals(extendProps.get(RERECEIVE_PAPER_STATUS))) {
                reReceivePaperStatusIsFinal = true;
            }
        }
        if(extendProps.containsKey(RERECEIVE_ELECTRONIC_STATUS)) {
            if(SignReturnEnumForElectronicStatus.YI_WAN_CHENG.getCode().equals(extendProps.get(RERECEIVE_ELECTRONIC_STATUS))
                    || SignReturnEnumForElectronicStatus.YI_QU_XIAO.getCode().equals(extendProps.get(RERECEIVE_ELECTRONIC_STATUS))) {
                reReceiveElectronicStatusIsFinal = true;
            }
        }

        // 若OFC回传状态为签收/部分签收和拒收时，需要判断是否存在增值服务签单返还，若存在，则按照以下规则更新返单状态
        Integer signType = getSignReturnByReReceiveType(orderSnapshot, orderModel);
        if((B2BOrderStatusCustomEnum.CUSTOMER_SIGNED.customOrderStatus().equals(orderModel.getCustomStatus())
                || B2BOrderStatusCustomEnum.PARTIAL_SIGNED.customOrderStatus().equals(orderModel.getCustomStatus())
                || B2BOrderStatusCustomEnum.CUSTOMER_REJECTED.customOrderStatus().equals(orderModel.getCustomStatus()))
                && signType != null) {
            if(WRITTEN.equals(signType)) {
                if(B2BOrderStatusCustomEnum.CUSTOMER_SIGNED.customOrderStatus().equals(orderModel.getCustomStatus())
                        || B2BOrderStatusCustomEnum.PARTIAL_SIGNED.customOrderStatus().equals(orderModel.getCustomStatus())) {
                    if(!reReceivePaperStatusIsFinal) {
                        extendProps.put(RERECEIVE_PAPER_STATUS, SignReturnEnumForPaperStatus.QIAN_DAN_DAI_FAN.getCode());
                    }
                }
                if(B2BOrderStatusCustomEnum.CUSTOMER_REJECTED.customOrderStatus().equals(orderModel.getCustomStatus())) {
                    if(!reReceivePaperStatusIsFinal) {
                        extendProps.put(RERECEIVE_PAPER_STATUS, SignReturnEnumForPaperStatus.YI_QU_XIAO.getCode());
                    }
                }
            }else if(ELECTRONIC.equals(signType)) {
                if(B2BOrderStatusCustomEnum.CUSTOMER_SIGNED.customOrderStatus().equals(orderModel.getCustomStatus())
                        || B2BOrderStatusCustomEnum.PARTIAL_SIGNED.customOrderStatus().equals(orderModel.getCustomStatus())) {
                    if(!reReceiveElectronicStatusIsFinal) {
                        extendProps.put(RERECEIVE_ELECTRONIC_STATUS, SignReturnEnumForElectronicStatus.YI_WAN_CHENG.getCode());
                    }
                }
                if(B2BOrderStatusCustomEnum.CUSTOMER_REJECTED.customOrderStatus().equals(orderModel.getCustomStatus())) {
                    if(!reReceiveElectronicStatusIsFinal) {
                        extendProps.put(RERECEIVE_ELECTRONIC_STATUS, SignReturnEnumForElectronicStatus.YI_QU_XIAO.getCode());
                    }
                }
            }else if(COMBINE.equals(signType)) {
                if(B2BOrderStatusCustomEnum.CUSTOMER_SIGNED.customOrderStatus().equals(orderModel.getCustomStatus())
                        || B2BOrderStatusCustomEnum.PARTIAL_SIGNED.customOrderStatus().equals(orderModel.getCustomStatus())) {
                    if(!reReceivePaperStatusIsFinal) {
                        extendProps.put(RERECEIVE_PAPER_STATUS, SignReturnEnumForPaperStatus.QIAN_DAN_DAI_FAN.getCode());
                    }
                    if(!reReceiveElectronicStatusIsFinal) {
                        extendProps.put(RERECEIVE_ELECTRONIC_STATUS, SignReturnEnumForElectronicStatus.YI_WAN_CHENG.getCode());
                    }
                }
                if(B2BOrderStatusCustomEnum.CUSTOMER_REJECTED.customOrderStatus().equals(orderModel.getCustomStatus())) {
                    if(!reReceivePaperStatusIsFinal) {
                        extendProps.put(RERECEIVE_PAPER_STATUS, SignReturnEnumForPaperStatus.YI_QU_XIAO.getCode());
                    }
                    if(!reReceiveElectronicStatusIsFinal) {
                        extendProps.put(RERECEIVE_ELECTRONIC_STATUS, SignReturnEnumForElectronicStatus.YI_QU_XIAO.getCode());
                    }
                }
            }
        }
        return extendProps;
    }

    public ModifyOrderFacadeRequest toCCB2CCallbackOrderSomeDataFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            if (orderModel.getOrderBusinessIdentity() != null) {
                OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
                businessIdentity.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
                businessIdentity.setBusinessType(orderModel.getOrderBusinessIdentity().getBusinessType());
                businessIdentity.setBusinessScene(orderModel.getOrderBusinessIdentity().getBusinessScene());
                modifyOrderFacadeRequest.setBusinessIdentity(toBusinessIdentityFacade(businessIdentity));
            }
            modifyOrderFacadeRequest.setOrderNo(orderModel.orderNo());
            if (shouldUpdateStatus(context)) {
                // 回传的时候订单主状态不可以被更新为取消，但是扩展状态是可以被更新的
                if (null != orderModel.getOrderStatus()
                        && null != orderModel.getOrderStatus().getOrderStatus()
                        && OrderStatusEnum.CANCELED != orderModel.getOrderStatus().getOrderStatus()) {
                    modifyOrderFacadeRequest.setOrderStatus(orderModel.getOrderStatus().getOrderStatus().getCode());
                }
                modifyOrderFacadeRequest.setOrderStatusCustom(orderModel.getCustomStatus());
                modifyOrderFacadeRequest.setExecutedStatus(orderModel.getExecutedStatus());
            }

            if (orderModel.requestProfile() != null) {
                modifyOrderFacadeRequest.setTenantId(orderModel.requestProfile().getTenantId());
            }
            //modifyOrderFacadeRequest.setChannel(toChannelFacade(orderModel.getChannel()));
            modifyOrderFacadeRequest.setFinance(toFinanceFacade(orderModel));
            modifyOrderFacadeRequest.setShipment(toShipmentFacade(orderModel.getShipment()));
            modifyOrderFacadeRequest.setOperator(orderModel.getOperator());
            modifyOrderFacadeRequest.setRemark(orderModel.getRemark());

            //只有主档扩展的扩展大字段里有beyondMessageDeliver、beyondMessagePick则更新扩展大字段，否则默认不更新
            if (MapUtils.isNotEmpty(orderModel.getExtendProps())) {
                Map<String, String> extendProps = new HashMap<>(orderModel.getExtendProps());
                if (!shouldUpdateStatus(context)) {
                    //不更新订单状态，商家扩展状态也不更新
                    if (extendProps.containsKey(OrderConstants.SELLER_EXTEND_STATUS)) {
                        extendProps.remove(OrderConstants.SELLER_EXTEND_STATUS);
                    }
                }
                String jsonStr = orderModel.getExtendProps().get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS);
                if (StringUtils.isNotBlank(jsonStr)) {
                    Map<String, String> customerInfoExtendPro = JSONUtils.jsonToMap(jsonStr);
                    if (StringUtils.isNotBlank(customerInfoExtendPro.get(OrderConstants.BEYOND_MESSAGE_PICK))
                            || StringUtils.isNotBlank(customerInfoExtendPro.get(OrderConstants.BEYOND_MESSAGE_DELIVER))) {
                        //取快照的
                        Map<String, String> snapshotCustomerInfoExtendPro = new HashMap<>();
                        String snapshotCustomerInfoExtendPropsJsonStr = orderModel.getOrderSnapshot().getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS);
                        if (StringUtils.isNotBlank(snapshotCustomerInfoExtendPropsJsonStr)) {
                            snapshotCustomerInfoExtendPro = JSONUtils.jsonToMap(snapshotCustomerInfoExtendPropsJsonStr);
                        }
                        if (StringUtils.isNotBlank(customerInfoExtendPro.get(OrderConstants.BEYOND_MESSAGE_PICK))) {
                            snapshotCustomerInfoExtendPro.put(OrderConstants.BEYOND_MESSAGE_PICK, customerInfoExtendPro.get(OrderConstants.BEYOND_MESSAGE_PICK));
                        }
                        if (StringUtils.isNotBlank(customerInfoExtendPro.get(OrderConstants.BEYOND_MESSAGE_DELIVER))) {
                            snapshotCustomerInfoExtendPro.put(OrderConstants.BEYOND_MESSAGE_DELIVER, customerInfoExtendPro.get(OrderConstants.BEYOND_MESSAGE_DELIVER));
                        }
                        extendProps.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS, JSONUtils.mapToJson(snapshotCustomerInfoExtendPro));
                    } else {
                        extendProps.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS, orderModel.getOrderSnapshot().getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                    }
                }
                modifyOrderFacadeRequest.setExtendProps(extendProps);
            }
            // 拦截回传时需要更新取消状态和拦截类型，
            // 拦截类型只有在仅拦截时才会更新，通过判断拦截类型是否有值来决定是否需要更新拦截类型
            if (null != orderModel.getOrderStatus()
                    && null != orderModel.getOrderStatus().getOrderStatus()) {
                //拦截状态处理
                callbackInterceptStatusPro(orderModel,orderModel.getOrderStatus().getOrderStatus(),modifyOrderFacadeRequest);
            }
            // 回传传入拦截失败-100000，更新订单的取消和拦截状态为拦截失败
            if (ExpressOrderStatusExtendEnum.INTERCEPT_FAILED.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_FAIL.getCode());
                modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.INTERCEPT_FAIL.getCode());
                setInterceptFacade(modifyOrderFacadeRequest);
            }

            // 回传接口回传的拦截成功需要更新订单取消状态为拦截成功，同时更新订单自定义状态为已拦截
            if (ExpressOrderStatusExtendEnum.LAN_JIE_CHENG_GONG.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_SUCCESS.getCode());
                modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.INTERCEPT_SUCCESS.getCode());
                setInterceptFacade(modifyOrderFacadeRequest);
            }

            // 回传传入解除拦截成功-2970，更新订单的拦截状态为解除拦截成功
            if (ExpressOrderStatusExtendEnum.LIFTED_INTERCEPT_SUCCESS.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                if (null != orderModel.getOrderSnapshot().getInterceptType()) {
                    modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.LIFTED_INTERCEPT_SUCCESS.getCode());
                    setInterceptFacade(modifyOrderFacadeRequest);
                }
            }
            // 回传传入解除拦截失败-100100，更新订单的拦截状态为解除拦截失败
            if (ExpressOrderStatusExtendEnum.LIFTED_INTERCEPT_FAIL.getExtendStatus().equals(orderModel.getExecutedStatus())) {
                if (null != orderModel.getOrderSnapshot().getInterceptType()) {
                    modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.LIFTED_INTERCEPT_FAIL.getCode());
                    setInterceptFacade(modifyOrderFacadeRequest);
                }
            }

            //预分拣结果处理
            handleCallBackPresortExtend(orderModel);
            if (orderModel.getOrderSnapshot().getProductDelegate() != null) {
                List<Product> list1 = (List<Product>) orderModel.getProductDelegate().getProducts();
                modifyOrderFacadeRequest.setProducts(toB2CCallBackProductFacades(orderModel.getOrderSnapshot(), list1));
            }
            modifyOrderFacadeRequest.setRecheckWeight(orderModel.getRecheckWeight());
            modifyOrderFacadeRequest.setRecheckVolume(orderModel.getRecheckVolume());

            // 商品信息
            modifyOrderFacadeRequest.setGoodsList(toGoodsFacades((List<Goods>) orderModel.getGoodsDelegate().getGoodsList()));
        }
        return modifyOrderFacadeRequest;
    }

    private Integer getSignReturnByReReceiveType(ExpressOrderModel orderSnapshot ,ExpressOrderModel order) {
        Integer signType = null;
        Product addOnProduct = isContainsCCProductDelegate(order.getProductDelegate());
        if (addOnProduct == null) {
            addOnProduct = isContainsCCProductDelegate(orderSnapshot.getProductDelegate());
        }
        if(null != addOnProduct){
            String reReceiveStr = addOnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE.getCode());
            if (StringUtils.isNotBlank(reReceiveStr)) {
                JSONArray reReceiveArr = JSON.parseArray(reReceiveStr);
                int signReturn = 0;
                for (int i = 0; i < reReceiveArr.size(); i++) {
                    if ("written".equals(reReceiveArr.get(i))) {
                        signReturn += 1;
                    } else if ("electronic".equals(reReceiveArr.get(i))) {
                        signReturn += 3;
                    }
                }
                signType = signReturn;
            }
        }
        return signType;
    }

    /**
     * 判断订单产品信息的增值产品
     * 是否包含冷链或医药签单返还
     * 如果包含并返回结果
     * @param productDelegate 产品信息
     * @return
     */
    private Product isContainsCCProductDelegate(ProductDelegate productDelegate){
        if(CollectionUtils.isEmpty(productDelegate.getProducts())){
            return null;
        }else{
            List<? extends IProduct> collect = productDelegate.getProducts().stream()
                    .filter(iProduct -> ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode().equals(iProduct.getProductType())).collect(Collectors.toList());//过滤主产品

            //回传除签单返还以外的增值服务 需要校验状态
            List<IProduct> products = collect.stream()
                    .filter(iProduct -> AddOnProductEnum.getSignReturnCode().contains(iProduct.getProductNo()))//过滤签单返还增值服务
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(products)){
                return (Product)products.get(0);
            }
        }
        return null;
    }

    /**
     * 回传扩展字段处理（预分拣结果处理）
     *
     * @param model
     * @return
     */
    private Map<String, String> toCallBackExtendPropsForCCB2B(ExpressOrderModel model) {
        Map<String, String> map = new HashMap<>();
        ManualPresortExtend presortExtend = new ManualPresortExtend();
        //获取快照中预分拣结果
        if (StringUtils.isNotBlank(model.getOrderSnapshot().getExtendProps().get(OrderConstants.PRESORT_EXTEND))) {
            presortExtend = JSONUtils.jsonToBean(model.getOrderSnapshot().getExtendProps().get(OrderConstants.PRESORT_EXTEND), ManualPresortExtend.class);
        }
        //揽收预分拣超区处理
        if (BEYOND_YES.equals(model.getAttachment(OrderConstants.PICKUP_PRESORT))) {
            if (StringUtils.isNotBlank(model.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS))) {
                Map<String, String> customerInfoExtendProps = JSONUtils.jsonToMap(model.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                if (StringUtils.isNotBlank(customerInfoExtendProps.get(OrderConstants.BEYOND_MESSAGE_PICK))) {
                    presortExtend.setStartStation(JSONUtils.jsonToBean(customerInfoExtendProps.get(OrderConstants.BEYOND_MESSAGE_PICK), ManualPresortExtend.ManualPresortDto.class));
                    map.put(OrderConstants.PRESORT_EXTEND, JSONUtils.beanToJSONDefault(presortExtend));
                }
            }
        }
        //派送预分拣超区处理
        if (BEYOND_YES.equals(model.getAttachment(OrderConstants.DELIVERY_PRESORT))) {
            if (StringUtils.isNotBlank(model.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS))) {
                Map<String, String> customerInfoExtendProps = JSONUtils.jsonToMap(model.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                if (StringUtils.isNotBlank(customerInfoExtendProps.get(OrderConstants.BEYOND_MESSAGE_DELIVER))) {
                    presortExtend.setEndStation(JSONUtils.jsonToBean(customerInfoExtendProps.get(OrderConstants.BEYOND_MESSAGE_DELIVER), ManualPresortExtend.ManualPresortDto.class));
                    map.put(OrderConstants.PRESORT_EXTEND, JSONUtils.beanToJSONDefault(presortExtend));
                }
            }
        }
        return map;
    }

    /**
     * 回传产品处理-第一个参数是历史产品，第二个参数是回传产品
     *
     * @param snapshotModel
     * @param products
     * @return
     */
    public List<ProductFacade> toCCB2BCallBackProductFacades(ExpressOrderModel snapshotModel, List<Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        LOGGER.info("回传增值服务转换：snapshotModel:{},products:{}", JSONUtils.beanToJSONDefault(snapshotModel), JSONUtils.beanToJSONDefault(products));
        List<ProductFacade> productFacades = new ArrayList<>();
        for (Product product : products) {
            Product snapshotProduct = snapshotModel.getProductDelegate().ofProductNo(product.getProductNo());

            ProductFacade productFacade = new ProductFacade();
            //签单返还
            if (AddOnProductEnum.getSignReturnCode().contains(product.getProductNo()) && product.getProductAttrs() != null) {
                //纸质类型-运单号
                if (null != product.getProductAttrs().get(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode())) {
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RETURN_WAYBILL_NO.getCode()));
                }
                //电子类型-照片和拍照时间
                if (null != product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode())) {
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO.getCode()));
                    snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_PHOTO_TIME.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_PHOTO_TIME.getCode()));
                }
            }
            //协商再投
            if (AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode().equals(product.getProductNo()) && product.getProductAttrs() != null) {
                snapshotProduct.getProductAttrs().put(AddOnProductAttrEnum.REAL_REJECT_AUDIT_NUMBERS.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.REAL_REJECT_AUDIT_NUMBERS.getCode()));
            }
            //产品编码
            productFacade.setProductNo(snapshotProduct.getProductNo());
            //产品名称
            productFacade.setProductName(snapshotProduct.getProductName());
            //产品类型
            productFacade.setProductType(snapshotProduct.getProductType());
            //降级前产品编码
            productFacade.setOriginalProductNo(snapshotProduct.getOriginalProductNo());
            //降级前产品名称
            productFacade.setOriginalProductName(snapshotProduct.getOriginalProductName());
            //主产品编码
            productFacade.setParentNo(snapshotProduct.getParentNo());
            //扩展信息
            productFacade.setExtendProps(snapshotProduct.getExtendProps());
            //产品属性
            productFacade.setProductAttrs(snapshotProduct.getProductAttrs());
            //操作类型
            productFacade.setOperateType(OperateTypeEnum.UPDATE);
            productFacades.add(productFacade);
        }
        return productFacades;
    }


    /**
     * 钱包代扣修改财务信息
     * @param orderModel
     * @return
     */
    public ModifyOrderFacadeRequest toPayModifyOrderFacadeRequest(ExpressOrderModel orderModel) {
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        if (null != orderModel && null != orderModel.getFinance()) {
            FinanceFacade finance = new FinanceFacade();
            finance.setExtendProps(orderModel.getFinance().getExtendProps());
            facadeRequest.setFinance(finance);
        }
        facadeRequest.setOrderNo(orderModel.orderNo());
        return facadeRequest;
    }


    /**
     * b2c 回传-拦截终态处理
     * @param orderStatusEnum
     * @param modifyOrderFacadeRequest
     */
    private void callbackInterceptStatusPro(ExpressOrderModel orderModel,OrderStatusEnum orderStatusEnum,ModifyOrderFacadeRequest modifyOrderFacadeRequest){
        //取消状态【只有回传拦截的时候更新取消状态】
        if(OrderStatusEnum.INTERCEPT == orderModel.getOrderStatus().getOrderStatus()){
            modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.CANCEL_SUCCESS.getCode());
        }

        //拦截状态
        //拒收、已取消、报废、异常终止、已拦截
        //若订单拦截状态为拦截中，则把订单拦截状态更新为拦截成功。
        if(OrderStatusEnum.CUSTOMER_REJECTED == orderModel.getOrderStatus().getOrderStatus()
                || OrderStatusEnum.CANCELED == orderModel.getOrderStatus().getOrderStatus()
                || OrderStatusEnum.SCRAPPED == orderModel.getOrderStatus().getOrderStatus()
                || OrderStatusEnum.ABNORMAL_END == orderModel.getOrderStatus().getOrderStatus()
                || OrderStatusEnum.INTERCEPT == orderModel.getOrderStatus().getOrderStatus()
        ){
            if(InterceptTypeEnum.INTERCEPTING == orderModel.getOrderSnapshot().getInterceptType()) {
                modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.INTERCEPT_SUCCESS.getCode());
                setInterceptFacade(modifyOrderFacadeRequest);
            }
            //同步更新取消状态字段
            if (CancelStatusEnum.INTERCEPTING == orderModel.getOrderSnapshot().getCancelStatus()) {
                modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_SUCCESS.getCode());
            }
        }
        //妥投、部分妥投
        //若订单拦截状态为拦截中，则把订单拦截状态更新为拦截失败。
        if(OrderStatusEnum.CUSTOMER_SIGNED == orderModel.getOrderStatus().getOrderStatus()
                || OrderStatusEnum.PARTIAL_SIGNED == orderModel.getOrderStatus().getOrderStatus()){
            if (InterceptTypeEnum.INTERCEPTING == orderModel.getOrderSnapshot().getInterceptType()) {
                modifyOrderFacadeRequest.setInterceptType(InterceptTypeEnum.INTERCEPT_FAIL.getCode());
                setInterceptFacade(modifyOrderFacadeRequest);
            }
            //同步更新取消状态字段
            if (CancelStatusEnum.INTERCEPTING == orderModel.getOrderSnapshot().getCancelStatus()) {
                modifyOrderFacadeRequest.setCancelStatus(CancelStatusEnum.INTERCEPT_FAIL.getCode());
            }
        }
    }
    /**
     * 冷链B2B计算费用，仅有财务信息以及必要信息
     *
     * @param context
     * @return
     * @throws ParseException
     */
    public ModifyOrderFacadeRequest toCCB2BCalculateFeeFacadeRequest(ExpressOrderContext context) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel model = context.getOrderModel();
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //业务身份信息
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(model.getOrderBusinessIdentity()));
        //订单id
        //facadeRequest.setOrderId(model.getOrderId());
        //订单号
        facadeRequest.setOrderNo(model.orderNo());
        //财务信息
        facadeRequest.setFinance(toFinanceFacade(model));
        return facadeRequest;
    }

    /**
     * 跨境报关信息转换
     */
    private CustomsFacade toCustomsFacade(Customs customs) {
        if (customs == null) {
            return null;
        }
        return CustomsFacadeMapper.INSTANCE.toCustomsFacade(customs);
    }

    /**
     * 附件列表转换
     */
    private List<AttachmentFacade> toAttachmentFacades(List<Attachment> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            return null;
        }
        return AttachmentFacadeMapper.INSTANCE.toAttachmentFacades(attachments);
    }

    /**
     * 取消拦截修改转换
     *
     * @return
     */
    public ModifyOrderFacadeRequest toCancelInterceptOrderFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            //订单ID
            //request.setOrderId(context.getOrderModel().getOrderSnapshot().getOrderId());
            //订单号
            request.setOrderNo(context.getOrderModel().getOrderSnapshot().orderNo());
            //租户
            request.setTenantId(context.getOrderModel().requestProfile() != null
                    ? context.getOrderModel().requestProfile().getTenantId() : null);

            Map<String, String> extendMap = new HashMap<>();
            //取消状态
            if (context.getOrderModel().getCancelStatus() != null) {
                request.setCancelStatus(context.getOrderModel().getCancelStatus().getCode());

                // customerInfoExtendProps（ -cancelReasonCode、 -cancelReason、-cancelRemark）融合快运不用写，原快递和融合快递需要写
                if (context.getOrderModel().isB2C() && !UnitedB2CUtil.isUnitedFreightB2C(context.getOrderModel())) {

                    Map<String, String> customerInfoExtendProps = JSONUtils.jsonToMap(context.getOrderModel().getOrderSnapshot()
                            .getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                    if (customerInfoExtendProps == null){
                        customerInfoExtendProps = new HashMap<>();
                    }
                    // customerInfoExtendProps的取消原因等，仅快递B2C使用 todo 废弃放customerInfoExtendProps，统一放到extendMap
                    customerInfoExtendProps.put("cancelReasonCode",context.getOrderModel().getCancelReasonCode());
                    customerInfoExtendProps.put("cancelReason",context.getOrderModel().getCancelReason());
                    customerInfoExtendProps.put("cancelRemark",context.getOrderModel().getRemark());
                    extendMap.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS,JSONUtils.beanToJSONDefault(customerInfoExtendProps));

                }

                if (StringUtils.isNotBlank(context.getOrderModel().getCancelReason())) {
                    extendMap.put(AttachmentKeyEnum.CANCEL_REASON.getKey(), context.getOrderModel().getCancelReason());
                }
                if (StringUtils.isNotBlank(context.getOrderModel().getCancelReasonCode())) {
                    extendMap.put(AttachmentKeyEnum.CANCEL_REASON_CODE.getKey(), context.getOrderModel().getCancelReasonCode());
                }
                if (StringUtils.isNotBlank(context.getOrderModel().getRemark())) {
                    extendMap.put(AttachmentKeyEnum.CANCEL_REMARK.getKey(), context.getOrderModel().getRemark());
                }
                if (CancelStatusEnum.CANCEL_SUCCESS.equals(context.getOrderModel().getCancelStatus())) {
                    request.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
                    request.setOrderStatusCustom(ExpressOrderStatusCustomEnum.CANCELED.customOrderStatus());

                    // 融合快运不用写，原快递和融合快递保持原逻辑
                    if (context.getOrderModel().isB2C() && !UnitedB2CUtil.isUnitedFreightB2C(context.getOrderModel())) {
                        //OFC取消不再补扩展状态和商家扩展状态
                        if (SystemCallerEnum.EXPRESS_OFC != context.getOrderModel().getChannel().getSystemCaller()) {
                            extendMap.put(OrderConstants.SELLER_EXTEND_STATUS, B2CExtendStatusEnum.XIA_DAN_QU_XIAO.getExtendStatus());
                        }
                    }

                    if (context.getOrderModel().getOrderSnapshot() != null && context.getOrderModel().getOrderSnapshot().getFinance() != null
                            && PaymentStageEnum.ONLINEPAYMENT == context.getOrderModel().getOrderSnapshot().getFinance().getPaymentStage()
                            && PaymentStatusEnum.WAITING_FOR_PAYMENT == context.getOrderModel().getOrderSnapshot().getFinance().getPaymentStatus()) {
                        request.setExecutedStatus(ExpressOrderStatusExtendEnum.XIA_DAN_QU_XIAO.getExtendStatus());
                    } else {
                        if (SystemCallerEnum.EXPRESS_OFC != context.getOrderModel().getChannel().getSystemCaller()) {
                            request.setExecutedStatus(ExpressOrderStatusExtendEnum.XIA_DAN_QU_XIAO.getExtendStatus());
                        } else {
                            request.setExecutedStatus(context.getOrderModel().getExecutedStatus());
                        }
                    }
                }
                if (CancelStatusEnum.INTERCEPTING.equals(context.getOrderModel().getCancelStatus())) {
                    request.setExecutedStatus(ExpressOrderStatusExtendEnum.FA_QI_LAN_JIE.getExtendStatus());
                    extendMap.put(OrderConstants.SELLER_EXTEND_STATUS, ExpressOrderStatusExtendEnum.FA_QI_LAN_JIE.getExtendStatus());
                }
            }

            if (null != context.getOrderModel().getInterceptType()) {
                InterceptFacade interceptFacade = new InterceptFacade();
                interceptFacade.setInterceptResultTime(context.getOrderModel().getIntercept().getInterceptResultTime());
                request.setInterceptTime(context.getOrderModel().getIntercept().getInterceptTime());
                request.setInterceptFacade(interceptFacade);
                request.setInterceptType(context.getOrderModel().getInterceptType().getCode());
                // 只有仅拦截的情况下订单主状态才会更新为已拦截
                if (CancelInterceptTypeEnum.ONLY_INTERCEPT == context.getOrderModel().getCancelInterceptType()
                        && InterceptTypeEnum.INTERCEPT_SUCCESS.equals(context.getOrderModel().getInterceptType())) {
                    request.setOrderStatus(OrderStatusEnum.INTERCEPT.getCode());
                    request.setOrderStatusCustom(ExpressOrderStatusCustomEnum.INTERCEPT.customOrderStatus());
                    //OFC取消不再补扩展状态和商家扩展状态
                    if (SystemCallerEnum.EXPRESS_OFC != context.getOrderModel().getChannel().getSystemCaller()) {
                        request.setExecutedStatus(EXECUTED_STATUS_INTERCEPT);
                        extendMap.put(OrderConstants.SELLER_EXTEND_STATUS, ExpressOrderStatusExtendEnum.LAN_JIE_CHENG_GONG.getExtendStatus());
                    }
                }
            }

            request.setExtendProps(extendMap);
            //修改时间
            request.setUpdateTime(new Date());
            //修改人
            request.setUpdateUser(context.getOrderModel().getOperator());
            request.setOperator(context.getOrderModel().getOperator());

            //拦截信息
            if (null != context.getOrderModel().getIntercept()){
                InterceptFacade interceptFacade = request.getInterceptFacade();
                if (null == interceptFacade) {
                    interceptFacade = new InterceptFacade();
                }
                Intercept intercept = context.getOrderModel().getIntercept();
                interceptFacade.setInterceptHandlingMode(intercept.getInterceptHandlingMode());
                interceptFacade.setInterceptStationNo(intercept.getInterceptStationNo());
                interceptFacade.setInterceptAddress(AddressMapper.INSTANCE.toAddressInfo(intercept.getInterceptAddress()));
                interceptFacade.setInterceptRouteNode(intercept.getInterceptRouteNode());
                interceptFacade.setInterceptPayAccountNo(intercept.getInterceptPayAccountNo());
                request.setInterceptFacade(interceptFacade);
            }

            // 识别ordersign-unitedB2CFlag=1且主产品=快运，则跳过弃货状态
            if (!UnitedB2CUtil.isUnitedFreightB2C(context.getOrderModel())) {
                //弃货状态
                if (context.getOrderModel().getDiscardStatus() != null) {
                    request.setDiscardStatus(context.getOrderModel().getDiscardStatus().getCode());
                }
            }

            // 只加渠道扩展字段
            if (context.getOrderModel().getChannel() != null) {

                if (context.getOrderModel().getChannel().getExtendProps() != null) {
                    ChannelFacade channelFacade = new ChannelFacade();
                    channelFacade.setExtendProps(context.getOrderModel().getChannel().getExtendProps());
                    request.setChannel(channelFacade);
                }

            }

        }
        return request;
    }

    public ModifyOrderFacadeRequest toTaxEnquiryModifyOrderFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单号
            request.setOrderNo(orderModel.orderNo());
            // 税金支付状态
            FinanceFacade finance = new FinanceFacade();
            Map<String, String> payStatusMap = new HashMap<>();
            payStatusMap.put(FinanceConstants.TAX_PAY_STATUS, String.valueOf(PaymentStatusEnum.COMPLETE_PAYMENT.getStatus()));
            finance.setPayStatusMap(payStatusMap);
            // 实际支付方式
            finance.setActualPaymentType(orderModel.getFinance().getActualPaymentType());
            request.setFinance(finance);
        }
        return request;
    }

    @UnitedBusinessIdentityConverter(convertMode = ConvertMode.BEFORE_ORIGINAL_AFTER_REAL)
    public ModifyOrderFacadeRequest toRefundModifyOrderFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单号
            request.setOrderNo(orderModel.orderNo());
            // 修改人
            request.setOperator(orderModel.getOperator());
            // oms修改时间
            request.setOperateTime(orderModel.getOperateTime());
            // 退款状态
            FinanceFacade finance = new FinanceFacade();
            finance.setRefundStatus(RefundStatusEnum.REFUNDING.getStatus());

            //关联单-退款单
            List<RefOrderFacade> refOrderFacades = new ArrayList<>(1);
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderNo(orderModel.getRefundInfoVo().getRefundOrderNo());
            refOrderFacade.setRefOrderType(RefOrderTypeEnum.REFUND_ORDER.getCode());
            refOrderFacades.add(refOrderFacade);
            request.setRefOrders(refOrderFacades);
            request.setFinance(finance);
        }
        return request;
    }

    /**
     * 清空订单支付方式请求参数
     *
     * @param orderModel
     * @return
     */
    public ModifyOrderFacadeRequest toClearPaymentFacadeRequest(ExpressOrderModel orderModel) {
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
        facadeRequest.setOrderNo(orderModel.orderNo());
        facadeRequest.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        facadeRequest.setOperateTime(new Date());
        List<String> clearFields = new ArrayList<>();
        clearFields.add(ModifyItemConfigEnum.PAYMENT.getCode());
        facadeRequest.setClearFields(clearFields);

        // 清空关联支付单
        // ext里的管理单删除，目前是通过设置为空字符串""实现
        List<RefOrderFacade> refOrders = new ArrayList<>();
        RefOrderFacade refOrderFacade = new RefOrderFacade();
        refOrderFacade.setOperateType(OperateTypeEnum.DELETE);
        refOrderFacade.setRefOrderType(RefOrderTypeEnum.PAY_ORDER.getCode());
        refOrderFacade.setRefOrderNo("");
        refOrders.add(refOrderFacade);
        facadeRequest.setRefOrders(refOrders);

        return facadeRequest;
    }

    /**
     * 持久化支付单号请求参数
     *
     * @param alipayOrderNo
     * @param orderModel
     * @return
     */
    public ModifyOrderFacadeRequest toPersistPayOrderNoFacadeRequest(String alipayOrderNo, ExpressOrderModel orderModel) {

        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
        facadeRequest.setOrderNo(orderModel.orderNo());
        facadeRequest.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        facadeRequest.setOperateTime(new Date());

        // 新增关联单
        List<RefOrderFacade> refOrders = new ArrayList<>();
        RefOrderFacade refOrderFacade = new RefOrderFacade();
        refOrderFacade.setOperateType(OperateTypeEnum.INSERT);
        refOrderFacade.setRefOrderType(RefOrderTypeEnum.PAY_ORDER.getCode());
        refOrderFacade.setRefOrderNo(alipayOrderNo);
        refOrders.add(refOrderFacade);
        facadeRequest.setRefOrders(refOrders);

        return facadeRequest;
    }

    /**
     * 批量构造取消修改防腐
     * @param expressOrderContextList
     * @return
     */
    public List<ModifyOrderFacadeRequest> toCCB2BBatchCancelModifyOrderFacadeRequest(List<ExpressOrderContext> expressOrderContextList) {
        if (CollectionUtils.isEmpty(expressOrderContextList)) {
            return null;
        }
        List<ModifyOrderFacadeRequest> modifyOrderFacadeRequests = new ArrayList<>();
        for (ExpressOrderContext context : expressOrderContextList) {
            modifyOrderFacadeRequests.add(toCCB2BBatchCancelModifyOrderFacadeRequest(context));
        }
        return modifyOrderFacadeRequests;
    }

    public List<ModifyOrderFacadeRequest> toBatchModifyOrderFacadeRequest(List<ExpressOrderContext> contextList) throws ParseException {
        if (CollectionUtils.isEmpty(contextList)) {
            return null;
        }
        List<ModifyOrderFacadeRequest> modifyOrderFacadeRequests = new ArrayList<>();
        for (ExpressOrderContext context : contextList) {
            modifyOrderFacadeRequests.add(toModifyOrderFacadeRequest(context));
        }
        return modifyOrderFacadeRequests;
    }

    /**
     * 取消修改转换
     * todo:待优化 @jiangwei279
     * @return
     */
    public ModifyOrderFacadeRequest toCCB2BBatchCancelModifyOrderFacadeRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            //订单号
            request.setOrderNo(context.getOrderModel().orderNo());
            //租户
            request.setTenantId(context.getOrderModel().requestProfile() != null ? context.getOrderModel().requestProfile().getTenantId() : null);
            //取消状态
            Map<String, String> extendMap = new HashMap<>();
            if (context.getOrderModel().getCancelStatus() != null) {
                if (CancelStatusEnum.CANCEL_SUCCESS.equals(context.getOrderModel().getCancelStatus())) {
                    request.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
                }
                request.setExecutedStatus(context.getOrderModel().getExecutedStatus());
                request.setCancelStatus(context.getOrderModel().getCancelStatus().getCode());

                request.setOrderStatusCustom(context.getOrderModel().getCustomStatus());
                if (MapUtils.isNotEmpty(context.getOrderModel().getExtendProps())) {
                    extendMap.put(AttachmentKeyEnum.BUSINESS_EXPANSION_STATUS.getKey(), context.getOrderModel().getExtendProps().get(AttachmentKeyEnum.BUSINESS_EXPANSION_STATUS.getKey()));
                    extendMap.put(AttachmentKeyEnum.CANCEL_REASON_CODE.getKey(), context.getOrderModel().getExtendProps().get(AttachmentKeyEnum.CANCEL_REASON_CODE.getKey()));
                }
            }
            request.setExtendProps(extendMap);
            //修改时间
            request.setUpdateTime(new Date());
            //修改人
            request.setUpdateUser(context.getOrderModel().getOperator());
            request.setOperator(context.getOrderModel().getOperator());
        }
        return request;
    }

    /**
     * 快运整车直达重新受理-重新询价
     */
    public ModifyOrderFacadeRequest toFreightFTLReacceptEnquiry(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单号
            request.setOrderNo(orderModel.orderNo());
            // 修改人
            request.setOperator(orderModel.getOperator());
            // oms修改时间
            request.setOperateTime(orderModel.getOperateTime());

            // 关联单-添加新询价单
            request.setRefOrders(toFreightFTLReacceptEnquiryRefOrders(orderModel));

            // 关联单-删除旧询价单
            request.setDeleteRefOrders(toFreightFTLReacceptEnquiryDeleteRefOrders(orderModel));

            // 财务信息
            Finance finance = orderModel.getFinance();
            if (finance != null) {
                FinanceFacade financeFacade = new FinanceFacade();
                // 询价状态
                if (finance.getEnquiryStatus() != null) {
                    financeFacade.setEnquiryStatus(finance.getEnquiryStatus().getCode());
                }
                // 费用明细
                if (CollectionUtils.isNotEmpty(finance.getFinanceDetails())) {
                    // 重新询价有费用则覆盖上次费用
                    financeFacade.setFinanceDetails(FinanceDetailFacadeMapper.INSTANCE.toFinanceDetailFacadeList(finance.getFinanceDetails()));
                } else {
                    // 重新询价无费用则清空上次费用
                    financeFacade.setFinanceDetails(null);
                    List<String> clearFields = request.getClearFields();
                    if (clearFields == null) {
                        clearFields = new ArrayList<>();
                        request.setClearFields(clearFields);
                    }
                    clearFields.add(ModifyItemConfigEnum.FINANCE_DETAILS.getCode());
                }
                // 折前金额
                financeFacade.setPreAmount(finance.getPreAmount());
                // 折后金额
                financeFacade.setDiscountAmount(finance.getDiscountAmount());
                // 扩展信息
                if (MapUtils.isNotEmpty(finance.getExtendProps())) {
                    financeFacade.setExtendProps(finance.getExtendProps());
                }
                request.setFinance(financeFacade);
            }

            // 派送信息
            Shipment shipment = orderModel.getShipment();
            if (shipment != null) {
                ShipmentFacade shipmentFacade = new ShipmentFacade();
                if (shipment.getExpectPickupStartTime() != null) {
                    shipmentFacade.setExpectPickupStartTime(shipment.getExpectPickupStartTime());
                }
                if (shipment.getExpectPickupEndTime() != null) {
                    shipmentFacade.setExpectPickupEndTime(shipment.getExpectPickupEndTime());
                }
                request.setShipment(shipmentFacade);
            }

            // 订单标识-更新成未修改
            Map<String, String> orderSign = new HashMap<>();
            orderSign.put(OrderSignEnum.MODIFY_VEHICLE_FEE.getCode(), OrderConstants.MODIFY_VEHICLE_FEE_INIT);
            request.setOrderSign(orderSign);
        }
        return request;
    }

    /**
     * 快运整车直达重新受理-关联单转换
     */
    private List<RefOrderFacade> toFreightFTLReacceptEnquiryRefOrders(ExpressOrderModel orderModel) {
        // 添加新询价单号
        String enquiryOrderNo = getEnquiryOrderNo(orderModel);
        if(StringUtils.isNotBlank(enquiryOrderNo)){
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderNo(enquiryOrderNo);
            refOrderFacade.setRefOrderType(RefOrderTypeEnum.ENQUIRY.getCode());
            refOrderFacade.setOperateType(OperateTypeEnum.INSERT);

            List<RefOrderFacade> refOrderFacades = new ArrayList<>(1);
            refOrderFacades.add(refOrderFacade);
            return refOrderFacades;
        }
        return null;
    }

    /**
     * 快运整车直达重新受理-删除关联单转换
     */
    private List<RefOrderFacade> toFreightFTLReacceptEnquiryDeleteRefOrders(ExpressOrderModel orderModel) {
        // 删除旧询价单号
        String enquiryOrderNo = getEnquiryOrderNo(orderModel.getOrderSnapshot());
        if (StringUtils.isNotBlank(enquiryOrderNo)) {
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderNo(enquiryOrderNo);
            refOrderFacade.setRefOrderType(RefOrderTypeEnum.ENQUIRY.getCode());
            refOrderFacade.setOperateType(OperateTypeEnum.DELETE);

            List<RefOrderFacade> refOrderFacades = new ArrayList<>(1);
            refOrderFacades.add(refOrderFacade);
            return refOrderFacades;
        }
        return null;
    }

    /**
     * 获取询价单号
     */
    private String getEnquiryOrderNo(ExpressOrderModel orderModel) {
        if (orderModel == null) {
            return null;
        }
        RefOrderDelegate refOrderInfoDelegate = orderModel.getRefOrderInfoDelegate();
        if (refOrderInfoDelegate == null) {
            return null;
        }
        return orderModel.getRefOrderInfoDelegate().getEnquiryOrderNo();
    }

    /**
     * 整车直达-快运修改财务请求
     */
    public ModifyOrderFacadeRequest toFreightFTLModifyFinance(ExpressOrderModel orderModel) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (orderModel != null) {
            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单号
            request.setOrderNo(orderModel.orderNo());
            // 修改人
            request.setOperator(orderModel.getOperator());
            // oms修改时间
            request.setOperateTime(orderModel.getOperateTime());

            // 财务信息
            Finance finance = orderModel.getFinance();
            if (finance != null) {
                FinanceFacade financeFacade = new FinanceFacade();
                // 询价状态
                if (finance.getEnquiryStatus() != null) {
                    financeFacade.setEnquiryStatus(finance.getEnquiryStatus().getCode());
                }
                // 费用明细
                if (CollectionUtils.isNotEmpty(finance.getFinanceDetails())) {
                    financeFacade.setFinanceDetails(FinanceDetailFacadeMapper.INSTANCE.toFinanceDetailFacadeList(finance.getFinanceDetails()));
                }
                // 折前金额
                if (finance.getPreAmount() != null) {
                    financeFacade.setPreAmount(finance.getPreAmount());
                }
                // 折后金额
                if (finance.getDiscountAmount() != null) {
                    financeFacade.setDiscountAmount(finance.getDiscountAmount());
                }
                // 支付截止时间
                if (finance.getPayDeadline() != null) {
                    financeFacade.setPayDeadline(finance.getPayDeadline());
                }
                request.setFinance(financeFacade);
            }

            // 订单标识
            Map<String, String> orderSign = orderModel.getOrderSign();
            if (MapUtils.isNotEmpty(orderSign)) {
                request.setOrderSign(orderSign);
            }

            // 扩展字段-修改信息标记
            Map<String, String> extendProps = orderModel.getExtendProps();
            if (MapUtils.isNotEmpty(extendProps)) {
                request.setExtendProps(extendProps);
            }

            // 清空和修改
            request.setClearFields(orderModel.getClearFields());
            request.setModifiedFields(orderModel.getModifiedFields());
        }
        return request;
    }

    /**
     * 修改询价单
     * @param orderModel
     * @return
     */
    public ModifyOrderFacadeRequest toModifyEnquiryOrderNo(ExpressOrderModel orderModel) {
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        // 业务身份信息
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
        // 订单号
        facadeRequest.setOrderNo(orderModel.orderNo());
        // 修改人
        facadeRequest.setOperator(orderModel.getOperator());
        // oms修改时间
        facadeRequest.setOperateTime(new Date());
        if (null != orderModel.getFinance()) {
            // 询价状态
            FinanceFacade finance = new FinanceFacade();
            if (null != orderModel.getFinance().getEnquiryStatus()) {
                finance.setEnquiryStatus(orderModel.getFinance().getEnquiryStatus().getCode());
            }
            if (MapUtils.isNotEmpty(orderModel.getFinance().getExtendProps())) {
                finance.setExtendProps(orderModel.getFinance().getExtendProps());
            }

            facadeRequest.setFinance(finance);
        }

        //关联单-询价单
        if(StringUtils.isNotBlank(orderModel.getRefOrderInfoDelegate().getEnquiryOrderNo())){
            List<RefOrderFacade> refOrderFacades = new ArrayList<>(1);
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderNo(orderModel.getRefOrderInfoDelegate().getEnquiryOrderNo());
            refOrderFacade.setRefOrderType(RefOrderTypeEnum.ENQUIRY.getCode());
            refOrderFacades.add(refOrderFacade);
            facadeRequest.setRefOrders(refOrderFacades);
        }

        return facadeRequest;
    }

    /**
     * 快运整车直达TMS报价确认
     */
    public ModifyOrderFacadeRequest toExecuteFreightConfirm(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单号
            request.setOrderNo(orderModel.orderNo());
            // 修改人
            request.setOperator(orderModel.getOperator());
            // oms修改时间
            request.setOperateTime(orderModel.getOperateTime());

            // 财务信息
            Finance finance = orderModel.getFinance();
            if (finance != null) {
                FinanceFacade financeFacade = new FinanceFacade();
                // 询价状态
                if (finance.getEnquiryStatus() != null) {
                    financeFacade.setEnquiryStatus(finance.getEnquiryStatus().getCode());
                }
                // 费用明细
                if (CollectionUtils.isNotEmpty(finance.getFinanceDetails())) {
                    financeFacade.setFinanceDetails(FinanceDetailFacadeMapper.INSTANCE.toFinanceDetailFacadeList(finance.getFinanceDetails()));
                }
                // 折前金额
                if (finance.getPreAmount() != null) {
                    financeFacade.setPreAmount(finance.getPreAmount());
                }
                // 折后金额
                if (finance.getDiscountAmount() != null) {
                    financeFacade.setDiscountAmount(finance.getDiscountAmount());
                }
                // 扩展信息
                if (MapUtils.isNotEmpty(finance.getExtendProps())) {
                    financeFacade.setExtendProps(finance.getExtendProps());
                }
                request.setFinance(financeFacade);
            }
        }
        return request;
    }

    /**
     * 更新司机信息
     * @param orderModel
     * @return
     */
    public ModifyOrderFacadeRequest toEnquiryVehicleDriver(ExpressOrderModel orderModel) {

        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        // 业务身份信息
        request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
        // 订单号
        request.setOrderNo(orderModel.orderNo());

        // oms修改时间
        request.setOperateTime(orderModel.getOperateTime());

        ShipmentFacade shipmentFacade = new ShipmentFacade();
        shipmentFacade.setExtendProps(orderModel.getShipment().getExtendProps());
        request.setShipment(shipmentFacade);

        return request;
    }

    /**
     * 快运原单取消服务询价单
     */
    public ModifyOrderFacadeRequest toFreightOriginOrderCancelServiceEnquiryOrder(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            ExpressOrderModel enquiryOrderSnapshot = context.getOrderModel().getOrderSnapshot();
            ExpressOrderModel originOrder = enquiryOrderSnapshot.getOrderSnapshot();

            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单号：原单号
            request.setOrderNo(originOrder.orderNo());
            // 修改人
            request.setOperator(orderModel.getOperator());
            // oms修改时间
            request.setOperateTime(orderModel.getOperateTime());

            // 删除关联单
            List<RefOrderFacade> deleteRefOrders = new ArrayList<>(2);
            // 服务单
            RefOrderFacade serviceRefOrder = new RefOrderFacade();
            serviceRefOrder.setRefOrderNo(enquiryOrderSnapshot.orderNo());
            serviceRefOrder.setRefOrderType(RefOrderTypeEnum.SERVICE_ENQUIRY_ORDER.getCode());
            serviceRefOrder.setOperateType(OperateTypeEnum.DELETE);
            deleteRefOrders.add(serviceRefOrder);
            // 服务单运单
            RefOrderFacade waybillRefOrder = new RefOrderFacade();
            waybillRefOrder.setRefOrderNo(enquiryOrderSnapshot.getCustomOrderNo());
            waybillRefOrder.setRefOrderType(RefOrderTypeEnum.SERVICE_ENQUIRY_WAYBILL.getCode());
            waybillRefOrder.setOperateType(OperateTypeEnum.DELETE);
            deleteRefOrders.add(waybillRefOrder);
            request.setDeleteRefOrders(deleteRefOrders);

            // 根据服务单删除增值服务
            request.setProducts(deleteAddedProduct(enquiryOrderSnapshot.getProductDelegate()));

        }

        return request;
    }

    /**
     * 删除增值产品
     */
    private List<ProductFacade> deleteAddedProduct(ProductDelegate productDelegate) {
        List<ProductFacade> productFacades = new ArrayList<>();
        if (productDelegate == null || productDelegate.isEmpty()) {
            return productFacades;
        }
        List<Product> products = (List<Product>) productDelegate.getProducts();
        for (Product product : products) {
            if (ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode().equals(product.getProductType())) {
                ProductFacade productFacade = toDeleteProductFacade(product);
                productFacades.add(productFacade);
            }
        }
        return productFacades;
    }

    /**
     * 快运服务询价单取消
     */
    public ModifyOrderFacadeRequest toFreightCancelServiceEnquiryOrder(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单号
            request.setOrderNo(orderModel.orderNo());
            // 修改人
            request.setOperator(orderModel.getOperator());
            // oms修改时间
            request.setOperateTime(orderModel.getOperateTime());
            // 删除
            request.setYn(YN_DELETE);
            // 取消状态：取消成功
            request.setCancelStatus(CancelStatusEnum.CANCEL_SUCCESS.getCode());

            Map<String, String> extendMap = orderModel.getExtendProps();
            if (extendMap == null) {
                extendMap = new HashMap<>();
            }
            extendMap.put("cancelReasonCode", orderModel.getCancelReasonCode());
            extendMap.put("cancelReason", orderModel.getCancelReason());
            extendMap.put("cancelRemark", orderModel.getRemark());
            request.setExtendProps(extendMap);

        }
        return request;
    }

    /**
     * 删除时将Product转为ProductFacade
     */
    private ProductFacade toDeleteProductFacade(Product product) {
        ProductFacade productFacade = new ProductFacade();
        //产品编码
        productFacade.setProductNo(product.getProductNo());
        //产品名称
        productFacade.setProductName(product.getProductName());
        //产品类型
        productFacade.setProductType(product.getProductType());
        //降级前产品编码
        productFacade.setOriginalProductNo(product.getOriginalProductNo());
        //降级前产品名称
        productFacade.setOriginalProductName(product.getOriginalProductName());
        //主产品编码
        productFacade.setParentNo(product.getParentNo());
        //扩展信息
        productFacade.setExtendProps(product.getExtendProps());
        //产品属性
        productFacade.setProductAttrs(product.getProductAttrs());
        //操作类型：删除
        productFacade.setOperateType(OperateTypeEnum.DELETE);
        return productFacade;
    }

    /**
     * 快运取消询价修改转换
     * 与 toCancelModifyOrderFacadeRequest 差别主要是不考虑cancelStatus是否为空直接更新相关内容（取消询价不更新cancelStatus）
     */
    public ModifyOrderFacadeRequest toFreightCancelEnquiryRequest(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            //订单号
            request.setOrderNo(context.getOrderModel().getOrderSnapshot().orderNo());
            //租户
            request.setTenantId(context.getOrderModel().requestProfile() != null
                    ? context.getOrderModel().requestProfile().getTenantId() : null);

            // 不更新取消状态，但是需要更新取消原因等（ECLP识别询价单状态依赖 cancelReasonCode 与 enquiryStatus）
            Map<String, String> extendMap = new HashMap<>();
            Map<String, String> customerInfoExtendProps = JSONUtils.jsonToMap(context.getOrderModel().getOrderSnapshot()
                    .getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
            if (customerInfoExtendProps == null){
                customerInfoExtendProps = new HashMap<>();
            }
            customerInfoExtendProps.put("cancelReasonCode",context.getOrderModel().getCancelReasonCode());
            customerInfoExtendProps.put("cancelReason",context.getOrderModel().getCancelReason());
            customerInfoExtendProps.put("cancelRemark",context.getOrderModel().getRemark());
            extendMap.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS,JSONUtils.beanToJSONDefault(customerInfoExtendProps));
            extendMap.put("cancelReasonCode",context.getOrderModel().getCancelReasonCode());
            extendMap.put("cancelReason",context.getOrderModel().getCancelReason());
            extendMap.put("cancelRemark",context.getOrderModel().getRemark());

            // 询价状态 快运整车存在【取消订单+取消询价单】【仅取消询价单】场景，会更新询价状态
            if (context.getOrderModel().getFinance() != null && context.getOrderModel().getFinance().getEnquiryStatus() != null) {
                FinanceFacade financeFacade = new FinanceFacade();
                financeFacade.setEnquiryStatus(context.getOrderModel().getFinance().getEnquiryStatus().getCode());
                request.setFinance(financeFacade);
            }

            request.setExtendProps(extendMap);
            //修改时间
            request.setUpdateTime(new Date());
            //修改人
            request.setUpdateUser(context.getOrderModel().getOperator());
            request.setOperator(context.getOrderModel().getOperator());
        }
        return request;
    }

    /**
     * 快运整车直达重新受理-客户确认
     */
    public ModifyOrderFacadeRequest toFreightFTLReacceptCustomerConfirm(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单号
            request.setOrderNo(orderModel.orderNo());
            // 修改人
            request.setOperator(orderModel.getOperator());
            // oms修改时间
            request.setOperateTime(orderModel.getOperateTime());

            // 关联单-添加新询价单
            request.setRefOrders(toFreightFTLReacceptEnquiryRefOrders(orderModel));

            // 财务信息
            Finance finance = orderModel.getFinance();
            if (finance != null) {
                FinanceFacade financeFacade = new FinanceFacade();
                // 询价状态
                if (finance.getEnquiryStatus() != null) {
                    financeFacade.setEnquiryStatus(finance.getEnquiryStatus().getCode());
                }
                // 费用明细
                if (CollectionUtils.isNotEmpty(finance.getFinanceDetails())) {
                    // 有费用则赋值
                    financeFacade.setFinanceDetails(FinanceDetailFacadeMapper.INSTANCE.toFinanceDetailFacadeList(finance.getFinanceDetails()));
                } else {
                    // 无费用则清空
                    financeFacade.setFinanceDetails(null);
                    List<String> clearFields = request.getClearFields();
                    if (clearFields == null) {
                        clearFields = new ArrayList<>();
                        request.setClearFields(clearFields);
                    }
                    clearFields.add(ModifyItemConfigEnum.FINANCE_DETAILS.getCode());
                }
                // 折前金额
                financeFacade.setPreAmount(finance.getPreAmount());
                // 折后金额
                financeFacade.setDiscountAmount(finance.getDiscountAmount());
                // 计费重量
                financeFacade.setBillingWeight(finance.getBillingWeight());
                // 计费体积
                financeFacade.setBillingVolume(finance.getBillingVolume());
                request.setFinance(financeFacade);
            }

            // 订单标识
            if (MapUtils.isNotEmpty(orderModel.getOrderSign())) {
                request.setOrderSign(orderModel.getOrderSign());
            }

        }
        return request;
    }

    /**
     * 快运C2C整车直达对账完成修改
     * 修改财务信息-支付状态
     * 修改派送信息-提货时间
     */
    public ModifyOrderFacadeRequest toFreightFTLReconciliation(ExpressOrderContext context) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel orderModel = context.getOrderModel();
            // 业务身份信息
            request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
            // 订单号
            request.setOrderNo(orderModel.orderNo());
            // 修改人
            request.setOperator(orderModel.getOperator());
            // oms修改时间
            request.setOperateTime(orderModel.getOperateTime());
            // 财务信息
            if (orderModel.getFinance() != null) {
                FinanceFacade financeFacade = new FinanceFacade();
                // 支付状态
                if (orderModel.getFinance().getPaymentStatus() != null) {
                    financeFacade.setPaymentStatus(orderModel.getFinance().getPaymentStatus().getStatus());
                }
                request.setFinance(financeFacade);
            }
            // 派送信息
            if (orderModel.getShipment() != null) {
                ShipmentFacade shipmentFacade = new ShipmentFacade();
                // 期望提货开始时间
                if (orderModel.getShipment().getExpectPickupStartTime() != null) {
                    shipmentFacade.setExpectPickupStartTime(orderModel.getShipment().getExpectPickupStartTime());
                }
                // 期望提货结束时间
                if (orderModel.getShipment().getExpectPickupEndTime() != null) {
                    shipmentFacade.setExpectPickupEndTime(orderModel.getShipment().getExpectPickupEndTime());
                }
                request.setShipment(toShipmentFacade(orderModel.getShipment()));
            }
        }
        return request;
    }

    /**
     * 取消修改转换
     * @return
     */
    public ModifyOrderFacadeRequest toReaddress1Order2EndModifyOrderRequest(String tenantId,ExpressOrderModel order,List<ModifyRecordDto> modifyRecordDtoList,Integer modifyRecordListOperateType) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        //租户
        request.setTenantId(tenantId);
        //修改时间
        request.setUpdateTime(new Date());
        if (order != null) {
            //订单号
            request.setOrderNo(order.orderNo());
            //改址状态
            if(null != order.getReaddressStatus()){
                request.setReaddressStatus(order.getReaddressStatus().getCode());
            }
        }
        if(CollectionUtils.isNotEmpty(modifyRecordDtoList)){
            request.setModifyRecordListOperateType(modifyRecordListOperateType);
            request.setModifyRecordDtos(modifyRecordDtoList);
        }
        //打改址失败标
        String modifyMark = null;
        Map<String, String> extendProps = order.getExtendProps();
        if (extendProps == null) {
            extendProps = new HashMap<>();
        }
        if (MapUtils.isNotEmpty(extendProps)) {
            modifyMark = order.getExtendProps().get(AttachmentKeyEnum.MODIFY_MARK.getKey());
        }
        if (StringUtils.isBlank(modifyMark)) {
            modifyMark = ModifyMarkUtil.getInitMark();
        }
        modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition(), ReaddressMarkEnum.RESULT_FAIL.getCode());
        extendProps.put(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);
        request.setExtendProps(extendProps);
        //财务信息多方计费
        Finance finance = order.getFinance();
        if (finance != null) {
            FinanceFacade financeFacade = new FinanceFacade();
            if(CollectionUtils.isNotEmpty(finance.getMultiPartiesTotalAmounts())){
                financeFacade.setMultiPartiesTotalAmounts(finance.getMultiPartiesTotalAmounts());
            }
            request.setFinance(financeFacade);
        }
        return request;
    }


    /**
     * 改址一单到底需要支付的先款单同步只需要持久化变更记录和改址状态
     * @param context
     * @return
     * @throws ParseException
     */
    public ModifyOrderFacadeRequest toReaddress1Order2EndNeedPayModifyRequest(ExpressOrderContext context) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel model = context.getOrderModel();
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //业务身份信息
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(model.getOrderBusinessIdentity()));
        // 订单标识
        if (MapUtils.isNotEmpty(model.getOrderSign())) {
            facadeRequest.setOrderSign(model.getOrderSign());
        }
        //订单号
        facadeRequest.setOrderNo(model.orderNo());
        // 改址状态
        if (model.getReaddressStatus() != null) {
            facadeRequest.setReaddressStatus(model.getReaddressStatus().getCode());
        }
        //扩展信息
        setReaddressExtendProps(model, facadeRequest);
        //变更信息列表
        if(null != context.getModifyRecordDelegate() && CollectionUtils.isNotEmpty(context.getModifyRecordDelegate().getModifyRecords())){
            facadeRequest.setModifyRecordListOperateType(ModifyRecordListUpdateTypeEnum.ALL_COVER.getCode());
            facadeRequest.setModifyRecordDtos(toAllCoverRecordDtos(context.getModifyRecordDelegate().getModifyRecords()));
        }
        if (model.getFinance() != null) {
            FinanceFacade financeFacade = new FinanceFacade();
            if (CollectionUtils.isNotEmpty(model.getFinance().getMultiPartiesTotalAmounts())) {
                financeFacade.setMultiPartiesTotalAmounts(model.getFinance().getMultiPartiesTotalAmounts());
            }
            facadeRequest.setFinance(financeFacade);
        }

        return facadeRequest;
    }

    /**
     * 设置扩展信息
     * @param model
     * @param facadeRequest
     */
    private void setReaddressExtendProps(ExpressOrderModel model, ModifyOrderFacadeRequest facadeRequest) {
        //B2C需要判断是否是揽收后修改，如果是，则更新是否修改标识
        Map<String, String> map = new HashMap<>();
        String readdressOperateMode = model.getAttachment(OrderConstants.READDRESS_OPERATE_MODE);
        if(StringUtils.isNotEmpty(readdressOperateMode)) {
            map.put(OrderConstants.READDRESS_OPERATE_MODE, readdressOperateMode);
        }
        facadeRequest.setExtendProps(map);
    }

    /**
     * 功能：关联单绑定原单 request
     * @param context 1
     * @version 0.0.1
     */
    public ModifyOrderFacadeRequest toRelatedOrderFacadeRequest(ExpressOrderContext context) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel model = context.getOrderModel();
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //订单号, 原单号
        facadeRequest.setOrderNo(model.getRefOrderInfoDelegate().getOriginalNo());
        //oms修改时间
        facadeRequest.setOperateTime(model.getOperateTime());

        List<RefOrderFacade> refOrderFacades = new ArrayList<>(1);
        RefOrderFacade refOrderFacade = new RefOrderFacade();
        refOrderFacade.setRefOrderNo(model.orderNo());
        refOrderFacade.setRefOrderType(Integer.valueOf(model.getOrderType().getCode()));
        refOrderFacades.add(refOrderFacade);

        // 签单返单运单号：当前单是签单返单时补充运单号（原单保存签单返单的订单号、运单号）
        if (OrderTypeEnum.SIGN_RETURN_ORDER == model.getOrderType()) {
            RefOrderFacade waybillRefOrderFacade = new RefOrderFacade();
            waybillRefOrderFacade.setRefOrderNo(model.getCustomOrderNo());
            waybillRefOrderFacade.setRefOrderType(RefOrderTypeEnum.SIGN_RETURN_WAYBILL.getCode());
            refOrderFacades.add(waybillRefOrderFacade);
        }

        //关联单信息
        facadeRequest.setRefOrders(refOrderFacades);

        return facadeRequest;
    }

    /**
     * 转换增量更新修改记录
     *
     * @param modifyRecord
     * @return
     */
    public ModifyOrderFacadeRequest toIncrementalUpdateModifyRecordInfo(ModifyOrderFacadeRequest facadeRequest, ModifyRecord modifyRecord) {
        //LOGGER.info("从历史记录退款后更新,modifyRecord:{},",JSONUtils.beanToJSONDisableCircularReferenceDetect(modifyRecord));
        if (facadeRequest == null) {
            facadeRequest = new ModifyOrderFacadeRequest();
        }
        facadeRequest.setOrderNo(modifyRecord.getOrderNo());
        ModifyRecordDto modifyRecordDto = ModifyRecordMapper.INSTANCE.toModifyRecordDto(modifyRecord);
        //LOGGER.info("从历史记录退款后更新,modifyRecordDto1:{},",JSONUtils.beanToJSONDisableCircularReferenceDetect(modifyRecord));
        //更新记录明细
        modifyRecordDto.setModifyRecordMsg(JSONUtils.beanToJSONDisableCircularReferenceDetect(modifyRecord.getModifyRecordDetail()));
        modifyRecordDto.setModifyInfoUpdateType(ModifyRecordUpdateTypeEnum.UPDATE.getCode());
        //LOGGER.info("从历史记录退款后更新,modifyRecordDto2:{},",JSONUtils.beanToJSONDisableCircularReferenceDetect(modifyRecord));
        facadeRequest.setModifyRecordListOperateType(ModifyRecordListUpdateTypeEnum.INCREMENTAL_UPDATE.getCode());
        List<ModifyRecordDto> modifyRecordDtos = new ArrayList<>();
        modifyRecordDtos.add(modifyRecordDto);
        facadeRequest.setModifyRecordDtos(modifyRecordDtos);
        return facadeRequest;
    }

    /**
     * 转换增量新增修改记录
     *
     * @param modifyRecord
     * @return
     */
    public ModifyOrderFacadeRequest toIncrementalInsertModifyRecordInfo(ModifyOrderFacadeRequest facadeRequest, ModifyRecord modifyRecord) {
        if (facadeRequest == null) {
            facadeRequest = new ModifyOrderFacadeRequest();
        }
        facadeRequest.setOrderNo(modifyRecord.getOrderNo());
        ModifyRecordDto modifyRecordDto = ModifyRecordMapper.INSTANCE.toModifyRecordDto(modifyRecord);
        //更新记录明细
        modifyRecordDto.setModifyRecordMsg(JSONUtils.beanToJSONDisableCircularReferenceDetect(modifyRecord.getModifyRecordDetail()));
        modifyRecordDto.setModifyInfoUpdateType(ModifyRecordUpdateTypeEnum.INSERT.getCode());
        facadeRequest.setModifyRecordListOperateType(ModifyRecordListUpdateTypeEnum.INCREMENTAL_UPDATE.getCode());
        List<ModifyRecordDto> modifyRecordDtos = new ArrayList<>();
        modifyRecordDtos.add(modifyRecordDto);
        facadeRequest.setModifyRecordDtos(modifyRecordDtos);
        return facadeRequest;
    }

    /**
     * 获取并解析extendProps-customerInfoExtendProps
     */
    private Map<String, String> getCustomerInfoExtendProps(ExpressOrderModel orderModel) {
        if (orderModel == null
                || orderModel.getExtendProps() == null
                || !orderModel.getExtendProps().containsKey(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS)) {
            return null;
        }
        String customerInfoExtendPropsJson = orderModel.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS);
        if (StringUtils.isBlank(customerInfoExtendPropsJson)) {
            return null;
        }
        return JSONUtils.jsonToMap(customerInfoExtendPropsJson);
    }

    /**
     * 功能：转换回传状态请求类
     */
    public ModifyOrderFacadeRequest toReaddress1Order2EndPersistRequest(RefundRequest refundRequest, ExpressOrderModel order, ModifyRecord fromModifyRecord) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        // 订单主档信息更新
        modifyOrderFacadeRequest.setOrderNo(refundRequest.getOrderNo());
        modifyOrderFacadeRequest.setTenantId(refundRequest.getTenantId());
        modifyOrderFacadeRequest.setUpdateTime(new Date());

        FinanceFacade financeFacade = new FinanceFacade();
        // 退款状态。
        // 1 - 退款中；
        // 2 - 已退款；
        // 3 - 退款失败；
        financeFacade.setRefundStatus(order.getFinance().getRefundStatus().getStatus());
        // 已退金额
        //transactionCostInfo.setRefundedAmount(orderModel.getFinance().getRefundedAmount().getAmount());
        modifyOrderFacadeRequest.setFinance(financeFacade);
        modifyOrderFacadeRequest.setExtendProps(refundRequest.getExtendProps());
        //退款单
        if (refundRequest.getRefId() != null) {
            List<RefOrderFacade> refOrders = new ArrayList<>();
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderType(RefOrderTypeEnum.REFUND_ORDER.getCode());
            refOrderFacade.setRefOrderNo("" + refundRequest.getRefId());
            refOrders.add(refOrderFacade);
            modifyOrderFacadeRequest.setRefOrders(refOrders);
        }

        List<ModifyRecordDto> modifyInfoList = new ArrayList<>();
        modifyInfoList.add(toModifyInfo(fromModifyRecord));
        modifyOrderFacadeRequest.setModifyRecordDtos(modifyInfoList);
        Map<String, Integer> modifyInfoUpdateType = new HashMap<>();
        modifyInfoUpdateType.put(fromModifyRecord.getModifyRecordNo(),ModifyRecordUpdateTypeEnum.UPDATE.getCode());//增量更新--修改
        modifyOrderFacadeRequest.setModifyRecordListOperateType(ModifyRecordListUpdateTypeEnum.INCREMENTAL_UPDATE.getCode());
        modifyOrderFacadeRequest.setModifyInfoUpdateType(modifyInfoUpdateType);
        return modifyOrderFacadeRequest;
    }

    /**
     * 变更记录转换
     * @param modifyRecord
     * @return
     */
    public ModifyRecordDto toModifyInfo(ModifyRecord modifyRecord){
        ModifyRecordDto modifyInfo = new ModifyRecordDto();
        modifyInfo.setTenantId(modifyRecord.getTenantId());
        modifyInfo.setModifyRecordNo(modifyRecord.getModifyRecordNo());
        modifyInfo.setModifyRecordSequence(modifyRecord.getModifyRecordSequence());
        modifyInfo.setModifyRecordType(modifyRecord.getModifyRecordType());
        modifyInfo.setModifyRecordStatus(modifyRecord.getModifyRecordStatus());
        modifyInfo.setOperator(modifyRecord.getOperator());
        modifyInfo.setOperateTime(modifyRecord.getOperateTime());
        modifyInfo.setOrderNo(modifyRecord.getOrderNo());
        modifyInfo.setExtendProps(modifyRecord.getExtendProps());
        ReaddressRecordDetailInfo readdressRecordDetail = (ReaddressRecordDetailInfo)modifyRecord.getModifyRecordDetail();
        modifyInfo.setModifyRecordMsg(JSONUtils.beanToJSONDisableCircularReferenceDetect(readdressRecordDetail));
        modifyInfo.setModifyInfoUpdateType(ModifyRecordUpdateTypeEnum.UPDATE.getCode());
        return modifyInfo;
    }

    /**
     * 仅有财务信息以及必要信息
     * @param context
     * @return
     * @throws ParseException
     */
    public ModifyOrderFacadeRequest toOnlyChangeFinanceRequest(ExpressOrderContext context) {
        if (context == null) {
            return null;
        }
        return toOnlyChangeFinanceRequest(context.getOrderModel());
    }

    /**
     * 仅有财务信息以及必要信息
     * @param model
     * @return
     * @throws ParseException
     */
    public ModifyOrderFacadeRequest toOnlyChangeFinanceRequest(ExpressOrderModel model) {
        if (model == null) {
            return null;
        }
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //业务身份信息
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(model.getOrderBusinessIdentity()));
        //订单号
        facadeRequest.setOrderNo(model.orderNo());
        //财务信息
        facadeRequest.setFinance(toFinanceFacade(model));
        return facadeRequest;
    }


    /**
     * 待删除关联单转换
     * 送取同步需要注意 toRefOrderFacades 和 toDeleteRefOrders 方法，要解除绑定的单号不能在 toRefOrderFacades 出现
     */
    private List<RefOrderFacade> toDeleteRefOrders(ExpressOrderContext context) {
        List<RefOrderFacade> refOrderFacades = new ArrayList<>();
        ExpressOrderModel orderModel = context.getOrderModel();
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();

        // 删除集单号转换
        String collectionOrderNo = null;
        String sourceCollectionOrderNo = null;
        if (orderModel.getRefOrderInfoDelegate() != null) {
            collectionOrderNo = orderModel.getRefOrderInfoDelegate().getCollectionOrderNo();
        }
        if (snapshot != null && snapshot.getRefOrderInfoDelegate() != null) {
            sourceCollectionOrderNo = snapshot.getRefOrderInfoDelegate().getCollectionOrderNo();
        }
        if (collectionOrderNo != null && StringUtils.isBlank(collectionOrderNo) && StringUtils.isNotBlank(sourceCollectionOrderNo)) {
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderNo(sourceCollectionOrderNo);
            refOrderFacade.setRefOrderType(RefOrderTypeEnum.COLLECTION_ORDER.getCode());
            refOrderFacade.setOperateType(OperateTypeEnum.DELETE);
            refOrderFacades.add(refOrderFacade);
        }

        // 送取同步，解除绑定场景
        Integer bindOperateType = (Integer) context.getExtInfo(ContextInfoEnum.DELIVERY_PICKUP_SYNC_BIND_OPERATE_TYPE.getCode());
        DeliveryPickupSyncBindOperateTypeEnum deliveryPickupSyncOperateType = DeliveryPickupSyncBindOperateTypeEnum.fromCode(bindOperateType);
        if (deliveryPickupSyncOperateType != null) {
            RefOrderDelegate refOrderInfoDelegate = context.getOrderModel().getRefOrderInfoDelegate();
            if (DeliveryPickupSyncBindOperateTypeEnum.DELIVERY_UNBIND_PICKUP == deliveryPickupSyncOperateType) {
                if (refOrderInfoDelegate != null) {
                    // 送取同步-取件单-订单号
                    RefOrderFacade pickupOrder = new RefOrderFacade();
                    pickupOrder.setRefOrderNo(refOrderInfoDelegate.getPickupOrderNo());
                    pickupOrder.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode());
                    pickupOrder.setOperateType(OperateTypeEnum.DELETE);
                    refOrderFacades.add(pickupOrder);

                    // 送取同步-取件单-运单号
                    RefOrderFacade pickupWaybill = new RefOrderFacade();
                    pickupWaybill.setRefOrderNo(refOrderInfoDelegate.getPickupWaybillNo());
                    pickupWaybill.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode());
                    pickupWaybill.setOperateType(OperateTypeEnum.DELETE);
                    refOrderFacades.add(pickupWaybill);
                }
            } else if (DeliveryPickupSyncBindOperateTypeEnum.PICKUP_UNBIND_DELIVERY == deliveryPickupSyncOperateType) {
                if (refOrderInfoDelegate != null) {
                    // 送取同步-派送单-订单号
                    RefOrderFacade deliveryOrder = new RefOrderFacade();
                    deliveryOrder.setRefOrderNo(refOrderInfoDelegate.getDeliveryOrderNo());
                    deliveryOrder.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY_ORDER.getCode());
                    deliveryOrder.setOperateType(OperateTypeEnum.DELETE);
                    refOrderFacades.add(deliveryOrder);

                    // 送取同步-派送单-运单号
                    RefOrderFacade deliveryWaybill = new RefOrderFacade();
                    deliveryWaybill.setRefOrderNo(refOrderInfoDelegate.getDeliveryWaybillNo());
                    deliveryWaybill.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY.getCode());
                    deliveryWaybill.setOperateType(OperateTypeEnum.DELETE);
                    refOrderFacades.add(deliveryWaybill);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(refOrderFacades)) {
            return refOrderFacades;
        }
        return null;
    }

    /**
     * 清空关联支付单请求入参
     *
     * @param orderModel
     * @param operator
     * @return
     */
    public ModifyOrderFacadeRequest toClearPayOrderNoFacadeRequest(ExpressOrderModel orderModel, String operator) {

        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
        facadeRequest.setOrderNo(orderModel.orderNo());
        facadeRequest.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        facadeRequest.setOperateTime(new Date());

        // 清空关联支付单
        // ext里的管理单删除，数据层目前是通过设置为空字符串""实现
        List<RefOrderFacade> refOrders = new ArrayList<>();
        RefOrderFacade refOrderFacade = new RefOrderFacade();
        refOrderFacade.setOperateType(OperateTypeEnum.DELETE);
        refOrderFacade.setRefOrderType(RefOrderTypeEnum.PAY_ORDER.getCode());
        refOrderFacade.setRefOrderNo("");
        refOrders.add(refOrderFacade);
        facadeRequest.setRefOrders(refOrders);

        return facadeRequest;

    }

    /**
     * 询价营销信息
     * @param orderModel
     * @return
     */
    public PromotionFacade toEnquiryPromotionFacade(ExpressOrderModel orderModel) {

        //比对折扣信息
        compareDiscounts(orderModel);

        Promotion promotion = orderModel.getPromotion();

        if (promotion == null || isNullObject(promotion, Promotion.class)) {
            return null;
        }
        PromotionFacade promotionFacade = new PromotionFacade();
        //营销折扣信息
        if (CollectionUtils.isNotEmpty(promotion.getOperationDiscountInfos())) {
            List<DiscountFacade> discountFacades = new ArrayList<>(promotion.getOperationDiscountInfos().size());
            for (Discount discount : promotion.getOperationDiscountInfos()) {
                if (discount.getOperateType() == null) {
                    continue;
                }
                DiscountFacade discountFacade = new DiscountFacade();
                //折扣码
                discountFacade.setDiscountNo(discount.getDiscountNo());
                discountFacade.setDiscountType(discount.getDiscountType());
                discountFacade.setOperateType(discount.getOperateType());
                discountFacade.setExtendProps(discount.getExtendProps());
                discountFacades.add(discountFacade);
            }
            if (discountFacades.size() > 0) {
                promotionFacade.setOperationDiscounts(discountFacades);
            }
        }
        if (isNullObject(promotionFacade, PromotionFacade.class)) {
            return null;
        }
        return promotionFacade;
    }


    /**
     * 比对折扣信息
     */
    public void compareDiscounts(ExpressOrderModel orderModel) {
        String modifiedFieldsValue = null;
        if (MapUtils.isNotEmpty(orderModel.getModifiedFields())) {
            modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.OPERATION_DISCOUNT_INFOS.getCode());
        }
        if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getOperationDiscountInfos())) {
                //全量覆盖，比对list
                List<Discount> targetList = orderModel.getPromotion().getOperationDiscountInfos();
                List<Discount> originList = orderModel.getOrderSnapshot().getPromotion().getOperationDiscountInfos();

                for (Discount discount : targetList) {
                    //判断是否在原集合中存在
                    if (orderModel.getOrderSnapshot().getPromotion().ofOperationDiscount(discount.getDiscountNo()) == null) {
                        //新增
                        discount.setOperateType(OperateTypeEnum.INSERT);
                    } else {
                        //修改
                        discount.setOperateType(OperateTypeEnum.UPDATE);
                    }
                }
                if (CollectionUtils.isNotEmpty(originList)) {
                    for (Discount discount : originList) {
                        //判断是否在新集合中存在
                        if (orderModel.getPromotion().ofOperationDiscount(discount.getDiscountNo()) == null) {
                            //删除
                            discount.setOperateType(OperateTypeEnum.DELETE);
                            targetList.add(discount);
                        }
                    }
                }
            }
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            if (CollectionUtils.isNotEmpty(orderModel.getOrderSnapshot().getPromotion().getOperationDiscountInfos())) {
                //全量删除
                for (Discount discount : orderModel.getOrderSnapshot().getPromotion().getOperationDiscountInfos()) {
                    discount.setOperateType(OperateTypeEnum.DELETE);
                }
                //将需要删除的优惠券信息赋值到model里，供后续节点使用
                orderModel.getPromotion().setOperationDiscountInfos(orderModel.getOrderSnapshot().getPromotion().getOperationDiscountInfos());
            }
        }
    }

    /**
     * MTD费用预估结果（预占金额）持久化请求
     *
     * @param requestProfile 请求头信息
     * @return ModifyOrderFacadeRequest
     */
    public ModifyOrderFacadeRequest toMtdEstimateModifyFacadeRequest(RequestProfile requestProfile, ExpressOrderModel orderModel) {
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = new ModifyOrderFacadeRequest();
        // 租户
        modifyOrderFacadeRequest.setTenantId(requestProfile.getTenantId());
        // 订单号
        modifyOrderFacadeRequest.setOrderNo(orderModel.orderNo());

        modifyOrderFacadeRequest.setFinance(toEstimateFinanceFacade(orderModel.getFinance()));
        return modifyOrderFacadeRequest;
    }

    /**
     * 财务信息预估费用转换
     *
     * @param finance
     * @return
     */
    public FinanceFacade toEstimateFinanceFacade(Finance finance) {
        if (finance == null) {
            return null;
        }
        FinanceFacade financeFacade = new FinanceFacade();

        // 预估费用
        financeFacade.setEstimateAmount(finance.getEstimateAmount());

        //预估财务信息
        Optional.ofNullable(finance.getEstimateFinanceInfo()).ifPresent(estimateFinanceInfo -> {
            FinanceFacade estimateFinanceFacade = new FinanceFacade();
            // 预估-折前金额
            Optional.ofNullable(estimateFinanceInfo.getPreAmount()).ifPresent(estimateFinanceFacade::setPreAmount);
            // 预估-折后金额
            Optional.ofNullable(estimateFinanceInfo.getDiscountAmount()).ifPresent(estimateFinanceFacade::setDiscountAmount);
            // 预估-计费重量
            Optional.ofNullable(estimateFinanceInfo.getBillingWeight()).ifPresent(estimateFinanceFacade::setBillingWeight);
            // 预估-计费体积
            Optional.ofNullable(estimateFinanceInfo.getBillingVolume()).ifPresent(estimateFinanceFacade::setBillingVolume);
            // 预估-加价后总金额
            Optional.ofNullable(estimateFinanceInfo.getTotalAdditionAmount()).ifPresent(estimateFinanceFacade::setTotalAdditionAmount);
            // 预估-费用明细
            Optional.ofNullable(estimateFinanceInfo.getFinanceDetails()).ifPresent(estimateFinanceDetails -> {
                // 预估-费用明细信息
                List<FinanceDetailFacade> financeDetailFacades = Lists.newArrayListWithCapacity(estimateFinanceDetails.size());
                estimateFinanceDetails.forEach(estimateFinanceDetail -> {
                    // 预估-费用明细对象
                    FinanceDetailFacade estimateFinanceDetailFacade = new FinanceDetailFacade();
                    // 预估-费用编号
                    estimateFinanceDetailFacade.setCostNo(estimateFinanceDetail.getCostNo());
                    // 预估-费用名称
                    estimateFinanceDetailFacade.setCostName(estimateFinanceDetail.getCostName());
                    // 预估-费用产品编码
                    estimateFinanceDetailFacade.setProductNo(estimateFinanceDetail.getProductNo());
                    // 预估-费用产品名称
                    estimateFinanceDetailFacade.setProductName(estimateFinanceDetail.getProductName());
                    // 预估-折扣信息
                    if (CollectionUtils.isNotEmpty(estimateFinanceDetail.getDiscounts())) {
                        List<DiscountFacade> estimateDiscountFacades = new ArrayList<>(estimateFinanceDetail.getDiscounts().size());
                        estimateFinanceDetail.getDiscounts().forEach(estimateDiscount -> {
                            // 预估-折扣信息对象
                            DiscountFacade estimateDiscountFacade = new DiscountFacade();
                            // 预估-折扣码
                            estimateDiscountFacade.setDiscountNo(estimateDiscount.getDiscountNo());
                            // 预估-折扣类型
                            estimateDiscountFacade.setDiscountType(estimateDiscount.getDiscountType());
                            // 预估-折扣金额
                            estimateDiscountFacade.setDiscountedAmount(estimateDiscount.getDiscountedAmount());
                            estimateDiscountFacades.add(estimateDiscountFacade);
                        });
                        estimateFinanceDetailFacade.setDiscountFacades(estimateDiscountFacades);
                    }
                    // 预估-折前金额
                    estimateFinanceDetailFacade.setPreAmount(estimateFinanceDetail.getPreAmount());
                    // 预估-折后金额
                    estimateFinanceDetailFacade.setDiscountAmount(estimateFinanceDetail.getDiscountAmount());
                    // 预估-加价后金额
                    estimateFinanceDetailFacade.setAdditionAmount(estimateFinanceDetail.getAdditionAmount());
                    // 预估-扩展字段
                    estimateFinanceDetailFacade.setExtendProps(estimateFinanceDetail.getExtendProps());

                    financeDetailFacades.add(estimateFinanceDetailFacade);
                });

                estimateFinanceFacade.setFinanceDetails(financeDetailFacades);
            });

            financeFacade.setEstimateFinanceInfo(estimateFinanceFacade);
        });
        return financeFacade;
    }


    /**
     * 仓配接配出库包裹信息更新
     */
    public ModifyOrderFacadeRequest toOutboundPackageUpdate(ExpressOrderContext context) {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel model = context.getOrderModel();
        ModifyOrderFacadeRequest facadeRequest = new ModifyOrderFacadeRequest();
        //业务身份信息
        facadeRequest.setBusinessIdentity(toBusinessIdentityFacade(model.getOrderBusinessIdentity()));
        //订单号
        facadeRequest.setOrderNo(model.orderNo());
        //下单人
        facadeRequest.setOperator(model.getOperator());
        //oms修改时间
        facadeRequest.setOperateTime(model.getOperateTime());
        //扩展信息
        if (model.getExtendProps() != null && model.getExtendProps().size() > 0) {
            facadeRequest.setExtendProps(model.getExtendProps());
        }
        //渠道信息
        facadeRequest.setChannel(toChannelFacade(model.getChannel()));
        //货品信息
        if (model.getCargoDelegate() != null) {
            List<Cargo> cargoList = (List<Cargo>) model.getCargoDelegate().getCargoList();
            facadeRequest.setCargos(this.toCargoFacades(cargoList));
        }
        //订单总毛重
        facadeRequest.setOrderWeight(model.getOrderWeight());
        //订单总体积
        facadeRequest.setOrderVolume(model.getOrderVolume());
        //修改场景-集合字段操作模式
        if (model.getModifiedFields() != null && model.getModifiedFields().size() > 0) {
            facadeRequest.setModifiedFields(model.getModifiedFields());
        }
        //履约信息
        facadeRequest.setFulfillment(this.toFulfillmentFacade(model.getFulfillment()));
        return facadeRequest;
    }
    /**
     * 已执行取件单绑定派送单(上游调订单接单接口传取件单)，异步处理派送单
     * @param deliveryOrder 派送单，本次处理单据
     */
    public ModifyOrderFacadeRequest toAsyncPickupBindDelivery(ExpressOrderModel deliveryOrder, String refOrderNo, String refWaybillNo) {
        ModifyOrderFacadeRequest request = toCommonAsyncDeliveryPickupSync(deliveryOrder);

        // 关联单
        List<RefOrderFacade> refOrderFacades = new ArrayList<>(2);
        // 送取同步-取件单-订单号
        RefOrderFacade refOrderPickupOrder = new RefOrderFacade();
        refOrderPickupOrder.setRefOrderNo(refOrderNo);
        refOrderPickupOrder.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode());
        refOrderPickupOrder.setOperateType(OperateTypeEnum.INSERT);
        refOrderFacades.add(refOrderPickupOrder);
        // 送取同步-取件单-运单号
        RefOrderFacade refOrderPickupWaybill = new RefOrderFacade();
        refOrderPickupWaybill.setRefOrderNo(refWaybillNo);
        refOrderPickupWaybill.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode());
        refOrderPickupOrder.setOperateType(OperateTypeEnum.INSERT);
        refOrderFacades.add(refOrderPickupWaybill);
        request.setRefOrders(refOrderFacades);

        // 删除关联单（处理覆盖绑定）
        List<RefOrderFacade> deleteRefOrders = new ArrayList<>(2);
        if (deliveryOrder.getRefOrderInfoDelegate() != null && !deliveryOrder.getRefOrderInfoDelegate().isEmpty()) {
            // 旧送取同步-取件单-订单号（前面流程不会补全新单号到关联单信息，此处还是已绑定的数据）
            String oldPickupOrderNo = deliveryOrder.getRefOrderInfoDelegate().getPickupOrderNo();
            if (StringUtils.isNotBlank(oldPickupOrderNo)) {
                RefOrderFacade oldRefOrderPickupOrder = new RefOrderFacade();
                oldRefOrderPickupOrder.setRefOrderNo(oldPickupOrderNo);
                oldRefOrderPickupOrder.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode());
                oldRefOrderPickupOrder.setOperateType(OperateTypeEnum.DELETE);
                deleteRefOrders.add(oldRefOrderPickupOrder);
            }

            // 旧送取同步-取件单-订单号（前面流程不会补全新单号到关联单信息，此处还是已绑定的数据）
            String oldPickupWaybillNo = deliveryOrder.getRefOrderInfoDelegate().getPickupWaybillNo();
            if (StringUtils.isNotBlank(oldPickupWaybillNo)) {
                RefOrderFacade oldRefOrderPickupWaybill = new RefOrderFacade();
                oldRefOrderPickupWaybill.setRefOrderNo(oldPickupWaybillNo);
                oldRefOrderPickupWaybill.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode());
                oldRefOrderPickupWaybill.setOperateType(OperateTypeEnum.DELETE);
                deleteRefOrders.add(oldRefOrderPickupWaybill);
            }
        }
        if (CollectionUtils.isNotEmpty(deleteRefOrders)) {
            request.setDeleteRefOrders(deleteRefOrders);
        }

        return request;
    }

    /**
     * 已执行取件单解除绑定派送单(上游调订单修改接口传取件单)，异步处理派送单
     * @param deliveryOrder 派送单，本次处理单据
     */
    public ModifyOrderFacadeRequest toAsyncPickupUnbindDelivery(ExpressOrderModel deliveryOrder, String refOrderNo, String refWaybillNo) {
        ModifyOrderFacadeRequest request = toCommonAsyncDeliveryPickupSync(deliveryOrder);

        // 删除关联单
        List<RefOrderFacade> deleteRefOrders = new ArrayList<>(2);
        // 送取同步-取件单-订单号
        RefOrderFacade refOrderPickupOrder = new RefOrderFacade();
        refOrderPickupOrder.setRefOrderNo(refOrderNo);
        refOrderPickupOrder.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode());
        refOrderPickupOrder.setOperateType(OperateTypeEnum.DELETE);
        deleteRefOrders.add(refOrderPickupOrder);
        // 送取同步-取件单-运单号
        RefOrderFacade refOrderPickupWaybill = new RefOrderFacade();
        refOrderPickupWaybill.setRefOrderNo(refWaybillNo);
        refOrderPickupWaybill.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode());
        refOrderPickupWaybill.setOperateType(OperateTypeEnum.DELETE);

        deleteRefOrders.add(refOrderPickupWaybill);
        request.setDeleteRefOrders(deleteRefOrders);

        return request;
    }

    /**
     * 已执行派送单解除绑定取件单(上游调订单修改接口传派送单)，异步处理取件单
     * @param pickupOrder 取件单，本次处理单据
     */
    public ModifyOrderFacadeRequest toAsyncDeliveryUnbindPickup(ExpressOrderModel pickupOrder, String refOrderNo, String refWaybillNo) {
        ModifyOrderFacadeRequest request = toCommonAsyncDeliveryPickupSync(pickupOrder);

        // 删除关联单
        List<RefOrderFacade> deleteRefOrders = new ArrayList<>(2);
        // 送取同步-派送单-订单号
        RefOrderFacade refOrderDeliveryOrder = new RefOrderFacade();
        refOrderDeliveryOrder.setRefOrderNo(refOrderNo);
        refOrderDeliveryOrder.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY_ORDER.getCode());
        refOrderDeliveryOrder.setOperateType(OperateTypeEnum.DELETE);
        deleteRefOrders.add(refOrderDeliveryOrder);
        // 送取同步-派送单-运单号
        RefOrderFacade refOrderDeliveryWaybill = new RefOrderFacade();
        refOrderDeliveryWaybill.setRefOrderNo(refWaybillNo);
        refOrderDeliveryWaybill.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY.getCode());
        refOrderDeliveryWaybill.setOperateType(OperateTypeEnum.DELETE);

        deleteRefOrders.add(refOrderDeliveryWaybill);
        request.setDeleteRefOrders(deleteRefOrders);

        return request;
    }

    /**
     * 送取同步绑定或解除绑定关联单，异步处理公共请求
     * @param orderModel 取件单或者派送单
     */
    private ModifyOrderFacadeRequest toCommonAsyncDeliveryPickupSync(ExpressOrderModel orderModel) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        // 业务身份信息
        request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
        // 订单号
        request.setOrderNo(orderModel.orderNo());
        // 修改人：订单中心
        request.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        // 操作时间：当前时间
        request.setOperateTime(new Date());

        // 订单标识：orderSign.deliveryPickupSync
        request.setOrderSign(orderModel.getOrderSign());

        // 派送信息：Shipment.extendProps.shipmentExtendProps.sendAndPickupType
        ShipmentFacade shipmentFacade = new ShipmentFacade();
        shipmentFacade.setExtendProps(orderModel.getShipment().getExtendProps());
        request.setShipment(shipmentFacade);

        // 关联单：不在公共方法，在各自单独方法

        return request;
    }

    /**
     * 平台应收明细同步
     * 主要更新财务信息-扩展字段
     */
    public ModifyOrderFacadeRequest toEcapFeeDetailsNoticeModify(ExpressOrderModel orderModel, Map<String, String> extendProps) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        // 业务身份信息
        request.setBusinessIdentity(toBusinessIdentityFacade(orderModel.getOrderBusinessIdentity()));
        // 订单号
        request.setOrderNo(orderModel.orderNo());
        // 修改人：订单中心
        request.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        // 操作时间：当前时间
        request.setOperateTime(new Date());
        // 财务信息（只更新扩展字段）
        FinanceFacade financeFacade = new FinanceFacade();
        financeFacade.setExtendProps(extendProps);
        request.setFinance(financeFacade);
        return request;
    }
}