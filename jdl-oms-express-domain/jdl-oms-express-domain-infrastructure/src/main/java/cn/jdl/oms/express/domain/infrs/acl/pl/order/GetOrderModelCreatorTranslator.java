package cn.jdl.oms.express.domain.infrs.acl.pl.order;

import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.FenceInfo;
import cn.jdl.oms.core.model.AttachmentInfo;
import cn.jdl.oms.core.model.QuantityInfo;
import cn.jdl.oms.core.model.WarehouseInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.converter.CostMapper;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.converter.SerialMapper;
import cn.jdl.oms.express.domain.converter.VolumeMapper;
import cn.jdl.oms.express.domain.converter.WeightMapper;
import cn.jdl.oms.express.domain.dto.ActivityInfoDto;
import cn.jdl.oms.express.domain.dto.AdditionPriceInfoDto;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.dto.AgreementInfoDto;
import cn.jdl.oms.express.domain.dto.AttachmentInfoDto;
import cn.jdl.oms.express.domain.dto.BusinessIdentityDto;
import cn.jdl.oms.express.domain.dto.BusinessSolutionInfoDto;
import cn.jdl.oms.express.domain.dto.CargoInfoDto;
import cn.jdl.oms.express.domain.dto.ChannelInfoDto;
import cn.jdl.oms.express.domain.dto.ConsigneeInfoDto;
import cn.jdl.oms.express.domain.dto.ConsignorInfoDto;
import cn.jdl.oms.express.domain.dto.CostInfoDto;
import cn.jdl.oms.express.domain.dto.CustomerInfoDto;
import cn.jdl.oms.express.domain.dto.CustomsInfoDto;
import cn.jdl.oms.express.domain.dto.DimensionInfoDto;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.EnquiryInfoDto;
import cn.jdl.oms.express.domain.dto.FenceInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.FulfillmentInfoDto;
import cn.jdl.oms.express.domain.dto.GoodsInfoDto;
import cn.jdl.oms.express.domain.dto.InterceptInfoDto;
import cn.jdl.oms.express.domain.dto.LengthInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.PointsInfoDto;
import cn.jdl.oms.express.domain.dto.ProductInfoDto;
import cn.jdl.oms.express.domain.dto.PromotionInfoDto;
import cn.jdl.oms.express.domain.dto.QuantityInfoDto;
import cn.jdl.oms.express.domain.dto.RefOrderInfoDto;
import cn.jdl.oms.express.domain.dto.ReturnInfoDto;
import cn.jdl.oms.express.domain.dto.ShipmentInfoDto;
import cn.jdl.oms.express.domain.dto.TicketInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WarehouseInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.AttachmentFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.CostFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.CustomsFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AgreementFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AttachmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessIdentityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessSolutionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CargoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ChannelFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsigneeFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsignorFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceDetailFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FulfillmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.GoodsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.InterceptFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PointsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PromotionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.RefOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ReturnInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ShipmentFacade;
import cn.jdl.oms.express.domain.spec.dict.ContactlessTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.CoordinateTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.IdentityTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSubTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderSubType;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefundStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.dict.TransportTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.WarmLayerEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightTypeEnum;
import cn.jdl.oms.express.domain.vo.Dimension;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.OrderStatus;
import cn.jdl.oms.express.domain.vo.Quantity;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.B2CExtendStatusEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ProjectName：jdl-oms-express-c2c-infrastructure
 * @Package： cn.jdl.oms.express.domain.infrs.acl.pl.order
 * @ClassName: GetOrderModelCreatorTranslator
 * @Description: 订单模型的契约对象转换
 * @Author： liyong549
 * @CreateDate 2021/3/23 21:25
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Translator
public class GetOrderModelCreatorTranslator {

    public ExpressOrderModelCreator toExpressOrderModelCreator(GetOrderFacadeResponse facadeResponse) {
        if (facadeResponse == null) {
            return null;
        }
        ExpressOrderModelCreator creator = new ExpressOrderModelCreator();
        //业务身份
        creator.setBusinessIdentity(this.toOrderBusinessIdentity(facadeResponse.getBusinessIdentity()));
        //渠道信息
        creator.setChannelInfo(this.toChannelInfo(facadeResponse.getChannel()));
        //交易客户信息
        creator.setCustomerInfo(this.toCustomerInfoDto(facadeResponse.getCustomer()));
        //产品信息
        creator.setProducts(this.toProductFacadeList(facadeResponse.getProducts()));
        creator.setOrderId(facadeResponse.getOrderId());
        creator.setOrderNo(facadeResponse.getOrderNo());
        creator.setCustomOrderNo(facadeResponse.getCustomOrderNo());
        creator.setOrderType(OrderTypeEnum.of(facadeResponse.getOrderType()));
        creator.setCustomOrderNo(facadeResponse.getCustomOrderNo());
        creator.setOrderSubType(OrderSubTypeEnum.of(facadeResponse.getOrderSubType()));
        //交易关联单
        creator.setRefOrder(this.toRefOrderInfoDto(facadeResponse));
        //货品信息
        creator.setCargoInfos(this.toCargoInfoDtoList(facadeResponse.getCargos()));
        //商品信息
        creator.setGoodsInfos(this.toGoodsInfoDtoList(facadeResponse.getGoodsList()));
        //发货人信息
        creator.setConsignorInfo(this.toConsignorInfoDto(facadeResponse.getConsignor()));
        //收货人信息
        creator.setConsigneeInfo(this.toConsigneeInfoDto(facadeResponse.getConsignee()));
        //配送信息
        creator.setShipmentInfo(this.toShipmentInfoDto(facadeResponse.getShipment()));
        //财务信息
        creator.setFinanceInfo(this.toFinanceInfoDto(facadeResponse.getFinance()));
        //营销信息
        creator.setPromotionInfo(this.toPromotionInfoDto(facadeResponse.getPromotion()));
        creator.setInitiatorType(InitiatorTypeEnum.of(facadeResponse.getInitiatorType()));
        creator.setOrderStatusCustom(facadeResponse.getOrderStatusCustom());
        creator.setOperator(facadeResponse.getOperator());
        creator.setOperateTime(facadeResponse.getOperateTime());
        creator.setRemark(facadeResponse.getRemark());
        creator.setHiddenMark(facadeResponse.getHiddenMark());
        creator.setExtendProps(facadeResponse.getExtendProps());
        Map<String,String> map = facadeResponse.getExtendProps();
        if (map == null){
            map = new HashMap<>();
        }
        B2CExtendStatusEnum b2CExtendStatusEnum = B2CExtendStatusEnum.ofExtendStatus(facadeResponse.getExtendStatus());
        if (b2CExtendStatusEnum != null){
            map.put(OrderConstants.SELLER_EXTEND_STATUS, b2CExtendStatusEnum.getExtendStatus());
        }
        creator.setExtendProps(map);

        creator.setOrderStatus(OrderStatus.orderStatusOf(OrderStatusEnum.of(facadeResponse.getOrderStatus())));
        creator.setCancelStatus(facadeResponse.getCancelStatus());
        creator.setExecutedStatus(facadeResponse.getExtendStatus());
        //解决方案
        creator.setBusinessSolutionInfoDto(toBusinessSolutionInfoDto(facadeResponse.getBusinessSolutionFacade()));
        creator.setInterceptType(facadeResponse.getInterceptType());
        //询价信息
        creator.setEnquiryInfo(toEnquiryInfoDto(facadeResponse));
        // 协议信息
        creator.setAgreementInfoDtos(this.toAgreementInfoDtos(facadeResponse.getAgreementFacades()));
        // 订单标识
        creator.setOrderSign(facadeResponse.getOrderSign());
        //退货信息
        creator.setReturnInfoDto(this.toReturnInfoDto(facadeResponse.getReturnInfoFacade()));
        // 改址状态
        creator.setReaddressStatus(facadeResponse.getReaddressStatus());
        // 拦截信息
        creator.setInterceptInfoDto(this.toInterceptInfoDto(facadeResponse.getInterceptFacade()));
        // 履约信息
        creator.setFulfillmentInfo(this.toFulfillmentInfoDto(facadeResponse.getFulfillmentFacade()));
        // 跨境过关信息
        creator.setCustomsInfoDto(toCustomsInfoDto(facadeResponse.getCustomsFacade()));
        // 附件信息
        creator.setAttachmentInfoDtos(toAttachmentInfoDtos(facadeResponse.getAttachmentFacades()));
        // 订单总净重
        creator.setOrderNetWeight(WeightMapper.INSTANCE.toWeightInfoDto(facadeResponse.getOrderNetWeight()));
        // 订单总毛重
        creator.setOrderWeight(WeightMapper.INSTANCE.toWeightInfoDto(facadeResponse.getOrderWeight()));
        // 订单总体积
        creator.setOrderVolume(VolumeMapper.INSTANCE.toVolumeInfoDto(facadeResponse.getOrderVolume()));
        // 复重体积
        creator.setRecheckVolumeDto(VolumeMapper.INSTANCE.toVolumeInfoDto(facadeResponse.getRecheckVolume()));
        // 复重重量
        creator.setRecheckWeightDto(WeightMapper.INSTANCE.toWeightInfoDto(facadeResponse.getRecheckWeight()));
        // 是否百川
        creator.setSyncSource(facadeResponse.getSyncSource());
        // 父单号
        creator.setParentOrderNo(facadeResponse.getParentOrderNo());
        // 操作记录列表
        creator.setModifyRecordDtos(facadeResponse.getModifyRecordDtos());
        // 弃货状态
        creator.setDiscardStatus(facadeResponse.getDiscardStatus());
        return creator;
    }

    /**
     * 退货信息
     * @param returnInfoFacade
     * @return
     */
    private ReturnInfoDto toReturnInfoDto(ReturnInfoFacade returnInfoFacade){
        if (returnInfoFacade == null){
            return null;
        }
        ReturnInfoDto returnInfoDto = new ReturnInfoDto();
        returnInfoDto.setReturnType(returnInfoFacade.getReturnType());
        returnInfoDto.setReturnConsigneeInfo(this.toConsigneeInfoDto(returnInfoFacade.getConsigneeFacade()));
        return returnInfoDto;
    }

    /**
     * 询价信息转换
     * @param facadeResponse
     * @return
     */
    private EnquiryInfoDto toEnquiryInfoDto(GetOrderFacadeResponse facadeResponse) {
        EnquiryInfoDto enquiryInfoDto = new EnquiryInfoDto();
        enquiryInfoDto.setEnquiryStartCityNo(facadeResponse.getFinance().getEnquiryStartCityNo());
        enquiryInfoDto.setPeakPeriodTime(facadeResponse.getFinance().getPeakPeriodTime());
        enquiryInfoDto.setEnquiryVolume(toVolumeInfoDto(facadeResponse.getRecheckVolume()));
        enquiryInfoDto.setEnquiryWeight(toWeightInfoDto(facadeResponse.getRecheckWeight()));
        return enquiryInfoDto;
    }

    /**
     * 商品信息
     *
     * @param list
     * @return
     */
    private List<GoodsInfoDto> toGoodsInfoDtoList(List<GoodsFacade> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<GoodsInfoDto> goodsInfoDtoList = new ArrayList<>(list.size());
        list.forEach(goodsFacade -> {
            GoodsInfoDto goodsInfoDto = new GoodsInfoDto();
            goodsInfoDto.setGoodsNo(goodsFacade.getGoodsNo());
            goodsInfoDto.setGoodsUniqueCode(goodsFacade.getGoodsUniqueCode());
            goodsInfoDto.setGoodsName(goodsFacade.getGoodsName());
            goodsInfoDto.setGoodsUniqueCode(goodsFacade.getGoodsUniqueCode());
            if (goodsFacade.getGoodsQuantity() != null) {
                QuantityInfo goodsQuantity = new QuantityInfo();
                goodsQuantity.setValue(goodsFacade.getGoodsQuantity().getValue());
                goodsQuantity.setUnit(goodsFacade.getGoodsQuantity().getUnit());
                goodsInfoDto.setGoodsQuantity(goodsQuantity);
            }

            //货品重量
            goodsInfoDto.setGoodsWeight(goodsFacade.getGoodsWeight());
            //货品体积
            goodsInfoDto.setGoodsVolume(goodsFacade.getGoodsVolume());
            //货品三维
            goodsInfoDto.setGoodsDimension(goodsFacade.getGoodsDimension());

            //促销信息
            goodsInfoDto.setSalesInfos(goodsFacade.getSalesInfos());

            // 序列号
            goodsInfoDto.setGoodsSerialInfos(
                    SerialMapper.INSTANCE.toSerialInfoList(goodsFacade.getGoodsSerialInfos())
            );

            if(CollectionUtils.isNotEmpty(goodsFacade.getGoodsProductInfos())){
                List<ProductInfoDto> productInfoDtos = goodsFacade.getGoodsProductInfos().stream().map(goodsProduct ->{
                    ProductInfoDto productInfo = new ProductInfoDto();
                    productInfo.setProductNo(goodsProduct.getProductNo());
                    productInfo.setProductName(goodsProduct.getProductName());
                    productInfo.setParentNo(goodsProduct.getParentNo());
                    productInfo.setProductType(goodsProduct.getProductType());
                    productInfo.setExtendProps((HashMap<String, String>) goodsProduct.getExtendProps());
                    productInfo.setProductAttrs(goodsProduct.getProductAttrs());
                    productInfo.setProductExecutionResult(goodsProduct.getProductExecutionResult());
                    return productInfo;
                }).collect(Collectors.toList());
                goodsInfoDto.setGoodsProductInfos(productInfoDtos);
            }
            goodsInfoDto.setExtendProps(goodsFacade.getExtendProps());

            //商品净重
            goodsInfoDto.setNetWeight(goodsFacade.getNetWeight());

            goodsInfoDtoList.add(goodsInfoDto);
        });
        return goodsInfoDtoList;
    }

    /**
     * 业务身份
     *
     * @param businessIdentity
     * @return
     */
    private BusinessIdentityDto toOrderBusinessIdentity(BusinessIdentityFacade businessIdentity) {
        BusinessIdentityDto businessIdentityDto = new BusinessIdentityDto();
        businessIdentityDto.setBusinessType(businessIdentity.getBusinessType());
        businessIdentityDto.setBusinessUnit(businessIdentity.getBusinessUnit());
        businessIdentityDto.setBusinessScene(businessIdentity.getBusinessScene());
        businessIdentityDto.setBusinessStrategy(businessIdentity.getBusinessStrategy());
        businessIdentityDto.setFulfillmentUnit(businessIdentity.getFulfillmentUnit());
        return businessIdentityDto;
    }


    /**
     * 渠道信息转换
     *
     * @param channelFacade
     * @return
     */
    private ChannelInfoDto toChannelInfo(ChannelFacade channelFacade) {
        ChannelInfoDto channelInfoDto = new ChannelInfoDto();
        channelInfoDto.setChannelOperateTime(channelFacade.getChannelOperateTime());
        channelInfoDto.setCustomerOrderNo(channelFacade.getCustomerOrderNo());
        channelInfoDto.setSystemCaller(SystemCallerEnum.of(channelFacade.getSystemCaller()));
        channelInfoDto.setSystemSubCaller(channelFacade.getSystemSubCaller());
        channelInfoDto.setChannelOrderNo(channelFacade.getChannelOrderNo());
        channelInfoDto.setChannelCustomerNo(channelFacade.getChannelCustomerNo());
        channelInfoDto.setChannelNo(channelFacade.getChannelNo());
        channelInfoDto.setExtendProps(channelFacade.getExtendProps());
        return channelInfoDto;
    }

    /**
     * 交易客户信息转换
     *
     * @param customerFacade
     * @return
     */
    private CustomerInfoDto toCustomerInfoDto(CustomerFacade customerFacade) {
        CustomerInfoDto customer = new CustomerInfoDto();
        customer.setAccountNo(customerFacade.getAccountNo());
        customer.setAccountId(customerFacade.getAccountId());
        customer.setAccount2No(customerFacade.getAccount2No());
        customer.setAccount3No(customerFacade.getAccount3No());
        // 履约账号的名称
        customer.setAccountName(customerFacade.getAccountName());
        customer.setAccount2Name(customerFacade.getAccount2Name());
        customer.setAccount3Name(customerFacade.getAccount3Name());
        //fixme 店铺pin
        customer.setStorePin(customerFacade.getStorePin());
        customer.setCustomerNo(customerFacade.getCustomerNo());
        return customer;
    }

    /**
     * 产品信息转换
     *
     * @param facadeList
     * @return
     */
    private List<ProductInfoDto> toProductFacadeList(List<ProductFacade> facadeList) {
        if (CollectionUtils.isEmpty(facadeList)) {
            return Collections.emptyList();
        }
        return facadeList.stream().map(productFacade -> {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            productInfoDto.setProductNo(productFacade.getProductNo());
            productInfoDto.setProductName(productFacade.getProductName());
            productInfoDto.setProductType(productFacade.getProductType());
            productInfoDto.setParentNo(productFacade.getParentNo());
            productInfoDto.setProductAttrs(productFacade.getProductAttrs());
            productInfoDto.setExtendProps(productFacade.getExtendProps());
            return productInfoDto;
        }).collect(Collectors.toList());
    }

    /**
     * 交易关联单转换
     *
     * @param facadeResponse
     * @return
     */
    public RefOrderInfoDto toRefOrderInfoDto(GetOrderFacadeResponse facadeResponse) {
        RefOrderInfoDto refOrderInfoDto = new RefOrderInfoDto();

        //扩展<单据类型，单据号>
        refOrderInfoDto.setExtendProps(facadeResponse.getExtendRefOrder());

        List<RefOrderFacade> facadeList = facadeResponse.getRefOrders();

        List<String> serviceEnquiryOrderNos = new ArrayList<>();
        List<String> serviceEnquiryWaybillNos = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(facadeList)) {
            facadeList.forEach(orderFacade -> {
                //运单
                if (RefOrderTypeEnum.DELIVERY.getCode().equals(orderFacade.getRefOrderType())
                    && RefOrderSubType.DeliveryEnum.FORWARD_DELIVERY.getCode().equals(orderFacade.getRefOrderSubType())) {
                    refOrderInfoDto.setWaybillNo(orderFacade.getRefOrderNo());
                }
                //采购单
                if (RefOrderTypeEnum.WORK_ORDER.getCode().equals(orderFacade.getRefOrderType())
                    && RefOrderSubType.WorkOrderEnum.INBOUND.getCode().equals(orderFacade.getRefOrderSubType())) {
                    refOrderInfoDto.setPurchaseOrderNo(orderFacade.getRefOrderNo());
                }
                //预约单
                if (RefOrderTypeEnum.WORK_ORDER.getCode().equals(orderFacade.getRefOrderType())
                    && RefOrderSubType.WorkOrderEnum.RESERVATION.getCode().equals(orderFacade.getRefOrderSubType())) {
                    refOrderInfoDto.setReservationOrderNo(orderFacade.getRefOrderNo());
                }
                //询价单
                if (RefOrderTypeEnum.ENQUIRY.getCode().equals(orderFacade.getRefOrderType())) {
                    refOrderInfoDto.setEnquiryOrderNo(orderFacade.getRefOrderNo());
                }

                //原订单
                if (RefOrderTypeEnum.ORDER.getCode().equals(orderFacade.getRefOrderType())) {
                    refOrderInfoDto.setOriginalOrderNo(orderFacade.getRefOrderNo());
                }

                //改址单
                if (RefOrderTypeEnum.READDRESS.getCode().equals(orderFacade.getRefOrderType())) {
                    refOrderInfoDto.setReaddressOrderNo(orderFacade.getRefOrderNo());
                }

                //逆向单
                if (RefOrderTypeEnum.RETURN_ORDER.getCode().equals(orderFacade.getRefOrderType())) {
                    refOrderInfoDto.setReverseOrderNo(orderFacade.getRefOrderNo());
                }

                //送取同步派送运单号
                if (RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY.getCode().equals(orderFacade.getRefOrderType())) {
                    refOrderInfoDto.setDeliveryWaybillNo(orderFacade.getRefOrderNo());
                }

                //送取同步取件运单号
                if (RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode().equals(orderFacade.getRefOrderType())) {
                    refOrderInfoDto.setPickupWaybillNo(orderFacade.getRefOrderNo());
                }

                //送取同步派送订单号
                if (RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY_ORDER.getCode().equals(orderFacade.getRefOrderType())) {
                    refOrderInfoDto.setDeliveryOrderNo(orderFacade.getRefOrderNo());
                }

                //送取同步取件订单号
                if (RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode().equals(orderFacade.getRefOrderType())) {
                    refOrderInfoDto.setPickupOrderNo(orderFacade.getRefOrderNo());
                }
                //服务询价单订单号
                if (RefOrderTypeEnum.SERVICE_ENQUIRY_ORDER.getCode().equals(orderFacade.getRefOrderType())) {
                    serviceEnquiryOrderNos.add(orderFacade.getRefOrderNo());
                }
                //服务询价单运单号
                if (RefOrderTypeEnum.SERVICE_ENQUIRY_WAYBILL.getCode().equals(orderFacade.getRefOrderType())) {
                    serviceEnquiryWaybillNos.add(orderFacade.getRefOrderNo());
                }

                //子单号
                if (RefOrderTypeEnum.CHILD.getCode().equals(orderFacade.getRefOrderType())) {
                    if (CollectionUtils.isEmpty(refOrderInfoDto.getChildOrderNos())) {
                        List<String> childOrderNos = new ArrayList<>();
                        childOrderNos.add(orderFacade.getRefOrderNo());
                        refOrderInfoDto.setChildOrderNos(childOrderNos);
                    } else {
                        refOrderInfoDto.getChildOrderNos().add(orderFacade.getRefOrderNo());
                    }
                }

                //集单号
                if (RefOrderTypeEnum.COLLECTION_ORDER.getCode().equals(orderFacade.getRefOrderType())) {
                    refOrderInfoDto.setCollectionOrderNo(orderFacade.getRefOrderNo());
                }

                //扩展<单据类型，单据号>
                //extendProps.put(String.valueOf(orderFacade.getRefOrderType()), orderFacade.getRefOrderNo());
            });
        }
        //服务询价订单
        if(CollectionUtils.isNotEmpty(serviceEnquiryOrderNos)){
            refOrderInfoDto.setAddOnServiceOrderNos(serviceEnquiryOrderNos);
        }
        //服务询价运单
        if(CollectionUtils.isNotEmpty(serviceEnquiryWaybillNos)){
            refOrderInfoDto.setAddOnServiceWaybillNos(serviceEnquiryWaybillNos);
        }
        return refOrderInfoDto;
    }

    /**
     * 货品信息转换
     *
     * @param facadeList
     * @return
     */
    private List<CargoInfoDto> toCargoInfoDtoList(List<CargoFacade> facadeList) {
        if (CollectionUtils.isEmpty(facadeList)) {
            return Collections.emptyList();
        }
        return facadeList.stream().map(cargoFacade -> {
            CargoInfoDto cargoInfoDto = new CargoInfoDto();
            cargoInfoDto.setCargoName(cargoFacade.getCargoName());
            cargoInfoDto.setCargoNo(cargoFacade.getCargoNo());
            cargoInfoDto.setCargoType(cargoFacade.getCargoType());
            cargoInfoDto.setCargoVolume(this.toVolumeInfoDto(cargoFacade.getCargoVolume()));
            cargoInfoDto.setDimensionInfo(this.toDimensionInfoDto(cargoFacade.getCargoDimension()));
            cargoInfoDto.setCargoWeight(toWeightInfoDto(cargoFacade.getCargoWeight()));
            cargoInfoDto.setCargoQuantityInfo(toCargoQuantityInfo(cargoFacade.getCargoQuantity()));
            cargoInfoDto.setCargoRemark(cargoFacade.getCargoRemark());
            cargoInfoDto.setPolluteSign(cargoFacade.getPolluteSign());
            //是否易损
            cargoInfoDto.setCargoVulnerable(cargoFacade.getCargoVulnerable());
            //货品标识
            cargoInfoDto.setCargoSign(cargoFacade.getCargoSign());
            //货品序列
            cargoInfoDto.setSerialInfos(SerialMapper.INSTANCE.toSerialInfoList(cargoFacade.getSerialInfos()));
            //附件赋值
            List<AttachmentInfoDto> attachmentInfoList = cargoInfoDto.getCargoAttachmentInfos();
            if (CollectionUtils.isNotEmpty(attachmentInfoList)) {
                List<AttachmentInfo> attachmentFaceInfoList = new ArrayList<>();
                for (AttachmentInfoDto attachmentInfoDto : attachmentInfoList) {
                    AttachmentInfo attachmentFaceInfo = new AttachmentInfo();
                    attachmentFaceInfo.setAttachmentUrl(attachmentInfoDto.getAttachmentUrl());//附件路径
                    attachmentFaceInfo.setAttachmentName(attachmentInfoDto.getAttachmentName());//附件名称
                    attachmentFaceInfo.setAttachmentSortNo(attachmentInfoDto.getAttachmentSortNo());//附件排序
                    attachmentFaceInfo.setAttachmentRemark(attachmentInfoDto.getAttachmentRemark());//附件备注
                    attachmentFaceInfo.setAttachmentType(attachmentInfoDto.getAttachmentType());//附件类型
                    attachmentFaceInfo.setAttachmentDocType(attachmentInfoDto.getAttachmentDocType());//附件文档类型
                    attachmentFaceInfoList.add(attachmentFaceInfo);
                }
                cargoFacade.setAttachmentInfos(attachmentFaceInfoList);
            }
            cargoInfoDto.setExtendProps(cargoFacade.getExtendProps());
            // 货品信息增值服务
            if(CollectionUtils.isNotEmpty(cargoFacade.getCargoProductInfos())){
                List<ProductInfoDto> productInfoDtos = cargoFacade.getCargoProductInfos().stream().map(cargoProduct ->{
                    ProductInfoDto productInfo = new ProductInfoDto();
                    productInfo.setProductNo(cargoProduct.getProductNo());
                    productInfo.setProductName(cargoProduct.getProductName());
                    productInfo.setParentNo(cargoProduct.getParentNo());
                    productInfo.setProductType(cargoProduct.getProductType());
                    productInfo.setExtendProps(cargoProduct.getExtendProps());
                    productInfo.setProductAttrs(cargoProduct.getProductAttrs());
                    return productInfo;
                }).collect(Collectors.toList());
                cargoInfoDto.setCargoProductInfos(productInfoDtos);
            }

            return cargoInfoDto;
        }).collect(Collectors.toList());
    }

    /**
     * 货物数量
     *
     * @param cargoQuantity
     * @return
     */
    private QuantityInfoDto toCargoQuantityInfo(Quantity cargoQuantity) {
        if (cargoQuantity == null) {
            return null;
        }
        QuantityInfoDto quantityInfoDto = new QuantityInfoDto();
        quantityInfoDto.setValue(cargoQuantity.getValue());
        quantityInfoDto.setUnit(cargoQuantity.getUnit());
        return quantityInfoDto;
    }

    /**
     * 发货人信息转换
     *
     * @param consignorFacade
     * @return
     */
    private ConsignorInfoDto toConsignorInfoDto(ConsignorFacade consignorFacade) {
        ConsignorInfoDto consignorInfoDto = new ConsignorInfoDto();
        consignorInfoDto.setConsignorName(consignorFacade.getConsignorName());
        consignorInfoDto.setConsignorMobile(consignorFacade.getConsignorMobile());
        consignorInfoDto.setConsignorPhone(consignorFacade.getConsignorPhone());
        consignorInfoDto.setConsignorZipCode(consignorFacade.getConsignorZipCode());
        consignorInfoDto.setConsignorCompany(consignorFacade.getConsignorCompany());
        consignorInfoDto.setConsignorNationNo(consignorFacade.getConsignorNationNo());
        consignorInfoDto.setConsignorNation(consignorFacade.getConsignorNation());
        consignorInfoDto.setConsignorIdType(IdentityTypeEnum.fromCode(consignorFacade.getConsignorIdType()));
        consignorInfoDto.setConsignorIdNo(consignorFacade.getConsignorIdNo());
        consignorInfoDto.setConsignorIdName(consignorFacade.getConsignorIdName());
        consignorInfoDto.setAddressInfoDto(this.toAddressInfoDto(consignorFacade.getAddress()));
        // 发货仓信息
        consignorInfoDto.setCustomerWarehouseDto(this.toWarehouseInfoDto(consignorFacade.getCustomerWarehouse()));
        consignorInfoDto.setConsignorEnName(consignorFacade.getConsignorEnName());
        return consignorInfoDto;
    }

    /**
     * 收货人信息转换
     *
     * @param consigneeFacade
     * @return
     */
    private ConsigneeInfoDto toConsigneeInfoDto(ConsigneeFacade consigneeFacade) {
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
        if (null == consigneeFacade) {
            return consigneeInfoDto;
        }
        consigneeInfoDto.setConsigneeName(consigneeFacade.getConsigneeName());
        consigneeInfoDto.setConsigneeMobile(consigneeFacade.getConsigneeMobile());
        consigneeInfoDto.setConsigneePhone(consigneeFacade.getConsigneePhone());
        consigneeInfoDto.setConsigneeZipCode(consigneeFacade.getConsigneeZipCode());
        consigneeInfoDto.setConsigneeCompany(consigneeFacade.getConsigneeCompany());
        consigneeInfoDto.setConsigneeNationNo(consigneeFacade.getConsigneeNationNo());
        consigneeInfoDto.setConsigneeNation(consigneeFacade.getConsigneeNation());
        consigneeInfoDto.setConsigneeIdType(IdentityTypeEnum.fromCode(consigneeFacade.getConsigneeIdType()));
        consigneeInfoDto.setConsigneeIdNo(consigneeFacade.getConsigneeIdNo());
        consigneeInfoDto.setConsigneeIdName(consigneeFacade.getConsigneeIdName());
        consigneeInfoDto.setAddressInfoDto(this.toAddressInfoDto(consigneeFacade.getAddress()));
        consigneeInfoDto.setExtendProps(consigneeFacade.getExtendProps());
        consigneeInfoDto.setConsigneeEmail(consigneeFacade.getConsigneeEmail());
        // 收货仓信息
        consigneeInfoDto.setReceiveWarehouse(this.toWarehouseInfoDto(consigneeFacade.getReceiveWarehouse()));
        return consigneeInfoDto;
    }

    /**
     * 配送信息
     *
     * @param shipmentFacade
     * @return
     */
    private ShipmentInfoDto toShipmentInfoDto(ShipmentFacade shipmentFacade) {
        ShipmentInfoDto shipmentInfoDto = new ShipmentInfoDto();
        shipmentInfoDto.setPlanDeliveryTime(shipmentFacade.getPlanDeliveryTime());
        shipmentInfoDto.setPlanDeliveryPeriod(shipmentFacade.getPlanDeliveryPeriod());
        shipmentInfoDto.setExpectDeliveryStartTime(shipmentFacade.getExpectDeliveryStartTime());
        shipmentInfoDto.setExpectDeliveryEndTime(shipmentFacade.getExpectDeliveryEndTime());
        shipmentInfoDto.setExpectPickupStartTime(shipmentFacade.getExpectPickupStartTime());
        shipmentInfoDto.setExpectPickupEndTime(shipmentFacade.getExpectPickupEndTime());
        shipmentInfoDto.setPickupType(PickupTypeEnum.of(shipmentFacade.getPickupType()));
        shipmentInfoDto.setDeliveryType(DeliveryTypeEnum.of(shipmentFacade.getDeliveryType()));
        shipmentInfoDto.setVehicleType(shipmentFacade.getVehicleType());
        shipmentInfoDto.setWarmLayer(WarmLayerEnum.fromCode(shipmentFacade.getWarmLayer()));
        shipmentInfoDto.setStartStationNo(shipmentFacade.getStartStationNo());
        shipmentInfoDto.setStartStationName(shipmentFacade.getStartStationName());
        shipmentInfoDto.setStartStationType(shipmentFacade.getStartStationType());
        shipmentInfoDto.setEndStationNo(shipmentFacade.getEndStationNo());
        shipmentInfoDto.setEndStationName(shipmentFacade.getEndStationName());
        shipmentInfoDto.setEndStationType(shipmentFacade.getEndStationType());
        shipmentInfoDto.setWarehouseNo(shipmentFacade.getWarehouseNo());
        shipmentInfoDto.setReceiveWarehouseNo(shipmentFacade.getReceiveWarehouseNo());
        shipmentInfoDto.setTransportType(TransportTypeEnum.of(shipmentFacade.getTransportType()));
        shipmentInfoDto.setContactlessType(ContactlessTypeEnum.of(shipmentFacade.getContactlessType()));
        shipmentInfoDto.setAssignedAddress(shipmentFacade.getAssignedAddress());
        shipmentInfoDto.setPlanReceiveTime(shipmentFacade.getPlanReceiveTime());
        shipmentInfoDto.setPickupCode(shipmentFacade.getPickupCode());
        shipmentInfoDto.setPickupCodeCreateType(shipmentFacade.getPickupCodeCreateType());
        shipmentInfoDto.setServiceRequirements(shipmentFacade.getServiceRequirements());
        //收货偏好
        shipmentInfoDto.setReceivingPreference(shipmentFacade.getReceivingPreference());
        shipmentInfoDto.setShipperNo(shipmentFacade.getShipperNo());
        shipmentInfoDto.setShipperName(shipmentFacade.getShipperName());
        shipmentInfoDto.setShipperType(shipmentFacade.getShipperType());
        //扩展信息
        shipmentInfoDto.setExtendProps(shipmentFacade.getExtendProps());
        shipmentInfoDto.setStartCenterNo(shipmentFacade.getStartCenterNo());
        shipmentInfoDto.setEndCenterNo(shipmentFacade.getEndCenterNo());
        shipmentInfoDto.setExpectDispatchStartTime(shipmentFacade.getExpectDispatchStartTime());
        shipmentInfoDto.setExpectDispatchEndTime(shipmentFacade.getExpectDispatchEndTime());
        shipmentInfoDto.setStartStationTypeL3(shipmentFacade.getStartStationTypeL3());
        shipmentInfoDto.setEndStationTypeL3(shipmentFacade.getEndStationTypeL3());
        return shipmentInfoDto;
    }

    /**
     * 财务信息转换
     *
     * @param financeFacade
     * @return
     */
    private FinanceInfoDto toFinanceInfoDto(FinanceFacade financeFacade) {
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setSettlementType(SettlementTypeEnum.of(financeFacade.getSettlementType()));
        financeInfoDto.setEnquiryType(EnquiryTypeEnum.of(financeFacade.getEnquiryType()));
        financeInfoDto.setEstimateAmount(this.toMoneyInfoDto(financeFacade.getEstimateAmount()));
        // 预估财务信息
        financeInfoDto.setEstimateFinanceInfo(this.toEstimateFinanceInfoDto(financeFacade.getEstimateFinanceInfo()));
        financeInfoDto.setPreemptType(financeFacade.getPreemptType());
        financeInfoDto.setPaymentStage(PaymentStageEnum.of(financeFacade.getPaymentStage()));
        financeInfoDto.setPreAmount(this.toMoneyInfoDto(financeFacade.getPreAmount()));
        financeInfoDto.setDiscountAmount(this.toMoneyInfoDto(financeFacade.getDiscountAmount()));
        financeInfoDto.setTotalDiscountAmount(this.toMoneyInfoDto(financeFacade.getTotalDiscountAmount()));
        financeInfoDto.setBillingWeight(this.toWeightInfoDto(financeFacade.getBillingWeight()));
        financeInfoDto.setBillingVolume(this.toVolumeInfoDto(financeFacade.getBillingVolume()));
        financeInfoDto.setBillingMode(financeFacade.getBillingMode());
        financeInfoDto.setPaymentAccountNo(financeFacade.getPaymentAccountNo());
        financeInfoDto.setCollectionOrgNo(financeFacade.getCollectionOrgNo());
        financeInfoDto.setPayment(PaymentTypeEnum.of(financeFacade.getPayment()));
        // 实际支付方式
        financeInfoDto.setActualPaymentType(financeFacade.getActualPaymentType());
        financeInfoDto.setSettlementAccountNo(financeFacade.getSettlementAccountNo());
        financeInfoDto.setPaymentStatus(PaymentStatusEnum.of(financeFacade.getPaymentStatus()));
        financeInfoDto.setRefundStatus(RefundStatusEnum.of(financeFacade.getRefundStatus()));
        financeInfoDto.setPayDeadline(financeFacade.getPayDeadline());
        if (CollectionUtils.isNotEmpty(financeFacade.getFinanceDetails())) {
            List<FinanceDetailInfoDto> detailInfoDtoList = financeFacade.getFinanceDetails().stream()
                    .map(this::toFinanceDetailInfoDto)
                    .collect(Collectors.toList());
            financeInfoDto.setFinanceDetailInfos(detailInfoDtoList);
        }
        financeInfoDto.setPointsInfoDto(toPointsInfoDto(financeFacade.getPointsFacade()));

        if (financeFacade.getEnquiryStatus() != null) {
            financeInfoDto.setEnquiryStatus(EnquiryStatusEnum.of(financeFacade.getEnquiryStatus()));
        }
        financeInfoDto.setRemark(financeFacade.getRemark());
        //抵扣信息
        financeInfoDto.setDeductionInfoDtos(financeFacade.getDeductionInfoDtos());
        //收费要求信息
        if(CollectionUtils.isNotEmpty(financeFacade.getCostInfos())){
            List<CostInfoDto> costInfos = financeFacade.getCostInfos().stream().map(costInfo ->{
                CostInfoDto costInfoDto = new CostInfoDto();
                costInfoDto.setCostNo(costInfo.getCostNo());
                costInfoDto.setCostName(costInfo.getCostName());
                costInfoDto.setChargingSource(costInfo.getChargingSource());
                costInfoDto.setSettlementAccountNo(costInfo.getSettlementAccountNo());
                costInfoDto.setAdditionPriceInfoDto(AdditionPriceInfoDto.toAdditionPriceInfoDto(costInfo.getAdditionPriceInfo()));
                costInfoDto.setExtendProps(costInfo.getExtendProps());
                return costInfoDto;
            }).collect(Collectors.toList());
            financeInfoDto.setCostInfoDtos(costInfos);
        }

        //扩展信息
        financeInfoDto.setExtendProps(financeFacade.getExtendProps());

        //预估税金
        financeInfoDto.setEstimatedTax(MoneyMapper.INSTANCE.toMoneyInfoDto(financeFacade.getEstimatedTax()));
        //真实税金
        financeInfoDto.setActualTax(MoneyMapper.INSTANCE.toMoneyInfoDto(financeFacade.getActualTax()));
        //扩展信息
        financeInfoDto.setExtendProps(financeFacade.getExtendProps());
        //费用支付状态归集
        financeInfoDto.setPayStatusMap(financeFacade.getPayStatusMap());

        //附加费用
        if(CollectionUtils.isNotEmpty(financeFacade.getAttachFees())){
            List<CostInfoDto> attachFees = financeFacade.getAttachFees().stream().map(attachFee ->{
                CostInfoDto costInfoDto = new CostInfoDto();
                costInfoDto.setCostNo(attachFee.getCostNo());
                costInfoDto.setCostName(attachFee.getCostName());
                costInfoDto.setChargingSource(attachFee.getChargingSource());
                costInfoDto.setSettlementAccountNo(attachFee.getSettlementAccountNo());
                costInfoDto.setExtendProps(attachFee.getExtendProps());
                return costInfoDto;
            }).collect(Collectors.toList());
            financeInfoDto.setAttachFees(attachFees);
        }

        // 支付人Pin集合
        financeInfoDto.setPayerPins(financeFacade.getPayerPins());
        // 税金结算方式
        financeInfoDto.setTaxSettlementType(financeFacade.getTaxSettlementType());

        // 预占金额
        financeInfoDto.setOccupyAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(financeFacade.getOccupyAmount()));
        // 预占模式
        financeInfoDto.setOccupyMode(financeFacade.getOccupyMode());
        //多方计费总金额
        financeInfoDto.setMultiPartiesTotalAmounts(CostFacadeMapper.INSTANCE.toCostInfoDtoList(financeFacade.getMultiPartiesTotalAmounts()));
        return financeInfoDto;
    }

    /**
     * 预估财务信息
     *
     * @param estimateFinanceFacade
     * @return
     */
    private FinanceInfoDto toEstimateFinanceInfoDto(FinanceFacade estimateFinanceFacade) {
        if (estimateFinanceFacade == null) {
            return null;
        }

        FinanceInfoDto estimateFinanceInfoDto = new FinanceInfoDto();

        // 预估-折前金额
        Optional.ofNullable(estimateFinanceFacade.getPreAmount()).ifPresent(estimatePreAmountFacade -> {
            estimateFinanceInfoDto.setPreAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(estimatePreAmountFacade));
        });
        // 预估-折后金额
        Optional.ofNullable(estimateFinanceFacade.getDiscountAmount()).ifPresent(estimateDiscountAmountFacade -> {
            estimateFinanceInfoDto.setDiscountAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(estimateDiscountAmountFacade));
        });
        // 预估-计费重量
        Optional.ofNullable(estimateFinanceFacade.getBillingWeight()).ifPresent(estimateBillingWeightFacade -> {
            estimateFinanceInfoDto.setBillingWeight(WeightMapper.INSTANCE.toWeightInfoDto(estimateBillingWeightFacade));
        });
        // 预估-计费体积
        Optional.ofNullable(estimateFinanceFacade.getBillingVolume()).ifPresent(estimateBillingVolumeFacade -> {
            estimateFinanceInfoDto.setBillingVolume(VolumeMapper.INSTANCE.toVolumeInfoDto(estimateBillingVolumeFacade));
        });
        // 预估-加价后总金额
        Optional.ofNullable(estimateFinanceFacade.getTotalAdditionAmount()).ifPresent(totalAdditionAmount -> {
            estimateFinanceInfoDto.setTotalAdditionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(totalAdditionAmount));
        });
        // 预估-费用明细
        Optional.ofNullable(estimateFinanceFacade.getFinanceDetails()).ifPresent(estimateFinanceDetailsFacade -> {
            List<FinanceDetailInfoDto> estimateFinanceDetailInfos = new ArrayList<>(estimateFinanceDetailsFacade.size());

            estimateFinanceDetailsFacade.forEach(estimateFinanceDetailFacade -> {
                // 预估-费用明细对象
                FinanceDetailInfoDto estimateFinanceDetailInfoDto = new FinanceDetailInfoDto();
                // 预估-费用编号
                estimateFinanceDetailInfoDto.setCostNo(estimateFinanceDetailFacade.getCostNo());
                // 预估-费用名称
                estimateFinanceDetailInfoDto.setCostName(estimateFinanceDetailFacade.getCostName());
                // 预估-费用产品编码
                estimateFinanceDetailInfoDto.setProductNo(estimateFinanceDetailFacade.getProductNo());
                // 预估-费用产品名称
                estimateFinanceDetailInfoDto.setProductName(estimateFinanceDetailFacade.getProductName());
                // 预估-折扣信息
                if (CollectionUtils.isNotEmpty(estimateFinanceDetailFacade.getDiscountFacades())) {
                    List<DiscountInfoDto> estimateDiscountInfoDtos = new ArrayList<>(estimateFinanceDetailFacade.getDiscountFacades().size());
                    estimateFinanceDetailFacade.getDiscountFacades().forEach(discountFacade -> {
                        DiscountInfoDto estimateDiscountInfoDto = new DiscountInfoDto();
                        // 预估-折扣编号
                        estimateDiscountInfoDto.setDiscountNo(discountFacade.getDiscountNo());
                        // 预估-折扣类型
                        estimateDiscountInfoDto.setDiscountType(discountFacade.getDiscountType());
                        // 预估-折扣金额
                        estimateDiscountInfoDto.setDiscountedAmount(discountFacade.getDiscountedAmount());
                        estimateDiscountInfoDtos.add(estimateDiscountInfoDto);
                    });

                    estimateFinanceDetailInfoDto.setDiscountInfoDtos(estimateDiscountInfoDtos);
                }
                // 预估-折前金额
                Optional.ofNullable(estimateFinanceDetailFacade.getPreAmount()).ifPresent(estimatePreAmount -> {
                    estimateFinanceDetailInfoDto.setPreAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(estimatePreAmount));
                });
                // 预估-折后金额
                Optional.ofNullable(estimateFinanceDetailFacade.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
                    estimateFinanceDetailInfoDto.setDiscountAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(estimateDiscountAmount));
                });
                // 预估-加价后金额
                Optional.ofNullable(estimateFinanceDetailFacade.getAdditionAmount()).ifPresent(additionAmount -> {
                    estimateFinanceDetailInfoDto.setAdditionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(additionAmount));
                });
                // 预估-扩展字段
                estimateFinanceDetailInfoDto.setExtendProps(estimateFinanceDetailFacade.getExtendProps());

                estimateFinanceDetailInfos.add(estimateFinanceDetailInfoDto);

            });
            estimateFinanceInfoDto.setFinanceDetailInfos(estimateFinanceDetailInfos);
        });

        return estimateFinanceInfoDto;
    }

    /**
     * 积分信息
     *
     * @param pointsFacade
     * @return
     */
    private PointsInfoDto toPointsInfoDto(PointsFacade pointsFacade) {
        if (pointsFacade == null) {
            return null;
        }
        PointsInfoDto pointsInfoDto = new PointsInfoDto();
        if (pointsFacade.getRedeemPointsQuantity() != null) {
            QuantityInfoDto quantityInfoDto = new QuantityInfoDto();
            quantityInfoDto.setUnit(pointsFacade.getRedeemPointsQuantity().getUnit());
            quantityInfoDto.setValue(pointsFacade.getRedeemPointsQuantity().getValue());
            pointsInfoDto.setRedeemPointsQuantity(quantityInfoDto);
        }
        if (pointsFacade.getRedeemPointsAmount() != null) {
            MoneyInfoDto moneyInfoDto = new MoneyInfoDto();
            moneyInfoDto.setAmount(pointsFacade.getRedeemPointsAmount().getAmount());
            moneyInfoDto.setCurrencyCode(pointsFacade.getRedeemPointsAmount().getCurrency());
            pointsInfoDto.setRedeemPointsAmount(moneyInfoDto);
        }
        return pointsInfoDto;
    }

    private PromotionInfoDto toPromotionInfoDto(PromotionFacade promotion) {
        PromotionInfoDto promotionInfoDto = new PromotionInfoDto();
        if (promotion != null) {
            if (CollectionUtils.isNotEmpty(promotion.getTickets())) {
                List<TicketInfoDto> ticketInfoDtoList = promotion.getTickets().stream().map(ticketFacade -> {
                    TicketInfoDto ticketInfoDto = new TicketInfoDto();
                    ticketInfoDto.setTicketNo(ticketFacade.getTicketNo());
                    ticketInfoDto.setTicketCategory(ticketFacade.getTicketCategory());
                    ticketInfoDto.setTicketType(ticketFacade.getTicketType());
                    ticketInfoDto.setTicketDiscountAmount(this.toMoneyInfoDto(ticketFacade.getTicketDiscountAmount()));
                    ticketInfoDto.setTicketDescription(ticketFacade.getTicketDescription());
                    ticketInfoDto.setTicketDiscountRate(ticketFacade.getTicketDiscountRate());
                    ticketInfoDto.setTicketDiscountUpperLimit(this.toMoneyInfoDto(ticketFacade.getTicketDiscountUpperLimit()));
                    ticketInfoDto.setCouponStatus(ticketFacade.getCouponStatus());
                    //ticketInfoDto.setExtendProps(ticketFacade.get);
                    ticketInfoDto.setTicketUseAmount(this.toMoneyInfoDto(ticketFacade.getTicketUseAmount()));
                    ticketInfoDto.setTicketSource(ticketFacade.getTicketSource());
                    ticketInfoDto.setTicketBatchNo(ticketFacade.getTicketBatchNo());
                    return ticketInfoDto;
                }).collect(Collectors.toList());
                promotionInfoDto.setTicketInfos(ticketInfoDtoList);
            }
            if (CollectionUtils.isNotEmpty(promotion.getActivities())) {
                List<ActivityInfoDto> activityInfoDtoList = promotion.getActivities().stream().map(activityFacade -> {
                    ActivityInfoDto activityInfoDto = new ActivityInfoDto();
                    activityInfoDto.setActivityNo(activityFacade.getActivityNo());
                    activityInfoDto.setActivityName(activityFacade.getActivityName());
                    activityInfoDto.setActivityStatus(activityFacade.getActivityStatus());
                    //activityInfoDto.setExtendProps(activityFacade.get);
                    activityInfoDto.setActivityValue(activityFacade.getActivityValue());
                    return activityInfoDto;
                }).collect(Collectors.toList());
                promotionInfoDto.setActivityInfos(activityInfoDtoList);
            }
            if (CollectionUtils.isNotEmpty(promotion.getDiscounts())) {
                List<DiscountInfoDto> discountInfoDtoList = promotion.getDiscounts().stream().map(discountFacade -> {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountFacade.getDiscountNo());
                    discountInfoDto.setDiscountType(discountFacade.getDiscountType());
                    discountInfoDto.setExtendProps(discountFacade.getExtendProps());
                    return discountInfoDto;
                }).collect(Collectors.toList());
                promotionInfoDto.setDiscountInfos(discountInfoDtoList);
            }

            if (CollectionUtils.isNotEmpty(promotion.getOperationDiscounts())) {
                List<DiscountInfoDto> discountInfoDtoList = promotion.getOperationDiscounts().stream().map(discountFacade -> {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountFacade.getDiscountNo());
                    discountInfoDto.setDiscountType(discountFacade.getDiscountType());
                    discountInfoDto.setExtendProps(discountFacade.getExtendProps());
                    return discountInfoDto;
                }).collect(Collectors.toList());
                promotionInfoDto.setOperationDiscountInfos(discountInfoDtoList);
            }
            //promotionInfoDto.setDiscountRefOrderNo(promotion.get);
        }
        return promotionInfoDto;
    }

    /**
     * 费用明细
     *
     * @param detailFacade
     * @return
     */
    private FinanceDetailInfoDto toFinanceDetailInfoDto(FinanceDetailFacade detailFacade) {
        FinanceDetailInfoDto financeDetailInfoDto = new FinanceDetailInfoDto();
        financeDetailInfoDto.setCostNo(detailFacade.getCostNo());
        financeDetailInfoDto.setCostName(detailFacade.getCostName());
        financeDetailInfoDto.setProductNo(detailFacade.getProductNo());
        financeDetailInfoDto.setProductName(detailFacade.getProductName());
        if (CollectionUtils.isNotEmpty(detailFacade.getDiscountFacades())) {
            List<DiscountInfoDto> discountInfoDtos = new ArrayList<>(detailFacade.getDiscountFacades().size());
            detailFacade.getDiscountFacades().forEach(discountFacade -> {
                DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                discountInfoDto.setDiscountNo(discountFacade.getDiscountNo());
                discountInfoDto.setDiscountType(discountFacade.getDiscountType());
                discountInfoDto.setDiscountedAmount(discountFacade.getDiscountedAmount());
                discountInfoDto.setExtendProps(discountFacade.getExtendProps());
                discountInfoDtos.add(discountInfoDto);
            });
            financeDetailInfoDto.setDiscountInfoDtos(discountInfoDtos);
        }

        financeDetailInfoDto.setPreAmount(this.toMoneyInfoDto(detailFacade.getPreAmount()));
        financeDetailInfoDto.setDiscountAmount(this.toMoneyInfoDto(detailFacade.getDiscountAmount()));
        financeDetailInfoDto.setRemark(detailFacade.getRemark());
        //积分信息赋值
        financeDetailInfoDto.setPointsInfoDto(toPointsInfoDto(detailFacade.getPointsFacade()));
        // 费用明细向谁收
        financeDetailInfoDto.setChargingSource(detailFacade.getChargingSource());
        // 扩展字段
        financeDetailInfoDto.setExtendProps(detailFacade.getExtendProps());
        return financeDetailInfoDto;
    }

    /**
     * 体积转换
     *
     * @param volume
     * @return
     */
    private VolumeInfoDto toVolumeInfoDto(Volume volume) {
        VolumeInfoDto volumeInfoDto = new VolumeInfoDto();
        volumeInfoDto.setValue(volume.getValue());
        volumeInfoDto.setUnit(volume.getUnit());
        return volumeInfoDto;
    }

    /**
     * 长、宽、高转换
     *
     * @param dimension
     * @return
     */
    private DimensionInfoDto toDimensionInfoDto(Dimension dimension) {
        DimensionInfoDto dimensionInfoDto = new DimensionInfoDto();
        dimensionInfoDto.setLength(dimension.getLength());
        dimensionInfoDto.setWidth(dimension.getWidth());
        dimensionInfoDto.setHeight(dimension.getHeight());
        dimensionInfoDto.setUnit(dimension.getUnit());
        return dimensionInfoDto;
    }

    /**
     * 重量转换
     *
     * @param weight
     * @return
     */
    private WeightInfoDto toWeightInfoDto(Weight weight) {
        WeightInfoDto weightInfoDto = new WeightInfoDto();
        weightInfoDto.setValue(weight.getValue());
        weightInfoDto.setUnit(WeightTypeEnum.of(weight.getUnit().getCode()));
        return weightInfoDto;
    }

    /**
     * 金额转换
     *
     * @param money
     * @return
     */
    private MoneyInfoDto toMoneyInfoDto(Money money) {
        MoneyInfoDto moneyInfoDto = new MoneyInfoDto();
        moneyInfoDto.setAmount(money.getAmount());
        moneyInfoDto.setCurrencyCode(money.getCurrency());
        return moneyInfoDto;
    }

    /**
     * 发货人地址信息转换
     *
     * @param addressInfo
     * @return
     */
    private AddressInfoDto toAddressInfoDto(AddressInfo addressInfo) {
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        if (null == addressInfo) {
            return addressInfoDto;
        }
        addressInfoDto.setProvinceNo(addressInfo.getProvinceNo());
        addressInfoDto.setProvinceName(addressInfo.getProvinceName());
        addressInfoDto.setCityNo(addressInfo.getCityNo());
        addressInfoDto.setCityName(addressInfo.getCityName());
        addressInfoDto.setCountyNo(addressInfo.getCountyNo());
        addressInfoDto.setCountyName(addressInfo.getCountyName());
        addressInfoDto.setTownNo(addressInfo.getTownNo());
        addressInfoDto.setTownName(addressInfo.getTownName());
        addressInfoDto.setAddress(addressInfo.getAddress());
        addressInfoDto.setCoordinateType(CoordinateTypeEnum.fromCode(addressInfo.getCoordinateType()));
        addressInfoDto.setLongitude(addressInfo.getLongitude());
        addressInfoDto.setLatitude(addressInfo.getLatitude());
        addressInfoDto.setProvinceNoGis(addressInfo.getProvinceNoGis());
        addressInfoDto.setProvinceNameGis(addressInfo.getProvinceNameGis());
        addressInfoDto.setCityNoGis(addressInfo.getCityNoGis());
        addressInfoDto.setCityNameGis(addressInfo.getCityNameGis());
        addressInfoDto.setCountyNoGis(addressInfo.getCountyNoGis());
        addressInfoDto.setCountyNameGis(addressInfo.getCountyNameGis());
        addressInfoDto.setTownNoGis(addressInfo.getTownNoGis());
        addressInfoDto.setTownNameGis(addressInfo.getTownNameGis());
        addressInfoDto.setAddressGis(addressInfo.getAddressGis());
        addressInfoDto.setPreciseGis(addressInfo.getPreciseGis());
        addressInfoDto.setChinaPostAddressCode(addressInfo.getChinaPostAddressCode());
        addressInfoDto.setFenceTrusted(addressInfo.getFenceTrusted());
        //jiangwei
        addressInfoDto.setFenceInfos(this.toFenceInfoDtos(addressInfo.getFenceInfos()));
        addressInfoDto.setAddressSource(addressInfo.getAddressSource());
        addressInfoDto.setRegionNo(addressInfo.getRegionNo());
        addressInfoDto.setRegionName(addressInfo.getRegionName());
        addressInfoDto.setEnCityName(addressInfo.getEnCityName());
        addressInfoDto.setEnAddress(addressInfo.getEnAddress());
        addressInfoDto.setPoiCode(addressInfo.getPoiCode());
        addressInfoDto.setPoiName(addressInfo.getPoiName());
        addressInfoDto.setHouseNumber(addressInfo.getHouseNumber());
        addressInfoDto.setExtendProps(addressInfo.getExtendProps());
        return addressInfoDto;
    }

    /**
     * 围栏信息列表转换
     * @param fenceInfos
     * @return
     */
    private List<FenceInfoDto> toFenceInfoDtos(List<FenceInfo> fenceInfos) {
        if (CollectionUtils.isEmpty(fenceInfos)) {
            return Collections.emptyList();
        }

        return fenceInfos.stream().map(this::toFenceInfoDto).collect(Collectors.toList());
    }

    /**
     * 围栏对象转换
     * @param fenceInfo
     * @return
     */
    private FenceInfoDto toFenceInfoDto(FenceInfo fenceInfo) {
        FenceInfoDto fenceInfoDto = new FenceInfoDto();
        fenceInfoDto.setFenceId(fenceInfo.getFenceId());
        fenceInfoDto.setFenceType(fenceInfo.getFenceType());
        return fenceInfoDto;
    }

    /**
     * 解决方案系信息转换
     *
     * @param facade
     * @return
     */
    private BusinessSolutionInfoDto toBusinessSolutionInfoDto(BusinessSolutionFacade facade) {
        if (facade == null) {
            return null;
        }
        BusinessSolutionInfoDto infoDto = new BusinessSolutionInfoDto();
        infoDto.setBusinessSolutionNo(facade.getBusinessSolutionNo());
        infoDto.setBusinessSolutionName(facade.getBusinessSolutionName());
        infoDto.setProductAttrs(facade.getProductAttrs());
        return infoDto;
    }

    /**
     * 协议信息转换
     * @param agreementFacades
     * @return
     */
    private List<AgreementInfoDto> toAgreementInfoDtos(List<AgreementFacade> agreementFacades) {
        if (CollectionUtils.isEmpty(agreementFacades)) {
            return null;
        }

        List<AgreementInfoDto> agreementInfoDtos = new ArrayList<>(agreementFacades.size());
        for (AgreementFacade agreementFacade : agreementFacades) {
            if (null == agreementFacade) {
                continue;
            }
            AgreementInfoDto agreementInfoDto = new AgreementInfoDto();
            agreementInfoDto.setAgreementType(agreementFacade.getAgreementType());
            agreementInfoDto.setAgreementId(agreementFacade.getAgreementId());
            agreementInfoDto.setSigner(agreementFacade.getSigner());
            agreementInfoDto.setSigningTime(agreementFacade.getSigningTime());
            agreementInfoDto.setExtendProps(agreementFacade.getExtendProps());
            agreementInfoDtos.add(agreementInfoDto);
        }
        return agreementInfoDtos;
    }

    /**
     * 拦截信息转换
     */
    private InterceptInfoDto toInterceptInfoDto(InterceptFacade interceptFacade) {
        if (interceptFacade == null) {
            return null;
        }
        InterceptInfoDto interceptInfoDto = new InterceptInfoDto();
        interceptInfoDto.setInterceptAddressInfoDto(toAddressInfoDto(interceptFacade.getInterceptAddress()));
        interceptInfoDto.setInterceptHandlingMode(interceptFacade.getInterceptHandlingMode());
        interceptInfoDto.setInterceptStationNo(interceptFacade.getInterceptStationNo());
        interceptInfoDto.setInterceptResultTime(interceptFacade.getInterceptResultTime());
        return interceptInfoDto;
    }

    /**
     * 发货仓信息转换
     * @param warehouseInfo
     * @return
     */
    private WarehouseInfoDto toWarehouseInfoDto(WarehouseInfo warehouseInfo) {
        if(null == warehouseInfo){
            return null;
        }
        WarehouseInfoDto warehouseInfoDto = new WarehouseInfoDto();
        warehouseInfoDto.setWarehouseNo(warehouseInfo.getWarehouseNo());
        warehouseInfoDto.setWarehouseName(warehouseInfo.getWarehouseName());
        warehouseInfoDto.setWarehouseSource(warehouseInfo.getWarehouseSource());
        // 京东仓库编码
        warehouseInfoDto.setActualWarehouseNo(warehouseInfo.getActualWarehouseNo());
        return warehouseInfoDto;
    }

    /**
     * 履约信息
     * @param fulfillmentFacade
     * @return
     */
    private FulfillmentInfoDto toFulfillmentInfoDto(FulfillmentFacade fulfillmentFacade) {
        FulfillmentInfoDto fulfillmentInfoDto = new FulfillmentInfoDto();
        if (fulfillmentFacade == null) {
            return fulfillmentInfoDto;
        }
        fulfillmentInfoDto.setFulfillmentSign(fulfillmentFacade.getFulfillmentSign());
        fulfillmentInfoDto.setExtendProps(fulfillmentFacade.getExtendProps());

        if (fulfillmentFacade.getActualReceivedQuantity() != null) {
            QuantityInfoDto quantityInfoDto = new QuantityInfoDto();
            quantityInfoDto.setValue(fulfillmentFacade.getActualReceivedQuantity().getValue());
            quantityInfoDto.setUnit(fulfillmentFacade.getActualReceivedQuantity().getUnit());
            fulfillmentInfoDto.setActualReceivedQuantity(quantityInfoDto);
        }

        fulfillmentInfoDto.setActualPickupTime(fulfillmentFacade.getActualPickupTime());
        fulfillmentInfoDto.setActualSignedTime(fulfillmentFacade.getActualSignedTime());

        if (fulfillmentFacade.getPackageMaxLen() != null) {
            LengthInfoDto lengthInfoDto = new LengthInfoDto();
            lengthInfoDto.setValue(fulfillmentFacade.getPackageMaxLen().getValue());
            lengthInfoDto.setUnit(fulfillmentFacade.getPackageMaxLen().getUnit());
            fulfillmentInfoDto.setPackageMaxLen(lengthInfoDto);
        }
        return fulfillmentInfoDto;
    }

    /**
     * 跨境报关信息转换
     */
    private CustomsInfoDto toCustomsInfoDto(CustomsFacade customsFacade) {
        if (customsFacade == null) {
            return null;
        }
        return CustomsFacadeMapper.INSTANCE.toCustomsInfoDto(customsFacade);
    }

    /**
     * 附件信息转换
     */
    private List<AttachmentInfoDto> toAttachmentInfoDtos(List<AttachmentFacade> attachmentFacades) {
        if (CollectionUtils.isEmpty(attachmentFacades)) {
            return null;
        }
        return AttachmentFacadeMapper.INSTANCE.toAttachmentInfoDtos(attachmentFacades);
    }


    /**
     * 财务信息转换
     * @param facadeResponse
     * @return
     */
    public ExpressOrderModelCreator toExpressOrderFinanceCreator(GetOrderFacadeResponse facadeResponse) {
        if (facadeResponse == null) {
            return null;
        }
        ExpressOrderModelCreator creator = new ExpressOrderModelCreator();
        //业务身份
        creator.setBusinessIdentity(this.toOrderBusinessIdentity(facadeResponse.getBusinessIdentity()));
        creator.setOrderNo(facadeResponse.getOrderNo());
        creator.setCustomOrderNo(facadeResponse.getCustomOrderNo());
        //交易关联单
        creator.setRefOrder(this.toRefOrderInfoDto(facadeResponse));
        //财务信息
        creator.setFinanceInfo(this.toFinanceInfoDto(facadeResponse.getFinance()));
        return creator;
    }
}