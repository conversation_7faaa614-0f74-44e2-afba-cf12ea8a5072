package cn.jdl.oms.express.domain.infrs.acl.pl.customer;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import com.jd.ldop.basic.dto.BasicTraderInfoDTO;
import com.jd.ldop.basic.dto.BasicTraderInfoFusionDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * 客户信息转换
 */
@Translator
public class CustomerTranslator {

    public BasicTraderResponse toBasicTraderResponse(BasicTraderInfoDTO basicTraderInfoDTO){
        BasicTraderResponse response = new BasicTraderResponse();
        response.setCollectionUpperValue(basicTraderInfoDTO.getCollectionUpperValue());
        response.setFreshColdChain(basicTraderInfoDTO.getFreshColdChain());
        response.setFreshFoodDelivery(basicTraderInfoDTO.getFreshFoodDelivery());
        response.setGuaranteeUpperValue(basicTraderInfoDTO.getGuaranteeUpperValue());
        response.setTraderSign(basicTraderInfoDTO.getTraderSign());
        response.setTraderOperateState(basicTraderInfoDTO.getTraderOperateState());
        response.setTraderMold(basicTraderInfoDTO.getTraderMold());
        response.setSubTraderMold(basicTraderInfoDTO.getSubTraderMold());
        response.setPriceConfirmed(basicTraderInfoDTO.getPriceConfirmed());
        response.setInsurePriceAmount(basicTraderInfoDTO.getInsurePriceAmount());
        response.setRepeatRejectAuditDays(basicTraderInfoDTO.getRepeatRejectAuditDays());
        response.setAirTransport(basicTraderInfoDTO.getAirTransport());
        response.setCustomerId(Long.valueOf(basicTraderInfoDTO.getId()));
        response.setCustomerName(basicTraderInfoDTO.getTraderName());
        response.setSignedCompany(basicTraderInfoDTO.getSignedCompany());
        response.setSignedOrg(basicTraderInfoDTO.getSignedOrg());
        response.setSpecialSignType(basicTraderInfoDTO.getSpecialSignType());
        response.setPreChargeMoney(basicTraderInfoDTO.getPreChargeMoney());
        response.setExternalUniqueCode(basicTraderInfoDTO.getExternalUniqueCode());
        return response;
    }

    public CustomerConfig toCustomerConfig(BasicTraderInfoDTO basicTraderInfoDTO) {
        if (null == basicTraderInfoDTO) {
            return null;
        }
        CustomerConfig customerConfig = new CustomerConfig();
        customerConfig.setTraderSign(basicTraderInfoDTO.getTraderSign());
        customerConfig.setCustomerId(Long.valueOf(basicTraderInfoDTO.getId()));
        customerConfig.setCustomerName(basicTraderInfoDTO.getTraderName());
        customerConfig.setSignedCompany(basicTraderInfoDTO.getSignedCompany());
        customerConfig.setSignedOrg(basicTraderInfoDTO.getSignedOrg());
        customerConfig.setTraderMold(basicTraderInfoDTO.getTraderMold());
        customerConfig.setSubTraderMold(basicTraderInfoDTO.getSubTraderMold());
        customerConfig.setTraderOperateState(basicTraderInfoDTO.getTraderOperateState());
        customerConfig.setPreChargeMoney(basicTraderInfoDTO.getPreChargeMoney());
        customerConfig.setPopId(basicTraderInfoDTO.getPopId());
        customerConfig.setExternalUniqueCode(basicTraderInfoDTO.getExternalUniqueCode());
        return customerConfig;
    }

    /**
     * 封装返回商家渠道信息
     * @param fusionDTO
     * @return
     */
    public BasicTraderFusionResponse toBasicTraderFusionResponse(BasicTraderInfoFusionDTO fusionDTO) {
        BasicTraderFusionResponse response = new BasicTraderFusionResponse();
        if (fusionDTO.getBasicTraderIntegrateDTO() != null) {
            response.setTraderChannel(fusionDTO.getBasicTraderIntegrateDTO().getTraderChannel());
        }
        return response;
    }

    public MultiAddressFacadeRequest toMultiAddressFacadeRequest(ExpressOrderContext context) {

        // 商家编码
        String traderCode = context.getOrderModel().getCustomer().getAccountNo();
        if (StringUtils.isBlank(traderCode)) {
            traderCode = context.getOrderModel().getOrderSnapshot().getCustomer().getAccountNo();
        }

        // 起始站点/揽收站点
        String startStationNo = context.getOrderModel().getShipment().getStartStationNo();
        if (StringUtils.isBlank(startStationNo)) {
            startStationNo = context.getOrderModel().getOrderSnapshot().getShipment().getStartStationNo();
        }

        String systemSubCaller;
        String businessScene = context.getOrderModel().getOrderBusinessIdentity().getBusinessScene();
        if (BusinessSceneEnum.CREATE.getCode().equals(businessScene)) {
            // 接单和修改传入的systemSubCaller可能不一致。接单取入参、其他取快照原单
            systemSubCaller = context.getOrderModel().getChannel().getSystemSubCaller();
        } else {
            systemSubCaller = context.getOrderModel().getOrderSnapshot().getChannel().getSystemSubCaller();
        }

        MultiAddressFacadeRequest request = new MultiAddressFacadeRequest();

        request.setTraderCode(traderCode);
        request.setStartStationNo(startStationNo);
        request.setSystemSubCaller(systemSubCaller);

        return request;
    }
}
