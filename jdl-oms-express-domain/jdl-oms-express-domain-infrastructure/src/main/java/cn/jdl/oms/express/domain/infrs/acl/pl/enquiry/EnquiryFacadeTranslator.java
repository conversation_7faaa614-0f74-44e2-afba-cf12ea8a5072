package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.ConsigneeMapper;
import cn.jdl.oms.express.domain.converter.CustomsMapper;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.CostInfoDto;
import cn.jdl.oms.express.domain.converter.ProductMapper;
import cn.jdl.oms.express.domain.dto.CredentialsDeliveryServiceDto;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.PointsInfoDto;
import cn.jdl.oms.express.domain.dto.QuantityInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.address.AddressBasicPrimaryWSFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.SurchargeFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingConvertor;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingInquiryTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.ProductAttrsUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ReaddressSettlementTypeUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AddressSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryModeEnum;
import cn.jdl.oms.express.domain.spec.dict.ModifyRecordTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceRequirementsEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.TicketSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.WarmLayerEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightingModeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.spec.util.ReceiptCurrencyUtil;
import cn.jdl.oms.express.domain.vo.Activity;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.CargoDelegate;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.DeductionDelegate;
import cn.jdl.oms.express.domain.vo.Dimension;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Enquiry;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Package;
import cn.jdl.oms.express.domain.vo.Points;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.SystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.lbs.product.inquiry.dto.request.DadaInquiryRequest;
import com.jd.lbs.product.inquiry.dto.request.DiscountDetailDTO;
import com.jd.lbs.product.inquiry.dto.request.ForwardInquiryRequest;
import com.jd.lbs.product.inquiry.dto.request.ProductParam;
import com.jd.lbs.product.inquiry.dto.request.StandardProductDetail;
import com.jd.lbs.product.inquiry.dto.request.StandardProductPackage;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.CALC_PRICE_ITEM_LIST;

/**
 * 询价防腐层数据转换器
 */
@Translator
public class EnquiryFacadeTranslator {

    private static final Logger LOGGER = LoggerFactory.getLogger(EnquiryFacadeTranslator.class);

    //揽收模式 默认传 0
    private static final String DEFAULT_COLLECTING_MODE = "0";
    // 仓配类型  默认传0 纯配
    private static final int DEFAULT_DISTRIBUTION_TYPE = 0;
    //单据状态 默认传0
    private static final int DEFAULT_ORDER_STATUS = 0;
    //订单状态 2 拒收
    private static final int ORDER_STATUS_CUSTOMER_REJECTED = 2;
    //融合入口
    private static final String NEW_ENTRANCE_RH = "1";
    //独立入口
    private static final String NEW_ENTRANCE_NOT_RH = "0";
    //达达询价扩展信息key
    private static final String DADA_INQUIRY_REQUEST_EXT_KEY ="dadaInquiryRequest";
    //不需要使用推荐优惠券
    private static final int NEED_ADVISED_FREIGHT_COUPON_NOT = 0;
    //达达上门取件默认时间
    private static final long DELAY_PUBLISH_TIME_DEFAULT = 0L;

    //计费折扣类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
    private static final int DISCOUNT_TYPE_DISCOUNT =1;
    private static final int DISCOUNT_TYPE_JD_TICKET =2;
    private static final int DISCOUNT_TYPE_DADA_TICKET =3;
    private static final int DISCOUNT_TYPE_JD_TICKET_BATCH_NO =4;

    // 1表示返回
    private static final String RETURN_MISMATCH = "1";

    /**
     * ucc配置
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    @Resource
    private AddressBasicPrimaryWSFacadeTranslator addressBasicPrimaryWSFacadeTranslator;

    @Resource
    private AddressBasicPrimaryWSFacade addressBasicPrimaryWSFacade;

    /**
     * 产品中心-附加费查询
     */
    @Resource
    private SurchargeFacadeTranslator surchargeFacadeTranslator;

    /**
     * 产品中心-附加费查询
     */
    @Resource
    private SurchargeFacade surchargeFacade;

    /**
     * 计费询价防腐层请求数据转换
     * 接单场景
     * 转换过程全用当前单信息
     */
    public BillingEnquiryFacadeRequest toBillingEnquiryFacadeRequest(ExpressOrderContext expressOrderContext) {
        return toBillingEnquiryFacadeRequest(expressOrderContext.getOrderModel());
    }

    /**
     * 计费询价防腐层请求数据转换
     * 接单场景
     * 转换过程全用当前单信息
     */
    public BillingEnquiryFacadeRequest toBillingEnquiryFacadeRequest(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        if (orderModel.getOrderBusinessIdentity() != null) {
            facadeRequest.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
        }
        facadeRequest.setOrderNo(orderModel.orderNo());
        facadeRequest.setOrderType(orderModel.getOrderType());
        facadeRequest.setOrderStatus(orderModel.getOrderStatus());
        //下单人编号
        facadeRequest.setOperator(orderModel.getOperator());
        //操作时间（订单中心服务接收到请求时的时间，接单场景是接单时间，修改是修改时间，取消是取消时间等）
        facadeRequest.setOperateTime(orderModel.getOperateTime());
        //青龙业主编码和青龙业主号名称
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = toCustomerFacadeDto(orderModel);
        facadeRequest.setCustomerFacadeDto(customerFacadeDto);
        //关联单号，正向运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        //客户、渠道
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));

        //产品信息
        if (OrderTypeEnum.READDRESS == orderModel.getOrderType()) {
            //产品信息
            // 改址单只询白名单中的增值产品 目前为改址增值服务
            facadeRequest.setProductFacadeDtoList(toReaddressProductFacadeDto(orderModel));
        } else {
            //产品信息
            facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel, true));
        }

        //货物信息，总重量、总体积、总数量
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel));
        //发件人信息
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(orderModel));
        //收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel));
        //配送相关信息
        facadeRequest.setShipmentFacadeDto(toShipmentFacadeDto(orderModel));
        //财务相关信息
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel));
        //订单快照
        facadeRequest.setOrderSnapshot(toOrderSnapshot(orderModel));
        //设置拓展字段
        facadeRequest.setExtendProps(toExtendProps(orderModel, false));
        // 改址拦截模式要素
        List<ModifyRecord> enabledThroughOrderModifyRecords = orderModel.getModifyRecordDelegate().getEnabledThroughOrderModifyRecords();
        if (CollectionUtils.isNotEmpty(enabledThroughOrderModifyRecords)) {
            setReaddressRecord(facadeRequest, enabledThroughOrderModifyRecords);
        }
        // 询价类型
        if(null != orderModel.getEnquiry() && null != orderModel.getEnquiry().getEnquiryMode()){
            facadeRequest.setInquiryType(orderModel.getEnquiry().getEnquiryMode().getPriceCode());
        }
        // 跨城急送 询价参数转换
        Map<String, Object> extendParam = facadeRequest.getExtendProps();
        if (null == extendParam) {
            extendParam = new HashMap<>();
            facadeRequest.setExtendProps(extendParam);
        }

        if (!orderModel.isO2O()) {
            String majorProductNo = orderModel.getProductDelegate().getMajorProductNo();
            if (ProductEnum.KCJS.getCode().equals(majorProductNo) || ProductEnum.TSSTC.getCode().equals(majorProductNo)) {
                facadeRequest.setWaiFa(true);
                ForwardInquiryRequest forwardInquiryFacadeRequest = this.toForwardInquiryFacadeRequest(orderModel);
                extendParam.put(EnquiryConstants.WAI_FA, JSONUtils.beanToJSONDefault(forwardInquiryFacadeRequest));
            }
        }

        return facadeRequest;
    }

    /**
     * 计费询价防腐层请求数据转换
     * 询价场景
     * 转换过程使用当前单、原单信息
     */
    public BillingEnquiryFacadeRequest toEnquirySceneFacadeRequest(ExpressOrderContext expressOrderContext, boolean useSnapshotAttachFees) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

        //交易业务单元：当前单
        if (orderModel.getOrderBusinessIdentity() != null) {
            facadeRequest.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
        }

        //订单号：原单
        facadeRequest.setOrderNo(orderSnapshot.orderNo());

        //订单类型：原单
        facadeRequest.setOrderType(orderSnapshot.getOrderType());

        //订单状态：原单
        facadeRequest.setOrderStatus(orderSnapshot.getOrderStatus());

        //操作人：当前单
        facadeRequest.setOperator(orderModel.getOperator());

        //操作时间：当前时间
        facadeRequest.setOperateTime(new Date());

        //客户信息：原单
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = toCustomerFacadeDto(orderSnapshot);
        facadeRequest.setCustomerFacadeDto(customerFacadeDto);

        //关联单号（运单号）：原单
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        //渠道信息：当前单
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));

        //产品信息：当前单没有取原单
        if (orderModel.getProductDelegate() != null && !orderModel.getProductDelegate().isEmpty()) {
            if (OrderTypeEnum.READDRESS == orderSnapshot.getOrderType()) {
                facadeRequest.setProductFacadeDtoList(toReaddressProductFacadeDto(orderModel));
            } else {
                facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel, useSnapshotAttachFees));
            }
        } else {
            if (OrderTypeEnum.READDRESS == orderSnapshot.getOrderType()) {
                facadeRequest.setProductFacadeDtoList(toReaddressProductFacadeDto(orderSnapshot));
            } else {
                facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderSnapshot, useSnapshotAttachFees));
            }
        }

        //货品信息（总重量、总体积、总数量）：当前单询价信息
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel.getEnquiry()));

        //发件人信息：原单，先根据始发站点查询，查不到才使用订单上的数据
        facadeRequest.setConsignorFacadeDto(toEnquirySceneConsignorFacadeDto(expressOrderContext));

        //收件人信息：原单
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderSnapshot));

        //配送相关信息：原单
        facadeRequest.setShipmentFacadeDto(toShipmentFacadeDto(orderSnapshot));

        //财务相关信息：原单
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderSnapshot));

        //订单快照：当前单（主要是获取当前单的快照，再设置到入参原单支付方式，因此用当前单）
        facadeRequest.setOrderSnapshot(toOrderSnapshot(orderModel));

        //拓展字段：询价信息用当前单，其余用原单
        facadeRequest.setExtendProps(toEnquirySceneExtendProps(orderModel, orderSnapshot));

        // 询价类型
        if(null != orderModel.getEnquiry() && null != orderModel.getEnquiry().getEnquiryMode()){
            facadeRequest.setInquiryType(orderModel.getEnquiry().getEnquiryMode().getPriceCode());
        }

        return facadeRequest;
    }

    /**
     * O2O转换逻辑
     *
     * @param expressOrderContext
     */
    public BillingEnquiryFacadeRequest toO2OBillingEnquiryFacadeRequest(ExpressOrderContext expressOrderContext) {
        //标准询价参数转换
        BillingEnquiryFacadeRequest facadeRequest = this.toBillingEnquiryFacadeRequest(expressOrderContext);
        //o2o询价参数转换
        Map<String, Object> extendProps = facadeRequest.getExtendProps();
        if (extendProps == null) {
            extendProps = new HashMap<>();
        }
        DadaInquiryRequest dadaInquiryRequest = new DadaInquiryRequest();

        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        //标识独立入口还是融合入口 1-融合入口 0-独立入口
        if (SystemSubCallerEnum.EXPRESS.getCode().equals(orderModel.getChannel().getSystemSubCaller())) {
            dadaInquiryRequest.setNewEntrance(NEW_ENTRANCE_RH);
        } else {
            dadaInquiryRequest.setNewEntrance(NEW_ENTRANCE_NOT_RH);
        }
        //	当前登陆pin
        dadaInquiryRequest.setPin(orderModel.getOperator());
        //是否使用推荐优惠券 0：不需要，1：需要
        dadaInquiryRequest.setNeedAdvisedFreightCoupon(NEED_ADVISED_FREIGHT_COUPON_NOT);

        //用户主动选择了传用户主动选择的时间切片的开始时间点,用户未主动选择，默认是立即上门，传0
        if (orderModel.getShipment().getExpectPickupStartTime() != null) {
            dadaInquiryRequest.setDelayPublishTime(orderModel.getShipment().getExpectPickupStartTime().getTime());
        } else {
            dadaInquiryRequest.setDelayPublishTime(DELAY_PUBLISH_TIME_DEFAULT);
        }
        //发货人手机号
        dadaInquiryRequest.setSupplierPhone(orderModel.getConsignor().getConsignorMobile());
        //发货详细地址
        dadaInquiryRequest.setSupplierAddress(orderModel.getConsignor().getConsignorFullAddressOfGis());
        //发货人地址纬度,火星坐标系
        if (StringUtils.isNotBlank(orderModel.getConsignor().getAddress().getLatitude())) {
            dadaInquiryRequest.setSupplierLat(new BigDecimal(orderModel.getConsignor().getAddress().getLatitude()).doubleValue());
        }
        //发货人地址经度,火星坐标系
        if(StringUtils.isNotBlank(orderModel.getConsignor().getAddress().getLongitude())){
            dadaInquiryRequest.setSupplierLng(new BigDecimal(orderModel.getConsignor().getAddress().getLongitude()).doubleValue());
        }
        //收货人手机号
        dadaInquiryRequest.setReceiverPhone(orderModel.getConsignee().getConsigneeMobile());
        //详细地址
        dadaInquiryRequest.setReceiverAddress(orderModel.getConsignee().getConsigneeFullAddressOfGis());
        //收货人地址纬度,火星坐标系
        if(StringUtils.isNotBlank(orderModel.getConsignee().getAddress().getLatitude())){
            dadaInquiryRequest.setReceiverLat(new BigDecimal(orderModel.getConsignee().getAddress().getLatitude()).doubleValue());
        }
        //收货人地址经度,火星坐标系
        if(StringUtils.isNotBlank(orderModel.getConsignee().getAddress().getLongitude())){
            dadaInquiryRequest.setReceiverLng(new BigDecimal(orderModel.getConsignee().getAddress().getLongitude()).doubleValue());
        }
        //京东城市code
        dadaInquiryRequest.setCityId(orderModel.getConsignor().getAddress().getCityNoGis());
        extendProps.put(DADA_INQUIRY_REQUEST_EXT_KEY,dadaInquiryRequest);

        facadeRequest.setExtendProps(extendProps);

        return facadeRequest;
    }

    /**
     * @description 功能描述: 拓展字段赋值逻辑
     *
     */
    private Map<String, Object> toModifyExtendProps(ExpressOrderModel orderModel, ExpressOrderModel snapshot, boolean firstReaddressMonth2Cash) {
        // 设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        // 揽收模式，（0、普通，默认；1、云柜弃用，若启用联系产品） TODO 目前需求还没有梳理出来，暂时默认为0
        extendParam.put(EnquiryConstants.COLLECTING_MODE, DEFAULT_COLLECTING_MODE);
        // 结算方式
        if (firstReaddressMonth2Cash) {
            extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, "2");
        } else {
            extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(orderModel));
        }
        // 单据状态 默认传0，逆向合并支付场景的原单询价传2会 再次调用 setReverseExtendProps 这个方法
        extendParam.put(EnquiryConstants.ORDER_STATUS, DEFAULT_ORDER_STATUS);
        // 温层类型  始发站编码 有则传，没有则不传
        extendParamToWarmLayerBusinessHallCode(extendParam, orderModel);
        // 下单渠道--散客业务涉及，计费会根据端上的渠道设置相应的折扣
        extendParam.put(EnquiryConstants.ORDER_CHANNEL, orderModel.getAttachment(AttachmentKeyEnum.ORDER_MEDIUM.getKey()));
        // 仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, DEFAULT_DISTRIBUTION_TYPE);
        // 校园快递类型，TODO 目前需求还没有梳理出来，暂时不设置值
        //extendParam.put("campusCourier", )
        // 营销信息，折扣信息
        extendParam.put(EnquiryConstants.DISCOUNT_DETAIL, ToModifyDiscountDetailList(orderModel, snapshot));
        // 积分信息
        extendParamToRewardPoints(extendParam, orderModel);
        // 包裹信息
        extendParamToPackageInformation(extendParam, orderModel);

        // 跨境报关信息：跨境业务类型、是否文件、币种
        Customs customs = CustomsMapper.INSTANCE.updateCustoms(
                orderModel.getCustoms()
                , CustomsMapper.INSTANCE.copyCustoms(snapshot.getCustoms())
        );
        extendParamToCustoms(extendParam, customs, GetFieldUtils.getSettlementType(orderModel));
        return extendParam;
    }

    /**
     * @description 功能描述: C2C拓展字段赋值逻辑
     * <AUTHOR>
     * @date 2021/6/29 10:07
     * @param orderModel
     * @throws
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String, Object> toExtendProps(ExpressOrderModel orderModel, boolean firstReaddressMonth2Cash) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //揽收模式，（0、普通，默认；1、云柜弃用，若启用联系产品） TODO 目前需求还没有梳理出来，暂时默认为0
        extendParam.put(EnquiryConstants.COLLECTING_MODE, DEFAULT_COLLECTING_MODE);
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(orderModel));
        if (firstReaddressMonth2Cash) {
            extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, "2");
        }
        //单据状态 默认传0，逆向合并支付场景的原单询价传2会 再次调用 setReverseExtendProps 这个方法
        extendParam.put(EnquiryConstants.ORDER_STATUS, DEFAULT_ORDER_STATUS);
        // 温层类型  始发站编码 有则传，没有则不传
        extendParamToWarmLayerBusinessHallCode(extendParam,orderModel);
        //下单渠道--散客业务涉及，计费会根据端上的渠道设置相应的折扣
        extendParam.put(EnquiryConstants.ORDER_CHANNEL, orderModel.getAttachment(AttachmentKeyEnum.ORDER_MEDIUM.getKey()));
        //仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, DEFAULT_DISTRIBUTION_TYPE);
        //校园快递类型，TODO 目前需求还没有梳理出来，暂时不设置值
        //extendParam.put("campusCourier", )
        //营销信息，折扣信息
        extendParam.put(EnquiryConstants.DISCOUNT_DETAIL, extendParamToDiscountDetailList(orderModel));
        //积分信息
        extendParamToRewardPoints(extendParam,orderModel);
        //包裹信息
        extendParamToPackageInformation(extendParam,orderModel);

        //跨境出口
        if(orderModel.isHKMO() || orderModel.isIntl()){
            if(null != orderModel.getConsignor()
                    && null != orderModel.getConsignor().getAddress()
                    && StringUtils.isNotBlank(orderModel.getConsignor().getAddress().getRegionNo())
                    && null != orderModel.getConsignee()
                    && null != orderModel.getConsignee().getAddress()
                    && StringUtils.isNotBlank(orderModel.getConsignee().getAddress().getRegionNo())
            ){
                String startRegionNo = orderModel.getConsignor().getAddress().getRegionNo();
                String endRegionNo = orderModel.getConsignee().getAddress().getRegionNo();
                //跨境类型
                extendParam.put(EnquiryConstants.CROSS_BORDER_TYPE,startRegionNo + "_" + endRegionNo);
                if(null != orderModel.getFinance() && null != orderModel.getFinance().getSettlementType()){
                    //换汇币种
                    // fixme 当前港澳改址改派只有同城改址，原单结算方式和改址结算方式不同不影响换汇币种；后续放开非同城改址需要考虑传改址结算方式
                    extendParam.put(EnquiryConstants.EXCHANGE_CURRENCY,
                            ReceiptCurrencyUtil.getCurrency(startRegionNo, endRegionNo, orderModel.getFinance().getSettlementType().getCode()).getCode());
                }
            }
            if(null != orderModel.getCustoms()){
                //是否是文件
                extendParam.put(EnquiryConstants.FILE_TAG,orderModel.getCustoms().getFileTag());
            }
        }

        // 证件配送服务
        if (StringUtils.isNotBlank(orderModel.getShipment().getServiceRequirementByKey(ServiceRequirementsEnum.CREDENTIALS_DELIVERY_SERVICE))) {
            CredentialsDeliveryServiceDto serviceDto = JSONUtils.jsonToBean(orderModel.getShipment().getServiceRequirementByKey(ServiceRequirementsEnum.CREDENTIALS_DELIVERY_SERVICE), CredentialsDeliveryServiceDto.class);
            if (null != serviceDto) {
                extendParam.put(EnquiryConstants.TARIFF_TYPE, serviceDto.getFeeType());
                extendParam.put(EnquiryConstants.SELLER_DEPT_ID, serviceDto.getDepartment());
                extendParam.put(EnquiryConstants.FLOW_DIRECTION_TYPE, serviceDto.getDeliveryMode());
            }
        }
        return extendParam;
    }

    /**
     * 询价场景扩展字段
     */
    private Map<String, Object> toEnquirySceneExtendProps(ExpressOrderModel enquiryOrderModel, ExpressOrderModel orderSnapshot) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //揽收模式（0-标准价（默认）、1-高铁非标价、2-机场非标价、3-其他）
        if (null == enquiryOrderModel.getEnquiry() || StringUtils.isBlank(enquiryOrderModel.getEnquiry().getExtendProps(AttachmentKeyEnum.COLLECTING_MODE.getKey()))) {
            extendParam.put(EnquiryConstants.COLLECTING_MODE, DEFAULT_COLLECTING_MODE);
        } else {
            extendParam.put(EnquiryConstants.COLLECTING_MODE, enquiryOrderModel.getEnquiry().getExtendProps(AttachmentKeyEnum.COLLECTING_MODE.getKey()));
        }

        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(orderSnapshot));
        //单据状态 默认传0，逆向合并支付场景的原单询价传2会 再次调用 setReverseExtendProps 这个方法
        extendParam.put(EnquiryConstants.ORDER_STATUS, DEFAULT_ORDER_STATUS);
        // 温层类型  始发站编码 有则传，没有则不传
        extendParamToWarmLayerBusinessHallCode(extendParam,orderSnapshot);
        //下单渠道--散客业务涉及，计费会根据端上的渠道设置相应的折扣
        extendParam.put(EnquiryConstants.ORDER_CHANNEL, orderSnapshot.getAttachment(AttachmentKeyEnum.ORDER_MEDIUM.getKey()));
        //仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, DEFAULT_DISTRIBUTION_TYPE);
        //校园快递类型，TODO 目前需求还没有梳理出来，暂时不设置值
        //extendParam.put("campusCourier", )
        // 营销信息需要处理全量覆盖和删除情况
        Map<String,String> modifiedFields = enquiryOrderModel.getModifiedFields();
        String modifiedFieldValue = null;
        if(MapUtils.isNotEmpty(modifiedFields)){
            modifiedFieldValue = enquiryOrderModel.getModifiedFields().get(ModifiedFieldEnum.OPERATION_DISCOUNT_INFOS.getCode());
        }
        List<DiscountDetailDTO> snapshotDiscountDetailS = new ArrayList<>();
        List<DiscountDetailDTO> orderDiscountDetailS = new ArrayList<>();
        // 如果没有则只看快照中的营销折扣
        // 如果是全量覆盖则只看入参里的营销折扣
        // 如果是快照和入参的营销折扣信息都不看
        if(modifiedFieldValue == null){
            snapshotDiscountDetailS = extendParamToDiscountDetailList(orderSnapshot);
            //营销信息，折扣信息-当前单-询价传入
            orderDiscountDetailS = extendParamToDiscountDetailList(enquiryOrderModel, false);
        } else if(ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldValue)){
            snapshotDiscountDetailS = extendParamToDiscountDetailList(orderSnapshot,false);
            //营销信息，折扣信息-当前单-询价传入
            orderDiscountDetailS = extendParamToDiscountDetailList(enquiryOrderModel);
        } else if(ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldValue)){
            snapshotDiscountDetailS = extendParamToDiscountDetailList(orderSnapshot,false);
            //营销信息，折扣信息-当前单-询价传入
            orderDiscountDetailS = extendParamToDiscountDetailList(enquiryOrderModel,false);
        }
        //营销信息，折扣信息-原单-下单传入
        extendParam.put(EnquiryConstants.DISCOUNT_DETAIL,CollectionUtils.union(snapshotDiscountDetailS,orderDiscountDetailS));
        //积分信息
        extendParamToRewardPoints(extendParam,orderSnapshot);
        //是否首单折扣
        extendParamToIsFirstOrder(extendParam, orderSnapshot);
        //询价模式 TODO 港澳误用 [除了fr-m-0003 整车直达传 1，其他都传 0 或不传，0表示全部费用，1表示增值费用，默认为0]
        //extendParamToInquiryType(extendParam, enquiryOrderModel);
        //跨境报关信息：跨境业务类型、是否文件、币种
        extendParamToCustoms(extendParam, orderSnapshot);
        //包裹信息：取询价参数的扩展信息，传enquiryOrderModel
        extendParamToPackageInformation(extendParam, enquiryOrderModel);
        // 是否返回折扣未匹配原因，1表示返回，不传默认为0，当为1时，返回入参中的指定折扣编码或优惠券编码未匹配使用的原因
        extendParam.put(EnquiryConstants.RETURN_MISMATCH, RETURN_MISMATCH);
        // 对外调用应该使用上下游流程中的实际业务身份
        if (orderSnapshot.isUnitedIdentity()) {
            extendParam.put(EnquiryConstants.BUSINESS_UNIT, BusinessUnitEnum.CN_JDL_UNITED_C2C.businessUnit());
        } else if (orderSnapshot.isC2C()){
            extendParam.put(EnquiryConstants.BUSINESS_UNIT, orderSnapshot.getBusinessIdentity().getBusinessUnit());
        }
        if (StringUtils.isNotBlank(orderSnapshot.getShipment().getEndStationTypeL3())) {
            extendParam.put(EnquiryConstants.SITE_THIRD_TYPE, orderSnapshot.getShipment().getEndStationTypeL3());
        }
        return extendParam;
    }

    /**
     * @description 功能描述: 设置逆向拓展字段
     * <AUTHOR>
     * @date 2021/7/1 19:55
     * @param billingEnquiryFacadeRequest
     * @throws
     * @return void
     */
    public void toSetReverseExtendProps(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        // 这个ExtendProps 是一定有的 ，在 toBillingEnquiryFacadeRequest 这个方法调用后再调用这个方法
        billingEnquiryFacadeRequest.getExtendProps().put(EnquiryConstants.ORDER_STATUS,toOrderStatus(billingEnquiryFacadeRequest));
    }

    /**
     * @description 功能描述: 单据状态
     *  逆向合并支付场景中，原单（C2C取原始正向单, 只取结算方式为到付现结的单子）询价传2-拒收，其他单据均传0
     * <AUTHOR>
     * @date 2021/7/1 18:13
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    /**
     *
     */
    private Integer toOrderStatus(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        // ReverseOrderInfo != null，则表示为逆向单原单询价,此时如果逆向单为到付现结且原单为到付现结，则返回2
        if (billingEnquiryFacadeRequest.getReverseOrderInfo() != null
                && SettlementTypeEnum.CASH_ON_DELIVERY == billingEnquiryFacadeRequest.getReverseOrderInfo().getSettlementType()
                && SettlementTypeEnum.CASH_ON_DELIVERY == billingEnquiryFacadeRequest.getFinanceFacadeDto().getSettlementType()) {
            return ORDER_STATUS_CUSTOMER_REJECTED;
        }
        // 其他情况 0
        return DEFAULT_ORDER_STATUS;
    }

    /**
     * 构建外发平台询价参数
     * @param orderModel
     * @return
     */
    public ForwardInquiryRequest toForwardInquiryFacadeRequest(ExpressOrderModel orderModel) {
        ForwardInquiryRequest fwdInquiryRequest = new ForwardInquiryRequest();
        Consignor consignor = orderModel.getConsignor();
        if (null != consignor) {
            fwdInquiryRequest.setSupplierName(consignor.getConsignorName());
            if (StringUtils.isNotEmpty(consignor.getConsignorMobile())) {
                fwdInquiryRequest.setSupplierPhone(consignor.getConsignorMobile());
            } else {
                fwdInquiryRequest.setSupplierPhone(consignor.getConsignorPhone());
            }

            if (null != consignor.getAddress()) {
                Address consignorAddr = consignor.getAddress();
                if (AddressSourceEnum.GIS.getCode().equals(consignorAddr.getAddressSource())) {
                    fwdInquiryRequest.setStartOneAddressName(consignorAddr.getProvinceNameGis());
                    fwdInquiryRequest.setStartTwoAddressName(consignorAddr.getCityNameGis());
                    fwdInquiryRequest.setStartThreeAddressName(consignorAddr.getCountyNameGis());
                    fwdInquiryRequest.setSupplierAddress(consignorAddr.getAddressGis());
                } else {
                    fwdInquiryRequest.setStartOneAddressName(consignorAddr.getProvinceName());
                    fwdInquiryRequest.setStartTwoAddressName(consignorAddr.getCityName());
                    fwdInquiryRequest.setStartThreeAddressName(consignorAddr.getCountyName());
                    fwdInquiryRequest.setSupplierAddress(consignorAddr.getAddress());
                }
                // 经纬度
                fwdInquiryRequest.setSupplierLat(consignorAddr.getLatitude());
                fwdInquiryRequest.setSupplierLng(consignorAddr.getLongitude());
                if (null != consignorAddr.getCoordinateType()) {
                    fwdInquiryRequest.setSupplierCoord(consignorAddr.getCoordinateType().getCode());
                }
            }
        }
        Consignee consignee = orderModel.getConsignee();
        if (null != consignee) {
            fwdInquiryRequest.setReceiverName(consignee.getConsigneeName());
            if (StringUtils.isNotEmpty(consignee.getConsigneeMobile())) {
                fwdInquiryRequest.setReceiverPhone(consignee.getConsigneeMobile());
            } else {
                fwdInquiryRequest.setReceiverPhone(consignee.getConsigneePhone());
            }

            if (null != consignee.getAddress()) {
                Address consigneeAddr = consignee.getAddress();
                if (AddressSourceEnum.GIS.getCode().equals(consigneeAddr.getAddressSource())) {
                    fwdInquiryRequest.setEndOneAddressName(consigneeAddr.getProvinceNameGis());
                    fwdInquiryRequest.setEndTwoAddressName(consigneeAddr.getCityNameGis());
                    fwdInquiryRequest.setEndThreeAddressName(consigneeAddr.getCountyNameGis());
                    fwdInquiryRequest.setReceiverAddress(consigneeAddr.getAddressGis());
                } else {
                    fwdInquiryRequest.setEndOneAddressName(consigneeAddr.getProvinceName());
                    fwdInquiryRequest.setEndTwoAddressName(consigneeAddr.getCityName());
                    fwdInquiryRequest.setEndThreeAddressName(consigneeAddr.getCountyName());
                    fwdInquiryRequest.setReceiverAddress(consigneeAddr.getAddress());
                }
                // 经纬度
                fwdInquiryRequest.setReceiverLat(consigneeAddr.getLatitude());
                fwdInquiryRequest.setReceiverLng(consigneeAddr.getLongitude());
                if (null != consigneeAddr.getCoordinateType()) {
                    fwdInquiryRequest.setReceiverCoord(consigneeAddr.getCoordinateType().getCode());
                }
            }
        }

        if (null != orderModel.getCargoDelegate() && !orderModel.getCargoDelegate().isEmpty()) {
            Cargo cargo = (Cargo) orderModel.getCargoDelegate().firstCargo();
            fwdInquiryRequest.setCargoName(cargo.getCargoName());
            Dimension cargoDimension = cargo.getCargoDimension();
            if (null != cargoDimension) {
                fwdInquiryRequest.setLength(cargoDimension.getLength().intValue());
                fwdInquiryRequest.setWidth(cargoDimension.getWidth().intValue());
                fwdInquiryRequest.setHeight(cargoDimension.getHeight().intValue());
            }
        }

        Shipment shipment = orderModel.getShipment();
        if (null != shipment) {
            fwdInquiryRequest.setExpectedCollectBeginTime(shipment.getExpectPickupStartTime());
            fwdInquiryRequest.setExpectedCollectEndTime(shipment.getExpectPickupEndTime());
            if (StringUtils.isNumeric(shipment.getShipperNo())) {
                fwdInquiryRequest.setProviderId(Integer.valueOf(shipment.getShipperNo()));
            } else {
                // 外发询价承运商编码必填
                LOGGER.error("承运商编码异常:{}", shipment.getShipperNo());
            }
        }

        // 标识独立入口还是融合入口 1-融合入口 0-独立入口
        Channel channel = orderModel.getChannel();
        Map<String, String> channelExt = channel.getExtendProps();
        if (null != channelExt) {
            String dadaEntrance = channelExt.get(AttachmentKeyEnum.DADA_ENTRANCE.getKey());
            if (StringUtils.isNotBlank(dadaEntrance)) {
                fwdInquiryRequest.setNewEntrance(Integer.valueOf(dadaEntrance));
            }
            fwdInquiryRequest.setOriginId(channelExt.get(AttachmentKeyEnum.ORIGIN_ID.getKey()));
        }

        // TODO
        fwdInquiryRequest.setChannelSource(channel.getSystemCaller().getCode());
        fwdInquiryRequest.setSecretSupplierIdentity(orderModel.getAttachment(AttachmentKeyEnum.CREATOR_PHONE.getKey())); // 订单主档扩展字段
        fwdInquiryRequest.setNeedAdvisedFreightCoupon(NEED_ADVISED_FREIGHT_COUPON_NOT); // 和 wangsongxiang 确认，订单写死0即可

        return fwdInquiryRequest;
    }

    /**
     * @description 功能描述: C2C拓展字段温层和始发站编码
     * <AUTHOR>
     * @date 2021/6/29 10:07
     * @param orderModel
     * @throws
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private void extendParamToWarmLayerBusinessHallCode(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        //营业厅编码或站点id
        if (orderModel.getShipment() != null) {
            extendParam.put(EnquiryConstants.BUSINESS_HALL_CODE,orderModel.getShipment().getStartStationNo());
            //温层属性
            WarmLayerEnum warmLayer = orderModel.getShipment().getWarmLayer();
            if (warmLayer != null) {
                // TODO 预分拣不改这个类
                extendParam.put(EnquiryConstants.WARM_LAYER, warmLayer.getCode());
            }
        }
    }

    /**
     * @description 功能描述: C2C拓展字段之积分信息
     * <AUTHOR>
     * @date 2021/6/29 10:21
     * @param extendParam
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    public void extendParamToRewardPoints(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        if (orderModel.getFinance() != null
                && orderModel.getFinance().getPoints()!= null
                &&  orderModel.getFinance().getPoints().getRedeemPointsQuantity() != null
                &&  orderModel.getFinance().getPoints().getRedeemPointsQuantity().getValue() != null) {
            extendParam.put(EnquiryConstants.REWARD_POINTS,  orderModel.getFinance().getPoints().getRedeemPointsQuantity().getValue().intValue());
        }
    }

    /**
     * 营销信息，折扣信息
     * @param orderModel
     * @param snapshot
     * @return
     */
    public List<DiscountDetailDTO> ToModifyDiscountDetailList(ExpressOrderModel orderModel, ExpressOrderModel snapshot) {
        List<DiscountDetailDTO> detailList = new ArrayList<>();
        // 修改场景 如果修改营销信息，则当前单存在全量信息，但是需要过滤删除的产品
        Map<String, String> modifiedFields = orderModel.getModifiedFields();
        if (null == modifiedFields) { modifiedFields = Collections.emptyMap(); }
        // 优惠券
        if (modifiedFields.containsKey(ModifiedFieldEnum.TICKET_INFOS.getCode())) {
            // 获取未删除的优惠券信息
            orderModel.getPromotion().getTickets().stream()
                    .filter(ticket -> ticket.getOperateType() != OperateTypeEnum.DELETE)
                    .forEach(ticket -> toDiscountDetail(ticket, orderModel.getOperator(), detailList));
        } else if (snapshot != null && snapshot.getPromotion() != null && snapshot.getPromotion().getTickets() != null) {
            snapshot.getPromotion().getTickets().stream()
                    .forEach(ticket -> toDiscountDetail(ticket, snapshot.getOperator(), detailList));
        }
        // 折扣
        if (modifiedFields.containsKey(ModifiedFieldEnum.DISCOUNT_INFOS.getCode())) {
            // 获取未删除的折扣信息
            orderModel.getPromotion().getDiscounts().stream()
                    .filter(discount -> discount.getOperateType() != OperateTypeEnum.DELETE)
                    .forEach(discount -> toDiscountDetail(discount, detailList));
        } else if (snapshot != null && snapshot.getPromotion() != null && snapshot.getPromotion().getDiscounts() != null) {
            snapshot.getPromotion().getDiscounts().stream()
                    .forEach(discount -> toDiscountDetail(discount, detailList));
        }
        // 营销折扣
        if (modifiedFields.containsKey(ModifiedFieldEnum.OPERATION_DISCOUNT_INFOS.getCode())) {
            // 获取未删除的营销折扣信息
            orderModel.getPromotion().getOperationDiscountInfos().stream()
                    .filter(discount -> discount.getOperateType() != OperateTypeEnum.DELETE)
                    .forEach(discount -> toDiscountDetail(discount, detailList));
        } else if (snapshot != null && snapshot.getPromotion() != null && snapshot.getPromotion().getOperationDiscountInfos() != null) {
            snapshot.getPromotion().getOperationDiscountInfos().stream()
                    .forEach(discount -> toDiscountDetail(discount, detailList));
        }

        return detailList;
    }

    private void toDiscountDetail(Discount discount, List<DiscountDetailDTO> detailList) {
        DiscountDetailDTO discountDetail = new DiscountDetailDTO();
        discountDetail.setDiscountNo(discount.getDiscountNo());
        //优惠类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
        discountDetail.setType(DISCOUNT_TYPE_DISCOUNT);
        detailList.add(discountDetail);
    }

    private void toDiscountDetail(Ticket ticket, String operator, List<DiscountDetailDTO> detailList) {
        DiscountDetailDTO discountDetail = new DiscountDetailDTO();
        //优惠类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
        if (ticket.getTicketSource() != null && TicketSourceEnum.DADA.getCode().equals(ticket.getTicketSource())) {
            discountDetail.setDiscountNo(ticket.getTicketNo());
            discountDetail.setType(DISCOUNT_TYPE_DADA_TICKET);
        } else {
            if (StringUtils.isNotBlank(ticket.getTicketBatchNo())) {
                discountDetail.setDiscountNo(ticket.getTicketBatchNo());
                discountDetail.setType(DISCOUNT_TYPE_JD_TICKET_BATCH_NO);
            } else {
                discountDetail.setDiscountNo(ticket.getTicketNo());
                discountDetail.setType(DISCOUNT_TYPE_JD_TICKET);
            }
        }
        // TODO 是否需要用下单人的
        discountDetail.setUserNo(operator);
        detailList.add(discountDetail);
    }

    /**
     * @description 功能描述: C2C拓展字段之营销信息，折扣信息
     * <AUTHOR>
     * @date 2021/6/29 10:21
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    public List<DiscountDetailDTO> extendParamToDiscountDetailList(ExpressOrderModel orderModel) {
        return extendParamToDiscountDetailList(orderModel, true);
    }

    /**
     * @description 功能描述: C2C拓展字段之营销信息，折扣信息
     * <AUTHOR>
     * @date 2021/6/29 10:21
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    public List<DiscountDetailDTO> extendParamToDiscountDetailList(ExpressOrderModel orderModel, boolean operationDiscountFlag) {
        List<DiscountDetailDTO> detailList = new ArrayList<>();
        if (orderModel.getPromotion()!= null) {
            //优惠券信息赋值逻辑
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getTickets())) {
                for (Ticket ticket : orderModel.getPromotion().getTickets()) {
                    DiscountDetailDTO discountDetail = new DiscountDetailDTO();
                    //优惠类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
                    if (ticket.getTicketSource() != null && TicketSourceEnum.DADA.getCode().equals(ticket.getTicketSource())) {
                        discountDetail.setDiscountNo(ticket.getTicketNo());
                        discountDetail.setType(DISCOUNT_TYPE_DADA_TICKET);
                    } else {
                        if (StringUtils.isNotBlank(ticket.getTicketBatchNo())) {
                            discountDetail.setDiscountNo(ticket.getTicketBatchNo());
                            discountDetail.setType(DISCOUNT_TYPE_JD_TICKET_BATCH_NO);
                        } else {
                            discountDetail.setDiscountNo(ticket.getTicketNo());
                            discountDetail.setType(DISCOUNT_TYPE_JD_TICKET);
                        }
                    }
                    discountDetail.setUserNo(orderModel.getOperator());
                    detailList.add(discountDetail);
                }
            }
            //折扣信息赋值逻辑
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getDiscounts())) {
                for (Discount discount : orderModel.getPromotion().getDiscounts()) {
                    DiscountDetailDTO discountDetail = new DiscountDetailDTO();
                    discountDetail.setDiscountNo(discount.getDiscountNo());
                    //优惠类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
                    discountDetail.setType(DISCOUNT_TYPE_DISCOUNT);
                    detailList.add(discountDetail);
                }
            }
            if (operationDiscountFlag) {
                //营销折扣赋值逻辑
                if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getOperationDiscountInfos())) {
                    for (Discount discount : orderModel.getPromotion().getOperationDiscountInfos()) {
                        DiscountDetailDTO discountDetail = new DiscountDetailDTO();
                        discountDetail.setDiscountNo(discount.getDiscountNo());
                        //优惠类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
                        discountDetail.setType(DISCOUNT_TYPE_DISCOUNT);
                        detailList.add(discountDetail);
                    }
                }
            }
        }
        return detailList;
    }

    /**
     * @description 功能描述: C2C拓展字段之下单渠道
     * <AUTHOR>
     * @date 2021/6/29 10:21
     * @param extendParam
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    private void extendParamToOrderChannel(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        //下单渠道
        if (StringUtils.isNotBlank(orderModel.getChannel().getChannelNo())) {
            extendParam.put(EnquiryConstants.ORDER_CHANNEL, orderModel.getChannel().getChannelNo());
        }
    }


    /**
     * @description 功能描述: C2C拓展字段之结算方式
     * <AUTHOR>
     * @date 2021/6/29 10:21
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    private Object extendParamToSettlementType(ExpressOrderModel orderModel) {
        if (SettlementTypeEnum.CASH_ON_PICK == orderModel.getFinance().getSettlementType()) {
            return "3";
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY ==  orderModel.getFinance().getSettlementType()) {
            return "2";
        } else if (SettlementTypeEnum.MONTHLY_PAYMENT ==  orderModel.getFinance().getSettlementType()) {
            return "1";
        }
        return null;
    }

    /**
     * 补全原单信息（支付方式）
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.OrderSnapshot toOrderSnapshot(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.OrderSnapshot orderSnapshot = new BillingEnquiryFacadeRequest.OrderSnapshot();
        ExpressOrderModel modelSnapshot = orderModel.getOrderSnapshot();
        if (modelSnapshot != null) {
            orderSnapshot.setSettlementType(modelSnapshot.getFinance().getSettlementType());
        }
        return orderSnapshot;
    }

    /**
     * 改址单合并原单财务信息
     * @param context
     */
    @Deprecated
    public void complementReaddressEnquiryInfo(ExpressOrderContext context) {
        // 改址财务
        Finance readdrFinance = context.getOrderModel().getFinance();
        // 原单财务
        Finance originFinance = context.getOrderModel().getOrderSnapshot().getFinance();

        // 更新改址单费用明细，增加原单明细 todo 改址单询价费用编码会不会重复 和产品确认 是否原单单独一个费用明细
        readdrFinance.getFinanceDetails().addAll(originFinance.getFinanceDetails());

        // 更新改址单折前折后金额
        updateMoneyInfo(readdrFinance.getPreAmount(), originFinance.getPreAmount());
        updateMoneyInfo(readdrFinance.getDiscountAmount(), originFinance.getDiscountAmount());

        LOGGER.info("更新后改址单财务信息:{}", JSONUtils.beanToJSONDefaultLazy(readdrFinance));
    }

    /**
     * 将m2 的金额信息更新到 m1
     * @param m1
     * @param m2
     */
    private void updateMoneyInfo(@NotNull Money m1, @NotNull Money m2) {
        if (null == m1.getAmount()) {
            m1.setAmount(m2.getAmount());
        } else if (null != m2.getAmount()) {
            m1.setAmount(m1.getAmount().add(m2.getAmount()));
        }
    }

    /**
     * 先款订单 改址一单到底 补齐信息
     * 补全计费结果信息
     */
    public boolean complementBillingResult(ReaddressRecordDetailInfo modifyRecordDetail, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse, ExpressOrderContext context) {
        ExpressOrderModel orderModel = context.getOrderModel();
        FinanceInfo financeInfo = modifyRecordDetail.getFinance();
        if (financeInfo == null) {
            financeInfo = new FinanceInfo();
            modifyRecordDetail.setFinance(financeInfo);
        }

        BillingConvertor.INSTANCE.complementFinanceInfo(billingEnquiryFacadeResponse.getFinanceFacadeDto(), financeInfo);

        // 差额
        // 根据询价后的金额 对比原金额 计算差额
        BigDecimal discountMoney = Optional.ofNullable(financeInfo.getDiscountAmount().getAmount()).orElse(BigDecimal.ZERO);
        BigDecimal originDiscountMoney = Optional.ofNullable(orderModel.getOrderSnapshot().getFinance().getDiscountAmount().getAmount()).orElse(BigDecimal.ZERO);
        BigDecimal diffAmount = discountMoney.subtract(originDiscountMoney);
        //返回是否需要支付
        int result = diffAmount.compareTo(BigDecimal.ZERO);
        LOGGER.info("改址一单到底：询价后差额={}", diffAmount);
        Map<String, String> financeExt = orderModel.getFinance().getExtendProps();//新单财务域扩展信息
        // 原单的支付环节
        String orderPaymentStage = MapUtils.isNotEmpty(financeExt) ? financeExt.get(AttachmentKeyEnum.ORIGIN_PAYMENT_STAGE.getKey()) : null;
        // 改址记录差额
        financeInfo.setReceivableDifferenceAmount(MoneyMapper.INSTANCE.toMoneyInfo(diffAmount, financeInfo.getDiscountAmount().getCurrencyCode()));
        // 先款改址
        if (PaymentStageEnum.ONLINEPAYMENT == orderModel.getFinance().getPaymentStage()
                || ReaddressSettlementTypeUtil.isTaoTianOriginalJiFu(orderModel)) {
            //设置是否先款合并支付标识
            String onlineCombinedPay = OrderConstants.NO_VAL;
            // 原单寄付现结（已支付） 差额 >0 支付； 差额 <0 退款
            if(SettlementTypeEnum.CASH_ON_PICK == orderModel.getOrderSnapshot().getFinance().getSettlementType()) {
                if (result > 0) {
                    // 淘天原单寄付，补款不处理
                    if (ProductAttrsUtil.containsTaoTianReaddressProductAttrs(orderModel)) {
                        context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY.getCode(), OrderConstants.NO_VAL);
                        MoneyInfo pendingMoney = MoneyMapper.INSTANCE.toMoneyInfo(BigDecimal.ZERO, financeInfo.getDiscountAmount().getCurrencyCode());
                        context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode(), pendingMoney);
                        financeInfo.setPendingMoney(pendingMoney);
                        LOGGER.info("淘天来源，补款不处理。pendingMoney: {}", JSONUtils.beanToJSONDefault(pendingMoney));
                    } else {
                        context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY.getCode(), OrderConstants.YES_VAL);
                        MoneyInfo pendingMoney = MoneyMapper.INSTANCE.toMoneyInfo(diffAmount, financeInfo.getDiscountAmount().getCurrencyCode());
                        context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode(), pendingMoney);
                        financeInfo.setPendingMoney(pendingMoney);
                    }
                } else if (result < 0) {
                    context.putExtMaps(ContextInfoEnum.READDRESS_NEED_REFUND.getCode(), OrderConstants.YES_VAL);
                }
            } else if (SettlementTypeEnum.CASH_ON_DELIVERY == orderModel.getOrderSnapshot().getFinance().getSettlementType()) {
                // 原单到付现结先款 （原来发生过改址 支付过全部费用 本次进行差额操作） 差额 >0 支付； 差额 <0 退款
                if (PaymentStageEnum.ONLINEPAYMENT == orderModel.getOrderSnapshot().getFinance().getPaymentStage()) {
                    if (result > 0) {
                        context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY.getCode(), OrderConstants.YES_VAL);
                        MoneyInfo pendingMoney = MoneyMapper.INSTANCE.toMoneyInfo(diffAmount, financeInfo.getDiscountAmount().getCurrencyCode());
                        context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode(), pendingMoney);
                        financeInfo.setPendingMoney(pendingMoney);
                    } else if (result < 0) {
                        context.putExtMaps(ContextInfoEnum.READDRESS_NEED_REFUND.getCode(), OrderConstants.YES_VAL);
                    }
                } else if (PaymentStageEnum.CASHONDELIVERY == orderModel.getOrderSnapshot().getFinance().getPaymentStage()) {
                    // 原单到付现结后款
                    if (StringUtils.isNotBlank(orderPaymentStage)
                            && PaymentStageEnum.ONLINEPAYMENT.getCode().toString().equals(orderPaymentStage)) {
                        if (discountMoney.compareTo(BigDecimal.ZERO) > 0) {
                            context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY.getCode(), OrderConstants.YES_VAL);
                            //若原单也是需要改成先款，则进行合并支付，需要支付全部费用
                            MoneyInfo pendingMoney = MoneyMapper.INSTANCE.toMoneyInfo(discountMoney, financeInfo.getDiscountAmount().getCurrencyCode());
                            context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode(), pendingMoney);
                            financeInfo.setPendingMoney(pendingMoney);
                            //改址记录差额--合并支付场景，差额==待支付金额
                            financeInfo.setReceivableDifferenceAmount(pendingMoney);
                            onlineCombinedPay = OrderConstants.YES_VAL;
                        } else if (discountMoney.compareTo(BigDecimal.ZERO) < 0) {
                            LOGGER.error("折后金额小于0，不存在此场景，询价金额异常");
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("先款合并支付场景,折后金额小于0,金额异常");
                        }
                    } else {
                        //若原单仍需后款，则先款只需支付改址的差额费
                        if (result > 0) {
                            context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY.getCode(), OrderConstants.YES_VAL);
                            MoneyInfo pendingMoney = MoneyMapper.INSTANCE.toMoneyInfo(diffAmount, financeInfo.getDiscountAmount().getCurrencyCode());
                            context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode(), pendingMoney);
                            financeInfo.setPendingMoney(pendingMoney);
                        } else if (result < 0) {
                            context.putExtMaps(ContextInfoEnum.READDRESS_NEED_REFUND.getCode(), OrderConstants.YES_VAL);
                        }
                    }

                }
            } else if(SettlementTypeEnum.MONTHLY_PAYMENT == orderModel.getOrderSnapshot().getFinance().getSettlementType()){
                // 原单到付现结先款 （原来发生过改址 支付过全部费用 本次进行差额操作） 差额 >0 支付； 差额 <0 退款
                if (PaymentStageEnum.ONLINEPAYMENT == orderModel.getOrderSnapshot().getFinance().getPaymentStage()) {
                    if (result > 0) {
                        context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY.getCode(), OrderConstants.YES_VAL);
                        MoneyInfo pendingMoney = MoneyMapper.INSTANCE.toMoneyInfo(diffAmount, financeInfo.getDiscountAmount().getCurrencyCode());
                        context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode(), pendingMoney);
                        financeInfo.setPendingMoney(pendingMoney);
                    } else if (result < 0) {
                        context.putExtMaps(ContextInfoEnum.READDRESS_NEED_REFUND.getCode(), OrderConstants.YES_VAL);
                    }
                } else if (PaymentStageEnum.CASHONDELIVERY == orderModel.getOrderSnapshot().getFinance().getPaymentStage()) {
                    // 原单到付现结后款
                    if (StringUtils.isNotBlank(orderPaymentStage)
                            && PaymentStageEnum.ONLINEPAYMENT.getCode().toString().equals(orderPaymentStage)) {
                        if (discountMoney.compareTo(BigDecimal.ZERO) > 0) {
                            context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY.getCode(), OrderConstants.YES_VAL);
                            //若原单也是需要改成先款，则进行合并支付，需要支付全部费用
                            MoneyInfo pendingMoney = MoneyMapper.INSTANCE.toMoneyInfo(discountMoney, financeInfo.getDiscountAmount().getCurrencyCode());
                            context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode(), pendingMoney);
                            financeInfo.setPendingMoney(pendingMoney);
                            //改址记录差额--合并支付场景，差额==待支付金额
                            financeInfo.setReceivableDifferenceAmount(pendingMoney);
                            onlineCombinedPay = OrderConstants.YES_VAL;
                        } else if (discountMoney.compareTo(BigDecimal.ZERO) < 0) {
                            LOGGER.error("折后金额小于0，不存在此场景，询价金额异常");
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("先款合并支付场景,折后金额小于0,金额异常");
                        }
                    } else {
                        //若原单仍需后款，则先款只需支付改址的差额费
                        if (result > 0) {
                            context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY.getCode(), OrderConstants.YES_VAL);
                            MoneyInfo pendingMoney = MoneyMapper.INSTANCE.toMoneyInfo(diffAmount, financeInfo.getDiscountAmount().getCurrencyCode());
                            context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode(), pendingMoney);
                            financeInfo.setPendingMoney(pendingMoney);
                        } else if (result < 0) {
                            context.putExtMaps(ContextInfoEnum.READDRESS_NEED_REFUND.getCode(), OrderConstants.YES_VAL);
                        }
                    }
                }
            }

            Map<String,String> extendProps = financeInfo.getExtendProps();
            if(extendProps == null){
                extendProps = new HashMap<>();
            }
            //设置合并支付标识，供后续对账成功消息处理等环节使用
            extendProps.put(AttachmentKeyEnum.ONLINE_COMBINED_PAY.getKey(), onlineCombinedPay);
            financeInfo.setExtendProps(extendProps);
        }

        LOGGER.info("改址一单到底：complementBillingResult:financeInfo={}", JSONUtils.beanToJSONDefault(financeInfo));
        return OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.READDRESS_NEED_PAY.getCode()));
    }

    /**
     * 补全计费结果信息
     */
    @Deprecated
    public void complementBillingResult(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();

        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getAmount());
        preAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getCurrencyCode());
        financeInfoDto.setPreAmount(preAmount);

        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
        discountAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
        financeInfoDto.setDiscountAmount(discountAmount);

        //计费重量
        financeInfoDto.setBillingWeight(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingVolume());

        //计费类型
        financeInfoDto.setBillingMode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingMode());

        //积分信息
        financeInfoDto.setPointsInfoDto(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPointsInfoDto());

        //费用明细
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : billingEnquiryFacadeResponse.getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
            FinanceDetailInfoDto detailInfoDto = new FinanceDetailInfoDto();
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount().getAmount());
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount().getCurrencyCode());
            detailInfoDto.setPreAmount(detailPreAmount);
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount().getAmount());
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount().getCurrencyCode());
            detailInfoDto.setDiscountAmount(detailDiscountAmount);
            detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            detailInfoDto.setCostName(detailFacadeDto.getCostName());
            detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            detailInfoDto.setProductName(detailFacadeDto.getProductName());

            //折扣明细
            if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
                for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
                    discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());
                    Money money = new Money();
                    money.setAmount(discountInfoFacadeDto.getDiscountedAmount().getAmount());
                    money.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
                    discountInfoDto.setDiscountedAmount(money);
                    Map<String, String> extendProps = new HashMap<>();
                    if(StringUtils.isNotBlank(discountInfoFacadeDto.getTicketNo())){
                        //优惠券编码
                        extendProps.put(AttachmentKeyEnum.TICKET_NO.getKey(), discountInfoFacadeDto.getTicketNo());
                    }
                    discountInfoDto.setExtendProps(extendProps);
                    discountInfoDtos.add(discountInfoDto);
                }
                detailInfoDto.setDiscountInfoDtos(discountInfoDtos);
            }

            detailInfoDto.setExtendProps(new HashMap<>());
            // 价格项明细
            if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
                detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
            }

            financeDetailInfoDtoList.add(detailInfoDto);
        }
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
        //支付截止时间
        financeInfoDto.setPayDeadline(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPayDeadline());
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

    private BillingEnquiryFacadeRequest.CustomerFacadeDto toCustomerFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = new BillingEnquiryFacadeRequest.CustomerFacadeDto();
        Customer customer = orderModel.getCustomer();
        customerFacadeDto.setAccountNo(customer.getAccountNo());
        customerFacadeDto.setAccount2No(customer.getAccountNo2());
        customerFacadeDto.setAccountName(customer.getAccountName());
        return customerFacadeDto;
    }

    /**
     * 转换产品信息
     * @param orderModel 订单模型
     * @param snapshot 订单快照
     * @param useSnapshotAttachFees 附加费取值来源 true: 取快照附加费 false: 取订单附加费
     * @return
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel orderModel, ExpressOrderModel snapshot, boolean useSnapshotAttachFees) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        // 获取产品信息 （todo）
        List<Product> products = orderModel.getProductDelegate().nonDeletedProducts();
        if (CollectionUtils.isEmpty(products) && null != snapshot) {
            products = snapshot.getProductDelegate().getProductList();
        }
        if (CollectionUtils.isNotEmpty(products)) {
            for (Product product : products) {
                // COD & 协商再投 不询价
                if (AddOnProductEnum.b2cSkipEnquiry(product.getProductNo())) {
                    continue;
                }
                // 国补-激活校验 https://joyspace.jd.com/pages/G0hyCRMoGagEM96u9gtC
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.GB_B2C_ENQUIRY_CULL_SWITCH)
                        && AddOnProductEnum.ACTIVATION_CHECK.getCode().equals(product.getProductNo())
                        && MapUtils.isNotEmpty(product.getProductAttrs())// 产品要素包含结算编码
                        && product.getProductAttrs().containsKey(AddOnProductAttrEnum.ACTIVATION_CHECK_SETTLEMENT_ACCOUNT.getCode())
                ) {
                    continue;
                }
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setAttachFees(Boolean.FALSE);
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                //港澳：特惠送，特快送计费时传入揽派模式
                if (ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(product.getProductType())
                        && (ProductEnum.THS.getCode().equals(product.getProductNo())
                        || ProductEnum.TKS.getCode().equals(product.getProductNo()))) {
                    Map<String, String> productAttrs = new HashMap<>();
                    if (MapUtils.isNotEmpty(product.getProductAttrs())) {
                        productAttrs.putAll(product.getProductAttrs());
                    }
                    PickupTypeEnum pickupType = GetFieldUtils.getField(orderModel, GetFieldUtils.PICKUP_TYPE);
                    if (null != pickupType) {
                        //揽收模式
                        productAttrs.put(EnquiryConstants.PICKUP_MODEL,String.valueOf(pickupType.getCode()));
                    }
                    DeliveryTypeEnum deliveryType = GetFieldUtils.getField(orderModel, GetFieldUtils.DELIVERY_TYPE);
                    if (null != deliveryType) {
                        //揽收模式
                        productAttrs.put(EnquiryConstants.PICKUP_MODEL,String.valueOf(deliveryType.getCode()));
                    }
                    productFacadeDto.setProductAttrs(productAttrs);
                }
                productFacadeDtos.add(productFacadeDto);
            }
        }
        //补全附加服务费
        ExpressOrderModel attachFeeSource = useSnapshotAttachFees ? snapshot : orderModel;
        List<CostInfo> attachFees = attachFeeSource.getFinance().getAttachFees();
        if (CollectionUtils.isNotEmpty(attachFees)) {
            for (CostInfo costInfo : attachFees) {
                if (costInfo == null || OperateTypeEnum.DELETE == costInfo.getOperateType()) {
                    continue;
                }
                BillingEnquiryFacadeRequest.ProductFacadeDto attachFeeFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                attachFeeFacadeDto.setProductNo(costInfo.getCostNo());
                attachFeeFacadeDto.setAttachFees(Boolean.TRUE);
                attachFeeFacadeDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                attachFeeAddSpecialProductAttrs(costInfo, attachFeeFacadeDto);
                if (orderModel.isSelfPickupTemporaryStorageOrder()) {
                    // 自提暂存单 设置生鲜/非生鲜扩展字段
                    if (MapUtils.isNotEmpty(costInfo.getExtendProps())
                            && costInfo.getExtendProps().containsKey(EnquiryConstants.IS_FRESH_OMS)) {
                        Map<String, String> productAttrs = attachFeeFacadeDto.getProductAttrs();
                        if(MapUtils.isEmpty(productAttrs)){
                            productAttrs = new HashMap<>();
                            attachFeeFacadeDto.setProductAttrs(productAttrs);
                        }
                        productAttrs.put(EnquiryConstants.IS_FRESH_FOOD, costInfo.getExtendProps().get(EnquiryConstants.IS_FRESH_OMS));
                    } else {
                        LOGGER.warn("自提暂存单{}计费询价是否生鲜扩展字段未设置", orderModel.orderNo());
                    }
                }
                productFacadeDtos.add(attachFeeFacadeDto);
            }
        }
        return productFacadeDtos;
    }

    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel orderModel, boolean useSnapshotAttachFees) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<? extends IProduct> products = orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            for (IProduct product : products) {
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setAttachFees(Boolean.FALSE);
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                //港澳：特惠送，特快送计费时传入揽派模式
                if(ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(product.getProductType())
                        && (ProductEnum.THS.getCode().equals(product.getProductNo())
                        || ProductEnum.TKS.getCode().equals(product.getProductNo()))){
                    Map<String, String> productAttrs = product.getProductAttrs();
                    if(MapUtils.isEmpty(productAttrs)){
                        productAttrs = new HashMap<>();
                    }
                    if(null != orderModel.getShipment()){
                        //揽收模式
                        if(null != orderModel.getShipment().getPickupType()){
                            productAttrs.put(EnquiryConstants.PICKUP_MODEL,String.valueOf(orderModel.getShipment().getPickupType().getCode()));
                        }
                        //派送模式
                        if(null != orderModel.getShipment().getDeliveryType()){
                            productAttrs.put(EnquiryConstants.DELIVERY_MODEL,String.valueOf(orderModel.getShipment().getDeliveryType().getCode()));
                        }
                    }
                    productFacadeDto.setProductAttrs(productAttrs);
                }
                productFacadeDtos.add(productFacadeDto);
            }
        }
        //补全附加服务费
        if (useSnapshotAttachFees) {
            if (CollectionUtils.isNotEmpty(orderModel.getFinance().getAttachFees())) {
                for (CostInfo costInfo : orderModel.getFinance().getAttachFees()) {
                    BillingEnquiryFacadeRequest.ProductFacadeDto attachFeeFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                    attachFeeFacadeDto.setProductNo(costInfo.getCostNo());
                    attachFeeFacadeDto.setAttachFees(Boolean.TRUE);
                    attachFeeFacadeDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                    attachFeeAddSpecialProductAttrs(costInfo, attachFeeFacadeDto);
                    if (orderModel.isSelfPickupTemporaryStorageOrder()) {
                        // 自提暂存单
                        if (MapUtils.isNotEmpty(costInfo.getExtendProps())) {
                            Map<String, String> productAttrs = attachFeeFacadeDto.getProductAttrs();
                            if(MapUtils.isEmpty(productAttrs)){
                                productAttrs = new HashMap<>();
                                attachFeeFacadeDto.setProductAttrs(productAttrs);
                            }
                            // 设置生鲜/非生鲜扩展字段
                            productAttrs.put(EnquiryConstants.IS_FRESH_FOOD, costInfo.getExtendProps().get(EnquiryConstants.IS_FRESH_OMS));
                            // 设置平台单据类型
                            productAttrs.put(EnquiryConstants.TEMPORARY_STORAGE_TYPE, costInfo.getExtendProps().get(EnquiryConstants.TEMPORARY_STORAGE_TYPE));
                        }
                    }
                    productFacadeDtos.add(attachFeeFacadeDto);
                }
            }
        }
        return productFacadeDtos;
    }

    /**
     * 改址单询价产品转换逻辑
     * @param orderModel
     * @return
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toReaddressProductFacadeDto(ExpressOrderModel orderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<? extends IProduct> products = orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            for (IProduct product : products) {
                if (ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode().equals(product.getProductType())
                        && !expressUccConfigCenter.isReaddressEnquiryAddOnProductWhite(product.getProductNo())) {
                    //如果是增值服务，且不在白名单中，则不询价
                    continue;
                }
                //如果产品编码在配置中，则需要计费
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setAttachFees(Boolean.FALSE);
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                if(ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(product.getProductType())
                        && (ProductEnum.THS.getCode().equals(product.getProductNo())
                        || ProductEnum.TKS.getCode().equals(product.getProductNo()))){
                    Map<String, String> productAttrs = product.getProductAttrs();
                    if(MapUtils.isEmpty(productAttrs)){
                        productAttrs = new HashMap<>();
                    }
                    if(null != orderModel.getShipment()){
                        //揽收模式
                        if(null != orderModel.getShipment().getPickupType()){
                            productAttrs.put(EnquiryConstants.PICKUP_MODEL,String.valueOf(orderModel.getShipment().getPickupType().getCode()));
                        }
                        //派送模式
                        if(null != orderModel.getShipment().getDeliveryType()){
                            productAttrs.put(EnquiryConstants.DELIVERY_MODEL,String.valueOf(orderModel.getShipment().getDeliveryType().getCode()));
                        }
                    }
                    productFacadeDto.setProductAttrs(productAttrs);
                }
                productFacadeDtos.add(productFacadeDto);
            }
        }
        //todo 补全附加服务费
        if(CollectionUtils.isNotEmpty(orderModel.getFinance().getAttachFees())){
            for (CostInfo costInfo : orderModel.getFinance().getAttachFees()) {
                BillingEnquiryFacadeRequest.ProductFacadeDto attachFeeFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                attachFeeFacadeDto.setProductNo(costInfo.getCostNo());
                attachFeeFacadeDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                attachFeeFacadeDto.setAttachFees(Boolean.TRUE);
                attachFeeAddSpecialProductAttrs(costInfo, attachFeeFacadeDto);
                productFacadeDtos.add(attachFeeFacadeDto);
            }
        }
        return productFacadeDtos;
    }

    /**
     * 客户、渠道
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ChannelFacadeDto toChannelFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ChannelFacadeDto channelFacadeDto = new BillingEnquiryFacadeRequest.ChannelFacadeDto();
        Channel channel = orderModel.getChannel();
        channelFacadeDto.setCustomerOrderNo(channel.getCustomerOrderNo());
        //非C2C传此字段
        if (!orderModel.isC2C()) {
            channelFacadeDto.setChannelNo(channel.getChannelNo());
        }
        channelFacadeDto.setChannelOrderNo(channel.getChannelOrderNo());
        channelFacadeDto.setChannelCustomerNo(channel.getChannelCustomerNo());
        channelFacadeDto.setChannelOperateTime(channel.getChannelOperateTime());
        channelFacadeDto.setSystemCaller(channel.getSystemCaller() != null ? channel.getSystemCaller().getCode() : null);
        channelFacadeDto.setSystemSubCaller(channel.getSystemSubCaller());
        return channelFacadeDto;
    }

    /**
     * 财务相关信息
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(ExpressOrderModel orderModel,ExpressOrderModel orderSnapshot) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        Finance finance = orderModel.getFinance();
        Finance snapshotFinance = orderSnapshot.getFinance();
        financeFacadeDto.setEnquireTime(DateUtils.now());
        financeFacadeDto.setSettlementType(null != finance.getSettlementType() ? finance.getSettlementType() : snapshotFinance.getSettlementType());
        //抵扣信息 TODO 只有逆向
        // 下了运费保，抵扣编码是OFC传过来的，所以抵扣信息在当前订单的fiance信息
        if (null != finance.getDeductionDelegate() && CollectionUtils.isNotEmpty(finance.getDeductionDelegate().getDeductions())) {
            financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) finance.getDeductionDelegate().getDeductions()));
        } else {
            financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) snapshotFinance.getDeductionDelegate().getDeductions()));
        }
        // C2C的是当前时间
        return financeFacadeDto;
    }

    /**
     * 财务相关信息
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        Finance finance = orderModel.getFinance();
        financeFacadeDto.setSettlementType(finance.getSettlementType());
        financeFacadeDto.setSettlementAccountNo(finance.getSettlementAccountNo());
        //抵扣信息
        financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) finance.getDeductionDelegate().getDeductions()));
        // C2C的是当前时间
        financeFacadeDto.setEnquireTime(DateUtils.now());
        return financeFacadeDto;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductions
     * @return
     */
    private List<DeductionInfoDto> toDeductionInfoDtos(List<Deduction> deductions) {
        if (CollectionUtils.isEmpty(deductions)) {
            return null;
        }
        List<DeductionInfoDto> deductionInfoDtos = new ArrayList<>(deductions.size());
        deductions.forEach(deduction -> {
            if (deduction != null) {
                DeductionInfoDto dto = new DeductionInfoDto();
                //抵扣编码
                dto.setDeductionNo(deduction.getDeductionNo());
                // 抵扣金额
                dto.setDeductionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(deduction.getDeductionAmount()));
                //扩展信息
                dto.setExtendProps(deduction.getExtendProps());
                deductionInfoDtos.add(dto);
            }
        });
        return deductionInfoDtos;
    }

    /**
     * 积分信息
     *
     * @return
     */
    private PointsInfoDto toPointsInfoDto(Points points) {
        if (points == null) {
            return null;
        }
        PointsInfoDto pointsInfoDto = new PointsInfoDto();
        if (points.getRedeemPointsAmount() != null) {
            MoneyInfoDto redeemPointsAmount = new MoneyInfoDto();
            redeemPointsAmount.setAmount(points.getRedeemPointsAmount().getAmount());
            redeemPointsAmount.setCurrencyCode(points.getRedeemPointsAmount().getCurrency());
            pointsInfoDto.setRedeemPointsAmount(redeemPointsAmount);
        }
        if (points.getRedeemPointsQuantity() != null) {
            QuantityInfoDto redeemPointsQuantity = new QuantityInfoDto();
            redeemPointsQuantity.setUnit(points.getRedeemPointsQuantity().getUnit());
            redeemPointsQuantity.setValue(points.getRedeemPointsQuantity().getValue());
            pointsInfoDto.setRedeemPointsQuantity(redeemPointsQuantity);
        }
        return pointsInfoDto;
    }

    /**
     * 收件人信息
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(ExpressOrderModel orderModel) {
        return toConsigneeFacadeDto(orderModel.getConsignee());
    }

    /**
     * 收件人信息
     *
     * @param consignee
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(Consignee consignee) {
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();
        consigneeFacadeDto.setConsigneeName(consignee.getConsigneeName());
        consigneeFacadeDto.setConsigneeMobile(consignee.getConsigneeMobile());
        consigneeFacadeDto.setConsigneePhone(consignee.getConsigneePhone());
        consigneeFacadeDto.setConsigneeZipCode(consignee.getConsigneeZipCode());
        consigneeFacadeDto.setConsigneeCompany(consignee.getConsigneeCompany());
        consigneeFacadeDto.setConsigneeNationNo(consignee.getConsigneeNationNo());
        consigneeFacadeDto.setConsigneeNation(consignee.getConsigneeNation());
        consigneeFacadeDto.setConsigneeIdType(consignee.getConsigneeIdType());
        consigneeFacadeDto.setConsigneeIdNo(consignee.getConsigneeIdNo());
        consigneeFacadeDto.setConsigneeIdName(consignee.getConsigneeIdName());
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        Address address = consignee.getAddress();
        if (address != null) {
            addressFacadeDto.setProvinceNo(address.getProvinceNo());
            addressFacadeDto.setProvinceName(address.getProvinceName());
            addressFacadeDto.setCityNo(address.getCityNo());
            addressFacadeDto.setCityName(address.getCityName());
            addressFacadeDto.setCountyNo(address.getCountyNo());
            addressFacadeDto.setCountyName(address.getCountyName());
            addressFacadeDto.setTownNo(address.getTownNo());
            addressFacadeDto.setTownName(address.getTownName());
            addressFacadeDto.setAddress(address.getAddress());
            addressFacadeDto.setCoordinateType(address.getCoordinateType());
            addressFacadeDto.setLongitude(address.getLongitude());
            addressFacadeDto.setLatitude(address.getLatitude());
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setProvinceNameGis(address.getProvinceNameGis());
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCityNameGis(address.getCityNameGis());
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
            addressFacadeDto.setCountyNameGis(address.getCountyNameGis());
            addressFacadeDto.setTownNoGis(address.getTownNoGis());
            addressFacadeDto.setTownNameGis(address.getTownNameGis());
            addressFacadeDto.setAddressGis(address.getAddressGis());
            addressFacadeDto.setRegionNo(address.getRegionNo());
            addressFacadeDto.setRegionName(address.getRegionName());
        }
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     *
     * @param orderModel
     * @return
     */
    public BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel orderModel,ExpressOrderModel orderSnapshot) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        CargoDelegate cargoDelegate = orderModel.getCargoDelegate();
        if (null == cargoDelegate || CollectionUtils.isEmpty(cargoDelegate.getCargoList())) {
            // 当前单货品为空 取快照
            if (null != orderSnapshot && null != orderSnapshot.getCargoDelegate()) {
                cargoDelegate = orderSnapshot.getCargoDelegate();
            }
        }

        if (null != cargoDelegate) {
            cargoFacadeDto.setTotalCargoWeight(cargoDelegate.totalCargoWeight());
        }

        Enquiry enquiry = orderModel.getEnquiry();

        // 询价场景 入参数优先 TODO 是否重量体积一起
        if (null != enquiry && null != enquiry.getEnquiryVolume() && null != enquiry.getEnquiryWeight()) {
            // 计费体积
            cargoFacadeDto.setTotalCargoVolume(enquiry.getEnquiryVolume().getValue());
            // 计费重量
            cargoFacadeDto.setTotalCargoWeight(enquiry.getEnquiryWeight().getValue());
            // 计费数量
            if (null != enquiry.getEnquiryQuantity() && null != enquiry.getEnquiryQuantity().getValue()){
                cargoFacadeDto.setTotalCargoQuantity(enquiry.getEnquiryQuantity().getValue());
            } else if (null != cargoDelegate) {
                // 计费数量(从外单里查出来的货品数量)
                cargoFacadeDto.setTotalCargoQuantity(cargoDelegate.totalCargoQuantity());
            }
        } else {
            // 优先取复核重量体积 若没有 取货品重量体积
            Optional<BigDecimal> recheckWeight = Optional.ofNullable(orderSnapshot)
                    .map(ExpressOrderModel::getRecheckWeight)
                    .map(Weight::getValue);
            Optional<BigDecimal> recheckVolume = Optional.ofNullable(orderSnapshot)
                    .map(ExpressOrderModel::getRecheckVolume)
                    .map(Volume::getValue);

            if (recheckWeight.isPresent() && recheckVolume.isPresent()) {
                cargoFacadeDto.setTotalCargoVolume(recheckVolume.get());
                cargoFacadeDto.setTotalCargoWeight(recheckWeight.get());
            } else if (null != cargoDelegate && CollectionUtils.isNotEmpty(cargoDelegate.getCargoList())) {
                cargoFacadeDto.setTotalCargoVolume(cargoDelegate.totalCargoVolume());
                cargoFacadeDto.setTotalCargoWeight(cargoDelegate.totalCargoWeight());
            }
            if (null != cargoDelegate) {
                // 计费数量
                cargoFacadeDto.setTotalCargoQuantity(cargoDelegate.totalCargoQuantity());
            }
        }

        return cargoFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     * @param orderModel 异步 订单详情
     * @return
     */
    public BillingEnquiryFacadeRequest.CargoFacadeDto asyncCargoFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        CargoDelegate cargoDelegate = orderModel.getCargoDelegate();
        // 优先取复核重量体积 若没有 取货品重量体积
        Optional<BigDecimal> recheckWeight = Optional.of(orderModel)
                .map(ExpressOrderModel::getRecheckWeight)
                .map(Weight::getValue);
        Optional<BigDecimal> recheckVolume = Optional.of(orderModel)
                .map(ExpressOrderModel::getRecheckVolume)
                .map(Volume::getValue);
        if (recheckWeight.isPresent() && recheckVolume.isPresent()) {
            cargoFacadeDto.setTotalCargoVolume(recheckVolume.get());
            cargoFacadeDto.setTotalCargoWeight(recheckWeight.get());
        } else if (null != cargoDelegate && CollectionUtils.isNotEmpty(cargoDelegate.getCargoList())) {
            cargoFacadeDto.setTotalCargoVolume(cargoDelegate.totalCargoVolume());
            cargoFacadeDto.setTotalCargoWeight(cargoDelegate.totalCargoWeight());
        }
        if (null != cargoDelegate) {
            // 计费数量
            cargoFacadeDto.setTotalCargoQuantity(cargoDelegate.totalCargoQuantity());
        }
        return cargoFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     * todo 优先获取复重信息
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
        cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
        cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
        return cargoFacadeDto;
    }

    /**
     * todo 确认新增非必填字段
     * 货物信息，总重量、总体积、总数量
     * 询价场景取询价信息
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(Enquiry enquiry) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        cargoFacadeDto.setTotalCargoQuantity(enquiry.getEnquiryQuantity().getValue());
        cargoFacadeDto.setTotalCargoVolume(enquiry.getEnquiryVolume().getValue());
        cargoFacadeDto.setTotalCargoWeight(enquiry.getEnquiryWeight().getValue());
        return cargoFacadeDto;
    }

    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toConsignorFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        Consignor consignor = orderModel.getConsignor();
        consignorFacadeDto.setConsignorName(consignor.getConsignorName());
        consignorFacadeDto.setConsignorMobile(consignor.getConsignorMobile());
        consignorFacadeDto.setConsignorPhone(consignor.getConsignorPhone());
        consignorFacadeDto.setConsignorZipCode(consignor.getConsignorZipCode());
        consignorFacadeDto.setConsignorCompany(consignor.getConsignorCompany());
        consignorFacadeDto.setConsignorNationNo(consignor.getConsignorNationNo());
        consignorFacadeDto.setConsignorNation(consignor.getConsignorNation());
        consignorFacadeDto.setConsignorIdType(consignor.getConsignorIdType());
        consignorFacadeDto.setConsignorIdNo(consignor.getConsignorIdNo());
        consignorFacadeDto.setConsignorIdName(consignor.getConsignorIdName());
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        Address address = consignor.getAddress();
        if (address != null) {
            addressFacadeDto.setProvinceNo(address.getProvinceNo());
            addressFacadeDto.setProvinceName(address.getProvinceName());
            addressFacadeDto.setCityNo(address.getCityNo());
            addressFacadeDto.setCityName(address.getCityName());
            addressFacadeDto.setCountyNo(address.getCountyNo());
            addressFacadeDto.setCountyName(address.getCountyName());
            addressFacadeDto.setTownNo(address.getTownNo());
            addressFacadeDto.setTownName(address.getTownName());
            addressFacadeDto.setAddress(address.getAddress());
            addressFacadeDto.setCoordinateType(address.getCoordinateType());
            addressFacadeDto.setLongitude(address.getLongitude());
            addressFacadeDto.setLatitude(address.getLatitude());
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setProvinceNameGis(address.getProvinceNameGis());
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCityNameGis(address.getCityNameGis());
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
            addressFacadeDto.setCountyNameGis(address.getCountyNameGis());
            addressFacadeDto.setTownNoGis(address.getTownNoGis());
            addressFacadeDto.setTownNameGis(address.getTownNameGis());
            addressFacadeDto.setAddressGis(address.getAddressGis());
            addressFacadeDto.setRegionNo(address.getRegionNo());
            addressFacadeDto.setRegionName(address.getRegionName());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 询价场景获取发件信息
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toEnquirySceneConsignorFacadeDto(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel orderSnapshot = expressOrderContext.getOrderModel().getOrderSnapshot();

        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        Consignor consignor = orderSnapshot.getConsignor();
        consignorFacadeDto.setConsignorName(consignor.getConsignorName());
        consignorFacadeDto.setConsignorMobile(consignor.getConsignorMobile());
        consignorFacadeDto.setConsignorPhone(consignor.getConsignorPhone());
        consignorFacadeDto.setConsignorZipCode(consignor.getConsignorZipCode());
        consignorFacadeDto.setConsignorCompany(consignor.getConsignorCompany());
        consignorFacadeDto.setConsignorNationNo(consignor.getConsignorNationNo());
        consignorFacadeDto.setConsignorNation(consignor.getConsignorNation());
        consignorFacadeDto.setConsignorIdType(consignor.getConsignorIdType());
        consignorFacadeDto.setConsignorIdNo(consignor.getConsignorIdNo());
        consignorFacadeDto.setConsignorIdName(consignor.getConsignorIdName());

        // 先根据始发站点查询，查不到才使用订单上的数据
        AddressBasicPrimaryWSFacadeResponse addressBasicPrimaryWSFacadeResponse = getBaseSiteByStartStationNo(orderModel);
        if (addressBasicPrimaryWSFacadeResponse != null) {
            BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
            //起始省
            addressFacadeDto.setProvinceNoGis(addressBasicPrimaryWSFacadeResponse.getProvinceId());
            addressFacadeDto.setProvinceNo(addressBasicPrimaryWSFacadeResponse.getProvinceId());
            //起始市
            addressFacadeDto.setCityNoGis(addressBasicPrimaryWSFacadeResponse.getCityId());
            addressFacadeDto.setCityNo(addressBasicPrimaryWSFacadeResponse.getCityId());
            //起始县
            addressFacadeDto.setCountyNoGis(addressBasicPrimaryWSFacadeResponse.getCountryId());
            addressFacadeDto.setCountyNo(addressBasicPrimaryWSFacadeResponse.getCountryId());
            //详细地址
            addressFacadeDto.setAddressGis(addressBasicPrimaryWSFacadeResponse.getAddress());
            addressFacadeDto.setAddress(addressBasicPrimaryWSFacadeResponse.getAddress());
            //行政区直接用原单，rpc转换用得到 standardProductInquiryRequest.setStartCountry
            addressFacadeDto.setRegionNo(consignor.getAddress().getRegionNo());
            addressFacadeDto.setRegionName(consignor.getAddress().getRegionName());

            consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);

            //存放在上下文中，以供询价台账节点使用
            LOGGER.info("询价节点查询到始发站点信息，AddressFacadeDto={}", addressFacadeDto);
            expressOrderContext.putExtMaps(ContextInfoEnum.START_STATION_ADDRESS.getCode(), addressBasicPrimaryWSFacadeResponse);
        } else {
            BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
            Address address = consignor.getAddress();
            if (address != null) {
                addressFacadeDto.setProvinceNo(address.getProvinceNo());
                addressFacadeDto.setProvinceName(address.getProvinceName());
                addressFacadeDto.setCityNo(address.getCityNo());
                addressFacadeDto.setCityName(address.getCityName());
                addressFacadeDto.setCountyNo(address.getCountyNo());
                addressFacadeDto.setCountyName(address.getCountyName());
                addressFacadeDto.setTownNo(address.getTownNo());
                addressFacadeDto.setTownName(address.getTownName());
                addressFacadeDto.setAddress(address.getAddress());
                addressFacadeDto.setCoordinateType(address.getCoordinateType());
                addressFacadeDto.setLongitude(address.getLongitude());
                addressFacadeDto.setLatitude(address.getLatitude());
                addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
                addressFacadeDto.setProvinceNameGis(address.getProvinceNameGis());
                addressFacadeDto.setCityNoGis(address.getCityNoGis());
                addressFacadeDto.setCityNameGis(address.getCityNameGis());
                addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
                addressFacadeDto.setCountyNameGis(address.getCountyNameGis());
                addressFacadeDto.setTownNoGis(address.getTownNoGis());
                addressFacadeDto.setTownNameGis(address.getTownNameGis());
                addressFacadeDto.setAddressGis(address.getAddressGis());
                addressFacadeDto.setRegionNo(address.getRegionNo());
                addressFacadeDto.setRegionName(address.getRegionName());
            }
            consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        }
        return consignorFacadeDto;
    }

    private BillingEnquiryFacadeRequest.ShipmentFacadeDto toShipmentFacadeDto(ExpressOrderModel orderModel,ExpressOrderModel orderSnapshot) {
        BillingEnquiryFacadeRequest.ShipmentFacadeDto shipmentFacadeDto = new BillingEnquiryFacadeRequest.ShipmentFacadeDto();
        Shipment shipment = orderModel.getShipment();
        Shipment snapshotShipment = orderSnapshot.getShipment();
        shipmentFacadeDto.setPickupType(null != shipment.getPickupType() ? shipment.getPickupType() : snapshotShipment.getPickupType());
        shipmentFacadeDto.setDeliveryType(null != shipment.getDeliveryType() ? shipment.getDeliveryType() : snapshotShipment.getDeliveryType());
        shipmentFacadeDto.setWarmLayer(null != shipment.getWarmLayer() ? shipment.getWarmLayer() : snapshotShipment.getWarmLayer());
        return shipmentFacadeDto;
    }

    private BillingEnquiryFacadeRequest.ShipmentFacadeDto toShipmentFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ShipmentFacadeDto shipmentFacadeDto = new BillingEnquiryFacadeRequest.ShipmentFacadeDto();
        Shipment shipment = orderModel.getShipment();
        shipmentFacadeDto.setPlanDeliveryTime(shipment.getPlanDeliveryTime());
        shipmentFacadeDto.setPlanReceiveTime(shipment.getPlanReceiveTime());
        shipmentFacadeDto.setExpectDeliveryStartTime(shipment.getExpectDeliveryStartTime());
        shipmentFacadeDto.setExpectDeliveryEndTime(shipment.getExpectDeliveryEndTime());
        shipmentFacadeDto.setExpectPickupStartTime(shipment.getExpectPickupStartTime());
        shipmentFacadeDto.setExpectPickupEndTime(shipment.getExpectPickupEndTime());
        shipmentFacadeDto.setPickupType(shipment.getPickupType());
        shipmentFacadeDto.setDeliveryType(shipment.getDeliveryType());
        shipmentFacadeDto.setTransportType(shipment.getTransportType());
        shipmentFacadeDto.setWarmLayer(shipment.getWarmLayer());
        shipmentFacadeDto.setStartStationNo(shipment.getStartStationNo());
        shipmentFacadeDto.setStartStationName(shipment.getStartStationName());
        shipmentFacadeDto.setEndStationNo(shipment.getEndStationNo());
        shipmentFacadeDto.setEndStationName(shipment.getEndStationName());
        shipmentFacadeDto.setWarehouseNo(shipment.getWarehouseNo());
        shipmentFacadeDto.setTotalWeight(orderModel.getCargoDelegate().totalCargoWeight());
        shipmentFacadeDto.setTotalVolume(orderModel.getCargoDelegate().totalCargoVolume());
        return shipmentFacadeDto;
    }

    /**
     * 补全计费结果信息
     */
    public void complementBillingResultForCCB2B(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();

        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getAmount());
        preAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getCurrencyCode());
        financeInfoDto.setPreAmount(preAmount);

        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
        discountAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
        financeInfoDto.setDiscountAmount(discountAmount);
        financeInfoDto.setEstimateAmount(discountAmount);

        //计费重量
        financeInfoDto.setBillingWeight(billingEnquiryFacadeResponse.getFinanceFacadeDto().getCalWeight());
        //计费体积
        financeInfoDto.setBillingVolume(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingVolume());

        //计费类型
        financeInfoDto.setBillingMode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingMode());

        //积分信息
        financeInfoDto.setPointsInfoDto(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPointsInfoDto());

        //费用明细
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : billingEnquiryFacadeResponse.getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
            FinanceDetailInfoDto detailInfoDto = new FinanceDetailInfoDto();
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount().getAmount());
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount().getCurrencyCode());
            detailInfoDto.setPreAmount(detailPreAmount);
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount().getAmount());
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount().getCurrencyCode());
            detailInfoDto.setDiscountAmount(detailDiscountAmount);
            detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            detailInfoDto.setCostName(detailFacadeDto.getCostName());
            detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            detailInfoDto.setProductName(detailFacadeDto.getProductName());

            //折扣明细
            if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
                for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
                    discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());
                    Money money = new Money();
                    money.setAmount(discountInfoFacadeDto.getDiscountedAmount().getAmount());
                    money.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
                    discountInfoDto.setDiscountedAmount(money);
                    Map<String, String> extendProps = new HashMap<>();
                    if(StringUtils.isNotBlank(discountInfoFacadeDto.getTicketNo())){
                        //优惠券编码
                        extendProps.put(AttachmentKeyEnum.TICKET_NO.getKey(), discountInfoFacadeDto.getTicketNo());
                    }
                    discountInfoDto.setExtendProps(extendProps);
                    discountInfoDtos.add(discountInfoDto);
                }
                detailInfoDto.setDiscountInfoDtos(discountInfoDtos);
            }

            detailInfoDto.setExtendProps(new HashMap<>());
            // 价格项明细
            if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
                detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
            }

            financeDetailInfoDtoList.add(detailInfoDto);
        }
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
        //支付截止时间
        financeInfoDto.setPayDeadline(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPayDeadline());
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

    /**
     * 询价场景扩展字段：是否首单折扣
     */
    private void extendParamToIsFirstOrder(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        boolean isFirstOrder = false;
        if (orderModel != null && orderModel.getPromotion() != null && CollectionUtils.isNotEmpty(orderModel.getPromotion().getActivities())) {
            List<Activity> activities = orderModel.getPromotion().getActivities();
            for (Activity activity : activities) {
                if (EnquiryConstants.ACTIVITY_NO_FIRST_ORDER.equals(activity.getActivityNo())) {
                    isFirstOrder = true;
                    break;
                }
            }
        }
        if (isFirstOrder) {
            extendParam.put(EnquiryConstants.IS_FIRST_ORDER, EnquiryConstants.IS_FIRST_ORDER_YES);
        } else {
            extendParam.put(EnquiryConstants.IS_FIRST_ORDER, EnquiryConstants.IS_FIRST_ORDER_NO);
        }
    }

    /**
     * 询价场景扩展字段：询价类型
     */
    private void extendParamToInquiryType(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        if (orderModel == null || orderModel.getEnquiry() == null || orderModel.getEnquiry().getEnquiryMode() == null) {
            return;
        }
        EnquiryModeEnum enquiryMode = orderModel.getEnquiry().getEnquiryMode();
        extendParam.put(EnquiryConstants.INQUIRY_TYPE, String.valueOf(enquiryMode.getCode()));
    }

    /**
     * 询价场景扩展字段：跨境报关信息
     * 跨境业务类型、是否文件、币种
     */
    private void extendParamToCustoms(Map<String, Object> extendParam, Customs customs, SettlementTypeEnum settlementTypeEnum) {
        // 跨境业务类型
        if (customs.getStartFlowDirection() != null && customs.getEndFlowDirection() != null) {
            // 格式：始发站点_目的站点
            String crossBorderType = customs.getStartFlowDirection().name() + "_" + customs.getEndFlowDirection().name();
            extendParam.put(EnquiryConstants.CROSS_BORDER_TYPE, crossBorderType);
        }
        // 是否文件
        if (customs.getFileTag() != null) {
            extendParam.put(EnquiryConstants.FILE_TAG, customs.getFileTag());
        }
        // 币种
        CurrencyCodeEnum currencyCodeEnum = GetFieldUtils.getCurrencyCodeEnum(customs, settlementTypeEnum);
        if (currencyCodeEnum != null) {
            extendParam.put(EnquiryConstants.EXCHANGE_CURRENCY, String.valueOf(currencyCodeEnum.getCode()));
        }
    }

    /**
     * 询价场景扩展字段：跨境报关信息
     * 跨境业务类型、是否文件、币种
     */
    private void extendParamToCustoms(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        Customs customs = orderModel.getCustoms();
        if (customs == null) {
            return;
        }
        // 跨境业务类型
        if (customs.getStartFlowDirection() != null && customs.getEndFlowDirection() != null) {
            // 格式：始发站点_目的站点
            String crossBorderType = customs.getStartFlowDirection().name() + "_" + customs.getEndFlowDirection().name();
            extendParam.put(EnquiryConstants.CROSS_BORDER_TYPE, crossBorderType);
        }
        // 是否文件
        if (customs.getFileTag() != null) {
            extendParam.put(EnquiryConstants.FILE_TAG, customs.getFileTag());
        }
        // 币种
        CurrencyCodeEnum currencyCodeEnum = getCurrencyCodeEnum(customs, orderModel.getFinance());
        if (currencyCodeEnum != null) {
            extendParam.put(EnquiryConstants.EXCHANGE_CURRENCY, String.valueOf(currencyCodeEnum.getCode()));
        }
    }

    /**
     * 获取币种
     */
    private CurrencyCodeEnum getCurrencyCodeEnum(Customs customs, Finance finance) {
        AdministrativeRegionEnum startFlowDirection = null;
        AdministrativeRegionEnum endFlowDirection = null;
        SettlementTypeEnum settlementType = null;
        if (customs != null) {
            startFlowDirection = customs.getStartFlowDirection();
            endFlowDirection = customs.getEndFlowDirection();
        }
        if (finance != null) {
            settlementType = finance.getSettlementType();
        }
        return ReceiptCurrencyUtil.getCurrency(startFlowDirection, endFlowDirection, settlementType);
    }

    /**
     * 根据派送信息中的起始站点获取站点信息
     */
    private AddressBasicPrimaryWSFacadeResponse getBaseSiteByStartStationNo(ExpressOrderModel orderModel) {
        String startStationNo = getStartStationNo(orderModel);
        if (StringUtils.isEmpty(startStationNo)) {
            return null;
        }
        AddressBasicPrimaryWSFacadeRequest addressBasicPrimaryWSFacadeRequest = addressBasicPrimaryWSFacadeTranslator.toAddressBasicPrimaryWSFacadeRequest(startStationNo);
        return addressBasicPrimaryWSFacade.getBaseSiteBySiteId(addressBasicPrimaryWSFacadeRequest.getSiteId());
    }

    /**
     * 获取派送信息中的起始站点
     */
    private String getStartStationNo(ExpressOrderModel orderModel) {
        if(orderModel != null) {
            // 询价始发站点，若传了站点，则以此站点的地址信息进行询价（扫描第一枪场景）
            if(orderModel.getEnquiry() != null && StringUtils.isNotBlank(orderModel.getEnquiry().getEnquiryStartStationNo())) {
                return orderModel.getEnquiry().getEnquiryStartStationNo();
            }

            // 优先当前单
            if (orderModel.getShipment() != null && StringUtils.isNotBlank(orderModel.getShipment().getStartStationNo())) {
                return orderModel.getShipment().getStartStationNo();
            }
            // 再从快照获取
            if (orderModel.getOrderSnapshot() != null && orderModel.getOrderSnapshot().getShipment() != null) {
                return orderModel.getOrderSnapshot().getShipment().getStartStationNo();
            }
        }
        return null;
    }

    /**
     * 补全计费结果信息
     * 主要额外处理heavyBubbleType、calWeight
     */
    public void complementBillingResultNew(ExpressOrderContext context, BillingEnquiryFacadeResponse response) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        expressOrderModelCreator.setFinanceInfo(EnquiryMapper.INSTANCE.toFinanceInfoDtoNEW(response.getFinanceFacadeDto()));
        if (null != expressOrderModelCreator.getFinanceInfo()) {
            expressOrderModelCreator.getFinanceInfo().setPayDeadline(context.getOrderModel().getFinance().getPayDeadline());
            //fixme 汇前金额用财务扩展字段承接
            Map<String,String> extendProps;
            if(null != response.getFinanceFacadeDto().getBeforeExchangeDiscountAmount()){
                extendProps = new HashMap();
                extendProps.put(FinanceConstants.BEFORE_EXCHANGE_DISCOUNT_AMOUNT,JSONUtils.beanToJSONDefault(response.getFinanceFacadeDto().getBeforeExchangeDiscountAmount()));
                extendProps.put(FinanceConstants.EXCHANGE_RATE,response.getFinanceFacadeDto().getExchangeRate());
                expressOrderModelCreator.getFinanceInfo().setExtendProps(extendProps);
            }
        }
        context.getOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

    /**
     * 功能描述: 根据询价入参，补全询价记录
     * @param context
     * @param request
     */
    public void complementEnquiry(ExpressOrderContext context, BillingEnquiryFacadeRequest request) {
        // 补全询价对象
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        expressOrderModelCreator.setEnquiryInfo(EnquiryMapper.INSTANCE.toEnquiryInfoDto(request));
        // 高峰期附加费从入参取
        if (null != expressOrderModelCreator.getEnquiryInfo()) {
            expressOrderModelCreator.getEnquiryInfo().setPeakPeriodTime(context.getOrderModel().getEnquiry().getPeakPeriodTime());
        }
        context.getOrderModel().complement().complementEnquiry(this, expressOrderModelCreator);
    }

    /**
     * 附加费补充特殊计费要素
     */
    private void attachFeeAddSpecialProductAttrs(CostInfo costInfo, BillingEnquiryFacadeRequest.ProductFacadeDto attachFeeFacadeDto) {
        if (costInfo == null) {
            return;
        }

        // 国际超长超重附加费
        if (EnquiryConstants.COST_NO_OVER_WEIGHT_INTL.equals(costInfo.getCostNo())) {
            String overweightNum = getCostQuantity(costInfo);
            if (StringUtils.isNotBlank(overweightNum)) {
                Map<String, String> productAttrs = attachFeeFacadeDto.getProductAttrs();
                if(MapUtils.isEmpty(productAttrs)){
                    productAttrs = new HashMap<>();
                    attachFeeFacadeDto.setProductAttrs(productAttrs);
                }
                productAttrs.put(EnquiryConstants.OVERWEIGHT_NUM, overweightNum);
            }
        }
    }

    /**
     * 获取费用项命中次数
     */
    private String getCostQuantity(CostInfo costInfo) {
        if (costInfo == null || MapUtils.isEmpty(costInfo.getExtendProps())) {
            return null;
        }
        return costInfo.getExtendProps().get(EnquiryConstants.COST_QUANTITY);
    }

    /**
     * 询价场景扩展字段：包裹信息
     */
    private void extendParamToPackageInformation(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        if (orderModel == null
                || orderModel.getEnquiry() == null
                || MapUtils.isEmpty(orderModel.getEnquiry().getExtendProps())
                || !orderModel.getEnquiry().getExtendProps().containsKey(EnquiryConstants.PACKAGE_LIST)) {
            return;
        }

        String jsonString = orderModel.getEnquiry().getExtendProps().get(EnquiryConstants.PACKAGE_LIST);
        if (StringUtils.isBlank(jsonString)) {
            return;
        }
        List<Package> packageList = JSONUtils.jsonToList(jsonString, Package.class);
        if (CollectionUtils.isEmpty(packageList)) {
            return;
        }
        extendParam.put(EnquiryConstants.PACKAGE_INFORMATION, PackageMapper.INSTANCE.toStandardProductPackageInformationList(packageList));
    }

    /**
     * 计费询价防腐层请求数据转换
     * 接单场景
     * 转换过程全用当前单信息
     */
    public BillingEnquiryFacadeRequest toSyncReaddressEnquiryFacadeRequest(boolean readdressEnquiry, ExpressOrderContext expressOrderContext, List<ModifyRecord> modifyRecordList, boolean firstReaddressMonth2Cash, ModifyRecord originModifyRecord) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot.getOrderBusinessIdentity() != null) {
            facadeRequest.setBusinessUnit(orderSnapshot.getOrderBusinessIdentity().getBusinessUnit());
        }
        facadeRequest.setOrderNo(orderSnapshot.orderNo());
        facadeRequest.setOrderType(orderSnapshot.getOrderType());
        //下单人编号
        facadeRequest.setOperator(orderModel.getOperator());
        //操作时间（订单中心服务接收到请求时的时间，接单场景是接单时间，修改是修改时间，取消是取消时间等）
        facadeRequest.setOperateTime(orderModel.getOperateTime());
        //青龙业主编码和青龙业主号名称
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = toCustomerFacadeDto(orderSnapshot);
        facadeRequest.setCustomerFacadeDto(customerFacadeDto);
        //关联单号，正向运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        if(!readdressEnquiry && firstReaddressMonth2Cash && null != originModifyRecord){
            LOGGER.info("改址一单到底-首次改址-月结转现结-从快照记录获取信息");
            ReaddressRecordDetailInfo modifyRecordDetail = (ReaddressRecordDetailInfo) originModifyRecord.getModifyRecordDetail();
            //产品信息
            facadeRequest.setProductFacadeDtoList(toReaddressWithOrderProductFacadeDto(orderModel, modifyRecordDetail));
            //收件人信息
            ExpressOrderModelCreator creator = new ExpressOrderModelCreator();
            creator.setConsigneeInfo(ConsigneeMapper.INSTANCE.toConsigneeInfoDto(modifyRecordDetail.getOldConsignee()));
            facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(Consignee.consigneeOf(creator)));
        } else{
            //产品信息
            facadeRequest.setProductFacadeDtoList(toReaddressWithOrderProductFacadeDto(orderModel));
            //收件人信息
            facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel));
        }
        //发件人信息
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(orderSnapshot));
        //货物信息，总重量、总体积、总数量
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel, orderSnapshot));
        //配送相关信息
        facadeRequest.setShipmentFacadeDto(toShipmentFacadeDto(orderModel, orderSnapshot));
        //财务相关信息
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel, orderSnapshot));
        //设置拓展字段 TODO 有问题
        facadeRequest.setExtendProps(toReaddressOnlinePayExtendProps(orderModel, orderSnapshot, firstReaddressMonth2Cash));
        if(readdressEnquiry){
            // 改址一单到底信息补充
            setReaddressRecord(facadeRequest, modifyRecordList);
        }

        // 询价类型
        if(null != orderModel.getEnquiry() && null != orderModel.getEnquiry().getEnquiryMode()){
            facadeRequest.setInquiryType(orderModel.getEnquiry().getEnquiryMode().getPriceCode());
        }
        return facadeRequest;
    }

    /**
     * @description 功能描述: 先款改址 添加拓展字段
     * <AUTHOR>
     * @date 2021/6/29 12:27
     * @param orderSnapshot 终端切百川前为外单数据，切百川后为原始订单数据
     * @throws
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String, Object> toReaddressOnlinePayExtendProps(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot, boolean firstReaddressMonth2Cash) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式 一单到底先款从当前单取
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(orderModel));
        if(firstReaddressMonth2Cash){
            extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, "2");
        }
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS, DEFAULT_ORDER_STATUS);
        // 仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, toDistributionType(orderSnapshot));
        //温层
//        extendParam.put(EnquiryConstants.WARM_LAYER, orderSnapshot.getShipment().getWarmLayer());
        // 温层类型  始发站编码 有则传，没有则不传
        extendParamToWarmLayerBusinessHallCode(extendParam, orderSnapshot);
        //下单渠道--散客业务涉及，计费会根据端上的渠道设置相应的折扣
        extendParam.put(EnquiryConstants.ORDER_CHANNEL, orderSnapshot.getAttachment(AttachmentKeyEnum.ORDER_MEDIUM.getKey()));
        //揽收模式，（0、普通，默认；1、云柜弃用，若启用联系产品） TODO 目前需求还没有梳理出来，暂时默认为0
        extendParam.put(EnquiryConstants.COLLECTING_MODE, DEFAULT_COLLECTING_MODE);
        //营销信息，折扣信息
        extendParam.put(EnquiryConstants.DISCOUNT_DETAIL, extendParamToDiscountDetailList(orderSnapshot));
        //积分信息
        extendParamToRewardPoints(extendParam, orderSnapshot);
        //跨境报关信息：跨境业务类型、是否文件、币种
        extendParamToCustoms(extendParam, orderSnapshot);

        // == 询价场景特有 ==
        //询价模式
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.ENQUIRY_EXTEND_INQUIRY_TYPE_SWITCH)) {
            LOGGER.info("调计费询价，传入extendParam:inquiryType,开关开启");
            extendParamToInquiryType(extendParam, orderModel);
        } else {
            LOGGER.info("调计费询价，传入extendParam:inquiryType,开关开启");
        }

        //包裹信息：取询价参数的扩展信息，传enquiryOrderModel
        extendParamToPackageInformation(extendParam, orderModel);

        return extendParam;
    }


    /**
     * @description 功能描述:  仓配类型(0：纯配；1：仓配)
     *  接单字段deliveryPattern若为1京仓发货 则该字段赋值1
     *  若为2纯配则该字段赋值0；
     *  接单字段deliveryPattern若为空且systemSubCaller为eclp则该字段赋值为1，
     *  其他情况全部赋值0
     */
    private Object toDistributionType(ExpressOrderModel orderModel) {
        Shipment shipment = orderModel.getShipment();
        if (null == shipment) {
            return DEFAULT_DISTRIBUTION_TYPE;
        }
        String deliveryPattern = shipment.getDeliveryPattern();
        if (OrderConstants.DELIVERY_PATTERN_ONE.equals(deliveryPattern)){
            return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
        } else if (deliveryPattern == null && null != orderModel.getChannel()
                && SystemSubCallerEnum.ECLP.getCode().equals(orderModel.getChannel().getSystemSubCaller())) {
            return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
        }
        return EnquiryConstants.DISTRIBUTION_PURE;

    }

    /**
     * 计费询价防腐层请求数据转换
     * 接单场景
     * 转换过程全用当前单信息
     */
    public BillingEnquiryFacadeRequest toReaddressEnquiryFacadeRequest(boolean readdressEnquiry, ExpressOrderContext expressOrderContext, List<ModifyRecord> modifyRecordList , boolean firstReaddressMonth2Cash, ModifyRecord originModifyRecord) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        if (orderModel.getOrderBusinessIdentity() != null) {
            facadeRequest.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
        }
        facadeRequest.setOrderNo(orderModel.orderNo());
        //facadeRequest.setOrderType(orderModel.getOrderType());
        //下单人编号
        facadeRequest.setOperator(orderModel.getOperator());
        //操作时间（订单中心服务接收到请求时的时间，接单场景是接单时间，修改是修改时间，取消是取消时间等）
        facadeRequest.setOperateTime(orderModel.getOperateTime());
        //青龙业主编码和青龙业主号名称 TODO
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = toCustomerFacadeDto(orderModel);
        facadeRequest.setCustomerFacadeDto(customerFacadeDto);
        //关联单号，正向运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        if(!readdressEnquiry && firstReaddressMonth2Cash && null != originModifyRecord){
            LOGGER.info("改址一单到底-首次改址-月结转现结-从快照记录获取信息");
            ReaddressRecordDetailInfo modifyRecordDetail = (ReaddressRecordDetailInfo) originModifyRecord.getModifyRecordDetail();
            //产品信息
            facadeRequest.setProductFacadeDtoList(toReaddressWithOrderProductFacadeDto(orderModel, modifyRecordDetail));
            //收件人信息
            ExpressOrderModelCreator creator = new ExpressOrderModelCreator();
            creator.setConsigneeInfo(ConsigneeMapper.INSTANCE.toConsigneeInfoDto(modifyRecordDetail.getOldConsignee()));
            facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(Consignee.consigneeOf(creator)));
        }else{
            //产品信息
            facadeRequest.setProductFacadeDtoList(toReaddressWithOrderProductFacadeDto(orderModel));
            //收件人信息
            facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel));
        }
        //发件人信息
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(orderModel));
        //货物信息，总重量、总体积、总数量
        facadeRequest.setCargoFacadeDto(asyncCargoFacadeDto(orderModel));
        //配送相关信息
        facadeRequest.setShipmentFacadeDto(toShipmentFacadeDto(orderModel));
        //财务相关信息
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel));
        //设置拓展字段
        facadeRequest.setExtendProps(toExtendProps(orderModel, firstReaddressMonth2Cash));
        if(readdressEnquiry){
            // 改址一单到底信息补充
            setReaddressRecord(facadeRequest, modifyRecordList);
        }

        // 询价类型
        if(null != orderModel.getEnquiry() && null != orderModel.getEnquiry().getEnquiryMode()){
            facadeRequest.setInquiryType(orderModel.getEnquiry().getEnquiryMode().getPriceCode());
        }
        return facadeRequest;
    }

    /**
     * 针对改址一单到底添加扩展字段
     * 次场景涉及到两个地方 同步询价 & 异步询价
     * 同步询价需要从快照判断 / 异步询价则在当前单
     * @param facadeRequest
     * @param modifyRecordList
     */
    public void setReaddressRecord(BillingEnquiryFacadeRequest facadeRequest, List<ModifyRecord> modifyRecordList) {
        // 通过当前单/快照判断单子是否是快递一单到底改址
        if (CollectionUtils.isEmpty(modifyRecordList)) {
            LOGGER.info("无改址修改记录");
            return;
        }
        // 防止扩展字段为空
        if (null == facadeRequest.getExtendProps()) {
            facadeRequest.setExtendProps(new HashMap<>());
        }

        Map<String, Object> extendProps = facadeRequest.getExtendProps();
        // 改址模式
        String readdressMode;
        int size = modifyRecordList.size();
        List<ModifyRecord> interceptModifyRecords = modifyRecordList.stream().filter(modifyRecord -> {
            return ModifyRecordTypeEnum.INTERCEPT.getCode().equals(modifyRecord.getModifyRecordType());
        }).collect(Collectors.toList());
        List<ModifyRecord> rejectModifyRecords = modifyRecordList.stream().filter(modifyRecord -> {
            return ModifyRecordTypeEnum.REJECT.getCode().equals(modifyRecord.getModifyRecordType());
        }).collect(Collectors.toList());

        // 根据修改记录处理改址模式
        if (CollectionUtils.isNotEmpty(rejectModifyRecords)) {
            // 有拒收改址记录：拒收（当前拒收一单到底不支持多种一单到底场景）
            readdressMode = EnquiryConstants.REJECTION_ORDER;
        } else {
            // 无拒收改址记录
            if(2 == size && CollectionUtils.isNotEmpty(interceptModifyRecords)){
                //数量为2且有拦截 仅拦截
                readdressMode = EnquiryConstants.INTERCEPT_THROUGH_ORDER;
            } else if(2 < size && CollectionUtils.isNotEmpty(interceptModifyRecords)){
                //数量>2且有拦截 改址后拦截
                readdressMode = EnquiryConstants.THROUGH_ORDER;
            } else {
                //仅改址
                readdressMode = EnquiryConstants.READDRESS_THROUGH_ORDER;
            }
        }

        extendProps.put(EnquiryConstants.READDRESS_MODE, readdressMode);
        // DETAIL_LIST
        List<StandardProductDetail> productDetails = (List<StandardProductDetail>) extendProps.get(EnquiryConstants.DETAIL_LIST);
        if (null == productDetails) {
            productDetails = new ArrayList<>();
        }
        // 遍历改址修改记录，获取改址详情
        for (ModifyRecord modifyRecord : modifyRecordList) {
            if(0 != modifyRecord.getModifyRecordSequence()){
                StandardProductDetail standardProductDetail = new StandardProductDetail();
                ReaddressRecordDetailInfo modifyRecordDetail = (ReaddressRecordDetailInfo) modifyRecord.getModifyRecordDetail();
                standardProductDetail.setDetailNo(String.valueOf(modifyRecordDetail.getRecordSequence()));
                standardProductDetail.setProductParams(Collections.singletonList(toReaddressProductParam(modifyRecordDetail, modifyRecord.getModifyRecordType())));
                productDetails.add(standardProductDetail);
            }
        }
        extendProps.put(EnquiryConstants.DETAIL_LIST, productDetails);

        // 指定询价返回费用扩展信息
        extendProps.put(EnquiryConstants.RETURN_FEE_EXTEND_INFO, 1);
    }

    /**
     * 根据改址修改记录，转化为计费系统的 ProductParam
     * @param modifyRecordDetail
     * @return ProductParam
     */
    private ProductParam toReaddressProductParam(ReaddressRecordDetailInfo modifyRecordDetail, String modifyRecordType) {
        ProductParam productParam = new ProductParam();
        modifyRecordDetail.getProductInfos()
                .stream()
                .filter(product -> AddOnProductEnum.READDRESS.getCode().equals(product.getProductNo()))
                .findFirst()
                .ifPresent(productInfo -> {
                    productParam.setProductCode(productInfo.getProductNo());
                    Map<String, String> productElement = new HashMap<>();
                    if (MapUtils.isNotEmpty(productInfo.getProductAttrs())) {
                        productElement.putAll(productInfo.getProductAttrs());
                    }
                    productElement.put(EnquiryConstants.RECORD_TYPE, modifyRecordType);
                    productParam.setProductElement(productElement);
                });
        return productParam;
    }


    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toReaddressWithOrderProductFacadeDto(ExpressOrderModel orderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<Product> products = orderModel.getProductDelegate().getProductList();
        String majorProductNo = orderModel.getProductDelegate().getMainProduct().getProductNo();
        boolean isHKMO = orderModel.isHKMO();
        if (CollectionUtils.isNotEmpty(products)) {
            for (Product product : products) {
                if (OperateTypeEnum.DELETE == product.getOperateType()) {
                    // 删除的产品无需参与询价
                    continue;
                }
                if (AddOnProductEnum.B2C_NON_ENQUIRY_PRODUCT.contains(product.getProductNo())) {
                    continue;
                }

                // 国补-激活校验 https://joyspace.jd.com/pages/G0hyCRMoGagEM96u9gtC
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.GB_B2C_ENQUIRY_CULL_SWITCH)
                        && AddOnProductEnum.ACTIVATION_CHECK.getCode().equals(product.getProductNo())
                        && MapUtils.isNotEmpty(product.getProductAttrs())// 产品要素包含结算编码
                        && product.getProductAttrs().containsKey(AddOnProductAttrEnum.ACTIVATION_CHECK_SETTLEMENT_ACCOUNT.getCode())
                ) {
                    continue;
                }

                // 港澳同城改址，只询改址增值服务
                if (isHKMO && !AddOnProductEnum.READDRESS_PRODUCT_CODE_SET.contains(product.getProductNo())) {
                    continue;
                }

                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                if((ProductEnum.TKZH.getCode().equals(majorProductNo) || ProductEnum.THZH.getCode().equals(majorProductNo))
                        && AddOnProductEnum.INSURED_VALUE_TOB.getCode().equals(product.getProductNo())){
                    /**
                     * 必要时将保价产品替换成特快重货-保价 fr-a-0016
                     * 2023-05-31 与快运产品王传宇、林恒波确认，主产品是fr-m-0004（特快重货）+fr-m-0002（特惠重货）时，调计费标准产品询价接口fr-a-0002（普通保价），需要转化成fr-a-0016（特快重货保价）
                     * 背景：业务及运营系统，目前保价只识别了fr-a-0002 （普通保价），下单也只会下一个码，但计费是根据两个保价码分别计费的。短期方案，订单中心调计费根据主产品去做码的转换，长期方案，需要王传宇和林恒波 推动业务及运营系统按标准的两个码执行
                     */
                    productFacadeDto.setProductNo(AddOnProductEnum.FR_A_0016.getCode());
                } else {
                    productFacadeDto.setProductNo(product.getProductNo());
                }
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setAttachFees(Boolean.FALSE);
                productFacadeDto.setProductAttrs(product.getProductAttrs());

                if(ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(product.getProductType())) {
                    fillPickupDeliveryMode(orderModel, productFacadeDto);
                }
                productFacadeDtos.add(productFacadeDto);
            }
        }
        //todo 补全附加服务费
        if(CollectionUtils.isNotEmpty(orderModel.getFinance().getAttachFees()) && !isHKMO){
            for (CostInfo costInfo : orderModel.getFinance().getAttachFees()) {
                BillingEnquiryFacadeRequest.ProductFacadeDto attachFeeFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                attachFeeFacadeDto.setProductNo(costInfo.getCostNo());
                attachFeeFacadeDto.setAttachFees(Boolean.TRUE);
                attachFeeFacadeDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                attachFeeAddSpecialProductAttrs(costInfo, attachFeeFacadeDto);
                productFacadeDtos.add(attachFeeFacadeDto);
            }
        }
        return productFacadeDtos;
    }

    /**
     * 月结转到付，原单询价
     * 港澳同城改址，异步询价写台帐，此处是月结转到付，原单询价，不能只传改址产品，无上面改址询价独有逻辑isHKMO
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toReaddressWithOrderProductFacadeDto(ExpressOrderModel orderModel, ReaddressRecordDetailInfo modifyRecordDetail) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<Product> products;
        if(CollectionUtils.isNotEmpty(modifyRecordDetail.getProductInfos())){
            products = ProductMapper.INSTANCE.toProducts(modifyRecordDetail.getProductInfos());
        } else {
            products = orderModel.getProductDelegate().getProductList();
        }
        if (CollectionUtils.isNotEmpty(products)) {
            for (Product product : products) {
                if (OperateTypeEnum.DELETE == product.getOperateType()) {
                    // 删除的产品无需参与询价
                    continue;
                }
                if (AddOnProductEnum.B2C_NON_ENQUIRY_PRODUCT.contains(product.getProductNo())) {
                    continue;
                }

                // 国补-激活校验 https://joyspace.jd.com/pages/G0hyCRMoGagEM96u9gtC
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.GB_B2C_ENQUIRY_CULL_SWITCH)
                        && AddOnProductEnum.ACTIVATION_CHECK.getCode().equals(product.getProductNo())
                        && MapUtils.isNotEmpty(product.getProductAttrs())// 产品要素包含结算编码
                        && product.getProductAttrs().containsKey(AddOnProductAttrEnum.ACTIVATION_CHECK_SETTLEMENT_ACCOUNT.getCode())
                ) {
                    continue;
                }

                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setAttachFees(Boolean.FALSE);
                productFacadeDto.setProductAttrs(product.getProductAttrs());

                if(ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(product.getProductType())) {
                    fillPickupDeliveryMode(orderModel, productFacadeDto);
                }
                productFacadeDtos.add(productFacadeDto);
            }
        }
        //todo 补全附加服务费
        if(CollectionUtils.isNotEmpty(orderModel.getFinance().getAttachFees())){
            for (CostInfo costInfo : orderModel.getFinance().getAttachFees()) {
                BillingEnquiryFacadeRequest.ProductFacadeDto attachFeeFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                attachFeeFacadeDto.setProductNo(costInfo.getCostNo());
                attachFeeFacadeDto.setAttachFees(Boolean.TRUE);
                attachFeeFacadeDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                attachFeeAddSpecialProductAttrs(costInfo, attachFeeFacadeDto);
                productFacadeDtos.add(attachFeeFacadeDto);
            }
        }
        return productFacadeDtos;
    }

    /**
     * 补充拦派模式
     * @param orderModel
     * @param productFacadeDto
     */
    private void fillPickupDeliveryMode(ExpressOrderModel orderModel, BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto) {
        //港澳：特惠送，特快送计费时传入揽派模式 TODO 这个揽派模式需要存储吗？
        if(ProductEnum.THS.getCode().equals(productFacadeDto.getProductNo())
                || ProductEnum.TKS.getCode().equals(productFacadeDto.getProductNo())) {
            Map<String, String> productAttrs = new HashMap<>();
            if(MapUtils.isNotEmpty(productFacadeDto.getProductAttrs())){
                productAttrs.putAll(productFacadeDto.getProductAttrs());
            }
            if(null != orderModel.getShipment()){
                //揽收模式
                if(null != orderModel.getShipment().getPickupType()){
                    productAttrs.put(EnquiryConstants.PICKUP_MODEL,String.valueOf(orderModel.getShipment().getPickupType().getCode()));
                }
                //派送模式
                if(null != orderModel.getShipment().getDeliveryType()){
                    productAttrs.put(EnquiryConstants.DELIVERY_MODEL,String.valueOf(orderModel.getShipment().getDeliveryType().getCode()));
                }
            }
            productFacadeDto.setProductAttrs(productAttrs);
        }
    }


    /**
     * 后款订单 改址一单到底 补齐信息
     * 补全计费结果信息
     */
    public void complementAsynReaddressBillingFinance(FinanceInfo newFinanceInfo, BigDecimal originDiscountMoney, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        if (newFinanceInfo == null) {
            newFinanceInfo = new FinanceInfo();
        }
        BillingConvertor.INSTANCE.complementFinanceInfo(billingEnquiryFacadeResponse.getFinanceFacadeDto(), newFinanceInfo);
        // 差额
        // 根据询价后的金额 对比原金额 计算差额
        BigDecimal newDiscountMoney = Optional.ofNullable(newFinanceInfo.getDiscountAmount().getAmount()).orElse(BigDecimal.ZERO);
        BigDecimal diffAmount = newDiscountMoney.subtract(originDiscountMoney);
        LOGGER.info("改址差额计算，old:{}, new:{}, diff:{}", originDiscountMoney, newDiscountMoney, diffAmount);
        //差额
        newFinanceInfo.setReceivableDifferenceAmount(MoneyMapper.INSTANCE.toMoneyInfo(diffAmount, newFinanceInfo.getDiscountAmount().getCurrencyCode()));
        //待支付金额
        if (BigDecimal.ZERO.compareTo(diffAmount) >= 0){
            newFinanceInfo.setPendingMoney(MoneyMapper.INSTANCE.toMoneyInfo(BigDecimal.ZERO, newFinanceInfo.getDiscountAmount().getCurrencyCode()));
        } else {
            newFinanceInfo.setPendingMoney(MoneyMapper.INSTANCE.toMoneyInfo(diffAmount, newFinanceInfo.getDiscountAmount().getCurrencyCode()));
        }
    }

    /**
     * 补充附加费
     *
     * @param requestProfile
     * @param orderModel
     * @param snapshot
     * @param billingEnquiryFacadeRequest
     * @return
     */
    public BillingEnquiryFacadeRequest fillProductCenterSurcharge(RequestProfile requestProfile, ExpressOrderModel orderModel,
                                                                  ExpressOrderModel snapshot, BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {

        // 整单维度附加费列表
        List<Product> products;
        // 包裹维度附加费列表
        List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList = new ArrayList<>();

        // 获取询价扩展字段包裹明细列表
        List<Package> packageList = getEnquiryPackages(orderModel);

        // 查询产品中心附加费综合查询接口
        List<SurchargeResponse> surchargeResponseList = getSurchargeFacade(orderModel, snapshot, requestProfile, packageList);

        // 获取主产品编码
        String mainProductNo = snapshot.getProductDelegate().getMainProduct().getProductNo();
        // 根据主产品编码获取整单维度附加费
        products = surchargeFacadeTranslator.fetchWaybillSurcharge(mainProductNo, surchargeResponseList);
        LOGGER.info("C2C询价-获取产品中心整单维度附加费 {}", JSONUtils.beanToJSONDefault(products));
        // 根据主产品编码获取包裹维度附加费
        packageSurchargeList = surchargeFacadeTranslator.fetchPackageSurcharge(mainProductNo, surchargeResponseList, packageList);
        LOGGER.info("C2C询价-获取产品中心包裹维度附加费 {}", JSONUtils.beanToJSONDefault(packageSurchargeList));

        // 有条件过滤掉超长超重附加费
        // 过滤条件：
        // a、运单模式下，包裹数大于等于2时过滤；
        // b、仓配快递接货仓模式时过滤
        if (needFilterOverLengthAndWeightSurcharge(orderModel, snapshot)) {
            LOGGER.info("C2C询价-满足过滤条件过滤超长超重附加费");
            filterOverLengthAndWeightSurcharge(products, packageSurchargeList);
        }

        // 如果上游请求的是运单维度附加费，但是因产品中心附加费编码不变，只是将附加费性质从运单维度切换到了包裹维度（目前业务已知的只有超长超重附加费）
        // 这种情况下，需要将包裹维度附加费转换成运单维度附加费
        resetSurchargeForWaybillMode(orderModel, products, packageSurchargeList);

        // 需收取包裹维度附加费的包裹数超过100时，按包裹重量降序排列只取前100个，并告警
        if (packageSurchargeList.size() > EnquiryConstants.MAX_PACKAGE_FOR_SURCHARGE) {
            // 包裹明细数量超阈值告警信息
            String businessAlarmMsg = new StringBuilder()
                    .append("订单[")
                    .append(orderModel.orderNo())
                    .append("]询价,包裹维度附加费涉及包裹个数[")
                    .append(packageSurchargeList.size())
                    .append("]超过最大限制[")
                    .append(EnquiryConstants.MAX_PACKAGE_FOR_SURCHARGE).append("],已按包裹重量倒叙排序并截取前100个").toString();

            // 若超过100，按包裹重量倒叙排列，截取前100个，并告警
            packageSurchargeList = packageSurchargeList.stream()
                    .sorted((p1, p2) -> p2.getWeight().compareTo(p1.getWeight()))
                    .limit(EnquiryConstants.MAX_PACKAGE_FOR_SURCHARGE).collect(Collectors.toList());

            // 自定义告警
            LOGGER.warn(businessAlarmMsg);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_SURCHARGE_PACKAGE_NUM_ALARM
                    , System.currentTimeMillis()
                    , businessAlarmMsg);
        }

        // 附加费写入订单
        // 附加费扩展字段中只存包裹号
        writeSurchargeToOrderModel(orderModel, products, packageSurchargeList);

        LOGGER.info("C2C询价-设置计费入参前的整单维度附加费 {}", JSONUtils.beanToJSONDefault(products));
        LOGGER.info("C2C询价-设置计费入参前的包裹维度附加费 {}", JSONUtils.beanToJSONDefault(packageSurchargeList));

        // 整单附加费
        if (CollectionUtils.isNotEmpty(products)) {
            billingEnquiryFacadeRequest.getProductFacadeDtoList().addAll(peakPeriodToProductFacadeDto(products));
        }

        // 包裹附加费(开关关闭时走不到此逻辑)
        if (CollectionUtils.isNotEmpty(packageSurchargeList)) {
            extendParamToPackageList(billingEnquiryFacadeRequest, packageSurchargeList);
        }

        return billingEnquiryFacadeRequest;
    }

    /**
     * 重置附加费性质，将包裹维度附加费重置为运单维度
     *
     * @param orderModel 订单模型
     * @param products 运单维度附加费
     * @param packageSurchargeList 包裹维度附加费（其中含运单维度附加费）
     */
    public void resetSurchargeForWaybillMode(ExpressOrderModel orderModel, List<Product> products, List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList) {

        // 没有需要处理的包裹维度附加费
        if (CollectionUtils.isEmpty(packageSurchargeList)) {
            LOGGER.info("C2C询价-包裹维度附加费为空不需要重置附加费性质");
            return;
        }

        // 非包裹模式（运单模式）下需要将包裹维度附加费列表重置为运单维度附加费
        if (!orderModel.getEnquiry().isPackageWeightingMode()) {
            LOGGER.info("C2C询价-运单模式-重置包裹维度附加费为运单维度附加费");

            // 收集需要重置的附加费
            List<Product> resetProducts = new ArrayList<>();

            // 取出所有包裹附加费
            for (BillingEnquiryFacadeRequest.PackageFacadeDto dto : packageSurchargeList) {
                List<BillingEnquiryFacadeRequest.ProductFacadeDto> productList = dto.getProductList();

                if (CollectionUtils.isEmpty(productList)) {
                    continue;
                }

                for (BillingEnquiryFacadeRequest.ProductFacadeDto facadeDto : productList) {
                    Product product = new Product();
                    product.setProductNo(facadeDto.getProductNo());
                    product.setProductName(facadeDto.getProductName());
                    product.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                    resetProducts.add(product);
                }
            }

            // 去重
            resetProducts = new ArrayList<>(resetProducts.stream().collect(Collectors.toMap(Product::getProductNo, Function.identity(), (v1, v2) -> v1)).values());

            // 写入运单维度
            if (products == null) {
                products = new ArrayList<>();
            }
            products.addAll(resetProducts);

            // 包裹维度附加费列表清空
            packageSurchargeList.clear();
        }
        LOGGER.info("C2C询价-重置后的整单维度附加费 {}", JSONUtils.beanToJSONDefault(products));
        LOGGER.info("C2C询价-重置后的包裹维度附加费 {}", JSONUtils.beanToJSONDefault(packageSurchargeList));
    }

    /**
     * 是否需要过滤掉超长超重附加费
     * -- 过滤条件：
     * -- a、运单模式下，包裹数大于等于2时过滤
     * -- b、仓配快递接货仓模式时过滤
     *
     * @param orderModel
     * @param snapshot
     * @return
     */
    public boolean needFilterOverLengthAndWeightSurcharge(ExpressOrderModel orderModel, ExpressOrderModel snapshot) {
        return (!orderModel.getEnquiry().isPackageWeightingMode()
                && orderModel.getEnquiry().isMultiPackage())
                ||
                (SupplyChainDeliveryOrderSignUtil.currentFlag(snapshot)
                        && OrderConstants.DELIVERY_PATTERN_ONE.equals(snapshot.getShipment().getShipmentExtendProp(OrderConstants.DELIVERY_PATTERN)))
                ||
                (!orderModel.getEnquiry().is238OpeType());
    }

    /**
     * 过滤掉超长超重附加费
     *
     * @param products
     * @param packageSurchargeList
     */
    public void filterOverLengthAndWeightSurcharge(List<Product> products, List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList) {
        // 过滤掉整单维度超长超重附加费
        products = products.stream().filter(product -> !EnquiryConstants.CCCZF_0002.equals(product.getProductNo())).collect(Collectors.toList());

        // 过滤掉包裹维度超长超重附加费
        for (BillingEnquiryFacadeRequest.PackageFacadeDto packageFacadeDto : packageSurchargeList) {
            List<BillingEnquiryFacadeRequest.ProductFacadeDto> productList = packageFacadeDto.getProductList();
            if (CollectionUtils.isEmpty(productList)) {
                continue;
            }
            productList = productList.stream().filter(product -> !EnquiryConstants.CCCZF_0002.equals(product.getProductNo())).collect(Collectors.toList());
            packageFacadeDto.setProductList(productList);
        }
    }

    /**
     * 把附加费写入订单模型
     *
     * @param orderModel
     * @param products 整单维度附加费
     * @param packageSurchargeList 包裹维度附加费
     */
    public void writeSurchargeToOrderModel(ExpressOrderModel orderModel, List<Product> products,
                                            List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList) {

        // 需要写入的附加费列表
        List<CostInfo> attachFees = new ArrayList<>();

        // 【运单维度附加费】
        for (Product product : products) {
            CostInfo costInfo = new CostInfo();
            costInfo.setCostNo(product.getProductNo());
            costInfo.setCostName(product.getProductName());
            // 只有超长超重附加费会有称重模式
            if (EnquiryConstants.CCCZF_0002.equals(costInfo.getCostNo())) {
                // 扩展字段写入称重模式
                Map<String, String> extendProps = new HashMap<>();
                extendProps.put(EnquiryConstants.WEIGHTING_MODE, String.valueOf(WeightingModeEnum.WAYBILL.getCode()));
                costInfo.setExtendProps(extendProps);
            }
            attachFees.add(costInfo);
        }

        // 【包裹维度附加费】
        // 产品编码-产品名称映射关系
        Map<String, String> productNo2NameMap = new HashMap<>();
        // 产品编码-包裹明细列表映射关系
        Map<String, List<BillingEnquiryFacadeRequest.PackageFacadeDto>> product2PackageListMap = new HashMap<>();
        // 结构转换：package-productList 转换成 product-packageList
        surchargeFacadeTranslator.convertPackageProductListToProductPackageList(packageSurchargeList, productNo2NameMap, product2PackageListMap);
        //
        for (Map.Entry<String, List<BillingEnquiryFacadeRequest.PackageFacadeDto>> entry : product2PackageListMap.entrySet()) {
            CostInfo costInfo = new CostInfo();
            // 附加费编码
            costInfo.setCostNo(entry.getKey());
            // 附加费名称
            costInfo.setCostName(productNo2NameMap.get(entry.getKey()));

            // 只有超长超重附加费会有称重模式和包裹明细
            if (EnquiryConstants.CCCZF_0002.equals(costInfo.getCostNo())) {
                // 包裹模式，附加费扩展字段写入包裹明细列表（只写包裹号）
                Map<String, String> extendProps = new HashMap<>();
                // 扩展字段写入称重模式
                extendProps.put(EnquiryConstants.WEIGHTING_MODE, String.valueOf(WeightingModeEnum.PACKAGE.getCode()));
                // 获取当前附加费关联的包裹号列表
                List<String> packageNoList = entry.getValue().stream().map(BillingEnquiryFacadeRequest.PackageFacadeDto::getPackageNo).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(packageNoList)) {
                    // 附加费包裹明细
                    extendProps.put(EnquiryConstants.PACKAGE_LIST, JSONUtils.beanToJSONDefault(packageNoList));
                }
                costInfo.setExtendProps(extendProps);
            }

            attachFees.add(costInfo);
        }

        // 附加费写入订单模型
        if (CollectionUtils.isNotEmpty(attachFees)) {
            // 最新附加费覆盖原有的
            orderModel.getFinance().setAttachFees(attachFees);
        } else {
            // 如果本次查询产品中心返回的附加费为空，则清空原附加费
            orderModel.getFinance().setAttachFees(null);
        }
    }

    /**
     * 获取询价扩展字段中的包裹明细列表
     *
     * @param orderModel
     * @return
     */
    public List<Package> getEnquiryPackages(ExpressOrderModel orderModel) {
        String packageListJson = orderModel.getEnquiry().getExtendProps(EnquiryConstants.PACKAGE_LIST);
        if (StringUtils.isBlank(packageListJson)) {
            return new ArrayList<>();
        }
        return JSONUtils.jsonToList(packageListJson, Package.class);
    }

    /**
     * 查询附加费（含运单维度+包裹维度）
     *
     * @param expressOrderModel
     * @param snapshot
     * @param profile
     * @return
     */
    public List<SurchargeResponse> getSurchargeFacade(ExpressOrderModel expressOrderModel, ExpressOrderModel snapshot,
                                                       RequestProfile profile, List<Package> packageList) {
        SurchargeRequest request = surchargeFacadeTranslator.toSurchargeFacadeRequest(expressOrderModel, snapshot, profile, packageList);
        return surchargeFacade.querySurchargeInfoFacade(request);
    }

    public List<BillingEnquiryFacadeRequest.ProductFacadeDto> peakPeriodToProductFacadeDto(List<Product> products) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(products)) {
            for (IProduct product : products) {
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }
        return productFacadeDtos;
    }

    /**
     * 包裹维度附加费列表 设置到询价扩展字段
     *
     * @param billingEnquiryFacadeRequest
     * @param packageSurchargeList
     */
    public void extendParamToPackageList(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest, List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList) {
        if (billingEnquiryFacadeRequest == null || CollectionUtils.isEmpty(packageSurchargeList)) {
            return;
        }

        Map<String, Object> extendProps = billingEnquiryFacadeRequest.getExtendProps();
        if (extendProps == null) {
            extendProps = new HashMap<>();
        }

        List<StandardProductPackage> standardProductPackageList = new ArrayList<>();
        for (BillingEnquiryFacadeRequest.PackageFacadeDto packageFacadeDto : packageSurchargeList) {
            StandardProductPackage standardProductPackage = new StandardProductPackage();
            // 包裹号
            standardProductPackage.setCode(packageFacadeDto.getPackageNo());
            // 包裹数
            standardProductPackage.setQty(packageFacadeDto.getQuantity());
            // 重量
            standardProductPackage.setWeight(packageFacadeDto.getWeight() == null ? null : packageFacadeDto.getWeight().doubleValue());
            // 体积
            standardProductPackage.setVolume(packageFacadeDto.getVolume() == null ? null : packageFacadeDto.getVolume().doubleValue());

            // 产品列表
            List<ProductParam> productParamList = new ArrayList<>();
            List<BillingEnquiryFacadeRequest.ProductFacadeDto> productList = packageFacadeDto.getProductList();
            if (CollectionUtils.isNotEmpty(productList)) {
                for (BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto : productList) {
                    ProductParam productParam = new ProductParam();
                    productParam.setProductCode(productFacadeDto.getProductNo());
                    productParam.setProductElement(productFacadeDto.getProductAttrs());
                    productParamList.add(productParam);
                }
                standardProductPackage.setProducts(productParamList);
                standardProductPackageList.add(standardProductPackage);
            }
        }

        if (CollectionUtils.isNotEmpty(standardProductPackageList)) {
            extendProps.put(EnquiryConstants.PACKAGE_LIST, standardProductPackageList);
            billingEnquiryFacadeRequest.setExtendProps(extendProps);
        }
    }

    /**
     * 改址-月结转到付-询价结果处理
     *
     * @param context                        上下文
     * @param modifyRecordDetail             改址记录明细
     * @param snapshotEnquiryFacadeResponse  原单询价结果
     * @param readdressEnquiryFacadeResponse 新单询价结果
     * @return 是否需要支付
     */
    public boolean readdressSnapshotMonthSettleComplementBillingResult(ExpressOrderContext context, ReaddressRecordDetailInfo modifyRecordDetail, BillingEnquiryFacadeResponse snapshotEnquiryFacadeResponse, BillingEnquiryFacadeResponse readdressEnquiryFacadeResponse) {
        LOGGER.info("改址一单到底-月结转现结,原单按到付询价结果：{}", JSONUtils.beanToJSONDefault(snapshotEnquiryFacadeResponse));
        ExpressOrderModel orderModel = context.getOrderModel();
        FinanceInfo financeInfo = modifyRecordDetail.getFinance();
        if (financeInfo == null) {
            financeInfo = new FinanceInfo();
            modifyRecordDetail.setFinance(financeInfo);
        }

        BillingConvertor.INSTANCE.complementFinanceInfo(readdressEnquiryFacadeResponse.getFinanceFacadeDto(), financeInfo);

        // 差额
        // 根据询价后的金额 对比原金额 计算差额
        BigDecimal discountMoney = Optional.ofNullable(financeInfo.getDiscountAmount().getAmount()).orElse(BigDecimal.ZERO);
        BigDecimal originDiscountMoney = Optional.ofNullable(snapshotEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount()).orElse(BigDecimal.ZERO);
        BigDecimal diffAmount = discountMoney.subtract(originDiscountMoney);
        //返回是否需要支付
        int result = diffAmount.compareTo(BigDecimal.ZERO);
        LOGGER.info("改址一单到底-月结转现结,询价后差额={}", result);
        // 改址记录差额
        financeInfo.setReceivableDifferenceAmount(MoneyMapper.INSTANCE.toMoneyInfo(diffAmount, financeInfo.getDiscountAmount().getCurrencyCode()));
        // 先款改址
        if (PaymentStageEnum.ONLINEPAYMENT == orderModel.getFinance().getPaymentStage()) {
            if (result > 0) {
                context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY.getCode(), OrderConstants.YES_VAL);
                MoneyInfo pendingMoney = MoneyMapper.INSTANCE.toMoneyInfo(diffAmount, financeInfo.getDiscountAmount().getCurrencyCode());
                context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode(), pendingMoney);
                financeInfo.setPendingMoney(pendingMoney);
            } else {
                financeInfo.setSettlementType(SettlementTypeEnum.MONTHLY_PAYMENT.getCode());
            }
            if (null == financeInfo.getExtendProps()) {
                financeInfo.setExtendProps(new HashMap<>());
            }
        }
        LOGGER.info("改址一单到底-月结转现结,complementBillingResult:financeInfo={}", JSONUtils.beanToJSONDefault(financeInfo));
        return OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.READDRESS_NEED_PAY.getCode()));
    }

    /**
     * 修改场景计费询价接口转换
     * @param orderModel 订单模型
     * @param snapshot 订单快照
     * @return
     */
    public BillingEnquiryFacadeRequest toModifyEnquiryRequest(ExpressOrderModel orderModel, ExpressOrderModel snapshot) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        facadeRequest.setOrderNo(orderModel.orderNo());
        facadeRequest.setOrderType(orderModel.getOrderType());
        facadeRequest.setOrderStatus(orderModel.getOrderStatus());
        //青龙业主编码和青龙业主号名称
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(snapshot));
        // 运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(snapshot.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        // 产品信息  用传过来的订单号查出来有都赋值进去
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel, snapshot, false));
        // 总重量、总体积、
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel, snapshot));
        // 发件人信息 -- 地址
        facadeRequest.setConsignorFacadeDto(toModifyConsignorFacadeDto(orderModel, snapshot));
        // 收件人信息 -- 地址
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(snapshot));
        // 财务相关信息
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel, snapshot));
        // 设置拓展字段 TODO 后续修改场景进入询价需要考虑是否补全一个完整模型，用完整模型调用方法，不需要考虑从快照还是当前单
        facadeRequest.setExtendProps(toModifyExtendProps(orderModel, snapshot, false));
        return facadeRequest;
    }

    /**
     * 修改时转换为计费接口参数
     * 揽收后改增值服务、揽收后改址，异步询价；
     * 揽收后改址，同步询价
     */
    public BillingEnquiryFacadeRequest toServiceEnquiryRequest(ExpressOrderContext expressOrderContext, ExpressOrderModel serviceOrder) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        // 当前单：异步询价是持久化数据；同步询价是修改请求
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        // 原单：订单快照信息
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        // 交易业务单元
        facadeRequest.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
        // 订单号
        facadeRequest.setOrderNo(serviceOrder.orderNo());
        // 操作人
        facadeRequest.setOperator(serviceOrder.getOperator());
        // 客户信息
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(serviceOrder));
        // 正向运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(snapshot.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        // 渠道信息
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));
        // 产品信息
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(serviceOrder, null, false));
        // 服务单号
        facadeRequest.setServiceNo(serviceOrder.getCustomOrderNo());
        // 询价类型
        facadeRequest.setInquiryType(BillingInquiryTypeEnum.ATTACH.getCode());
        // 货品信息
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel, snapshot));
        // 发件人信息
        facadeRequest.setConsignorFacadeDto(toModifyConsignorFacadeDto(serviceOrder));
        // 收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(serviceOrder));
        // 财务相关信息
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(serviceOrder));
        // 扩展属性
        facadeRequest.setExtendProps(toExtendProps(serviceOrder, false));
        return facadeRequest;
    }

    /**
     * 修改时转换为计费接口参数
     * 揽收后改增值服务、揽收后改址，异步询价；
     * 揽收后改址，同步询价
     */
    public BillingEnquiryFacadeRequest toZCServiceEnquiryRequest(ExpressOrderContext expressOrderContext, ExpressOrderModel zcOrder) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        // 当前单：异步询价是持久化数据；同步询价是修改请求
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        // 原单
        ExpressOrderModel originalOrderModel = orderModel.getOrderSnapshot();

        // 交易业务单元
        facadeRequest.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());

        // 订单号
        facadeRequest.setOrderNo(zcOrder.orderNo());

        // 操作人
        facadeRequest.setOperator(zcOrder.getOperator());

        // 客户信息
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(originalOrderModel));

        // 正向运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(originalOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        // 渠道信息
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));

        // 产品信息
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel, true));

        //服务单号
        facadeRequest.setServiceNo(zcOrder.getCustomOrderNo());

        //询价类型
        facadeRequest.setInquiryType(BillingInquiryTypeEnum.ATTACH.getCode());

        // 货品信息
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel, originalOrderModel));

        // 发件人信息
        facadeRequest.setConsignorFacadeDto(toModifyConsignorFacadeDto(originalOrderModel));

        // 收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(originalOrderModel));

        // 财务相关信息
        facadeRequest.setFinanceFacadeDto(toModifyFinanceFacadeDto(zcOrder));

        // 扩展属性
        facadeRequest.setExtendProps(new HashMap<>());
        return facadeRequest;
    }

    /**
     * 发货人信息：修改场景
     * 新单没有取原单
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toModifyConsignorFacadeDto(ExpressOrderModel orderModel, ExpressOrderModel snapshot) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        Consignor consignor;
        if (orderModel.getConsignor() != null
                && orderModel.getConsignor().getAddress() != null
                && orderModel.getConsignor().getAddress().getAddress() != null) {
            consignor = orderModel.getConsignor();
        } else {
            consignor = orderModel.getOrderSnapshot().getConsignor();
        }
        Address address = consignor.getAddress();
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        if (address != null) {
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 发货人信息：修改场景
     * 新单没有取原单
     */
    @Deprecated
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toModifyConsignorFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        Consignor consignor;
        if (orderModel.getConsignor() != null && orderModel.getConsignor().getAddress() != null) {
            consignor = orderModel.getConsignor();
        } else {
            consignor = orderModel.getOrderSnapshot().getConsignor();
        }
        Address address = consignor.getAddress();
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        if (address != null) {
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 财务信息：修改场景
     * 新单没有取原单
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toModifyFinanceFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        financeFacadeDto.setEnquireTime(DateUtils.now());
        //结算方式
        if (orderModel.getFinance() != null
                && orderModel.getFinance().getSettlementType() != null) {
            financeFacadeDto.setSettlementType(orderModel.getFinance().getSettlementType());
        } else if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getFinance() != null
                && orderModel.getOrderSnapshot().getFinance().getSettlementType() != null) {
            financeFacadeDto.setSettlementType(orderModel.getOrderSnapshot().getFinance().getSettlementType());
        }

        // 附加费信息
        if (orderModel.getFinance() != null
                && CollectionUtils.isNotEmpty(orderModel.getFinance().getAttachFees())) {
            financeFacadeDto.setAttachFees(toCostInfoDtoList(orderModel.getFinance().getAttachFees()));
        } else if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getFinance() != null
                && CollectionUtils.isNotEmpty(orderModel.getOrderSnapshot().getFinance().getAttachFees())) {
            financeFacadeDto.setAttachFees(toCostInfoDtoList(orderModel.getOrderSnapshot().getFinance().getAttachFees()));
        }

        return financeFacadeDto;
    }


    /**
     * 附加费对象转换
     */
    private List<CostInfoDto> toCostInfoDtoList(List<CostInfo> costInfoList) {
        if (CollectionUtils.isEmpty(costInfoList)) {
            return null;
        }
        List<CostInfoDto> costInfoDtoList = new ArrayList<>();
        costInfoList.forEach(costInfo -> {
            if (costInfo != null && OperateTypeEnum.DELETE != costInfo.getOperateType()) {
                costInfoDtoList.add(toCostInfoDto(costInfo));
            }
        });
        return costInfoDtoList;
    }

    /**
     * 附加费对象转换
     *
     * @param costInfo
     * @return
     */
    public CostInfoDto toCostInfoDto(CostInfo costInfo) {
        CostInfoDto dto = new CostInfoDto();
        //费用项编码
        dto.setCostNo(costInfo.getCostNo());
        //费用项名称
        dto.setCostName(costInfo.getCostName());
        //收费方	0：向商家收 1：向寄件方收
        dto.setChargingSource(costInfo.getChargingSource());
        //月结费用填写（结算编码）前端卡控条件必填
        dto.setSettlementAccountNo(costInfo.getSettlementAccountNo());
        dto.setExtendProps(costInfo.getExtendProps());
        return dto;
    }
}
