package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.CostInfoDto;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.EnquiryInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.PointsInfoDto;
import cn.jdl.oms.express.domain.dto.ProductInfoDto;
import cn.jdl.oms.express.domain.dto.QuantityInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingInquiryTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.FreightGetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.FreightReaddressUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.PackageServiceUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.TrustSellerUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachFeeEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryModeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.TicketSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightingModeEnum;
import cn.jdl.oms.express.domain.spec.util.ReceiptCurrencyUtil;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.Dimension;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Fence;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Package;
import cn.jdl.oms.express.domain.vo.Points;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.google.common.collect.Lists;
import com.jd.lbs.product.inquiry.dto.request.DiscountDetailDTO;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.CALC_PRICE_ITEM_LIST;

/**
 * 快运询价防腐层数据转换器
 */
@Translator
public class FreightEnquiryFacadeTranslator {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(FreightEnquiryFacadeTranslator.class);

    // 单据状态 默认传0
    private static final int DEFAULT_ORDER_STATUS = 0;
    // 交付模式
    private static final String DELIVERY_PATTERN = "deliveryPattern";
    // 交付模式 为 1京仓发货
    private static final String DELIVERY_PATTERN_ONE = "1";
    // 交付模式 为 2
    private static final String DELIVERY_PATTERN_TWO = "2";
    // 拓展字段
    private static final String SHIPMENT_EXTEND_PROPS ="shipmentExtendProps";
    // eclp
    private static final String ECLP ="eclp";

    //计费折扣类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
    private static final int DISCOUNT_TYPE_DISCOUNT =1;
    private static final int DISCOUNT_TYPE_JD_TICKET =2;
    private static final int DISCOUNT_TYPE_DADA_TICKET =3;
    private static final int DISCOUNT_TYPE_JD_TICKET_BATCH_NO =4;

    //计费产品要素，主产品编码
    private static final String MAIN_PRODUCT_CODE = "mainProductCode";

    /**
     * 配送信息-扩展信息 营业厅编码 KEY
     */
    private static final String BUSINESS_HALL_NO_KEY = "businessHallNo";

    // 1表示返回
    private static final String RETURN_MISMATCH = "1";
    /**
     * 事业部信息
     */
    @Resource
    private CustomerFacade customerFacade;

    /**
     * 包装服务产品要素处理工具类
     */
    @Resource
    private PackageServiceUtil packageServiceUtil;

    /**
     * Ucc
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    @Resource
    private EnquiryFacadeTranslator enquiryFacadeTranslator;

    /**
     * 产品中心-附加费查询
     */
    @Resource
    private SurchargeFacadeTranslator surchargeFacadeTranslator;

    /**
     * OFC复重复量方调用询价接口（正向单、逆向单）
     */
    public BillingEnquiryFacadeRequest toOfcEnquiryRequest(ExpressOrderContext expressOrderContext, boolean attachFeesFromModel) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        // OFC调询价接口的请求参数
        ExpressOrderModel ofcOrderModel = expressOrderContext.getOrderModel();
        // 外单数据更新后的订单
        ExpressOrderModel updatedOrderModel = ofcOrderModel.getOrderSnapshot();
        // 原单
        ExpressOrderModel originalOrderModel = updatedOrderModel.getOrderSnapshot();

        // 交易业务单元：快运B2C或快运C2C
        facadeRequest.setBusinessUnit(originalOrderModel.getOrderBusinessIdentity().getBusinessUnit());

        // 订单号：取OFC传值
        facadeRequest.setOrderNo(ofcOrderModel.orderNo());

        // 客户信息：原单
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(originalOrderModel));

        // 运单号：取外单更新值
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(updatedOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        // 渠道信息：取OFC传值
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(ofcOrderModel));

        // 产品信息：取OFC传值、外单更新值
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(ofcOrderModel, updatedOrderModel, originalOrderModel));

        // 货品信息：总重量、总体积按是否信任商家取值，总数量取外单更新值
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(ofcOrderModel, updatedOrderModel, originalOrderModel));

        // 发件人信息：二级地址市取OFC传值，其余取外单更新值
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(ofcOrderModel, updatedOrderModel));

        // 收件人信息：取外单更新值
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(updatedOrderModel));

        // 财务相关信息：询价时间取当前时间、结算方式取外单更新值、抵扣信息取OFC传值、积分信息取外单更新值
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(ofcOrderModel, updatedOrderModel, attachFeesFromModel));

        // 拓展字段
        facadeRequest.setExtendProps(toOfcExtendProps(updatedOrderModel, originalOrderModel));

        // 询价类型
        if(null != ofcOrderModel.getEnquiry() && null != ofcOrderModel.getEnquiry().getEnquiryMode()){
            facadeRequest.setInquiryType(ofcOrderModel.getEnquiry().getEnquiryMode().getPriceCode());
        }

        return facadeRequest;
    }

    /**
     * 终端切百川后
     * 询价接口（正向单、逆向单）
     */
    public BillingEnquiryFacadeRequest toBillingEnquiryRequest(ExpressOrderContext expressOrderContext, boolean attachFeesFromModel) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        // 上游调询价接口的请求参数
        ExpressOrderModel requestOrderModel = expressOrderContext.getOrderModel();
        // 原单
        ExpressOrderModel originalOrderModel = expressOrderContext.getOrderModel().getOrderSnapshot();

        // 交易业务单元：快运B2C或快运C2C
        facadeRequest.setBusinessUnit(originalOrderModel.getOrderBusinessIdentity().getBusinessUnit());

        // 订单号：取上游传值
        facadeRequest.setOrderNo(requestOrderModel.orderNo());

        // 客户信息：原单
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(originalOrderModel));

        // 运单号：取原始订单
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(originalOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        // 渠道信息：取上游传值
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(requestOrderModel));

        // 产品信息
        // originalOrderModel:订单中心原单 requestOrderModel：OFC接口参数
        facadeRequest.setProductFacadeDtoList(toEnquiryProductFacadeDto(originalOrderModel, requestOrderModel));

        // 货品信息：总重量、总体积按是否信任商家取值，总数量取原始订单
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(requestOrderModel, originalOrderModel));

        // 发件人信息：二级地址市取上游传值，其余取原始订单
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(requestOrderModel, originalOrderModel));

        // 收件人信息：取原始订单
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(originalOrderModel));

        // 财务相关信息：询价时间取当前时间、结算方式取原始订单、抵扣信息取上游传值、积分信息取原始订单
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(requestOrderModel, originalOrderModel, attachFeesFromModel));

        // 拓展字段
        facadeRequest.setExtendProps(toExtendProps(requestOrderModel, originalOrderModel));

        // 询价类型
        if(null != requestOrderModel.getEnquiry() && null != requestOrderModel.getEnquiry().getEnquiryMode()){
            facadeRequest.setInquiryType(requestOrderModel.getEnquiry().getEnquiryMode().getPriceCode());
        }

        return facadeRequest;
    }

    /**
     * 逆向单创建异步询价
     */
    public BillingEnquiryFacadeRequest toCreateSceneEnquiryRequest(ExpressOrderContext expressOrderContext) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        // 当前单：持久化数据
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();

        // 交易业务单元：快运B2C或快运C2C
        facadeRequest.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());

        // 订单号
        facadeRequest.setOrderNo(orderModel.orderNo());

        // 客户信息
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(orderModel));

        // 运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        // 渠道信息
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));

        // 产品信息
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel));

        // 货品信息
        facadeRequest.setCargoFacadeDto(toCreateCargoFacadeDto(orderModel));

        // 发件人信息
        facadeRequest.setConsignorFacadeDto(toCreateConsignorFacadeDto(orderModel));

        // 收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel));

        // 财务相关信息
        facadeRequest.setFinanceFacadeDto(toCreateFinanceFacadeDto(orderModel));

        // 拓展字段
        facadeRequest.setExtendProps(toCreateExtendProps(orderModel));

        // 询价类型：默认1-询价
        facadeRequest.setInquiryType(EnquiryModeEnum.ENQUIRY.getCode());

        return facadeRequest;
    }

    /**
     * 修改时转换为计费接口参数
     * 揽收后改址，异步询价；
     * 揽收后改增值服务、揽收后改址，同步询价
     */
    public BillingEnquiryFacadeRequest toModifyEnquiryRequest(ExpressOrderContext expressOrderContext) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        // 当前单：异步询价是持久化数据；同步询价是修改请求
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        // 原单
        ExpressOrderModel originalOrderModel = orderModel.getOrderSnapshot();

        // 交易业务单元：快运B2C或快运C2C
        facadeRequest.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());

        // 订单号
        facadeRequest.setOrderNo(orderModel.orderNo());

        // 操作人
        facadeRequest.setOperator(orderModel.getOperator());

        // 客户信息
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(originalOrderModel));

        // 正向运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(originalOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        // 渠道信息
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));

        // 产品信息
        facadeRequest.setProductFacadeDtoList(toModifyProductFacadeDto(orderModel, originalOrderModel));

        // 货品信息
        facadeRequest.setCargoFacadeDto(toModifyCargoFacadeDto(orderModel, originalOrderModel));

        // 发件人信息
        facadeRequest.setConsignorFacadeDto(toModifyConsignorFacadeDto(orderModel));

        // 收件人信息：当前单没有取原单
        facadeRequest.setConsigneeFacadeDto(toModifyConsigneeFacadeDto(orderModel));

        // 财务相关信息
        facadeRequest.setFinanceFacadeDto(toModifyFinanceFacadeDto(orderModel));

        // 拓展字段（标准产品计费需要，通用询价计费不需要；后续会调用orderModel.getOrderSnapshot()）
        facadeRequest.setExtendProps(toModifyExtendProps(orderModel));

        // 询价类型
        if(null != orderModel.getEnquiry() && null != orderModel.getEnquiry().getEnquiryMode()){
            facadeRequest.setInquiryType(orderModel.getEnquiry().getEnquiryMode().getPriceCode());
        }

        return facadeRequest;
    }

    /**
     * 修改时转换为计费接口参数
     * 揽收后改增值服务、揽收后改址，异步询价；
     * 揽收后改址，同步询价
     */
    public BillingEnquiryFacadeRequest toServiceEnquiryRequest(ExpressOrderContext expressOrderContext) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        // 当前单：异步询价是持久化数据；同步询价是修改请求
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        // 原单
        ExpressOrderModel originalOrderModel = orderModel.getOrderSnapshot();
        //询价单
        ExpressOrderModel enquiryOrderModel = expressOrderContext.getEnquiryOrderModel();

        // 交易业务单元：快运B2C或快运C2C
        facadeRequest.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());

        // 订单号
        facadeRequest.setOrderNo(enquiryOrderModel.orderNo());

        // 操作人
        facadeRequest.setOperator(enquiryOrderModel.getOperator());

        // 客户信息
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(originalOrderModel));

        // 正向运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(originalOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        // 渠道信息
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));

        // 产品信息

        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtoList = new ArrayList<>();

        //主产品
        Product mainProduct = (Product) orderModel.getProductDelegate().getMainProduct();
        //productFacadeDtoList.add(this.toProductFacadeDto(mainProduct));

        //获取新增的需要询价的增值服务
        List<Product> products = (List<Product>) enquiryOrderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            products.forEach(obj -> productFacadeDtoList.add(this.toProductFacadeDto(obj, mainProduct)));
        }
        facadeRequest.setProductFacadeDtoList(productFacadeDtoList);

        //服务单号
        facadeRequest.setServiceNo(expressOrderContext.getEnquiryOrderModel().getCustomOrderNo());
        //询价类型
        facadeRequest.setInquiryType(BillingInquiryTypeEnum.ATTACH.getCode());

        // 货品信息
        facadeRequest.setCargoFacadeDto(toModifyCargoFacadeDto(orderModel, originalOrderModel));

        // 发件人信息
        facadeRequest.setConsignorFacadeDto(toModifyConsignorFacadeDto(originalOrderModel));

        // 收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(originalOrderModel));

        // 财务相关信息
        facadeRequest.setFinanceFacadeDto(toModifyFinanceFacadeDto(enquiryOrderModel));

        // 拓展字段
        facadeRequest.setExtendProps(toServiceEnquiryExtendProps(enquiryOrderModel,orderModel));

        return facadeRequest;
    }

    /**
     * 添加拓展字段
     * 终端修改切百川前，订单中心数据可能不是最新的
     *
     * @param updatedOrderModel 用运单数据更新后的订单持久化数据，可能缺少部分字段
     * @param originalOrderModel 更新前的订单持久化数据，数据可能不是最新值
     */
    private Map<String, Object> toOfcExtendProps(ExpressOrderModel updatedOrderModel, ExpressOrderModel originalOrderModel) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(FreightGetFieldUtils.getCurrentSettlementType(updatedOrderModel)));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS,DEFAULT_ORDER_STATUS);
        //仓配类型(0：纯配；1：仓配)：快运只有纯配
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, EnquiryConstants.DISTRIBUTION_PURE);
        //温层
        if (null != updatedOrderModel.getShipment().getWarmLayer()) {
            extendParam.put(EnquiryConstants.WARM_LAYER, updatedOrderModel.getShipment().getWarmLayer().getCode());
        }
        //营销信息：优惠券信息、折扣信息：可能会更新，取更新后数据；优惠券使用人取原单（更新数据不包含操作人）
        extendParam.put(EnquiryConstants.DISCOUNT_DETAIL, extendParamToDiscountDetailList(updatedOrderModel, originalOrderModel.getOperator()));
        //积分信息
        extendParamToRewardPoints(extendParam, updatedOrderModel);
        return extendParam;
    }


    /**
     * 添加拓展字段
     * 终端修改切百川后，订单中心数据是最新值
     *
     * @param requestOrderModel 询价请求
     * @param originalOrderModel 订单持久化数据
     */
    private Map<String, Object> toExtendProps(ExpressOrderModel requestOrderModel, ExpressOrderModel originalOrderModel) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(FreightGetFieldUtils.getCurrentSettlementType(originalOrderModel)));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS,DEFAULT_ORDER_STATUS);
        //仓配类型(0：纯配；1：仓配)：快运只有纯配
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, EnquiryConstants.DISTRIBUTION_PURE);

        // 营业厅编码
        if (null != originalOrderModel.getShipment().getExtendProps()) {
            extendParam.put(EnquiryConstants.BUSINESS_HALL_CODE, originalOrderModel.getShipment().getExtendProps().get(BUSINESS_HALL_NO_KEY));
        }

        //温层
        if (null != originalOrderModel.getShipment().getWarmLayer()) {
            extendParam.put(EnquiryConstants.WARM_LAYER, originalOrderModel.getShipment().getWarmLayer().getCode());
        }

        // 营销信息需要处理全量覆盖和删除情况
        Map<String, String> modifiedFields = requestOrderModel.getModifiedFields();
        String modifiedFieldValue = null;
        if (MapUtils.isNotEmpty(modifiedFields)) {
            modifiedFieldValue = requestOrderModel.getModifiedFields().get(ModifiedFieldEnum.OPERATION_DISCOUNT_INFOS.getCode());
        }
        List<DiscountDetailDTO> snapshotDiscountDetailS = new ArrayList<>();
        List<DiscountDetailDTO> orderDiscountDetailS = new ArrayList<>();
        // 如果没有则只看快照中的营销折扣
        // 如果是全量覆盖则只看入参里的营销折扣
        // 如果是快照和入参的营销折扣信息都不看
        if (modifiedFieldValue == null) {
            //营销信息，折扣信息-原单-下单传入
            snapshotDiscountDetailS = extendParamToDiscountDetailLists(originalOrderModel, originalOrderModel.getOperator() );
            //营销信息，折扣信息-当前单-询价传入
            orderDiscountDetailS = extendParamToDiscountDetailLists(requestOrderModel, originalOrderModel.getOperator(), false);
        } else if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldValue)) {
            //营销信息，折扣信息-原单-下单传入
            snapshotDiscountDetailS = extendParamToDiscountDetailLists(originalOrderModel, originalOrderModel.getOperator(), false);
            //营销信息，折扣信息-当前单-询价传入
            orderDiscountDetailS = extendParamToDiscountDetailLists(requestOrderModel, originalOrderModel.getOperator());
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldValue)) {
            //营销信息，折扣信息-原单-下单传入
            snapshotDiscountDetailS = extendParamToDiscountDetailLists(originalOrderModel, originalOrderModel.getOperator(), false);
            //营销信息，折扣信息-当前单-询价传入
            orderDiscountDetailS = extendParamToDiscountDetailLists(requestOrderModel, originalOrderModel.getOperator(), false);
        }

        extendParam.put(EnquiryConstants.DISCOUNT_DETAIL,CollectionUtils.union(snapshotDiscountDetailS,orderDiscountDetailS));
        //积分信息
        extendParamToRewardPoints(extendParam, originalOrderModel);

        //跨境报关信息
        extendParamToCustoms(extendParam,originalOrderModel);
        //包裹明细
        extendParamToPackageInformation(extendParam,requestOrderModel);

        // 是否返回折扣未匹配原因，1表示返回，不传默认为0，当为1时，返回入参中的指定折扣编码或优惠券编码未匹配使用的原因
        extendParam.put(EnquiryConstants.RETURN_MISMATCH, RETURN_MISMATCH);
        return extendParam;
    }

    /**
     * @description 功能描述: C2C拓展字段之营销信息，折扣信息
     * <AUTHOR>
     * @date 2021/6/29 10:21
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    public List<DiscountDetailDTO> extendParamToDiscountDetailList(ExpressOrderModel orderModel) {
        List<DiscountDetailDTO> detailList = new ArrayList<>();
        if (orderModel.getPromotion()!= null) {
            //优惠券信息赋值逻辑
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getTickets())) {
                for (Ticket ticket : orderModel.getPromotion().getTickets()) {
                    DiscountDetailDTO discountDetail = new DiscountDetailDTO();
                    //优惠类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
                    if (ticket.getTicketSource() != null && TicketSourceEnum.DADA.getCode().equals(ticket.getTicketSource())) {
                        discountDetail.setDiscountNo(ticket.getTicketNo());
                        discountDetail.setType(DISCOUNT_TYPE_DADA_TICKET);
                    } else {
                        if (StringUtils.isNotBlank(ticket.getTicketBatchNo())) {
                            discountDetail.setDiscountNo(ticket.getTicketBatchNo());
                            discountDetail.setType(DISCOUNT_TYPE_JD_TICKET_BATCH_NO);
                        } else {
                            discountDetail.setDiscountNo(ticket.getTicketNo());
                            discountDetail.setType(DISCOUNT_TYPE_JD_TICKET);
                        }
                    }
                    discountDetail.setUserNo(orderModel.getOperator());
                    detailList.add(discountDetail);
                }
            }
            //折扣信息赋值逻辑
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getDiscounts())) {
                for (Discount discount : orderModel.getPromotion().getDiscounts()) {
                    DiscountDetailDTO discountDetail = new DiscountDetailDTO();
                    discountDetail.setDiscountNo(discount.getDiscountNo());
                    //优惠类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
                    discountDetail.setType(DISCOUNT_TYPE_DISCOUNT);
                    detailList.add(discountDetail);
                }
            }
        }
        return detailList;
    }

    /**
     * 添加拓展字段：逆向单创建异步询价
     */
    private Map<String, Object> toCreateExtendProps(ExpressOrderModel orderModel) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(FreightGetFieldUtils.getCurrentSettlementType(orderModel)));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS,DEFAULT_ORDER_STATUS);
        //仓配类型(0：纯配；1：仓配)：快运只有纯配
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, EnquiryConstants.DISTRIBUTION_PURE);
        //温层
        extendParam.put(EnquiryConstants.WARM_LAYER, orderModel.getShipment().getWarmLayer());
        //营销信息：优惠券信息、折扣信息、优惠券使用人：取原单
        extendParam.put(EnquiryConstants.DISCOUNT_DETAIL, extendParamToDiscountDetailList(orderModel, orderModel.getOperator()));
        //积分信息
        extendParamToRewardPoints(extendParam, orderModel);
        //跨境报关信息
        extendParamToCustoms(extendParam,orderModel);
        //包裹明细
        extendParamToPackageInformation(extendParam,orderModel);
        return extendParam;
    }

    /**
     * 修改场景扩展字段
     */
    private Map<String, Object> toModifyExtendProps(ExpressOrderModel orderModel) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(FreightGetFieldUtils.getSettlementType(orderModel)));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS,DEFAULT_ORDER_STATUS);
        //仓配类型(0：纯配；1：仓配)：快运只有纯配
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, EnquiryConstants.DISTRIBUTION_PURE);
        //温层
        extendParam.put(EnquiryConstants.WARM_LAYER, orderModel.getShipment().getWarmLayer());
        //营销信息：优惠券信息、折扣信息
        extendParam.put(EnquiryConstants.DISCOUNT_DETAIL, modifyExtendParamToDiscountDetailList(orderModel));
        //积分信息
        modifyExtendParamToRewardPoints(extendParam, orderModel);
        return extendParam;
    }

    /**
     * 修改场景扩展字段
     */
    private Map<String, Object> toServiceEnquiryExtendProps(ExpressOrderModel enquiryOrderModel,ExpressOrderModel orderModel) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(FreightGetFieldUtils.getSettlementType(enquiryOrderModel)));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS,DEFAULT_ORDER_STATUS);
        //仓配类型(0：纯配；1：仓配)：快运只有纯配
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, EnquiryConstants.DISTRIBUTION_PURE);
        //温层
        extendParam.put(EnquiryConstants.WARM_LAYER, orderModel.getOrderSnapshot().getShipment().getWarmLayer());
        //营销信息：优惠券信息、折扣信息
        extendParam.put(EnquiryConstants.DISCOUNT_DETAIL, modifyExtendParamToDiscountDetailList(enquiryOrderModel));
        //积分信息
        modifyExtendParamToRewardPoints(extendParam, enquiryOrderModel);
        return extendParam;
    }

    /**
     *  仓配类型
     *  0：纯配；1：仓配
     *  接单字段deliveryPattern若为1京仓发货 则该字段赋值1
     *  若为2纯配则该字段赋值0；
     *  接单字段deliveryPattern若为空且systemSubCaller为eclp则该字段赋值为1，
     *  其他情况全部赋值0
     */
    private Object toDistributionType(ExpressOrderModel orderModel) {
        // 配送的拓展字段 deliveryPattern 从这里获取
        Map<String, String> extendProps = orderModel.getOrderSnapshot().getShipment().getExtendProps();
        if(MapUtils.isNotEmpty(extendProps)){
            String shipmentExtendProps = extendProps.get(SHIPMENT_EXTEND_PROPS);
            if (shipmentExtendProps != null){
                Map map = JSONUtils.jsonToMap(shipmentExtendProps);
                if(MapUtils.isNotEmpty(map)){
                    String deliveryPattern = (String)map.get(DELIVERY_PATTERN);
                    if (DELIVERY_PATTERN_ONE.equals(deliveryPattern)){
                        return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                    }else if (DELIVERY_PATTERN_TWO.equals(deliveryPattern)){
                        return EnquiryConstants.DISTRIBUTION_PURE;
                    }else if (deliveryPattern == null && ECLP.equals(orderModel.getOrderSnapshot().getChannel().getSystemSubCaller())){
                        return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                    }
                }
            }
        }
        return EnquiryConstants.DISTRIBUTION_PURE;
    }

    /**
     * 拓展信息-结算方式
     */
    private Object extendParamToSettlementType(SettlementTypeEnum settlementTypeEnum) {
        switch (settlementTypeEnum) {
            case CASH_ON_PICK:
                return EnquiryConstants.SETTLEMENT_CASH_ON_PICK;
            case CASH_ON_DELIVERY:
                return EnquiryConstants.SETTLEMENT_CASH_ON_DELIVERY;
            case MONTHLY_PAYMENT:
                return EnquiryConstants.SETTLEMENT_MONTHLY_PAYMENT;
            default:
                return null;
        }
    }

    /**
     * 补全询价记录，入参
     */
    public void complementEnquiry(ExpressOrderModel expressOrderModel, BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        // 询价记录
        EnquiryInfoDto enquiryInfoDto = new EnquiryInfoDto();
        // B2C询价时间为接单时间
        enquiryInfoDto.setEnquireTime(billingEnquiryFacadeRequest.getFinanceFacadeDto().getEnquireTime());
        // 高峰附加费时间 要从model里拿
        enquiryInfoDto.setPeakPeriodTime(expressOrderModel.getEnquiry().getPeakPeriodTime());

        enquiryInfoDto.setEnquiryStartProvinceNo(billingEnquiryFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().getProvinceNoGis());
        //起始人的市id
        enquiryInfoDto.setEnquiryStartCityNo(billingEnquiryFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().getCityNoGis());
        enquiryInfoDto.setEnquiryStartCountyNo(billingEnquiryFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().getCountyNoGis());

        enquiryInfoDto.setEnquiryEndProvinceNo(billingEnquiryFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().getProvinceNoGis());
        enquiryInfoDto.setEnquiryEndCityNo(billingEnquiryFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().getCityNoGis());
        enquiryInfoDto.setEnquiryEndCountyNo(billingEnquiryFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().getCountyNoGis());

        // 这里面有可能有包装耗材
        if (CollectionUtils.isNotEmpty(billingEnquiryFacadeRequest.getProductFacadeDtoList()) ){
            //产品集合
            List<ProductInfoDto> productInfoDtos = Lists.newArrayListWithCapacity(billingEnquiryFacadeRequest.getProductFacadeDtoList().size());
            //遍历当前费用明细
            billingEnquiryFacadeRequest.getProductFacadeDtoList().forEach(productFacadeDto -> {
                //产品信息
                ProductInfoDto productInfoDto = new ProductInfoDto();
                //产品编码
                productInfoDto.setProductNo(productFacadeDto.getProductNo());
                //产品类型
                productInfoDto.setProductType(productFacadeDto.getProductType());
                //产品关系(所属主产品编码)
                productInfoDto.setParentNo(productFacadeDto.getParentNo());
                //产品要素属性
                productInfoDto.setProductAttrs(productFacadeDto.getProductAttrs());
                //扩展字段说明
                productInfoDto.setExtendProps(productFacadeDto.getExtendProps());
                //产品信息
                productInfoDtos.add(productInfoDto);
            });
            enquiryInfoDto.setProductInfos(productInfoDtos);
        }

        Optional.ofNullable(billingEnquiryFacadeRequest.getFinanceFacadeDto().getBillingWeight()).ifPresent(enquiryWeight->{
            WeightInfoDto weightInfoDto = new WeightInfoDto();
            weightInfoDto.setUnit(enquiryWeight.getUnit());
            weightInfoDto.setValue(enquiryWeight.getValue());
            enquiryInfoDto.setEnquiryWeight(weightInfoDto);
        });

        Optional.ofNullable(billingEnquiryFacadeRequest.getFinanceFacadeDto().getBillingVolume()).ifPresent(enquiryVolume->{
            VolumeInfoDto volumeInfo = new VolumeInfoDto();
            volumeInfo.setUnit(enquiryVolume.getUnit());
            volumeInfo.setValue(enquiryVolume.getValue());
            enquiryInfoDto.setEnquiryVolume(volumeInfo);
        });

        expressOrderModelCreator.setEnquiryInfo(enquiryInfoDto);
        // 补全询价对象
        expressOrderModel.complement().complementEnquiry(this, expressOrderModelCreator);
    }

    /**
     * 补全计费结果信息
     */
    public void complementBillingResult(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();

        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getAmount());
        preAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getCurrencyCode());
        financeInfoDto.setPreAmount(preAmount);

        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
        discountAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
        financeInfoDto.setDiscountAmount(discountAmount);

        //计费重量
        financeInfoDto.setBillingWeight(billingEnquiryFacadeResponse.getFinanceFacadeDto().getCalWeight());
        //计费体积
        financeInfoDto.setBillingVolume(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingVolume());

        //计费类型
        financeInfoDto.setBillingMode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingMode());

        //积分信息
        financeInfoDto.setPointsInfoDto(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPointsInfoDto());

        //费用明细
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : billingEnquiryFacadeResponse.getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
            FinanceDetailInfoDto detailInfoDto = new FinanceDetailInfoDto();
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount().getAmount());
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount().getCurrencyCode());
            detailInfoDto.setPreAmount(detailPreAmount);
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount().getAmount());
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount().getCurrencyCode());
            detailInfoDto.setDiscountAmount(detailDiscountAmount);
            detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            detailInfoDto.setCostName(detailFacadeDto.getCostName());
            detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            detailInfoDto.setProductName(detailFacadeDto.getProductName());
            detailInfoDto.setRemark(detailFacadeDto.getRemark());
            //折扣明细
            if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
                for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
                    discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());
                    Money money = new Money();
                    money.setAmount(discountInfoFacadeDto.getDiscountedAmount().getAmount());
                    money.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
                    discountInfoDto.setDiscountedAmount(money);
                    discountInfoDtos.add(discountInfoDto);
                }
                detailInfoDto.setDiscountInfoDtos(discountInfoDtos);
            }
            detailInfoDto.setExtendProps(detailFacadeDto.getExtendProps());
            detailInfoDto.setPointsInfoDto(detailFacadeDto.getPointsInfoDto());
            financeDetailInfoDtoList.add(detailInfoDto);
        }
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
        // 收款机构
        financeInfoDto.setCollectionOrgNo(billingEnquiryFacadeResponse.getFinanceFacadeDto().getCollectionOrgNo());
        // 快运揽收后修改收货人信息，寄付现结需要设置支付超时时间
        SettlementTypeEnum settlementType = FreightGetFieldUtils.getSettlementType(expressOrderContext);
        boolean needPayDeadLine = false;
        if (FreightReaddressUtil.isReaddress(expressOrderContext.getOrderModel())
                && settlementType != null
                && SettlementTypeEnum.CASH_ON_PICK.getCode().equals(settlementType.getCode())) {
            LOGGER.info("快运揽收后修改收货人信息且寄付现结，需要设置支付截止时间");
            needPayDeadLine = true;
        } else if (expressOrderContext.haveEnquiryOrderModel()
                && PaymentStageEnum.ONLINEPAYMENT == expressOrderContext.getEnquiryOrderModel().getFinance().getPaymentStage()) {
            LOGGER.info("先款服务单询价，需要设置支付截止时间");
            needPayDeadLine = true;
        }

        if (needPayDeadLine) {
            Integer freightPayOutTimeBuffer = expressUccConfigCenter.getFreightPayOutTimeBuffer();
            Date payDeadline = new Date(System.currentTimeMillis() + 60000L * freightPayOutTimeBuffer);
            LOGGER.info("支付时间默认为当前时间加{}分钟，payDeadline={}", freightPayOutTimeBuffer, payDeadline);
            financeInfoDto.setPayDeadline(payDeadline);
        }
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);

        if (expressOrderContext.haveEnquiryOrderModel()) {
            expressOrderContext.getEnquiryOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
        } else {
            expressOrderContext.getOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
        }

    }

    /**
     * 客户信息
     */
    private BillingEnquiryFacadeRequest.CustomerFacadeDto toCustomerFacadeDto(ExpressOrderModel originalOrderModel) {
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = new BillingEnquiryFacadeRequest.CustomerFacadeDto();
        Customer customer = originalOrderModel.getCustomer();
        customerFacadeDto.setAccountNo(customer.getAccountNo());
        customerFacadeDto.setAccount2No(customer.getAccountNo2());
        return customerFacadeDto;
    }

    /**
     * 设置产品服务信息
     * 包括从外单获取的增值服务、询价请求参数的增值产品
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel requestOrderModel, ExpressOrderModel updatedOrderModel, ExpressOrderModel originalOrderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        // 外单最新的增值服务
        List<Product> updatedProducts = (List<Product>) updatedOrderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(updatedProducts)) {
            String majorProductNo = getMajorProductNo(updatedProducts);
            for (Product product : updatedProducts) {
                // 包装服务从询价入参取
                if (packageServiceUtil.isPackageService(product)) {
                    continue;
                }
                // 目前不过滤代收货款（标准产品询价接口、通用询价计费接口）
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(getProductNo(majorProductNo, product.getProductNo()));
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }

        // 包装服务从询价入参取
        List<Product> ofcProducts = (List<Product>) requestOrderModel.getProductDelegate().getProducts();
        if(CollectionUtils.isNotEmpty(ofcProducts)){
            for (Product product : ofcProducts) {
                // 只处理包装服务
                if (packageServiceUtil.isPackageService(product)) {
                    // 从询价入参取，或者查运单的包装耗材信息，查询不到再过滤
                    packageServiceUtil.handlePackageService(updatedOrderModel.orderNo(), getWaybillCode(updatedOrderModel), product, productFacadeDtos);
                }
            }
        }

        // 补全主产品计费用的产品要素
        complementMainProductAttrs(productFacadeDtos, originalOrderModel);

        return productFacadeDtos;
    }


    /**
     * 设置产品服务信息
     * 港澳正向单询价
     * 港澳逆向单接单异步询价
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel orderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        // 最新的增值服务
        List<Product> products = (List<Product>) orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            String majorProductNo = getMajorProductNo(products);
            for (Product product : products) {
                // 包装服务从当前单取
                // 目前不过滤代收货款（标准产品询价接口、通用询价计费接口）
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(getProductNo(majorProductNo, product.getProductNo()));
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                //港澳：特快重货计费时传入揽派模式
                if((ProductEnum.TKZH.getCode().equals(product.getProductNo()))){
                    Map<String, String> productAttrs = productFacadeDto.getProductAttrs();
                    if(MapUtils.isEmpty(productAttrs)){
                        productAttrs = new HashMap<>();
                    }
                    if(null != orderModel.getShipment()){
                        //揽收模式
                        if(null != orderModel.getShipment().getPickupType()){
                            productAttrs.put(EnquiryConstants.PICKUP_MODEL,String.valueOf(orderModel.getShipment().getPickupType().getCode()));
                        }
                        //派送模式
                        if(null != orderModel.getShipment().getDeliveryType()){
                            productAttrs.put(EnquiryConstants.DELIVERY_MODEL,String.valueOf(orderModel.getShipment().getDeliveryType().getCode()));
                        }
                    }
                    productFacadeDto.setProductAttrs(productAttrs);
                }
                productFacadeDtos.add(productFacadeDto);
            }
        }

        // 补全主产品计费用的产品要素
        complementMainProductAttrs(productFacadeDtos, orderModel);

        return productFacadeDtos;
    }

    /**
     * 原单财务数据根据计费结果重算
     *
     * @param orderContext
     * @param billMap      所有计费
     */
    public void complementSumBillingResult(ExpressOrderContext orderContext, Map<String, BillingEnquiryFacadeResponse> billMap, BillingEnquiryFacadeResponse currentOrder) {
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();

        BigDecimal preMoney = new BigDecimal(0);
        BigDecimal disMoney = new BigDecimal(0);
        CurrencyCodeEnum preCodeEnum = null;
        CurrencyCodeEnum disCodeEnum = null;
        //费用明细 key：CostNo
        Map<String,FinanceDetailInfoDto> detailInfoDtoMap =  new HashMap<>();
        StringBuilder financeRemark = new StringBuilder();
        for (Map.Entry<String, BillingEnquiryFacadeResponse> entry : billMap.entrySet()) {
            // 费用总额
            preMoney = preMoney.add(entry.getValue().getFinanceFacadeDto().getPreAmount().getAmount());
            preCodeEnum = entry.getValue().getFinanceFacadeDto().getPreAmount().getCurrencyCode();

            disMoney = disMoney.add(entry.getValue().getFinanceFacadeDto().getDiscountAmount().getAmount());
            disCodeEnum = entry.getValue().getFinanceFacadeDto().getDiscountAmount().getCurrencyCode();
            financeRemark.append(entry.getKey()).append(":").append(entry.getValue().getFinanceFacadeDto().getDiscountAmount().getAmount()).append(",");
            // 费用明细
            for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : entry.getValue().getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
                //费用明细按CostNo分组
                FinanceDetailInfoDto detailInfoDto = detailInfoDtoMap.get(detailFacadeDto.getCostNo());
                if (detailInfoDto == null) {
                    detailInfoDto = new FinanceDetailInfoDto();
                }
                //费用明细整合
                toFinanceDetailInfoDto(detailInfoDto,detailFacadeDto);
                LOGGER.info("费用明细整合出参detailInfoDto：{}",JSONUtils.beanToJSONDefault(detailInfoDto));

                if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                    // 费用折扣
                    Map<String,DiscountInfoDto> discountInfoDtoMap = new HashMap<>();
                    for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                        //费用折扣按DiscountNo分组
                        DiscountInfoDto discountInfoDto = discountInfoDtoMap.get(discountInfoFacadeDto.getDiscountNo());
                        if (discountInfoDto == null) {
                            discountInfoDto = new DiscountInfoDto();
                        }
                        //折扣整合
                        toDiscountInfoFacadeDto(discountInfoDto, discountInfoFacadeDto);
                        discountInfoDtoMap.put(discountInfoDto.getDiscountNo(), discountInfoDto);
                    }
                    List<DiscountInfoDto> discountInfoDtoList = new ArrayList<>(discountInfoDtoMap.values());
                    detailInfoDto.setDiscountInfoDtos(discountInfoDtoList);
                }

                //明细remark
                StringBuilder detailRemark;
                if (StringUtils.isNotBlank(detailInfoDto.getRemark())) {
                    detailRemark = new StringBuilder(detailInfoDto.getRemark());
                } else {
                    detailRemark = new StringBuilder();
                }
                detailRemark.append(entry.getKey()).append(":").append(detailInfoDto.getDiscountAmount().getAmount()).append(",");
                detailInfoDto.setRemark(detailRemark.toString());

                detailInfoDto.setExtendProps(new HashMap<>());
                // 价格项明细
                if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
                    detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
                }

                detailInfoDtoMap.put(detailInfoDto.getCostNo(),detailInfoDto);
            }
        }
        LOGGER.info("费用明细整合出参detailInfoDtoMap：{}",JSONUtils.mapToJson(detailInfoDtoMap));
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>(detailInfoDtoMap.values());
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(preMoney);
        preAmount.setCurrencyCode(preCodeEnum);
        financeInfoDto.setPreAmount(preAmount);
        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(disMoney);
        discountAmount.setCurrencyCode(disCodeEnum);
        financeInfoDto.setDiscountAmount(discountAmount);
        //计费重量
        financeInfoDto.setBillingWeight(currentOrder.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(currentOrder.getFinanceFacadeDto().getBillingVolume());
        financeInfoDto.setBillingMode(currentOrder.getFinanceFacadeDto().getBillingMode());
        //费用明细
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
//        toFinanceDetailInfoDto(currentOrder.getFinanceFacadeDto().getFinanceDetailFacadeDtoList(),detailRemark.toString())
        //总优惠金额
        MoneyInfoDto totalDiscountAmount = new MoneyInfoDto();
        totalDiscountAmount.setAmount(preMoney.subtract(disMoney));
        totalDiscountAmount.setCurrencyCode(preCodeEnum);
        financeInfoDto.setTotalDiscountAmount(totalDiscountAmount);
        financeInfoDto.setRemark(financeRemark.toString());

        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        modelCreator.setFinanceInfo(financeInfoDto);
        orderContext.getOrderModel().complement().complementFinanceInfo(this, modelCreator);
    }

    private void toFinanceDetailInfoDto(FinanceDetailInfoDto detailInfoDto,BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto) {
        //折前金额
        MoneyInfoDto detailPreAmount = new MoneyInfoDto();
        BigDecimal preNewMoney = detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getAmount() : null;
        BigDecimal preMoneySum = detailInfoDto.getPreAmount() != null ? detailInfoDto.getPreAmount().getAmount().add(preNewMoney != null ? preNewMoney : BigDecimal.ZERO) : preNewMoney;
        detailPreAmount.setAmount(preMoneySum);
        detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getCurrencyCode() : null);
        detailInfoDto.setPreAmount(detailPreAmount);
        //折后金额
        MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
        BigDecimal disNewMoney = detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getAmount() : null;
        BigDecimal disMoneySum = detailInfoDto.getDiscountAmount() != null ? detailInfoDto.getDiscountAmount().getAmount().add(disNewMoney != null ? disNewMoney : BigDecimal.ZERO) : disNewMoney;
        detailDiscountAmount.setAmount(disMoneySum);
        detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getCurrencyCode() : null);
        detailInfoDto.setDiscountAmount(detailDiscountAmount);
        detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
        detailInfoDto.setCostName(detailFacadeDto.getCostName());
        detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
    }

    private void toDiscountInfoFacadeDto(DiscountInfoDto discountInfoDto, BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto) {
        discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
        discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());

        Money disAmount = new Money();
        BigDecimal discountNewMoney = discountInfoFacadeDto.getDiscountedAmount() != null ? discountInfoFacadeDto.getDiscountedAmount().getAmount() : null;
        BigDecimal discountSumMoney = discountInfoDto.getDiscountedAmount() != null ? discountInfoDto.getDiscountedAmount().getAmount().add(discountNewMoney != null ? discountNewMoney : BigDecimal.ZERO) : discountNewMoney;
        disAmount.setAmount(discountSumMoney);
        disAmount.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
        discountInfoDto.setDiscountedAmount(disAmount);
    }

    /**
     * 产品服务信息
     * 新单没有取原单，过滤删除的产品、过滤产品要素为空的包装服务
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toModifyProductFacadeDto(ExpressOrderModel expressOrderModel, ExpressOrderModel originalOrderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<Product> products = (List<Product>) expressOrderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isEmpty(products)
                && expressOrderModel.getOrderSnapshot() != null
                && expressOrderModel.getOrderSnapshot().getProductDelegate() != null) {
            products = (List<Product>) expressOrderModel.getOrderSnapshot().getProductDelegate().getProducts();
        }

        if (CollectionUtils.isNotEmpty(products)) {
            String majorProductNo = getMajorProductNo(products);
            for (Product product : products) {
                // 过滤删除的产品
                if (product.getOperateType() != null
                        && OperateTypeEnum.DELETE.getCode().equals(product.getOperateType().getCode())) {
                    continue;
                }

                // 包装服务特殊处理
                if (packageServiceUtil.isPackageService(product)) {
                    // 从询价入参取，或者查运单的包装耗材信息，查询不到再过滤
                    packageServiceUtil.handlePackageService(getModifyOrderNo(expressOrderModel), getModifyWaybillCode(expressOrderModel), product, productFacadeDtos);
                    continue;
                }

                // 产品转为询价参数
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(getProductNo(majorProductNo, product.getProductNo()));
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }

        // 补全主产品计费用的产品要素
        complementMainProductAttrs(productFacadeDtos, originalOrderModel);

        return productFacadeDtos;
    }

    /**
     * 渠道信息
     */
    private BillingEnquiryFacadeRequest.ChannelFacadeDto toChannelFacadeDto(ExpressOrderModel requestOrderModel) {
        BillingEnquiryFacadeRequest.ChannelFacadeDto channelFacadeDto = new BillingEnquiryFacadeRequest.ChannelFacadeDto();
        Channel channel = requestOrderModel.getChannel();
        channelFacadeDto.setChannelNo(channel.getChannelNo());
        return channelFacadeDto;
    }

    /**
     * 收件人信息
     */
    private BillingEnquiryFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(ExpressOrderModel updatedOrderModel) {
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();

        Consignee consignee = updatedOrderModel.getConsignee();

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        Address address = consignee.getAddress();
        if (address != null) {
            // 收件人省
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            // 收件人市
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            // 收件人县
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 收件人信息：修改场景
     * 新单没有取原单
     */
    private BillingEnquiryFacadeRequest.ConsigneeFacadeDto toModifyConsigneeFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();

        Consignee consignee;
        if (orderModel.getConsignee() != null && orderModel.getConsignee().getAddress() != null) {
            consignee = orderModel.getConsignee();
        } else {
            consignee = orderModel.getOrderSnapshot().getConsignee();
        }

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        Address address = consignee.getAddress();
        if (address != null) {
            // 收件人省
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            // 收件人市
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            // 收件人县
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     * 快照已处理，处理后快照关系：当前单.快照 -> 运单；运单.快照 -> 原单
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel requestOrderModel, ExpressOrderModel updatedOrderModel, ExpressOrderModel originalOrderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        if (!expressUccConfigCenter.isFreightNewEnquiryProcessSwitch()) {
            LOGGER.info("快运询价-旧流程");
            // 根据是否信任商家分别处理
            if (TrustSellerUtil.isTrustWeightVolume(originalOrderModel)) {
                LOGGER.info("信任商家：取下单时的重量和体积");
                // 信任商家：取下单时的重量和体积
                // 货品信息里的计算总体积
                cargoFacadeDto.setTotalCargoVolume(originalOrderModel.getCargoDelegate().totalCargoVolume());
                // 货品信息里的计算总重量
                cargoFacadeDto.setTotalCargoWeight(originalOrderModel.getCargoDelegate().totalCargoWeight());
            } else {
                LOGGER.info("非信任商家：取OFC传的询价重量、询价体积");
                // 非信任商家：取OFC传的询价重量、询价体积
                // 询价体积
                if (requestOrderModel.getEnquiry().getEnquiryVolume() != null
                        && requestOrderModel.getEnquiry().getEnquiryVolume().getValue() != null){
                    cargoFacadeDto.setTotalCargoVolume(requestOrderModel.getEnquiry().getEnquiryVolume().getValue());
                } else {
                    LOGGER.error("非信任商家并且询价体积为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价体积为空");
                }
                // 询价重量
                if (requestOrderModel.getEnquiry().getEnquiryWeight() != null
                        && requestOrderModel.getEnquiry().getEnquiryWeight().getValue() != null){
                    cargoFacadeDto.setTotalCargoWeight(requestOrderModel.getEnquiry().getEnquiryWeight().getValue());
                }else {
                    LOGGER.error("非信任商家并且询价重量为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价重量为空");
                }
            }

            // 计费数量(从外单里查出来的货品数量)
            cargoFacadeDto.setTotalCargoQuantity(updatedOrderModel.getCargoDelegate().totalCargoQuantity());

            return cargoFacadeDto;
        } else {
            LOGGER.info("快运询价-新流程");
            // 根据是否信任商家分别处理
            if (TrustSellerUtil.isTrustWeightVolume(originalOrderModel)) {
                LOGGER.info("信任商家：取下单时的重量和体积");
                // 信任商家：取下单时的重量和体积
                // 货品信息里的计算总体积
                cargoFacadeDto.setTotalCargoVolume(originalOrderModel.getCargoDelegate().totalCargoVolume());
                // 货品信息里的计算总重量
                cargoFacadeDto.setTotalCargoWeight(originalOrderModel.getCargoDelegate().totalCargoWeight());
                // 货品信息里的计算总数量
                cargoFacadeDto.setTotalCargoQuantity(originalOrderModel.getCargoDelegate().totalCargoQuantity());
            } else {
                LOGGER.info("非信任商家：取OFC传的询价重量、询价体积");
                // 非信任商家：取OFC传的询价重量、询价体积
                // 询价体积
                if (requestOrderModel.getEnquiry().getEnquiryVolume() != null
                        && requestOrderModel.getEnquiry().getEnquiryVolume().getValue() != null){
                    cargoFacadeDto.setTotalCargoVolume(requestOrderModel.getEnquiry().getEnquiryVolume().getValue());
                } else {
                    LOGGER.error("非信任商家并且询价体积为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价体积为空");
                }
                // 询价重量
                if (requestOrderModel.getEnquiry().getEnquiryWeight() != null
                        && requestOrderModel.getEnquiry().getEnquiryWeight().getValue() != null){
                    cargoFacadeDto.setTotalCargoWeight(requestOrderModel.getEnquiry().getEnquiryWeight().getValue());
                }else {
                    LOGGER.error("非信任商家并且询价重量为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价重量为空");
                }
                // 计费数量
                if(requestOrderModel.getEnquiry().getEnquiryQuantity() != null
                        && requestOrderModel.getEnquiry().getEnquiryQuantity().getValue() != null){
                    // 计费数量(包裹) 询价接口传了就从入参取 终端接入
                    cargoFacadeDto.setTotalCargoQuantity(requestOrderModel.getEnquiry().getEnquiryQuantity().getValue());
                } else {
                    // 计费数量(从外单里查出来的货品数量)
                    cargoFacadeDto.setTotalCargoQuantity(updatedOrderModel.getCargoDelegate().totalCargoQuantity());
                }
            }

            return cargoFacadeDto;
        }
    }

    /**
     * 货物信息，总重量、总体积、总数量
     * 快照已处理，处理后快照关系：当前单.快照 -> 原单
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel requestOrderModel, ExpressOrderModel originalOrderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        // 根据是否信任商家分别处理
        if (TrustSellerUtil.isTrustWeightVolume(originalOrderModel)) {
            LOGGER.info("信任商家：取下单时的重量和体积");
            // 信任商家：取下单时的重量和体积
            // 货品信息里的计算总体积
            cargoFacadeDto.setTotalCargoVolume(originalOrderModel.getCargoDelegate().totalCargoVolume());
            // 货品信息里的计算总重量
            cargoFacadeDto.setTotalCargoWeight(originalOrderModel.getCargoDelegate().totalCargoWeight());
            // 计费数量(从原单里查出来的货品数量)
            cargoFacadeDto.setTotalCargoQuantity(originalOrderModel.getCargoDelegate().totalCargoQuantity());
        } else {
            LOGGER.info("非信任商家：取上游传的询价重量、询价体积");
            // 非信任商家：取上游传的询价重量、询价体积
            // 询价体积
            if (requestOrderModel.getEnquiry().getEnquiryVolume() != null
                    && requestOrderModel.getEnquiry().getEnquiryVolume().getValue() != null){
                cargoFacadeDto.setTotalCargoVolume(requestOrderModel.getEnquiry().getEnquiryVolume().getValue());
            } else {
                LOGGER.error("非信任商家并且询价体积为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价体积为空");
            }
            // 询价重量
            if (requestOrderModel.getEnquiry().getEnquiryWeight() != null
                    && requestOrderModel.getEnquiry().getEnquiryWeight().getValue() != null){
                cargoFacadeDto.setTotalCargoWeight(requestOrderModel.getEnquiry().getEnquiryWeight().getValue());
            }else {
                LOGGER.error("非信任商家并且询价重量为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价重量为空");
            }

            // 询价数量
            if (requestOrderModel.getEnquiry().getEnquiryQuantity() != null
                    && requestOrderModel.getEnquiry().getEnquiryQuantity().getValue() != null) {
                // 计费数量(包裹) 询价接口传了就从入参取 终端接入
                cargoFacadeDto.setTotalCargoQuantity(requestOrderModel.getEnquiry().getEnquiryQuantity().getValue());
            } else {
                // 计费数量(从原单里查出来的货品数量)
                cargoFacadeDto.setTotalCargoQuantity(originalOrderModel.getCargoDelegate().totalCargoQuantity());
            }
        }

        return cargoFacadeDto;
    }

    /**
     * 货物信息：逆向单创建异步询价
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCreateCargoFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        // 计费数量
        cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
        // 货品信息里的计算总体积
        cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
        // 货品信息里的计算总重量
        cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());

        return cargoFacadeDto;
    }

    /**
     * 财务信息
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(ExpressOrderModel requestOrderModel, ExpressOrderModel updatedOrderModel, boolean attachFeesFromModel) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        //经王维金和张勇确认，询价时间需要用当前时间，具体原因：
        // 如果高峰期附加费时间是28日到29日，询价的时候需要查询高峰期附加费再调计费接口询价，
        // 查询高峰期附加费传的是当前时间，比如28号，命中高峰期附加费时间段（比如高峰期附加费时间段时28日到29日），
        // 但询价的时候计费日期用的是下单时间，比如是27日，没有命中高峰期附加费时间，计费就会报错，
        // 因此查询高峰期的时间和询价的计费日期都需要用当前时间
        financeFacadeDto.setEnquireTime(DateUtils.now());
        //结算方式 这里在查询外单那里已经补在快照里了,传递给计费询价接口
        financeFacadeDto.setSettlementType(updatedOrderModel.getFinance().getSettlementType());

        // 抵扣信息 只有逆向
        // 下了运费保，抵扣编码是OFC传过来的，所以抵扣信息在当前订单的fiance信息
        if (requestOrderModel.getFinance() != null
                && requestOrderModel.getFinance().getDeductionDelegate() != null
                && !requestOrderModel.getFinance().getDeductionDelegate().isEmpty()) {
            financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) requestOrderModel.getFinance().getDeductionDelegate().getDeductions()));
        }

        // 积分信息
        if (updatedOrderModel.getFinance() != null
                && updatedOrderModel.getFinance().getPoints() != null) {
            financeFacadeDto.setPointsInfoDto(toPointsInfoDto(updatedOrderModel.getFinance().getPoints()));
        }
        //附加费信息
        if (attachFeesFromModel) {
            if (updatedOrderModel.getFinance() != null &&
                    CollectionUtils.isNotEmpty(updatedOrderModel.getFinance().getAttachFees())) {
                List<CostInfoDto> attachFees = new ArrayList<>();
                updatedOrderModel.getFinance().getAttachFees().forEach(costInfo -> {
                    if (costInfo != null) {
                        attachFees.add(toCostInfoDto(costInfo));
                    }
                });
                financeFacadeDto.setAttachFees(attachFees);
            }
        }
        return financeFacadeDto;
    }

    /**
     * 财务信息：逆向单创建异步询价
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toCreateFinanceFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        //经王维金和张勇确认，询价时间需要用当前时间，具体原因：
        // 如果高峰期附加费时间是28日到29日，询价的时候需要查询高峰期附加费再调计费接口询价，
        // 查询高峰期附加费传的是当前时间，比如28号，命中高峰期附加费时间段（比如高峰期附加费时间段时28日到29日），
        // 但询价的时候计费日期用的是下单时间，比如是27日，没有命中高峰期附加费时间，计费就会报错，
        // 因此查询高峰期的时间和询价的计费日期都需要用当前时间
        financeFacadeDto.setEnquireTime(DateUtils.now());
        //结算方式 这里在查询外单那里已经补在快照里了,传递给计费询价接口
        financeFacadeDto.setSettlementType(orderModel.getFinance().getSettlementType());

        // 抵扣信息 只有逆向
        // 下了运费保，抵扣编码是OFC传过来的，所以抵扣信息在当前订单的fiance信息
        if (orderModel.getFinance() != null
                && orderModel.getFinance().getDeductionDelegate() != null
                && !orderModel.getFinance().getDeductionDelegate().isEmpty()) {
            financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) orderModel.getFinance().getDeductionDelegate().getDeductions()));
        }

        // 积分信息
        if (orderModel.getFinance() != null
                && orderModel.getFinance().getPoints() != null) {
            financeFacadeDto.setPointsInfoDto(toPointsInfoDto(orderModel.getFinance().getPoints()));
        }
        //附加费信息
        if (orderModel.getFinance() != null &&
                CollectionUtils.isNotEmpty(orderModel.getFinance().getAttachFees())) {
            List<CostInfoDto> attachFees = new ArrayList<>();
            orderModel.getFinance().getAttachFees().forEach(costInfo -> {
                if (costInfo != null) {
                    attachFees.add(toCostInfoDto(costInfo));
                }
            });
            financeFacadeDto.setAttachFees(attachFees);
        }
        return financeFacadeDto;
    }

    /**
     * 财务信息：修改场景
     * 新单没有取原单
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toModifyFinanceFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        //经王维金和张勇确认，询价时间需要用当前时间，具体原因：
        // 如果高峰期附加费时间是28日到29日，询价的时候需要查询高峰期附加费再调计费接口询价，
        // 查询高峰期附加费传的是当前时间，比如28号，命中高峰期附加费时间段（比如高峰期附加费时间段时28日到29日），
        // 但询价的时候计费日期用的是下单时间，比如是27日，没有命中高峰期附加费时间，计费就会报错，
        // 因此查询高峰期的时间和询价的计费日期都需要用当前时间
        financeFacadeDto.setEnquireTime(DateUtils.now());
        //结算方式
        if (orderModel.getFinance() != null
                && orderModel.getFinance().getSettlementType() != null) {
            financeFacadeDto.setSettlementType(orderModel.getFinance().getSettlementType());
        } else if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getFinance() != null
                && orderModel.getOrderSnapshot().getFinance().getSettlementType() != null) {
            financeFacadeDto.setSettlementType(orderModel.getOrderSnapshot().getFinance().getSettlementType());
        }

        // 抵扣信息
        if (orderModel.getFinance() != null
                && orderModel.getFinance().getDeductionDelegate() != null
                && !orderModel.getFinance().getDeductionDelegate().isEmpty()) {
            financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) orderModel.getFinance().getDeductionDelegate().getDeductions()));
        } else if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getFinance() != null
                && orderModel.getOrderSnapshot().getFinance().getDeductionDelegate() != null
                && !orderModel.getOrderSnapshot().getFinance().getDeductionDelegate().isEmpty()) {
            financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) orderModel.getOrderSnapshot().getFinance().getDeductionDelegate().getDeductions()));
        }

        // 积分信息
        if (orderModel.getFinance() != null
                && orderModel.getFinance().getPoints() != null) {
            financeFacadeDto.setPointsInfoDto(toPointsInfoDto(orderModel.getFinance().getPoints()));
        } else if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getFinance() != null
                && orderModel.getOrderSnapshot().getFinance().getPoints() != null) {
            financeFacadeDto.setPointsInfoDto(toPointsInfoDto(orderModel.getOrderSnapshot().getFinance().getPoints()));
        }

        // 附加费信息
        if (orderModel.getFinance() != null
                && CollectionUtils.isNotEmpty(orderModel.getFinance().getAttachFees())) {
            financeFacadeDto.setAttachFees(toCostInfoDtoList(orderModel.getFinance().getAttachFees()));
        } else if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getFinance() != null
                && CollectionUtils.isNotEmpty(orderModel.getOrderSnapshot().getFinance().getAttachFees())) {
            financeFacadeDto.setAttachFees(toCostInfoDtoList(orderModel.getOrderSnapshot().getFinance().getAttachFees()));
        }

        return financeFacadeDto;
    }

    /**
     * 附加费对象转换
     */
    private List<CostInfoDto> toCostInfoDtoList(List<CostInfo> costInfoList) {
        if (CollectionUtils.isEmpty(costInfoList)) {
            return null;
        }
        List<CostInfoDto> costInfoDtoList = new ArrayList<>();
        costInfoList.forEach(costInfo -> {
            if (costInfo != null && OperateTypeEnum.DELETE != costInfo.getOperateType()) {
                costInfoDtoList.add(toCostInfoDto(costInfo));
            }
        });
        return costInfoDtoList;
    }

    /**
     * 附加费对象转换
     *
     * @param costInfo
     * @return
     */
    public CostInfoDto toCostInfoDto(CostInfo costInfo) {
        CostInfoDto dto = new CostInfoDto();
        //费用项编码
        dto.setCostNo(costInfo.getCostNo());
        //费用项名称
        dto.setCostName(costInfo.getCostName());
        //收费方	0：向商家收 1：向寄件方收
        dto.setChargingSource(costInfo.getChargingSource());
        //月结费用填写（结算编码）前端卡控条件必填
        dto.setSettlementAccountNo(costInfo.getSettlementAccountNo());
        dto.setExtendProps(costInfo.getExtendProps());
        return dto;
    }
    /**
     * 抵扣信息
     */
    private List<DeductionInfoDto> toDeductionInfoDtos(List<Deduction> deductions) {
        if (CollectionUtils.isEmpty(deductions)) {
            return null;
        }
        List<DeductionInfoDto> deductionInfoDtos = new ArrayList<>(deductions.size());
        deductions.forEach(deduction -> {
            if (deduction != null) {
                DeductionInfoDto dto = new DeductionInfoDto();
                // 抵扣编码
                dto.setDeductionNo(deduction.getDeductionNo());
                // 抵扣金额
                dto.setDeductionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(deduction.getDeductionAmount()));
                // 扩展信息
                dto.setExtendProps(deduction.getExtendProps());
                deductionInfoDtos.add(dto);
            }
        });
        return deductionInfoDtos;
    }

    /**
     * 发货人信息
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toConsignorFacadeDto(ExpressOrderModel requestOrderModel, ExpressOrderModel updatedOrderModel) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        Consignor consignor = updatedOrderModel.getConsignor();
        Address address = consignor.getAddress();
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        if (address != null) {
            // 起始省(外单查出来的)
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            // 发件人二级地址上游传递过来的
            if(StringUtils.isNotBlank(requestOrderModel.getEnquiry().getEnquiryStartCityNo())){
                addressFacadeDto.setCityNoGis(requestOrderModel.getEnquiry().getEnquiryStartCityNo());
            } else {
                // 上游未传取订单原始数据
                addressFacadeDto.setCityNoGis(address.getCityNoGis());
            }
            // 起始县(外单查出来的)
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 发货人信息：逆向单创建异步询价
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toCreateConsignorFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        Consignor consignor = orderModel.getConsignor();
        Address address = consignor.getAddress();
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        if (address != null) {
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 发货人信息：修改场景
     * 新单没有取原单
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toModifyConsignorFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        Consignor consignor;
        if (orderModel.getConsignor() != null && orderModel.getConsignor().getAddress() != null) {
            consignor = orderModel.getConsignor();
        } else {
            consignor = orderModel.getOrderSnapshot().getConsignor();
        }
        Address address = consignor.getAddress();
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        if (address != null) {
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 货品信息：修改场景
     * 按是否信任商家处理
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toModifyCargoFacadeDto(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        if (TrustSellerUtil.isTrustWeightVolume(orderSnapshot)) {
            LOGGER.info("信任商家：取下单时的重量和体积");
            // 信任商家，直接新单没有取原单
            setTotalCargoVolume(cargoFacadeDto, orderModel, orderSnapshot);
            setTotalCargoWeight(cargoFacadeDto, orderModel, orderSnapshot);
            setTotalCargoQuantity(cargoFacadeDto, orderModel, orderSnapshot);
        } else {
            if (!expressUccConfigCenter.isFreightNewEnquiryProcessSwitch()) {
                LOGGER.info("快运询价-旧流程");
                LOGGER.info("非信任商家：取OFC传的询价重量、询价体积");
                // 非信任商家，取原单复重体积重量，没有再取新单，没有再取原单
                if (hasBillingVolumeValue(orderSnapshot)) {
                    cargoFacadeDto.setTotalCargoVolume(orderSnapshot.getFinance().getBillingVolume().getValue());
                } else {
                    setTotalCargoVolume(cargoFacadeDto, orderModel, orderSnapshot);
                }
                if (hasBillingWeightValue(orderSnapshot)) {
                    cargoFacadeDto.setTotalCargoWeight(orderSnapshot.getFinance().getBillingWeight().getValue());
                } else {
                    setTotalCargoWeight(cargoFacadeDto, orderModel, orderSnapshot);
                }
                setTotalCargoQuantity(cargoFacadeDto, orderModel, orderSnapshot);
            } else {
                LOGGER.info("快运询价-新流程");
                LOGGER.info("非信任商家：取recheckVolume、recheckWeight、actualReceivedQuantity");
                // 体积
                BigDecimal recheckVolume = FreightGetFieldUtils.getRecheckVolumeValue(orderSnapshot);
                if (recheckVolume == null) {
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_FREIGHT_ENQUIRY_INVALID_DATA, "快运订单询价时数据不全:复核体积(recheckVolume)为空:" + orderModel.orderNo());
                    LOGGER.error("复核体积(recheckVolume)为空:" + orderModel.orderNo());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("复核体积(recheckVolume)为空:" + orderModel.orderNo());
                }
                cargoFacadeDto.setTotalCargoVolume(recheckVolume);

                // 重量
                BigDecimal recheckWeight = FreightGetFieldUtils.getRecheckWeightValue(orderSnapshot);
                if (recheckWeight == null) {
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_FREIGHT_ENQUIRY_INVALID_DATA, "快运订单询价时数据不全:复核重量(recheckWeight)为空:" + orderModel.orderNo());
                    LOGGER.error("复核重量(recheckWeight)为空:" + orderModel.orderNo());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("复核重量(recheckWeight)为空:" + orderModel.orderNo());
                }
                cargoFacadeDto.setTotalCargoWeight(recheckWeight);

                // 数量
                BigDecimal actualReceivedQuantity = FreightGetFieldUtils.getActualReceivedQuantityValue(orderSnapshot);
                if (actualReceivedQuantity == null) {
                    actualReceivedQuantity = FreightGetFieldUtils.getTotalCargoQuantity(orderModel);
                    if (actualReceivedQuantity == null) {
                        actualReceivedQuantity = FreightGetFieldUtils.getTotalCargoQuantity(orderSnapshot);
                    }
                }
                cargoFacadeDto.setTotalCargoQuantity(actualReceivedQuantity);
            }
        }
        return cargoFacadeDto;
    }

    /**
     * 判断计费体积有没有值
     */
    private boolean hasBillingVolumeValue(ExpressOrderModel orderModel) {
        return orderModel != null
                && orderModel.getFinance() != null
                && orderModel.getFinance().getBillingVolume() != null
                && orderModel.getFinance().getBillingVolume().getValue() != null;
    }

    /**
     * 判断计费重量有没有值
     */
    private boolean hasBillingWeightValue(ExpressOrderModel orderModel) {
        return orderModel != null
                && orderModel.getFinance() != null
                && orderModel.getFinance().getBillingWeight() != null
                && orderModel.getFinance().getBillingWeight().getValue() != null;
    }

    /**
     * 修改场景设置总体积
     * 新单没有取原单
     */
    private void setTotalCargoVolume(BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto, ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        if (orderModel.getCargoDelegate() != null
                && orderModel.getCargoDelegate().totalCargoVolume() != null) {
            cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
        } else if (orderSnapshot != null
                && orderSnapshot.getCargoDelegate() != null
                && orderSnapshot.getCargoDelegate().totalCargoVolume() != null){
            cargoFacadeDto.setTotalCargoVolume(orderModel.getOrderSnapshot().getCargoDelegate().totalCargoVolume());
        }
    }

    /**
     * 修改场景设置总重量
     * 新单没有取原单
     */
    private void setTotalCargoWeight(BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto, ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        if (orderModel.getCargoDelegate() != null
                && orderModel.getCargoDelegate().totalCargoWeight() != null) {
            cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
        } else if (orderSnapshot != null
                && orderSnapshot.getCargoDelegate() != null
                && orderSnapshot.getCargoDelegate().totalCargoWeight() != null){
            cargoFacadeDto.setTotalCargoWeight(orderModel.getOrderSnapshot().getCargoDelegate().totalCargoWeight());
        }
    }

    /**
     * 修改场景设置总数量
     * 新单没有取原单
     */
    private void setTotalCargoQuantity(BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto, ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        if (orderModel.getCargoDelegate() != null
                && orderModel.getCargoDelegate().totalCargoQuantity() != null) {
            cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
        } else if (orderSnapshot != null
                && orderSnapshot.getCargoDelegate() != null
                && orderSnapshot.getCargoDelegate().totalCargoQuantity() != null){
            cargoFacadeDto.setTotalCargoQuantity(orderModel.getOrderSnapshot().getCargoDelegate().totalCargoQuantity());
        }
    }

    /**
     * 转换积分信息
     */
    private PointsInfoDto toPointsInfoDto(Points points) {
        if (points == null) {
            return null;
        }
        PointsInfoDto pointsInfoDto = new PointsInfoDto();
        if (points.getRedeemPointsAmount() != null) {
            MoneyInfoDto redeemPointsAmount = new MoneyInfoDto();
            redeemPointsAmount.setAmount(points.getRedeemPointsAmount().getAmount());
            redeemPointsAmount.setCurrencyCode(points.getRedeemPointsAmount().getCurrency());
            pointsInfoDto.setRedeemPointsAmount(redeemPointsAmount);
        }
        if (points.getRedeemPointsQuantity() != null) {
            QuantityInfoDto redeemPointsQuantity = new QuantityInfoDto();
            redeemPointsQuantity.setUnit(points.getRedeemPointsQuantity().getUnit());
            redeemPointsQuantity.setValue(points.getRedeemPointsQuantity().getValue());
            pointsInfoDto.setRedeemPointsQuantity(redeemPointsQuantity);
        }
        return pointsInfoDto;
    }

    /**
     * 扩展字段优惠相关信息
     */
    private Object extendParamToDiscountDetailList(ExpressOrderModel orderModel, String operator) {
        List<DiscountDetailDTO> detailList = new ArrayList<>();

        // 优惠券信息
        if (orderModel.getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getPromotion().getTickets())) {
            detailList.addAll(ticketsToDiscountDetailDTOs(orderModel.getPromotion().getTickets(), operator));
        }

        // 折扣信息
        if (orderModel.getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getPromotion().getDiscounts())) {
            detailList.addAll(discountsToDiscountDetailDTOs(orderModel.getPromotion().getDiscounts()));
        }
        // 营销折扣信息
        if (orderModel.getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getPromotion().getOperationDiscountInfos())) {
            detailList.addAll(discountsToDiscountDetailDTOs(orderModel.getPromotion().getOperationDiscountInfos()));
        }

        return detailList;
    }

    private List<DiscountDetailDTO> extendParamToDiscountDetailLists(ExpressOrderModel orderModel, String operator) {
        return extendParamToDiscountDetailLists(orderModel, operator, true);
    }

    /**
     * 扩展字段优惠相关信息
     */
    private List<DiscountDetailDTO> extendParamToDiscountDetailLists(ExpressOrderModel orderModel, String operator, boolean operationDiscountFlag) {
        List<DiscountDetailDTO> detailList = new ArrayList<>();

        // 优惠券信息
        if (orderModel.getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getPromotion().getTickets())) {
            detailList.addAll(ticketsToDiscountDetailDTOs(orderModel.getPromotion().getTickets(), operator));
        }

        // 折扣信息
        if (orderModel.getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getPromotion().getDiscounts())) {
            detailList.addAll(discountsToDiscountDetailDTOs(orderModel.getPromotion().getDiscounts()));
        }

        if (operationDiscountFlag) {
            // 营销折扣信息
            if (orderModel.getPromotion() != null
                    && CollectionUtils.isNotEmpty(orderModel.getPromotion().getOperationDiscountInfos())) {
                detailList.addAll(discountsToDiscountDetailDTOs(orderModel.getPromotion().getOperationDiscountInfos()));
            }
        }

        return detailList;
    }

    /**
     * 扩展字段优惠相关信息：修改场景
     * 新单没有取原单
     */
    private Object modifyExtendParamToDiscountDetailList(ExpressOrderModel orderModel) {
        List<DiscountDetailDTO> detailList = new ArrayList<>();

        // 优惠券信息
        if (orderModel.getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getPromotion().getTickets())) {
            detailList.addAll(ticketsToDiscountDetailDTOs(orderModel.getPromotion().getTickets(), orderModel.getOperator()));
        } else if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getOrderSnapshot().getPromotion().getTickets())) {
            detailList.addAll(ticketsToDiscountDetailDTOs(orderModel.getOrderSnapshot().getPromotion().getTickets(), orderModel.getOperator()));
        }

        // 折扣信息
        if (orderModel.getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getPromotion().getDiscounts())) {
            detailList.addAll(discountsToDiscountDetailDTOs(orderModel.getPromotion().getDiscounts()));
        } else if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getOrderSnapshot().getPromotion().getDiscounts())) {
            detailList.addAll(discountsToDiscountDetailDTOs(orderModel.getOrderSnapshot().getPromotion().getDiscounts()));
        }

        // 营销信息
        if (orderModel.getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getPromotion().getOperationDiscountInfos())) {
            detailList.addAll(discountsToDiscountDetailDTOs(orderModel.getPromotion().getOperationDiscountInfos()));
        } else if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getPromotion() != null
                && CollectionUtils.isNotEmpty(orderModel.getOrderSnapshot().getPromotion().getOperationDiscountInfos())) {
            detailList.addAll(discountsToDiscountDetailDTOs(orderModel.getOrderSnapshot().getPromotion().getOperationDiscountInfos()));
        }

        return detailList;
    }

    /**
     * 扩展字段转换优惠券信息
     * 修改场景跳过删除项
     */
    private List<DiscountDetailDTO> ticketsToDiscountDetailDTOs(List<Ticket> tickets, String operator) {
        List<DiscountDetailDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(tickets)) {
            return list;
        }
        for (Ticket ticket : tickets) {
            // 修改场景跳过删除项
            if (ticket.getOperateType() != null
                    && OperateTypeEnum.DELETE == ticket.getOperateType()) {
                continue;
            }

            DiscountDetailDTO discountDetail = new DiscountDetailDTO();
            //优惠类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
            if (ticket.getTicketSource() != null && TicketSourceEnum.DADA.getCode().equals(ticket.getTicketSource())) {
                discountDetail.setDiscountNo(ticket.getTicketNo());
                discountDetail.setType(DISCOUNT_TYPE_DADA_TICKET);
            } else {
                if (StringUtils.isNotBlank(ticket.getTicketBatchNo())) {
                    discountDetail.setDiscountNo(ticket.getTicketBatchNo());
                    discountDetail.setType(DISCOUNT_TYPE_JD_TICKET_BATCH_NO);
                } else {
                    discountDetail.setDiscountNo(ticket.getTicketNo());
                    discountDetail.setType(DISCOUNT_TYPE_JD_TICKET);
                }
            }

            discountDetail.setUserNo(operator);
            list.add(discountDetail);
        }
        return list;
    }

    /**
     * 扩展字段转换折扣信息
     * 修改场景跳过删除项
     */
    private List<DiscountDetailDTO> discountsToDiscountDetailDTOs(List<Discount> discounts) {
        List<DiscountDetailDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(discounts)) {
            return list;
        }
        for (Discount discount : discounts) {
            // 修改场景跳过删除项
            if (discount.getOperateType() != null
                    && OperateTypeEnum.DELETE == discount.getOperateType()) {
                continue;
            }
            DiscountDetailDTO discountDetail = new DiscountDetailDTO();
            discountDetail.setDiscountNo(discount.getDiscountNo());
            // 优惠类型：1：指定折扣；2：京东优惠券 ；3：达达优惠券
            discountDetail.setType(DISCOUNT_TYPE_DISCOUNT);
            list.add(discountDetail);
        }
        return list;
    }

    /**
     * 扩展字段转换积分信息
     */
    private void extendParamToRewardPoints(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        if (orderModel.getFinance() != null
                && orderModel.getFinance().getPoints()!= null
                && orderModel.getFinance().getPoints().getRedeemPointsQuantity() != null
                && orderModel.getFinance().getPoints().getRedeemPointsQuantity().getValue() != null) {
            extendParam.put(EnquiryConstants.REWARD_POINTS, orderModel.getFinance().getPoints().getRedeemPointsQuantity().getValue().intValue());
        }
    }

    /**
     * 扩展字段转换积分信息：修改场景
     * 修改场景，新单没有取原单
     */
    private void modifyExtendParamToRewardPoints(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        if (orderModel.getFinance() != null
                && orderModel.getFinance().getPoints()!= null
                && orderModel.getFinance().getPoints().getRedeemPointsQuantity() != null
                && orderModel.getFinance().getPoints().getRedeemPointsQuantity().getValue() != null) {
            extendParam.put(EnquiryConstants.REWARD_POINTS, orderModel.getFinance().getPoints().getRedeemPointsQuantity().getValue().intValue());
        } else if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getFinance() != null
                && orderModel.getOrderSnapshot().getFinance().getPoints()!= null
                && orderModel.getOrderSnapshot().getFinance().getPoints().getRedeemPointsQuantity() != null
                && orderModel.getOrderSnapshot().getFinance().getPoints().getRedeemPointsQuantity().getValue() != null) {
            extendParam.put(EnquiryConstants.REWARD_POINTS, orderModel.getOrderSnapshot().getFinance().getPoints().getRedeemPointsQuantity().getValue().intValue());
        }
    }

    /**
     * 获取未删除的主产品
     */
    private String getMajorProductNo(List<Product> products) {
        if (CollectionUtils.isNotEmpty(products)) {
            for (Product product : products) {
                if (isMajorProduct(product) && notDelete(product)) {
                    return product.getProductNo();
                }
            }
        }
        LOGGER.error("没有未删除的主产品");
        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("没有未删除的主产品");
    }

    /**
     * 是否主产品
     */
    private boolean isMajorProduct(Product product) {
        return ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(product.getProductType());
    }

    /**
     * 是否未删除
     */
    private boolean notDelete(Product product) {
        return (product.getOperateType() == null
                || !OperateTypeEnum.DELETE.getCode().equals(product.getOperateType().getCode()));
    }

    /**
     * 必要时将保价产品替换成特快重货-保价 fr-a-0016
     * 2023-05-31 与快运产品王传宇、林恒波确认，主产品是fr-m-0004（特快重货）+fr-m-0002（特惠重货）时，调计费标准产品询价接口fr-a-0002（普通保价），需要转化成fr-a-0016（特快重货保价）
     * 背景：业务及运营系统，目前保价只识别了fr-a-0002 （普通保价），下单也只会下一个码，但计费是根据两个保价码分别计费的。短期方案，订单中心调计费根据主产品去做码的转换，长期方案，需要王传宇和林恒波 推动业务及运营系统按标准的两个码执行
     */
    private String getProductNo(String majorProductNo, String productNo) {
        // 主产品不是特快重货或特惠重货，不需要替换
        if (!ProductEnum.TKZH.getCode().equals(majorProductNo)
                && !ProductEnum.THZH.getCode().equals(majorProductNo)) {
            return productNo;
        }
        // 增值产品不是保价 fr-a-0002 不需要替换
        if (!AddOnProductEnum.INSURED_VALUE_TOB.getCode().equals(productNo)) {
            return productNo;
        }
        LOGGER.info("询价时增值产品保价替换：主产品是{},增值产品保价是{},替换成fr-a-0016", majorProductNo, productNo);
        return AddOnProductEnum.FR_A_0016.getCode();
    }

    /**
     * 修改场景获取订单号
     */
    private String getModifyOrderNo(ExpressOrderModel orderModel) {
        // 当前单
        if (StringUtils.isNotEmpty(orderModel.orderNo())) {
            return orderModel.orderNo();
        }

        // 原单
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (StringUtils.isNotEmpty(orderSnapshot.orderNo())) {
            return orderSnapshot.orderNo();
        }

        LOGGER.error("获取订单号失败");
        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("获取订单号失败");
    }

    /**
     * 获取运单号
     */
    private String getWaybillCode(ExpressOrderModel orderModel) {
        // 当前单
        if (orderModel.getRefOrderInfoDelegate() != null
                && !orderModel.getRefOrderInfoDelegate().isEmpty()
                && StringUtils.isNotEmpty(orderModel.getRefOrderInfoDelegate().getWaybillNo())) {
            return orderModel.getRefOrderInfoDelegate().getWaybillNo();
        }

        LOGGER.error("获取运单号失败");
        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("获取运单号失败");
    }

    /**
     * 修改场景获取运单号
     */
    private String getModifyWaybillCode(ExpressOrderModel orderModel) {
        // 当前单
        if (orderModel.getRefOrderInfoDelegate() != null
                && !orderModel.getRefOrderInfoDelegate().isEmpty()
                && StringUtils.isNotEmpty(orderModel.getRefOrderInfoDelegate().getWaybillNo())) {
            return orderModel.getRefOrderInfoDelegate().getWaybillNo();
        }

        // 原单
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot.getRefOrderInfoDelegate() != null
                && !orderSnapshot.getRefOrderInfoDelegate().isEmpty()
                && StringUtils.isNotEmpty(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo())) {
            return orderSnapshot.getRefOrderInfoDelegate().getWaybillNo();
        }

        LOGGER.error("获取运单号失败");
        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("获取运单号失败");
    }

    /**
     * 补全主产品计费用的产品要素
     */
    public void complementMainProductAttrs(List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos, ExpressOrderModel orderModel) {
        if (CollectionUtils.isEmpty(productFacadeDtos) || orderModel == null) {
            return;
        }
        for (BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto : productFacadeDtos) {
            complementMainProductAttrsForTHPH(productFacadeDto, orderModel);
        }
    }

    /**
     * fr-m-0017特惠泡货补全寄件围栏ID
     */
    private void complementMainProductAttrsForTHPH(BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto, ExpressOrderModel orderModel) {
        if (!ProductEnum.THPH.getCode().equals(productFacadeDto.getProductNo())) {
            return;
        }

        String fenceId = getConsignorAddressFenceId(orderModel);
        if (StringUtils.isBlank(fenceId)) {
            return;
        }

        // 避免补全的数据持久化，需要拷贝一个产品要素再补全
        Map<String, String> newProductAttrs = new HashMap<>();
        if (MapUtils.isNotEmpty(productFacadeDto.getProductAttrs())) {
            // 以防有其他计费要素，需要putAll
            newProductAttrs.putAll(productFacadeDto.getProductAttrs());
        }
        newProductAttrs.put(EnquiryConstants.FENCE_ID, fenceId);
        productFacadeDto.setProductAttrs(newProductAttrs);
    }

    /**
     * 获取寄件围栏ID
     */
    private String getConsignorAddressFenceId(ExpressOrderModel orderModel) {
        if (orderModel == null) {
            return null;
        }

        Consignor consignor = orderModel.getConsignor();
        if (consignor == null) {
            return null;
        }

        Address address = consignor.getAddress();
        if (address == null) {
            return null;
        }

        List<Fence> fenceInfos = address.getFenceInfos();
        if (CollectionUtils.isEmpty(fenceInfos)) {
            return null;
        }

        return fenceInfos.get(0).getFenceId();
    }

    /**
     * 询价场景扩展字段：跨境报关信息
     * 跨境业务类型、是否文件、币种
     */
    private void extendParamToCustoms(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        Customs customs = orderModel.getCustoms();
        if (customs == null) {
            return;
        }
        // 跨境业务类型
        if (customs.getStartFlowDirection() != null && customs.getEndFlowDirection() != null) {
            // 格式：始发站点_目的站点
            String crossBorderType = customs.getStartFlowDirection().name() + "_" + customs.getEndFlowDirection().name();
            extendParam.put(EnquiryConstants.CROSS_BORDER_TYPE, crossBorderType);
        }
        // 是否文件
        if (customs.getFileTag() != null) {
            extendParam.put(EnquiryConstants.FILE_TAG, customs.getFileTag());
        }
        // 币种
        CurrencyCodeEnum currencyCodeEnum = getCurrencyCodeEnum(customs, orderModel.getFinance());
        if (currencyCodeEnum != null) {
            extendParam.put(EnquiryConstants.EXCHANGE_CURRENCY, String.valueOf(currencyCodeEnum.getCode()));
        }
    }

    /**
     * 获取币种
     */
    private CurrencyCodeEnum getCurrencyCodeEnum(Customs customs, Finance finance) {
        AdministrativeRegionEnum startFlowDirection = null;
        AdministrativeRegionEnum endFlowDirection = null;
        SettlementTypeEnum settlementType = null;
        if (customs != null) {
            startFlowDirection = customs.getStartFlowDirection();
            endFlowDirection = customs.getEndFlowDirection();
        }
        if (finance != null) {
            settlementType = finance.getSettlementType();
        }
        return ReceiptCurrencyUtil.getCurrency(startFlowDirection, endFlowDirection, settlementType);
    }

    /**
     * 询价场景扩展字段：包裹信息
     */
    private void extendParamToPackageInformation(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        if (orderModel == null
                || orderModel.getEnquiry() == null
                || MapUtils.isEmpty(orderModel.getEnquiry().getExtendProps())
                || !orderModel.getEnquiry().getExtendProps().containsKey(EnquiryConstants.PACKAGE_LIST)) {
            return;
        }

        String jsonString = orderModel.getEnquiry().getExtendProps().get(EnquiryConstants.PACKAGE_LIST);
        if (StringUtils.isBlank(jsonString)) {
            return;
        }
        List<Package> packageList = JSONUtils.jsonToList(jsonString, Package.class);
        if (CollectionUtils.isEmpty(packageList)) {
            return;
        }
        extendParam.put(EnquiryConstants.PACKAGE_INFORMATION, PackageMapper.INSTANCE.toStandardProductPackageInformationList(packageList));
    }

    /**
     * 转换为计费产品防腐层
     * @param product
     * @return
     */
    public BillingEnquiryFacadeRequest.ProductFacadeDto toProductFacadeDto(Product product, Product mainProduct) {
        if(product == null){
            return null;
        }
        BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
        productFacadeDto.setProductNo(product.getProductNo());
        productFacadeDto.setProductType(product.getProductType());
        productFacadeDto.setParentNo(product.getParentNo());
        Map<String, String> productAttrs = new HashMap<>();
        productAttrs.putAll(product.getProductAttrs());
        //主产品编码，计费用来根据主产品来区分增值服务的价格本
        productAttrs.put(MAIN_PRODUCT_CODE, mainProduct.getProductNo());
        productFacadeDto.setProductAttrs(productAttrs);
        return productFacadeDto;
    }


    /**
     * 补充附加费
     *
     * @param requestProfile
     * @param orderModel
     * @param snapshot
     * @param billingEnquiryFacadeRequest
     * @return
     */
    public BillingEnquiryFacadeRequest fillProductCenterSurcharge(RequestProfile requestProfile, ExpressOrderModel orderModel,
                                                                  ExpressOrderModel snapshot, BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {

        // 整单维度附加费列表
        List<Product> products;
        // 包裹维度附加费列表
        List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList = new ArrayList<>();

        // 获取询价扩展字段包裹明细列表
        List<Package> packageList = enquiryFacadeTranslator.getEnquiryPackages(orderModel);

        // 查询产品中心附加费综合查询接口
        List<SurchargeResponse> surchargeResponseList = enquiryFacadeTranslator.getSurchargeFacade(orderModel, snapshot, requestProfile, packageList);

        // 获取主产品编码
        String mainProductNo = snapshot.getProductDelegate().getMainProduct().getProductNo();
        // 根据主产品编码获取整单维度附加费
        products = fetchWaybillSurcharge(mainProductNo, surchargeResponseList);
        LOGGER.info("询价-获取产品中心整单维度附加费 {}", JSONUtils.beanToJSONDefault(products));
        // 根据主产品编码获取包裹维度附加费
        packageSurchargeList = fetchPackageSurcharge(mainProductNo, surchargeResponseList, packageList);
        LOGGER.info("询价-获取产品中心包裹维度附加费 {}", JSONUtils.beanToJSONDefault(packageSurchargeList));

        // 有条件过滤掉超长超重附加费
        // 过滤条件：
        // a、运单模式下，包裹数大于等于2时过滤；
        // b、仓配快递接货仓模式时过滤
        if (enquiryFacadeTranslator.needFilterOverLengthAndWeightSurcharge(orderModel, snapshot)) {
            LOGGER.info("询价-满足过滤条件过滤超长超重附加费");
            filterOverLengthAndWeightSurcharge(products, packageSurchargeList);
        }

        // 如果上游请求的是运单维度附加费，但是因产品中心附加费编码不变，只是将附加费性质从运单维度切换到了包裹维度（目前业务已知的只有超长超重附加费）
        // 这种情况下，需要将包裹维度附加费转换成运单维度附加费
        enquiryFacadeTranslator.resetSurchargeForWaybillMode(orderModel, products, packageSurchargeList);

        // 需收取包裹维度附加费的包裹数超过100时，按包裹重量降序排列只取前100个，并告警
        if (packageSurchargeList.size() > EnquiryConstants.MAX_PACKAGE_FOR_SURCHARGE) {
            // 包裹明细数量超阈值告警信息
            String businessAlarmMsg = new StringBuilder()
                    .append("订单[")
                    .append(orderModel.orderNo())
                    .append("]询价,包裹维度附加费涉及包裹个数[")
                    .append(packageSurchargeList.size())
                    .append("]超过最大限制[")
                    .append(EnquiryConstants.MAX_PACKAGE_FOR_SURCHARGE).append("],已按包裹重量倒叙排序并截取前100个").toString();

            // 若超过100，按包裹重量倒叙排列，截取前100个，并告警
            packageSurchargeList = packageSurchargeList.stream()
                    .sorted((p1, p2) -> p2.getWeight().compareTo(p1.getWeight()))
                    .limit(EnquiryConstants.MAX_PACKAGE_FOR_SURCHARGE).collect(Collectors.toList());

            // 自定义告警
            LOGGER.warn(businessAlarmMsg);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_SURCHARGE_PACKAGE_NUM_ALARM
                    , System.currentTimeMillis()
                    , businessAlarmMsg);
        }

        // 快照数据中的CCCZ，放到调用计费入参/持久化的products
        reserveSomeSnapshotAttachFees(orderModel, products);

        // 附加费写入订单
        // 附加费扩展字段中只存包裹号
        writeSurchargeToOrderModel(orderModel, products, packageSurchargeList);

        LOGGER.info("询价-设置计费入参前的整单维度附加费 {}", JSONUtils.beanToJSONDefault(products));
        LOGGER.info("询价-设置计费入参前的包裹维度附加费 {}", JSONUtils.beanToJSONDefault(packageSurchargeList));

        // 整单附加费
        if (CollectionUtils.isNotEmpty(products)) {
            billingEnquiryFacadeRequest.getProductFacadeDtoList().addAll(enquiryFacadeTranslator.peakPeriodToProductFacadeDto(products));
        }

        // 包裹附加费(开关关闭时走不到此逻辑)
        if (CollectionUtils.isNotEmpty(packageSurchargeList)) {
            enquiryFacadeTranslator.extendParamToPackageList(billingEnquiryFacadeRequest, packageSurchargeList);
        }

        return billingEnquiryFacadeRequest;
    }

    /**
     * 过滤掉超长超重附加费
     *
     * @param products
     * @param packageSurchargeList
     */
    public void filterOverLengthAndWeightSurcharge(List<Product> products, List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList) {
        // 过滤掉整单维度超长超重附加费
        products = products.stream().filter(product -> !AttachFeeEnum.CC_CZ_FJF.getCode().equals(product.getProductNo())).collect(Collectors.toList());

        // 过滤掉包裹维度超长超重附加费
        for (BillingEnquiryFacadeRequest.PackageFacadeDto packageFacadeDto : packageSurchargeList) {
            List<BillingEnquiryFacadeRequest.ProductFacadeDto> productList = packageFacadeDto.getProductList();
            if (CollectionUtils.isEmpty(productList)) {
                continue;
            }
            productList = productList.stream().filter(product -> !AttachFeeEnum.CC_CZ_FJF.getCode().equals(product.getProductNo())).collect(Collectors.toList());
            packageFacadeDto.setProductList(productList);
        }
    }

    /**
     * 把附加费写入订单模型
     *
     * @param orderModel
     * @param products 整单维度附加费
     * @param packageSurchargeList 包裹维度附加费
     */
    private void writeSurchargeToOrderModel(ExpressOrderModel orderModel, List<Product> products,
                                            List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList) {

        // 需要写入的附加费列表
        List<CostInfo> attachFees = new ArrayList<>();

        // 【运单维度附加费】
        for (Product product : products) {
            CostInfo costInfo = new CostInfo();
            costInfo.setCostNo(product.getProductNo());
            costInfo.setCostName(product.getProductName());
            // 只有超长超重附加费会有称重模式
            if (AttachFeeEnum.CC_CZ_FJF.getCode().equals(costInfo.getCostNo())) {
                // 扩展字段写入称重模式
                Map<String, String> extendProps = new HashMap<>();
                extendProps.put(EnquiryConstants.WEIGHTING_MODE, String.valueOf(WeightingModeEnum.WAYBILL.getCode()));
                costInfo.setExtendProps(extendProps);
            }
            attachFees.add(costInfo);
        }

        // 【包裹维度附加费】
        // 产品编码-产品名称映射关系
        Map<String, String> productNo2NameMap = new HashMap<>();
        // 产品编码-包裹明细列表映射关系
        Map<String, List<BillingEnquiryFacadeRequest.PackageFacadeDto>> product2PackageListMap = new HashMap<>();
        // 结构转换：package-productList 转换成 product-packageList
        surchargeFacadeTranslator.convertPackageProductListToProductPackageList(packageSurchargeList, productNo2NameMap, product2PackageListMap);
        //
        for (Map.Entry<String, List<BillingEnquiryFacadeRequest.PackageFacadeDto>> entry : product2PackageListMap.entrySet()) {
            CostInfo costInfo = new CostInfo();
            // 附加费编码
            costInfo.setCostNo(entry.getKey());
            // 附加费名称
            costInfo.setCostName(productNo2NameMap.get(entry.getKey()));

            // 只有超长超重附加费会有称重模式和包裹明细
            if (AttachFeeEnum.CC_CZ_FJF.getCode().equals(costInfo.getCostNo())) {
                // 包裹模式，附加费扩展字段写入包裹明细列表（只写包裹号）
                Map<String, String> extendProps = new HashMap<>();
                // 扩展字段写入称重模式
                extendProps.put(EnquiryConstants.WEIGHTING_MODE, String.valueOf(WeightingModeEnum.PACKAGE.getCode()));
                // 获取当前附加费关联的包裹号列表
                List<String> packageNoList = entry.getValue().stream().map(BillingEnquiryFacadeRequest.PackageFacadeDto::getPackageNo).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(packageNoList)) {
                    // 附加费包裹明细
                    extendProps.put(EnquiryConstants.PACKAGE_LIST, JSONUtils.beanToJSONDefault(packageNoList));
                }
                costInfo.setExtendProps(extendProps);
            }

            attachFees.add(costInfo);
        }

        // 附加费写入订单模型
        if (CollectionUtils.isNotEmpty(attachFees)) {
            // 最新附加费覆盖原有的
            orderModel.getFinance().setAttachFees(attachFees);
        } else {
            // 如果本次查询产品中心返回的附加费为空，则清空原附加费
            orderModel.getFinance().setAttachFees(null);
        }
    }

    /**
     * 从综合附加费查询结果中获取【运单】维度附加费
     *
     * @param mainProductNo 主产品编码
     * @param responseList  综合附加费查询结果
     * @return
     */
    public List<Product> fetchWaybillSurcharge(String mainProductNo, List<SurchargeResponse> responseList) {

        if (StringUtils.isBlank(mainProductNo) || CollectionUtils.isEmpty(responseList)) {
            return new ArrayList<>();
        }

        List<Product> products = new ArrayList<>();
        for (SurchargeResponse surchargeResponse : responseList) {

            // 整单维度附加费
            Map<String, List<SurchargeResponse.SurchargeInfo>> surchargeInfoMap = surchargeResponse.getSurchargeInfoMap();
            if (MapUtils.isEmpty(surchargeInfoMap)) {
                continue;
            }

            // 当前主产品编码下的整单维度附加费
            List<SurchargeResponse.SurchargeInfo> surchargeInfoList = surchargeInfoMap.get(mainProductNo);
            if (CollectionUtils.isEmpty(surchargeInfoList)) {
                continue;
            }

            // 结构转换
            surchargeInfoList.forEach(surchargeInfo -> {
                if(!AttachFeeEnum.CC_CZ_FJF.getCode().equals(surchargeInfo.getSurchargeNo())) {
                    Product product = new Product();
                    product.setProductNo(surchargeInfo.getSurchargeNo());
                    product.setProductName(surchargeInfo.getSurchargeName());
                    product.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                    products.add(product);
                }
            });
        }

        return products;
    }

    /**
     * 从综合附加费查询结果中获取【包裹】维度附加费
     *
     * @param mainProductNo 主产品编码
     * @param responseList  综合附加费查询结果
     * @param packageList   包裹明细列表
     * @return
     */
    public List<BillingEnquiryFacadeRequest.PackageFacadeDto> fetchPackageSurcharge(String mainProductNo, List<SurchargeResponse> responseList, List<Package> packageList) {

        if (StringUtils.isBlank(mainProductNo) || CollectionUtils.isEmpty(responseList)) {
            return new ArrayList<>();
        }

        // [包裹号:包裹]
        Map<String, Package> packageMap = packageList.stream().collect(Collectors.toMap(Package::getPackageNo, Function.identity(), (p1, p2) -> p2));

        // 包裹维度附加费列表
        List<BillingEnquiryFacadeRequest.PackageFacadeDto> packageSurchargeList = new ArrayList<>();

        for (SurchargeResponse surchargeResponse : responseList) {

            // 包裹维度附加费
            Map<String, List<SurchargeResponse.CargoSurchargeInfo>> cargoSurchargeInfoMap = surchargeResponse.getCargoSurchargeMap();
            if (MapUtils.isEmpty(cargoSurchargeInfoMap)) {
                continue;
            }

            // 当前主产品下的包裹维度附加费
            List<SurchargeResponse.CargoSurchargeInfo> cargoSurchargeInfoList = cargoSurchargeInfoMap.get(mainProductNo);
            if (CollectionUtils.isEmpty(cargoSurchargeInfoList)) {
                continue;
            }

            cargoSurchargeInfoList.forEach(cargoSurchargeInfo -> {
                BillingEnquiryFacadeRequest.PackageFacadeDto packageFacadeDto = new BillingEnquiryFacadeRequest.PackageFacadeDto();
                // 包裹号
                String packageNo = cargoSurchargeInfo.getCargoNo();
                // 附加费列表
                List<BillingEnquiryFacadeRequest.ProductFacadeDto> productList = new ArrayList<>();
                List<SurchargeResponse.SurchargeInfo> surchargeInfoList = cargoSurchargeInfo.getSurchargeInfos();
                if (CollectionUtils.isNotEmpty(surchargeInfoList)) {
                    // 结构转换
                    surchargeInfoList.forEach(surchargeInfo -> {
                        if(!AttachFeeEnum.CC_CZ_FJF.getCode().equals(surchargeInfo.getSurchargeNo())) {
                            BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                            productFacadeDto.setProductNo(surchargeInfo.getSurchargeNo());
                            productFacadeDto.setProductName(surchargeInfo.getSurchargeName());
                            productFacadeDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                            productList.add(productFacadeDto);
                        }
                    });

                    // 设置包裹号
                    packageFacadeDto.setPackageNo(packageNo);
                    // 设置包裹数（固定为1）
                    packageFacadeDto.setQuantity(1);
                    // 设置产品列表
                    packageFacadeDto.setProductList(productList);
                    Package enquiryPackage = packageMap.get(packageNo);
                    if (enquiryPackage != null) {
                        // 补全包裹重量
                        packageFacadeDto.setWeight(enquiryPackage.getPackageWeight().getValue());
                        // 补全包裹长、宽、高、体积
                        Dimension dimension = enquiryPackage.getPackageDimension();
                        if (dimension != null) {
                            BigDecimal length = dimension.getLength();
                            BigDecimal width = dimension.getWidth();
                            BigDecimal height = dimension.getHeight();
                            packageFacadeDto.setLength(length);
                            packageFacadeDto.setWidth(width);
                            packageFacadeDto.setHeight(height);
                            if (length != null && width != null && height != null) {
                                packageFacadeDto.setVolume(length.multiply(width).multiply(height));
                            }
                        }
                    }
                    packageSurchargeList.add(packageFacadeDto);
                } else {
                    LOGGER.info("包裹[{}]未命中附加费", packageNo);
                }

            });
        }
        return packageSurchargeList;
    }

    /**
     * 设置产品服务信息（终端切百川仅用于快运C2C，因为当前B2C数据走询价，C2C数据走修改加询价接口传入）
     * 包括从外单获取的增值服务、询价请求参数的增值产品
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDtoForC2C(ExpressOrderModel updatedOrderModel, ExpressOrderModel originalOrderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        // 外单最新的增值服务
        List<Product> updatedProducts = (List<Product>) updatedOrderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(updatedProducts)) {
            String majorProductNo = getMajorProductNo(updatedProducts);
            for (Product product : updatedProducts) {
                // 目前不过滤代收货款（标准产品询价接口、通用询价计费接口）
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(getProductNo(majorProductNo, product.getProductNo()));
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }
        // 补全主产品计费用的产品要素
        complementMainProductAttrs(productFacadeDtos, originalOrderModel);

        return productFacadeDtos;
    }


    /**
     *
     * @param orderModel
     * @param products
     */
    private void reserveSomeSnapshotAttachFees(ExpressOrderModel orderModel, List<Product> products) {
        // 获取快照附加费
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        List<CostInfo> attachFees = orderSnapshot.getFinance().getAttachFees();
        if (CollectionUtils.isEmpty(attachFees)) {
            LOGGER.info("快照附加费为空,无需保留");
            return;
        }

        for (CostInfo attachFee : attachFees) {
            // 整单模式
            if(AttachFeeEnum.CC_CZ_FJF.getCode().equals(attachFee.getCostNo())) {
                Product product = buildProduct(attachFee);
                products.add(product);
                LOGGER.info("整单维度附加费{}已被保留", attachFee.getCostNo());
            }
        }
    }

    /**
     * 构建Product
     *
     * @param attachFee
     * @return
     */
    private Product buildProduct(CostInfo attachFee) {
        Product product = new Product();
        product.setProductNo(attachFee.getCostNo());
        product.setProductName(attachFee.getCostName());
        product.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
        return product;
    }

    /**
     * 设置产品服务信息
     * 港澳正向单询价
     * 港澳逆向单接单异步询价
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toEnquiryProductFacadeDto(ExpressOrderModel orderModel, ExpressOrderModel requestOrderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();

        boolean requestHavePackageService = false;
        // 包装服务从询价入参取
        if (null != requestOrderModel) {
            List<Product> ofcProducts = (List<Product>) requestOrderModel.getProductDelegate().getProducts();
            if (CollectionUtils.isNotEmpty(ofcProducts)) {
                for (Product product : ofcProducts) {
                    // 只处理包装服务
                    if (packageServiceUtil.isPackageService(product)) {
                        if (MapUtils.isNotEmpty(product.getProductAttrs())) {
                            // 直接取当前单的包装服务
                            BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                            productFacadeDto.setProductNo(product.getProductNo());
                            productFacadeDto.setProductType(product.getProductType());
                            productFacadeDto.setParentNo(product.getParentNo());
                            productFacadeDto.setProductAttrs(product.getProductAttrs());
                            productFacadeDtos.add(productFacadeDto);
                            requestHavePackageService = true;
                        }
                    }
                }
            }
        }

        // 最新的增值服务
        List<Product> products = (List<Product>) orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            String majorProductNo = getMajorProductNo(products);
            for (Product product : products) {

                // 包装服务从询价入参取
                if (packageServiceUtil.isPackageService(product) && requestHavePackageService) {
                    continue;
                }
                // 包装服务从当前单取
                // 目前不过滤代收货款（标准产品询价接口、通用询价计费接口）
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(getProductNo(majorProductNo, product.getProductNo()));
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                //港澳：特快重货计费时传入揽派模式
                if ((ProductEnum.TKZH.getCode().equals(product.getProductNo()))) {
                    Map<String, String> productAttrs = productFacadeDto.getProductAttrs();
                    if (MapUtils.isEmpty(productAttrs)) {
                        productAttrs = new HashMap<>();
                    }
                    if (null != orderModel.getShipment()) {
                        //揽收模式
                        if (null != orderModel.getShipment().getPickupType()) {
                            productAttrs.put(EnquiryConstants.PICKUP_MODEL, String.valueOf(orderModel.getShipment().getPickupType().getCode()));
                        }
                        //派送模式
                        if (null != orderModel.getShipment().getDeliveryType()) {
                            productAttrs.put(EnquiryConstants.DELIVERY_MODEL, String.valueOf(orderModel.getShipment().getDeliveryType().getCode()));
                        }
                    }
                    productFacadeDto.setProductAttrs(productAttrs);
                }
                productFacadeDtos.add(productFacadeDto);
            }
        }

        // 补全主产品计费用的产品要素
        complementMainProductAttrs(productFacadeDtos, orderModel);

        return productFacadeDtos;
    }
}