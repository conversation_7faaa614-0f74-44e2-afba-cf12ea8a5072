package cn.jdl.oms.express.domain.infrs.acl.pl.address;

import cn.jdl.oms.express.domain.annotation.Translator;
import org.apache.commons.lang3.StringUtils;

/**
 * @ProjectName：jdl-oms-express-shared-common
 * @Package： cn.jdl.oms.express.domain.infrs.acl.pl.address
 * @ClassName: AddressBasicPrimaryWSFacadeTranslator
 * @Description: B2C 调用青龙基础资料查询起始省市县facade转换器
 * @Author： xinghuanjie
 * @CreateDate 2021-06-29 15:25
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Translator
public class AddressBasicPrimaryWSFacadeTranslator {

    public AddressBasicPrimaryWSFacadeRequest toAddressBasicPrimaryWSFacadeRequest(String stationNo) {
        AddressBasicPrimaryWSFacadeRequest addressBasicPrimaryWSFacadeRequest = new AddressBasicPrimaryWSFacadeRequest();
        if(StringUtils.isNumeric(stationNo)){
            addressBasicPrimaryWSFacadeRequest.setSiteId(Integer.valueOf(stationNo));
        }
        return addressBasicPrimaryWSFacadeRequest;
    }
}
