package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.CostInfoDto;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.PointsInfoDto;
import cn.jdl.oms.express.domain.dto.QuantityInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.address.AddressBasicPrimaryWSFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.FreightGetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.PackageServiceUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.TrustSellerUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.RejectionTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Points;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 快运接单时，逆向单异步询价facade转换器
 */
@Translator
public class FreightCreateEnquiryFacadeTranslator {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(FreightCreateEnquiryFacadeTranslator.class);

    //单据状态 默认传0
    private static final int DEFAULT_ORDER_STATUS = 0;
    // 交付模式
    private static final String DELIVERY_PATTERN = "deliveryPattern";
    // 交付模式 为 1京仓发货
    private static final String DELIVERY_PATTERN_ONE = "1";
    //交付模式 为 2
    private static final String DELIVERY_PATTERN_TWO = "2";
    // 拓展字段
    private static final String SHIPMENT_EXTEND_PROPS = "shipmentExtendProps";
    //eclp
    private static final String ECLP = "eclp";
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;
    /**
     * 事业部信息
     */
    @Resource
    private CustomerFacade customerFacade;
    /**
     * @description 功能描述: 逆向单询价查询青龙基础资料获取起始省市县facade
     * <AUTHOR>
     * @date 2021/7/1 9:33
     * @param
     * @throws
     * @return
     */
    @Resource
    private AddressBasicPrimaryWSFacade addressBasicPrimaryWSFacade;

    /**
     * @description 功能描述: 逆向单询价查询青龙基础资料获取起始省市县facade转换器
     * <AUTHOR>
     * @date 2021/7/1 9:33
     * @param
     * @throws
     * @return
     */
    @Resource
    private AddressBasicPrimaryWSFacadeTranslator addressBasicPrimaryWSFacadeTranslator;

    @Resource
    private EnquiryFacadeTranslator enquiryFacadeTranslator;

    /**
     * 包装服务产品要素处理工具类
     */
    @Resource
    private PackageServiceUtil packageServiceUtil;

    @Resource
    private FreightEnquiryFacadeTranslator freightEnquiryFacadeTranslator;

    /**
     * 计费询价防腐层请求数据转换
     */
    public BillingEnquiryFacadeRequest toBillingEnquiryFacadeRequest(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        if (orderModel.getOrderBusinessIdentity() != null) {
            facadeRequest.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
        }
        facadeRequest.setOrderNo(orderModel.orderNo());
        //青龙业主编码和青龙业主号名称
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(orderModel));
        //关联单号，正向运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        //客户、渠道
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));
        //产品信息  用传过来的订单号查出来有都赋值进去
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel));
        // 总重量、总体积、
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel));
        //发件人信息
        facadeRequest.setConsignorFacadeDto(toReverseConsignorFacadeDto(orderModel));
        //收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel));
        //财务相关信息
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel));
        //设置拓展字段
        facadeRequest.setExtendProps(toExtendProps(orderModel));
        return facadeRequest;
    }

    /**
     * 逆向单原单询价，参数组装
     *
     * @param originOrderModel  原单
     * @param reverseOrderModel 逆向单
     * @param rejectionType     拒收类型
     * @return
     */
    public BillingEnquiryFacadeRequest toReverseOriginOrderEnquiry(ExpressOrderModel originOrderModel, ExpressOrderModel reverseOrderModel, String rejectionType) {
        BillingEnquiryFacadeRequest facadeRequest = toReverseOriginOrderEnquiry(originOrderModel);

        //产品信息组装--如果原单状态是拒收，且不是整单拒收，不用过滤产品信息
        if (OrderStatusEnum.CUSTOMER_REJECTED == originOrderModel.getOrderStatus().getOrderStatus() && !isOrderRejection(rejectionType)) {
            LOGGER.info("原单状态={},rejectionType={},不需要过滤原单产品信息", originOrderModel.getOrderStatus().getOrderStatus(), rejectionType);
        } else {
            LOGGER.info("原单状态={},rejectionType={},需要过滤原单产品信息", originOrderModel.getOrderStatus().getOrderStatus(), rejectionType);
            facadeRequest.setProductFacadeDtoList(toOriginOrderEnquiryProductFacadeDto(originOrderModel));
        }

        //收件人信息 拦截逆向
        if (OrderStatusEnum.CUSTOMER_REJECTED != originOrderModel.getOrderStatus().getOrderStatus()) {
            //如果是拦截逆向，原单询价需要替换收件人地址为逆向单的站点的地址
            //拦截逆向的逆向单一定有站点
            String startStationNo = reverseOrderModel.getShipment().getStartStationNo();
            facadeRequest.getConsigneeFacadeDto().setAddressFacadeDto(getStationAddressFacadeDto(startStationNo));
        }
        return facadeRequest;
    }

    /**
     * 逆向单原单询价，不处理拒收类型
     */
    public BillingEnquiryFacadeRequest toReverseOriginOrderEnquiry(ExpressOrderModel orderModel) {
        LOGGER.info("快运询价-逆向单原单询价");
        BillingEnquiryFacadeRequest facadeRequest = toBillingEnquiryFacadeRequest(orderModel);
        if (expressUccConfigCenter.isFreightNewEnquiryProcessSwitch()) {
            LOGGER.info("快运询价-新流程");
            // 逆向单询价流程中的原单询价，原单体积重量数量取下单数据（信任商家）或复核数据（非信任商家）
            if (!TrustSellerUtil.isTrustWeightVolume(orderModel)) {
                LOGGER.info("非信任商家：取recheckVolume、recheckWeight、actualReceivedQuantity");
                // 体积
                BigDecimal recheckVolume = FreightGetFieldUtils.getRecheckVolumeValue(orderModel);
                if (recheckVolume == null) {
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_FREIGHT_ENQUIRY_INVALID_DATA, "快运订单询价时数据不全:复核体积(recheckVolume)为空:" + orderModel.orderNo());
                    LOGGER.error("复核体积(recheckVolume)为空:" + orderModel.orderNo());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("复核体积(recheckVolume)为空:" + orderModel.orderNo());
                }
                facadeRequest.getCargoFacadeDto().setTotalCargoVolume(recheckVolume);
                // 重量
                BigDecimal recheckWeight = FreightGetFieldUtils.getRecheckWeightValue(orderModel);
                if (recheckWeight == null) {
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_FREIGHT_ENQUIRY_INVALID_DATA, "快运订单询价时数据不全:复核重量(recheckWeight)为空:" + orderModel.orderNo());
                    LOGGER.error("复核重量(recheckWeight)为空:" + orderModel.orderNo());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("复核重量(recheckWeight)为空:" + orderModel.orderNo());
                }
                facadeRequest.getCargoFacadeDto().setTotalCargoWeight(recheckWeight);
                // 数量
                BigDecimal actualReceivedQuantity = FreightGetFieldUtils.getActualReceivedQuantityValue(orderModel);
                if (actualReceivedQuantity == null) {
                    actualReceivedQuantity = FreightGetFieldUtils.getTotalCargoQuantity(orderModel);
                }
                facadeRequest.getCargoFacadeDto().setTotalCargoQuantity(actualReceivedQuantity);
            }
        }
        return facadeRequest;
    }

    /**
     * 是否整单拒收
     *
     * @param rejectionType
     * @return
     */
    private boolean isOrderRejection(String rejectionType) {
        if (StringUtils.isBlank(rejectionType)) {
            //拒收类型为空，默认整单拒收
            return true;
        }
        return RejectionTypeEnum.ORDER.getCode().equals(rejectionType);
    }

    /**
     * @param orderModel
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @throws
     * @description 功能描述: 添加拓展字段
     * <AUTHOR>
     * @date 2021/6/29 12:27
     */
    private Map<String, Object> toExtendProps(ExpressOrderModel orderModel) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(FreightGetFieldUtils.getSettlementType(orderModel)));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS, DEFAULT_ORDER_STATUS);
        // 仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, toDistributionType(orderModel));
        //营销信息，折扣信息
        extendParam.put(EnquiryConstants.DISCOUNT_DETAIL, enquiryFacadeTranslator.extendParamToDiscountDetailList(orderModel));
        //积分信息
        enquiryFacadeTranslator.extendParamToRewardPoints(extendParam, orderModel);
        return extendParam;
    }

    /**
     * @param orderModel
     * @return java.lang.Object
     * @throws
     * @description 功能描述:  仓配类型(0：纯配；1：仓配)
     * 接单字段deliveryPattern若为1京仓发货 则该字段赋值1
     * 若为2纯配则该字段赋值0；
     * 接单字段deliveryPattern若为空且systemSubCaller为eclp则该字段赋值为1，
     * 其他情况全部赋值0
     * <AUTHOR>
     * @date 2021/6/29 18:04
     */
    private Object toDistributionType(ExpressOrderModel orderModel) {
        // 配送的拓展字段 deliveryPattern 从这里获取
        Map<String, String> extendProps = orderModel.getShipment().getExtendProps();
        if (MapUtils.isNotEmpty(extendProps)) {
            String shipmentExtendProps = extendProps.get(SHIPMENT_EXTEND_PROPS);
            if (shipmentExtendProps != null) {
                Map map = JSONUtils.jsonToMap(shipmentExtendProps);
                if (MapUtils.isNotEmpty(map)) {
                    String deliveryPattern = (String) map.get(DELIVERY_PATTERN);
                    if (DELIVERY_PATTERN_ONE.equals(deliveryPattern)) {
                        return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                    } else if (DELIVERY_PATTERN_TWO.equals(deliveryPattern)) {
                        return EnquiryConstants.DISTRIBUTION_PURE;
                    } else if (deliveryPattern == null && ECLP.equals(orderModel.getChannel().getSystemSubCaller())) {
                        return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                    }
                }
            }
        }
        return EnquiryConstants.DISTRIBUTION_PURE;

    }


    /**
     * 拓展信息-结算方式
     */
    private Object extendParamToSettlementType(SettlementTypeEnum settlementTypeEnum) {
        switch (settlementTypeEnum) {
            case CASH_ON_PICK:
                return EnquiryConstants.SETTLEMENT_CASH_ON_PICK;
            case CASH_ON_DELIVERY:
                return EnquiryConstants.SETTLEMENT_CASH_ON_DELIVERY;
            case MONTHLY_PAYMENT:
                return EnquiryConstants.SETTLEMENT_MONTHLY_PAYMENT;
            default:
                return null;
        }
    }

    /**
     * 补全计费结果信息
     */
    public void complementBillingResult(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();

        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getAmount());
        preAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getCurrencyCode());
        financeInfoDto.setPreAmount(preAmount);

        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
        discountAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
        financeInfoDto.setDiscountAmount(discountAmount);

        //计费重量
        financeInfoDto.setBillingWeight(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingVolume());

        //计费类型
        financeInfoDto.setBillingMode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingMode());

        //积分信息
        financeInfoDto.setPointsInfoDto(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPointsInfoDto());

        //费用明细
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : billingEnquiryFacadeResponse.getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
            FinanceDetailInfoDto detailInfoDto = new FinanceDetailInfoDto();
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount().getAmount());
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount().getCurrencyCode());
            detailInfoDto.setPreAmount(detailPreAmount);
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount().getAmount());
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount().getCurrencyCode());
            detailInfoDto.setDiscountAmount(detailDiscountAmount);
            detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            detailInfoDto.setCostName(detailFacadeDto.getCostName());
            detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            detailInfoDto.setProductName(detailFacadeDto.getProductName());
            detailInfoDto.setRemark(detailFacadeDto.getRemark());
            //折扣明细
            if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
                for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
                    discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());
                    Money money = new Money();
                    money.setAmount(discountInfoFacadeDto.getDiscountedAmount().getAmount());
                    money.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
                    discountInfoDto.setDiscountedAmount(money);
                    discountInfoDtos.add(discountInfoDto);
                }
                detailInfoDto.setDiscountInfoDtos(discountInfoDtos);
            }
            financeDetailInfoDtoList.add(detailInfoDto);
        }
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
        // 收款机构
        financeInfoDto.setCollectionOrgNo(billingEnquiryFacadeResponse.getFinanceFacadeDto().getCollectionOrgNo());
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

    private BillingEnquiryFacadeRequest.CustomerFacadeDto toCustomerFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = new BillingEnquiryFacadeRequest.CustomerFacadeDto();
        Customer customer = orderModel.getCustomer();
        customerFacadeDto.setAccountNo(customer.getAccountNo());
        customerFacadeDto.setAccount2No(customer.getAccountNo2());
        return customerFacadeDto;
    }

    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel orderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<Product> products = (List<Product>) orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            String majorProductNo = getMajorProductNo(products);
            for (Product product : products) {
                if (AddOnProductEnum.getCodCode().contains(product.getProductNo())
                        || AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode().equals(product.getProductNo())) {
                    continue;
                }
                // 包装服务特殊处理
                if (packageServiceUtil.isPackageService(product)) {
                    // 从询价入参取，或者查运单的包装耗材信息，查询不到再过滤
                    packageServiceUtil.handlePackageService(orderModel.orderNo(), getWaybillCode(orderModel), product, productFacadeDtos);
                    continue;
                }
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(getProductNo(majorProductNo, product.getProductNo()));
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }

        // 补全主产品计费用的产品要素
        freightEnquiryFacadeTranslator.complementMainProductAttrs(productFacadeDtos, orderModel);

        return productFacadeDtos;
    }

    /**
     * 逆向单原单询价，产品信息转换，原单根据业务诉求，配置具体的产品信息
     *
     * @param orderModel
     * @return
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toOriginOrderEnquiryProductFacadeDto(ExpressOrderModel orderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<Product> products = (List<Product>) orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            String majorProductNo = getMajorProductNo(products);
            for (Product product : products) {
                if ((product.getProductType() != null && ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(product.getProductType()))
                        || expressUccConfigCenter.isFreightReverseOriginOrderEnquiryAddOnProductWhite(product.getProductNo())) {
                    // 包装服务特殊处理
                    if (packageServiceUtil.isPackageService(product)) {
                        // 从询价入参取，或者查运单的包装耗材信息，查询不到再过滤
                        packageServiceUtil.handlePackageService(orderModel.orderNo(), getWaybillCode(orderModel), product, productFacadeDtos);
                        continue;
                    }
                    BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                    productFacadeDto.setProductNo(getProductNo(majorProductNo, product.getProductNo()));
                    productFacadeDto.setProductType(product.getProductType());
                    productFacadeDto.setParentNo(product.getParentNo());
                    productFacadeDto.setProductAttrs(product.getProductAttrs());
                    productFacadeDtos.add(productFacadeDto);
                }
            }
        }

        // 补全主产品计费用的产品要素
        freightEnquiryFacadeTranslator.complementMainProductAttrs(productFacadeDtos, orderModel);

        return productFacadeDtos;
    }

    /**
     * 客户、渠道
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ChannelFacadeDto toChannelFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ChannelFacadeDto channelFacadeDto = new BillingEnquiryFacadeRequest.ChannelFacadeDto();
        Channel channel = orderModel.getChannel();
        channelFacadeDto.setChannelNo(channel.getChannelNo());
        return channelFacadeDto;
    }


    /**
     * 收件人信息 传过来的orderModel
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();

        Consignee consignee = orderModel.getConsignee();

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        Address address = consignee.getAddress();
        if (address != null) {
            //收件人省
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setProvinceNo(address.getProvinceNo());
            // 收件人市
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCityNo(address.getCityNo());
            //收件人县
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
            addressFacadeDto.setCountyNo(address.getCountyNo());

        }
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 收件人信息 传过来的orderModel
     *
     * @param consigneeAddress
     * @return
     */
    private BillingEnquiryFacadeRequest.AddressFacadeDto toConsigneeAddressFacadeDto(Address consigneeAddress) {

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        if (consigneeAddress != null) {
            //收件人省
            addressFacadeDto.setProvinceNoGis(consigneeAddress.getProvinceNoGis());
            addressFacadeDto.setProvinceNo(consigneeAddress.getProvinceNo());
            // 收件人市
            addressFacadeDto.setCityNoGis(consigneeAddress.getCityNoGis());
            addressFacadeDto.setCityNo(consigneeAddress.getCityNo());
            //收件人县
            addressFacadeDto.setCountyNoGis(consigneeAddress.getCountyNoGis());
            addressFacadeDto.setCountyNo(consigneeAddress.getCountyNo());
            addressFacadeDto.setTownNoGis(consigneeAddress.getTownNoGis());
            addressFacadeDto.setTownNo(consigneeAddress.getTownNo());
        }
        return addressFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     *
     * @param orderModel 当前订单对象model取值
     * @return
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        if (!expressUccConfigCenter.isFreightNewEnquiryProcessSwitch()) {
            LOGGER.info("快运询价-旧流程");
            // 根据是否信任商家分别处理
            if (TrustSellerUtil.isTrustWeightVolume(orderModel)) {
                LOGGER.info("信任商家：取下单时的重量和体积");
                // 信任商家：取下单时的重量和体积
                // 货品信息里的计算总体积
                cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
                // 货品信息里的计算总重量
                cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
                // 计费数量
                cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
            } else {
                // 非信任商家：取OFC传的询价重量、询价体积
                // 询价体积
                if (orderModel.getEnquiry() != null
                        && orderModel.getEnquiry().getEnquiryVolume() != null
                        && orderModel.getEnquiry().getEnquiryVolume().getValue() != null) {
                    LOGGER.info("非信任商家：取询价时传的体积");
                    cargoFacadeDto.setTotalCargoVolume(orderModel.getEnquiry().getEnquiryVolume().getValue());
                } else if (orderModel.getRecheckVolume() != null
                        && orderModel.getRecheckVolume().getValue() != null) {
                    LOGGER.info("非信任商家：取复核体积");
                    cargoFacadeDto.setTotalCargoVolume(orderModel.getRecheckVolume().getValue());
                } else {
                    LOGGER.info("非信任商家：取下单体积");
                    cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
                }
                // 询价重量
                if (orderModel.getEnquiry() != null
                        && orderModel.getEnquiry().getEnquiryWeight() != null
                        && orderModel.getEnquiry().getEnquiryWeight().getValue() != null) {
                    LOGGER.info("非信任商家：取询价时传的重量");
                    cargoFacadeDto.setTotalCargoWeight(orderModel.getEnquiry().getEnquiryWeight().getValue());
                } else if (orderModel.getRecheckWeight() != null
                        && orderModel.getRecheckWeight().getValue() != null) {
                    LOGGER.info("非信任商家：取复核重量");
                    cargoFacadeDto.setTotalCargoWeight(orderModel.getRecheckWeight().getValue());
                } else {
                    LOGGER.info("非信任商家：取下单重量");
                    // 货品信息里的计算总重量
                    cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
                }
                // 计费数量
                if (orderModel.getEnquiry() != null
                        && orderModel.getEnquiry().getEnquiryQuantity() != null
                        && orderModel.getEnquiry().getEnquiryQuantity().getValue() != null) {
                    LOGGER.info("非信任商家：取询价时传的数量");
                    cargoFacadeDto.setTotalCargoQuantity(orderModel.getEnquiry().getEnquiryQuantity().getValue());
                } else if (orderModel.getFulfillment() != null
                        && orderModel.getFulfillment().getActualReceivedQuantity() != null
                        && orderModel.getFulfillment().getActualReceivedQuantity().getValue() != null) {
                    LOGGER.info("非信任商家：取实际揽收数量");
                    cargoFacadeDto.setTotalCargoQuantity(orderModel.getFulfillment().getActualReceivedQuantity().getValue());
                } else {
                    LOGGER.info("非信任商家：取下单数量");
                    cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
                }
            }
        } else {
            LOGGER.info("快运询价-新流程");
            // 货品信息里的总体积
            cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
            // 货品信息里的总重量
            cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
            // 货品信息里的总数量
            cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
        }

        return cargoFacadeDto;
    }

    /**
     * 财务相关信息
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        //快运是用接单时间
        financeFacadeDto.setEnquireTime(orderModel.getOperateTime());
        //结算方式
        financeFacadeDto.setSettlementType(orderModel.getFinance().getSettlementType());

        // 抵扣信息 只有逆向
        // 下了运费保，抵扣编码是OFC传过来的，所以抵扣信息在当前订单的fiance信息
        if (null != orderModel.getFinance().getDeductionDelegate()) {
            financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) orderModel.getFinance().getDeductionDelegate().getDeductions()));
        }
        // 积分信息
        if (orderModel.getFinance() != null
                && orderModel.getFinance().getPoints() != null) {
            financeFacadeDto.setPointsInfoDto(toPointsInfoDto(orderModel.getFinance().getPoints()));
        }
        //附加费信息
        if (orderModel.getFinance() != null && CollectionUtils.isNotEmpty(orderModel.getFinance().getAttachFees())) {
            List<CostInfoDto> dtoList = new ArrayList<>();
            orderModel.getFinance().getAttachFees().forEach(costInfo -> {
                if (costInfo != null) {
                    dtoList.add(toCostInfoDto(costInfo));
                }
            });
            financeFacadeDto.setAttachFees(dtoList);
        }
        return financeFacadeDto;
    }

    /**
     * 附加费对象转换
     *
     * @param costInfo
     * @return
     */
    private CostInfoDto toCostInfoDto(CostInfo costInfo) {
        CostInfoDto dto = new CostInfoDto();
        //费用项编码
        dto.setCostNo(costInfo.getCostNo());
        //费用项名称
        dto.setCostName(costInfo.getCostName());
        //收费方	0：向商家收 1：向寄件方收
        dto.setChargingSource(costInfo.getChargingSource());
        //月结费用填写（结算编码）前端卡控条件必填
        dto.setSettlementAccountNo(costInfo.getSettlementAccountNo());
        dto.setExtendProps(costInfo.getExtendProps());
        return dto;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductions
     * @return
     */
    private List<DeductionInfoDto> toDeductionInfoDtos(List<Deduction> deductions) {
        if (CollectionUtils.isEmpty(deductions)) {
            return null;
        }
        List<DeductionInfoDto> deductionInfoDtos = new ArrayList<>(deductions.size());
        deductions.forEach(deduction -> {
            if (deduction != null) {
                DeductionInfoDto dto = new DeductionInfoDto();
                //抵扣编码
                dto.setDeductionNo(deduction.getDeductionNo());
                // 抵扣金额
                dto.setDeductionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(deduction.getDeductionAmount()));
                //扩展信息
                dto.setExtendProps(deduction.getExtendProps());
                deductionInfoDtos.add(dto);
            }
        });
        return deductionInfoDtos;
    }

    /**
     * 转换积分信息
     */
    private PointsInfoDto toPointsInfoDto(Points points) {
        if (points == null) {
            return null;
        }
        PointsInfoDto pointsInfoDto = new PointsInfoDto();
        if (points.getRedeemPointsAmount() != null) {
            MoneyInfoDto redeemPointsAmount = new MoneyInfoDto();
            redeemPointsAmount.setAmount(points.getRedeemPointsAmount().getAmount());
            redeemPointsAmount.setCurrencyCode(points.getRedeemPointsAmount().getCurrency());
            pointsInfoDto.setRedeemPointsAmount(redeemPointsAmount);
        }
        if (points.getRedeemPointsQuantity() != null) {
            QuantityInfoDto redeemPointsQuantity = new QuantityInfoDto();
            redeemPointsQuantity.setUnit(points.getRedeemPointsQuantity().getUnit());
            redeemPointsQuantity.setValue(points.getRedeemPointsQuantity().getValue());
            pointsInfoDto.setRedeemPointsQuantity(redeemPointsQuantity);
        }
        return pointsInfoDto;
    }

    /**
     * 逆向单询价，根据终端揽收时传的站点ID，调青龙基础资料 获取cityId countryId provinceId
     * 如果是改址单，不需要查青龙基础资料，因此不用此方法
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toReverseConsignorFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();

        // 逆向单询价和寄付现结一样 根据终端揽收时传的站点ID，调青龙基础资料 获取cityId countryId provinceId
        // 输入揽收站点id
        String stationNo = getReturnEndStationNo(orderModel);
        consignorFacadeDto.setAddressFacadeDto(getStationAddressFacadeDto(stationNo));
        return consignorFacadeDto;
    }

    /**
     * 根据站点获取地址信息
     *
     * @param stationNo 站点编码
     * @return 询价防腐层地址信息
     */
    private BillingEnquiryFacadeRequest.AddressFacadeDto getStationAddressFacadeDto(String stationNo){

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();

        AddressBasicPrimaryWSFacadeRequest addressBasicPrimaryWSFacadeRequest = addressBasicPrimaryWSFacadeTranslator.toAddressBasicPrimaryWSFacadeRequest(stationNo);

        AddressBasicPrimaryWSFacadeResponse addressBasicPrimaryWSFacadeResponse = addressBasicPrimaryWSFacade.getBaseSiteBySiteId(Integer.valueOf(addressBasicPrimaryWSFacadeRequest.getSiteId()));
        //起始省
        addressFacadeDto.setProvinceNoGis(addressBasicPrimaryWSFacadeResponse.getProvinceId());
        addressFacadeDto.setProvinceNo(addressBasicPrimaryWSFacadeResponse.getProvinceId());
        // 起始市
        addressFacadeDto.setCityNoGis(addressBasicPrimaryWSFacadeResponse.getCityId());
        addressFacadeDto.setCityNo(addressBasicPrimaryWSFacadeResponse.getCityId());
        //起始县
        addressFacadeDto.setCountyNoGis(addressBasicPrimaryWSFacadeResponse.getCountryId());
        addressFacadeDto.setCountyNo(addressBasicPrimaryWSFacadeResponse.getCountryId());

        return addressFacadeDto;
    }

    /**
     * 逆向单新单询价时，起始省市县赋值逻辑为：
     * 1、逆向单询价取逆向新单传入的揽收站点ID，调青龙基础资料获取省市县id
     * 2、若未传入站点ID则取该逆向单原单的末端站点endstationno调青龙基础资料查询获取省市县id
     *
     * @param orderModel
     * @return
     */
    private String getReturnEndStationNo(ExpressOrderModel orderModel) {
        //逆向单揽收站点非必填，因此需要判断是否为空
        if (StringUtils.isNotBlank(orderModel.getShipment().getStartStationNo())) {
            //若逆向新单传入了揽收站点ID，则用传入的揽收站点ID，调青龙基础资料获取省市县id；
            return orderModel.getShipment().getStartStationNo();
        } else {
            //若未传入揽收站点ID则取该逆向单原单的末端站点endstationno调青龙基础资料查询获取省市县id；
            return orderModel.getOrderSnapshot().getShipment().getEndStationNo();
        }
    }

    /**
     * 获取未删除的主产品
     */
    private String getMajorProductNo(List<Product> products) {
        if (CollectionUtils.isNotEmpty(products)) {
            for (Product product : products) {
                if (isMajorProduct(product) && notDelete(product)) {
                    return product.getProductNo();
                }
            }
        }
        LOGGER.error("没有未删除的主产品");
        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("没有未删除的主产品");
    }

    /**
     * 是否主产品
     */
    private boolean isMajorProduct(Product product) {
        return ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(product.getProductType());
    }

    /**
     * 是否未删除
     */
    private boolean notDelete(Product product) {
        return (product.getOperateType() == null
                || !OperateTypeEnum.DELETE.getCode().equals(product.getOperateType().getCode()));
    }

    /**
     * 必要时将保价产品替换成特快重货-保价 fr-a-0016
     * 2023-05-31 与快运产品王传宇、林恒波确认，主产品是fr-m-0004（特快重货）+fr-m-0002（特惠重货）时，调计费标准产品询价接口fr-a-0002（普通保价），需要转化成fr-a-0016（特快重货保价）
     * 背景：业务及运营系统，目前保价只识别了fr-a-0002 （普通保价），下单也只会下一个码，但计费是根据两个保价码分别计费的。短期方案，订单中心调计费根据主产品去做码的转换，长期方案，需要王传宇和林恒波 推动业务及运营系统按标准的两个码执行
     */
    private String getProductNo(String majorProductNo, String productNo) {
        // 主产品不是特快重货或特惠重货，不需要替换
        if (!ProductEnum.TKZH.getCode().equals(majorProductNo)
                && !ProductEnum.THZH.getCode().equals(majorProductNo)) {
            return productNo;
        }
        // 增值产品不是保价 fr-a-0002 不需要替换
        if (!AddOnProductEnum.INSURED_VALUE_TOB.getCode().equals(productNo)) {
            return productNo;
        }
        LOGGER.info("询价时增值产品保价替换：主产品是{},增值产品保价是{},替换成fr-a-0016", majorProductNo, productNo);
        return AddOnProductEnum.FR_A_0016.getCode();
    }

    /**
     * 获取运单号
     * @param orderModel 逆向单或者原单
     */
    private String getWaybillCode(ExpressOrderModel orderModel) {
        // 当前单
        if (orderModel.getRefOrderInfoDelegate() != null
                && !orderModel.getRefOrderInfoDelegate().isEmpty()
                && StringUtils.isNotEmpty(orderModel.getRefOrderInfoDelegate().getWaybillNo())) {
            return orderModel.getRefOrderInfoDelegate().getWaybillNo();
        }

        // 不从原单获取原因是，逆向单运单号与原单不同

        LOGGER.error("获取运单号失败");
        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("获取运单号失败");
    }
}
