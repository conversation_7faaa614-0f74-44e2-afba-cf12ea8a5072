package cn.jdl.oms.express.domain.infrs.acl.pl.order;

import cn.jdl.oms.express.domain.dto.record.ModifyRecordDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AgreementFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AttachmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessIdentityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessSolutionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CargoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ChannelFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsigneeFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsignorFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FulfillmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.GoodsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.InterceptFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PromotionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.RefOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ReturnInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ShipmentFacade;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.SceneDeliveryEnum;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cn.jdl.oms.express.domain.spec.dict.OrderSignEnum.SCENE_DELIVERY;

/**
 * @ClassName GetOrderFacadeResponse
 * @Description 防腐层订单详情
 * <AUTHOR>
 * @Date 2021/3/21 4:36 下午
 * @ModifyDate 2021/3/21 4:36 下午
 * @Version 1.0
 */
@Data
public class GetOrderFacadeResponse {

    /**
     * 业务身份信息
     */
    private BusinessIdentityFacade businessIdentity;
    /**
     * 交易订单号
     */
    private String orderNo;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 交易单据类型(500-纯配、504-逆向、505-改址)
     */
    private String orderType;
    /**
     * 交易单据子类型(C2C、O2O(特瞬送同城))
     */
    private String orderSubType;
    /**
     * 订单主状态
     */
    private Integer orderStatus;
    /**
     * 订单状态
     */
    private Integer orderStatusCustom;

    /**
     * 扩展状态
     */
    private String extendStatus;

    /**
     * 扩展状态描述
     */
    private String extendStatusDesc;

    /**
     * 订单取消状态
     */
    private Integer cancelStatus;
    /**
     * 业务订单号
     */
    private String customOrderNo;
    /**
     * 下单人类型
     */
    private Integer initiatorType;
    /**
     * 下单人唯一标识
     */
    private String operator;
    /**
     * oms接单时间
     */
    private Date operateTime;
    /**
     * 订单备注
     */
    private String remark;
    /**
     * 隐藏标识
     */
    private String hiddenMark;
    /**
     * 扩展信息
     */
    private Map<String, String> extendProps;

    /**
     * 交易客户信息
     */
    private CustomerFacade customer;
    /**
     * 渠道信息
     */
    private ChannelFacade channel;
    /**
     * 交易产品/增值产品信息
     */
    private List<ProductFacade> products;
    /**
     * 发货信息
     */
    private ConsignorFacade consignor;
    /**
     * 收货信息
     */
    private ConsigneeFacade consignee;
    /**
     * 货品信息
     */
    private List<CargoFacade> cargos;
    /**
     * 商品信息
     */
    private List<GoodsFacade> goodsList;
    /**
     * 配送信息
     */
    private ShipmentFacade shipment;
    /**
     * 财务信息
     */
    private FinanceFacade finance;
    /**
     * 营销信息
     */
    private PromotionFacade promotion;
    /**
     * 交易关联单
     */
    private List<RefOrderFacade> refOrders;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 删除标识
     */
    private Byte yn;
    /**
     * 同步来源 1-百川 2-非百川
     */
    private Integer syncSource;

    /**
     * 拦截类型
     */
    private Integer interceptType;

    /**
     * 核算体积
     */
    private Volume recheckVolume;

    /**
     * 核算重量
     */
    private Weight recheckWeight;

    /**
     * 解决方案
     */
    private BusinessSolutionFacade businessSolutionFacade;

    /**
     * 订单标识信息
     */
    private Map<String, String> orderSign;

    /**
     * 协议信息
     */
    private List<AgreementFacade> agreementFacades;

    /**
     * 关联单扩展单据类型
     */
    private Map<String, String> extendRefOrder;

    /**
     * 退货信息
     */
    private ReturnInfoFacade returnInfoFacade;

    /**
     * 拦截信息
     */
    private InterceptFacade interceptFacade;

    /**
     * 改址状态
     */
    private Integer readdressStatus;

    /**
     * 订单数据环境标识
     */
    private String environment;

    /**
     * 履约信息
     */
    private FulfillmentFacade fulfillmentFacade;

    /**
     * 跨境报关信息
     */
    private CustomsFacade customsFacade;

    /**
     * 附件列表
     */
    private List<AttachmentFacade> attachmentFacades;

    /**
     * 订单总净重
     */
    private Weight orderNetWeight;

    /**
     * 订单总毛重
     */
    private Weight orderWeight;

    /**
     * 订单总体积
     */
    private Volume orderVolume;

    /**
     * 父单号
     */
    private String parentOrderNo;

    /**
     * 操作记录列表
     */
    private List<ModifyRecordDto> modifyRecordDtos;

    /**
     * 弃货状态
     */
    private Integer discardStatus;

    /**
     * 判断是否港澳订单
     * @return
     */
    @JSONField(serialize = false)
    public boolean isHKMO() {
        // 获取跨境报关信息，始发目的流向存在与否，判断是否是港澳订单。若始发流向目的流向都是大陆，不是港澳订单。
        return Optional.ofNullable(this.customsFacade)
                .filter(customsVo -> null != customsVo.getStartFlowDirection()
                        && null != customsVo.getEndFlowDirection()
                        && !(AdministrativeRegionEnum.CN == customsVo.getStartFlowDirection() && AdministrativeRegionEnum.CN == customsVo.getEndFlowDirection()))
                .isPresent();
    }

    /**
     * 判断是否交管12123
     * @return
     */
    @JSONField(serialize = false)
    public boolean isJG12123() {
        return MapUtils.isNotEmpty(this.orderSign)
                && orderSign.containsKey(SCENE_DELIVERY.getCode())
                && SceneDeliveryEnum.JG_12123.getCode().equals(this.orderSign.get(SCENE_DELIVERY.getCode()));
    }
}
