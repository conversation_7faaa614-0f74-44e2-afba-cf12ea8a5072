package cn.jdl.oms.express.domain.infrs.acl.pl.address;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import com.jd.addresstranslation.api.base.BaseResponse;
import com.jd.addresstranslation.api.base.ExternalAddressRequest;
import com.jd.addresstranslation.api.base.ExternalJDAddress;
import com.jd.addresstranslation.api.base.GBAddressLevelsResponse;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 功能：地址RPC返回转换器
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/3/24 14:41
 */
@Translator
public class JdAddressRpcTranslator {

    /**
     * 功能：组装京标地址解析前入参
     *
     * @param jdAddressFacadeRequest 1
     * @return com.jd.addresstranslation.api.base.ExternalAddressRequest
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/24 21:11
     */
    public ExternalAddressRequest toExternalAddressRequest(JdAddressFacadeRequest jdAddressFacadeRequest) {
        ExternalAddressRequest request = new ExternalAddressRequest();
        request.setProvinceCode(jdAddressFacadeRequest.getProvinceCode());
        request.setProvinceName(jdAddressFacadeRequest.getProvinceName());
        request.setCityCode(jdAddressFacadeRequest.getCityCode());
        request.setCityName(jdAddressFacadeRequest.getCityName());
        request.setDistrictCode(jdAddressFacadeRequest.getDistrictCode());
        request.setDistrictName(jdAddressFacadeRequest.getDistrictName());
        request.setTownCode(jdAddressFacadeRequest.getTownCode());
        request.setTownName(jdAddressFacadeRequest.getTownName());
        request.setDetailAddress(jdAddressFacadeRequest.getDetailAddress());
        //request.setFullAddress(jdAddressFacadeRequest.getFullAddress());
        request.setSource(jdAddressFacadeRequest.getSource());
        return request;
    }

    /**
     * 功能：Rpc返回结果转换成facade
     *
     * @param externalJDAddress 1
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.address.JdAddressFacadeResponse
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/24 21:32
     */
    public JdAddressFacadeResponse toJdAddressFacadeResponse(ExternalJDAddress externalJDAddress) {
        JdAddressFacadeResponse jdAddressFacadeResponse = new JdAddressFacadeResponse();
        JdAddressFacadeResponse.Address address = new JdAddressFacadeResponse.Address();
        if (null != externalJDAddress.getProvinceCode()) {
            address.setProvinceNo(String.valueOf(externalJDAddress.getProvinceCode()));
            address.setProvinceName(externalJDAddress.getProvinceName());
        }
        if (null != externalJDAddress.getCityCode()) {
            address.setCityNo(String.valueOf(externalJDAddress.getCityCode()));
            address.setCityName(externalJDAddress.getCityName());
        }
        if (null != externalJDAddress.getLatitude()) {
            address.setLatitude(String.valueOf(externalJDAddress.getLatitude()));
        }
        if (null != externalJDAddress.getLongitude()) {
            address.setLongitude(String.valueOf(externalJDAddress.getLongitude()));
        }
        if (externalJDAddress.getDistrictCode() != null) {
            address.setCountyNo(String.valueOf(externalJDAddress.getDistrictCode()));
            address.setCountyName(externalJDAddress.getDistrictName());
        }
        if (externalJDAddress.getTownCode() != null) {
            address.setTownNo(String.valueOf(externalJDAddress.getTownCode()));
            address.setTownName(externalJDAddress.getTownName());
        }
        //地址嵌套等级
        if (externalJDAddress.getConflictLevel() != null) {
            address.setConflictLevel(externalJDAddress.getConflictLevel());
        }
        address.setAddress(externalJDAddress.getDetailAddress());
        //GIS精准度
        address.setPreciseGis(externalJDAddress.getPrecise());
        //国家/地区码
        address.setRegionNo(externalJDAddress.getCountryOrRegionCode());
        jdAddressFacadeResponse.setExternalJDAddress(address);
        return jdAddressFacadeResponse;
    }

    public ExternalAddressRequest[] toBatchExternalAddressRequest(List<JdAddressFacadeRequest> jdAddressFacadeRequestList) {
        ExternalAddressRequest[] externalAddressRequests = new ExternalAddressRequest[jdAddressFacadeRequestList.size()];
        for (int i = 0; i < jdAddressFacadeRequestList.size(); i++) {
            externalAddressRequests[i] = toExternalAddressRequest(jdAddressFacadeRequestList.get(i));
        }
        return externalAddressRequests;
    }

    public Map<Integer, JdAddressFacadeResponse> toBatchJdAddressFacadeResponse(Map<Integer, BaseResponse<ExternalJDAddress>> externalJDAddressMap) {
        if (externalJDAddressMap == null) {
            return null;
        }
        Map<Integer, JdAddressFacadeResponse> responseMap = new HashMap<>();
        for (Integer key : externalJDAddressMap.keySet()) {
            responseMap.put(key, toJdAddressFacadeResponse(externalJDAddressMap.get(key).getResult()));
        }
        return responseMap;
    }

    /**
     * 国标地址信息防腐层对象转换
     *
     * @param gbAddressLevelsResponse
     * @return
     */
    public AddressInfoDto toAddressInfoDto(GBAddressLevelsResponse gbAddressLevelsResponse) {
        if (gbAddressLevelsResponse == null) {
            return null;
        }
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        addressInfoDto.setProvinceNo(gbAddressLevelsResponse.getProvinceCode());
        addressInfoDto.setProvinceName(gbAddressLevelsResponse.getProvinceName());
        addressInfoDto.setCityNo(gbAddressLevelsResponse.getCityCode());
        addressInfoDto.setCityName(gbAddressLevelsResponse.getCityName());
        addressInfoDto.setCountyNo(gbAddressLevelsResponse.getDistrictCode());
        addressInfoDto.setCountyName(gbAddressLevelsResponse.getDistrictName());
        addressInfoDto.setTownNo(gbAddressLevelsResponse.getTownCode());
        addressInfoDto.setTownName(gbAddressLevelsResponse.getTownName());
        return addressInfoDto;
    }
}
