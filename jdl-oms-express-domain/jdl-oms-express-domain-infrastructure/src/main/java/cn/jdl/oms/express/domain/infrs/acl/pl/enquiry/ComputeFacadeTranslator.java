package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.dto.EnquiryInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.ProductInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.FreightGetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.FreightReaddressUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.PackageServiceUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.TrustSellerUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.VolumeTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightTypeEnum;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.ShipmentConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.TransWayEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import com.google.common.collect.Lists;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 通用计费询价防腐层转换器
 */
@Translator
public class ComputeFacadeTranslator {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ComputeFacadeTranslator.class);

    /**
     * 支付超时时间buffer
     */
    @Value("${payOutTimeBuffer}")
    private Integer payOutTimeBuffer;

    /**
     * 包装服务产品要素处理工具类
     */
    @Resource
    private PackageServiceUtil packageServiceUtil;

    /**
     * Ucc
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 运输方式专线包仓
     */
    private Byte ZXBC = 17;

    /**
     * 运力平台询价时，转换为计费接口参数
     */
    public ComputeFacadeRequest toTmsEnquiryRequest(ExpressOrderContext context) {
        // 订单模型
        ExpressOrderModel orderModel = context.getOrderModel();
        ComputeFacadeRequest facadeRequest = new ComputeFacadeRequest();

        // 订单号
        facadeRequest.setOrderNo(orderModel.orderNo());
        // 操作人
        facadeRequest.setOperator(orderModel.getOperator());
        // 客户信息
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(orderModel));
        // 运单号
        ComputeFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new ComputeFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getCustomOrderNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        // 渠道信息
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));
        // 产品信息 TODO liujiangwai1 目前不需要传增值服务
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel));
        // 货品信息：总重量 总数量 取货品信息，总体积取仓位信息
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel));
        // 发件人信息
        facadeRequest.setConsignorFacadeDto(toCreateConsignorFacadeDto(orderModel));
        // 收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel));
        // 财务相关信息 todo 后续需要确认结算方式
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel));
        Byte transportMode = null;
        // 运输方式
        if (orderModel.isTmsZx()) {
            transportMode = ZXBC;
        }
        facadeRequest.setTransportMode(transportMode);
        // 运输类型 取配送信息
        String transWay = orderModel.getShipment().getExtendProps(ShipmentConstants.TRANS_WAY);
        TransWayEnum transWayEnum = TransWayEnum.ofCode(transWay);
        if (null != transWayEnum) {
            facadeRequest.setTransWay(transWayEnum.getCcjfCode());
        }
        // 配送方式
        facadeRequest.setShipmentFacadeDto(toShipmentFacadeDto(orderModel));

        return facadeRequest;
    }

    /**
     * OFC同步询价时，转换为计费接口参数
     * 正向单复重复量方
     * 逆向单复重复量方
     */
    public ComputeFacadeRequest toOfcEnquiryRequest(ExpressOrderContext expressOrderContext) {
        ComputeFacadeRequest facadeRequest = new ComputeFacadeRequest();
        // OFC调询价接口的请求参数
        ExpressOrderModel ofcOrderModel = expressOrderContext.getOrderModel();
        // 外单数据更新后的订单
        ExpressOrderModel updatedOrderModel = ofcOrderModel.getOrderSnapshot();
        // 原单
        ExpressOrderModel originalOrderModel = updatedOrderModel.getOrderSnapshot();

        // 订单号：取OFC传值
        facadeRequest.setOrderNo(ofcOrderModel.orderNo());

        // 操作人：取OFC传值
        facadeRequest.setOperator(ofcOrderModel.getOperator());

        // 业务信息：原单
        facadeRequest.setBusinessIdentityFacadeDto(toBusinessIdentityFacadeDto(originalOrderModel));

        // 客户信息：原单
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(originalOrderModel));

        // 运单号：取外单更新值
        ComputeFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new ComputeFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(updatedOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        // 渠道信息：取OFC传值
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(ofcOrderModel));

        // 产品信息：取OFC传值、外单更新值
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(ofcOrderModel, updatedOrderModel));

        // 货品信息：总重量、总体积按是否信任商家取值，总数量取外单更新值
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(ofcOrderModel, updatedOrderModel, originalOrderModel));

        // 发件人信息：二级地址市取OFC传值，其余取外单更新值
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(ofcOrderModel, updatedOrderModel));

        // 收件人信息：取外单更新值
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(updatedOrderModel));

        // 财务相关信息：询价时间取当前时间、结算方式取外单更新值
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(updatedOrderModel));

        return facadeRequest;
    }

    /**
     * 逆向单接单异步询价时，转换为计费接口参数
     * @param orderModel 逆向单
     */
    public ComputeFacadeRequest toReverseCreateEnquiry(ExpressOrderModel orderModel) {
        ComputeFacadeRequest facadeRequest = new ComputeFacadeRequest();

        // 订单号
        facadeRequest.setOrderNo(orderModel.orderNo());

        // 操作人
        facadeRequest.setOperator(orderModel.getOperator());

        // 业务信息
        facadeRequest.setBusinessIdentityFacadeDto(toBusinessIdentityFacadeDto(orderModel));

        // 客户信息
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(orderModel));

        // 运单号
        ComputeFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new ComputeFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        // 渠道信息
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));

        // 产品信息
        facadeRequest.setProductFacadeDtoList(toCreateProductFacadeDto(orderModel));

        // 货品信息
        facadeRequest.setCargoFacadeDto(toCreateCargoFacadeDto(orderModel));

        // 发件人信息
        facadeRequest.setConsignorFacadeDto(toCreateConsignorFacadeDto(orderModel));

        // 收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel));

        // 财务相关信息：询价时间取当前时间、结算方式取外单更新值
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel));

        return facadeRequest;
    }

    /**
     * 逆向单原单询价
     * @param orderModel 逆向单的原单
     */
    public ComputeFacadeRequest toReverseOriginOrderEnquiry(ExpressOrderModel orderModel) {
        ComputeFacadeRequest facadeRequest = toReverseCreateEnquiry(orderModel);
        LOGGER.info("快运询价-新流程");
        // 逆向单询价流程中的原单询价，原单体积重量数量取下单数据（信任商家）或复核数据（非信任商家）
        if (!TrustSellerUtil.isTrustWeightVolume(orderModel)) {
            LOGGER.info("非信任商家：取recheckVolume、recheckWeight、actualReceivedQuantity");
            // 体积
            BigDecimal recheckVolume = FreightGetFieldUtils.getRecheckVolumeValue(orderModel);
            if (recheckVolume == null) {
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_FREIGHT_ENQUIRY_INVALID_DATA, "快运订单询价时数据不全:复核体积(recheckVolume)为空:" + orderModel.orderNo());
                LOGGER.error("复核体积(recheckVolume)为空:" + orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("复核体积(recheckVolume)为空:" + orderModel.orderNo());
            }
            facadeRequest.getCargoFacadeDto().setTotalCargoVolume(recheckVolume);
            // 重量
            BigDecimal recheckWeight = FreightGetFieldUtils.getRecheckWeightValue(orderModel);
            if (recheckWeight == null) {
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_FREIGHT_ENQUIRY_INVALID_DATA, "快运订单询价时数据不全:复核重量(recheckWeight)为空:" + orderModel.orderNo());
                LOGGER.error("复核重量(recheckWeight)为空:" + orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("复核重量(recheckWeight)为空:" + orderModel.orderNo());
            }
            facadeRequest.getCargoFacadeDto().setTotalCargoWeight(recheckWeight);
            // 数量
            BigDecimal actualReceivedQuantity = FreightGetFieldUtils.getActualReceivedQuantityValue(orderModel);
            if (actualReceivedQuantity == null) {
                actualReceivedQuantity = FreightGetFieldUtils.getTotalCargoQuantity(orderModel);
            }
            facadeRequest.getCargoFacadeDto().setTotalCargoQuantity(actualReceivedQuantity);
        }
        return facadeRequest;
    }

    /**
     * 修改时，转换为计费接口参数
     * 揽收后改址，异步询价；
     * 揽收后改增值服务、揽收后改址，同步询价
     */
    public ComputeFacadeRequest toModifyEnquiryRequest(ExpressOrderContext expressOrderContext) {
        ComputeFacadeRequest facadeRequest = new ComputeFacadeRequest();
        // 当前单：异步询价是持久化数据；同步询价是修改请求
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        // 原单
        ExpressOrderModel originalOrderModel = orderModel.getOrderSnapshot();

        // 订单号
        facadeRequest.setOrderNo(orderModel.orderNo());

        // 操作人
        facadeRequest.setOperator(orderModel.getOperator());

        // 业务信息：取原单
        facadeRequest.setBusinessIdentityFacadeDto(toBusinessIdentityFacadeDto(originalOrderModel));

        // 客户信息：取原单
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(originalOrderModel));

        // 运单号：取原单（逆向单不可修改，所以不会发生本该取逆向单运单，结果取原单的运单）
        ComputeFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new ComputeFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(originalOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        // 渠道信息
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));

        // 产品信息
        facadeRequest.setProductFacadeDtoList(toModifyProductFacadeDto(orderModel));

        // 货品信息
        facadeRequest.setCargoFacadeDto(toModifyCargoFacadeDto(orderModel, originalOrderModel));

        // 发件人信息
        facadeRequest.setConsignorFacadeDto(toModifyConsignorFacadeDto(orderModel));

        // 收件人信息：当前单没有取原单
        facadeRequest.setConsigneeFacadeDto(toModifyConsigneeFacadeDto(orderModel));

        // 财务相关信息：询价时间取当前时间、结算方式取原单
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(originalOrderModel));

        return facadeRequest;
    }

    /**
     * 客户信息
     */
    private ComputeFacadeRequest.CustomerFacadeDto toCustomerFacadeDto(ExpressOrderModel updatedOrderModel) {
        ComputeFacadeRequest.CustomerFacadeDto customerFacadeDto = new ComputeFacadeRequest.CustomerFacadeDto();
        Customer customer = updatedOrderModel.getCustomer();
        customerFacadeDto.setAccountNo(customer.getAccountNo());
        customerFacadeDto.setAccountNo2(customer.getAccountNo2());
        return customerFacadeDto;
    }

    /**
     * 渠道信息
     */
    private ComputeFacadeRequest.ChannelFacadeDto toChannelFacadeDto(ExpressOrderModel ofcOrderModel) {
        ComputeFacadeRequest.ChannelFacadeDto channelFacadeDto = new ComputeFacadeRequest.ChannelFacadeDto();
        Channel channel = ofcOrderModel.getChannel();
        channelFacadeDto.setChannelNo(channel.getChannelNo());
        return channelFacadeDto;
    }

    /**
     * 产品服务信息
     */
    private List<ComputeFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel orderModel) {
        List<ComputeFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<Product> products = (List<Product>) orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isEmpty(products)) {
            return productFacadeDtos;
        }

        for (Product product : products) {
            ComputeFacadeRequest.ProductFacadeDto productFacadeDto = new ComputeFacadeRequest.ProductFacadeDto();
            productFacadeDto.setProductNo(product.getProductNo());
            productFacadeDto.setProductType(product.getProductType());
            productFacadeDto.setParentNo(product.getParentNo());
            productFacadeDto.setProductAttrs(product.getProductAttrs());
            productFacadeDtos.add(productFacadeDto);
        }

        return productFacadeDtos;
    }

    /**
     * 产品服务信息
     * OFC询价：取OFC传值、外单更新值
     */
    private List<ComputeFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel ofcOrderModel, ExpressOrderModel updatedOrderModel) {
        List<ComputeFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        // 外单最新的增值服务
        List<Product> updatedProducts = (List<Product>) updatedOrderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(updatedProducts)) {
            for (Product product : updatedProducts) {
                // 包装服务从询价入参取
                if (packageServiceUtil.isPackageService(product)) {
                    continue;
                }
                // 目前不过滤代收货款（标准产品询价接口、通用询价计费接口）
                ComputeFacadeRequest.ProductFacadeDto productFacadeDto = new ComputeFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }

        // 包装服务从询价入参取
        List<Product> ofcProducts = (List<Product>) ofcOrderModel.getProductDelegate().getProducts();
        if(CollectionUtils.isNotEmpty(ofcProducts)){
            for (Product product : ofcProducts) {
                // 包装服务特殊处理
                if (packageServiceUtil.isPackageService(product)) {
                    // 从询价入参取，或者查运单的包装耗材信息，查询不到再过滤
                    packageServiceUtil.computeHandlePackageService(updatedOrderModel.orderNo(), getWaybillCode(updatedOrderModel), product, productFacadeDtos);
                }
            }
        }

        return productFacadeDtos;
    }

    /**
     * 产品服务信息
     * 逆向单接单询价：取传入的当前单
     */
    private List<ComputeFacadeRequest.ProductFacadeDto> toCreateProductFacadeDto(ExpressOrderModel orderModel) {
        List<ComputeFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<Product> products = (List<Product>) orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            for (Product product : products) {
                // 包装服务特殊处理
                if (packageServiceUtil.isPackageService(product)) {
                    // 从询价入参取，或者查运单的包装耗材信息，查询不到再过滤
                    packageServiceUtil.computeHandlePackageService(orderModel.orderNo(), getWaybillCode(orderModel), product, productFacadeDtos);
                    continue;
                }
                ComputeFacadeRequest.ProductFacadeDto productFacadeDto = new ComputeFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }
        return productFacadeDtos;
    }

    /**
     * 产品服务信息
     * 新单没有取原单，过滤删除的产品
     */
    private List<ComputeFacadeRequest.ProductFacadeDto> toModifyProductFacadeDto(ExpressOrderModel expressOrderModel) {
        List<ComputeFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<Product> products = (List<Product>) expressOrderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isEmpty(products)
                && expressOrderModel.getOrderSnapshot() != null
                && expressOrderModel.getOrderSnapshot().getProductDelegate() != null) {
            products = (List<Product>) expressOrderModel.getOrderSnapshot().getProductDelegate().getProducts();
        }
        if (CollectionUtils.isNotEmpty(products)) {
            for (Product product : products) {
                // 过滤删除的产品
                if (product.getOperateType() != null
                        && OperateTypeEnum.DELETE.getCode().equals(product.getOperateType().getCode())) {
                    continue;
                }

                // 包装服务特殊处理
                if (packageServiceUtil.isPackageService(product)) {
                    // 从询价入参取，或者查运单的包装耗材信息，查询不到再过滤
                    packageServiceUtil.computeHandlePackageService(getModifyOrderNo(expressOrderModel), getModifyWaybillCode(expressOrderModel), product, productFacadeDtos);
                    continue;
                }

                // 产品转为询价参数
                ComputeFacadeRequest.ProductFacadeDto productFacadeDto = new ComputeFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }
        return productFacadeDtos;
    }

    /**
     * 货品信息
     */
    private ComputeFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel orderModel) {
        ComputeFacadeRequest.CargoFacadeDto cargoFacadeDto = new ComputeFacadeRequest.CargoFacadeDto();
        // 取下单时的重量和体积
        // 货品信息 计算总重量
        cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
        // 货品信息 计算总数量
        cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
        // 体积取配送信息的仓位信息
        Optional.ofNullable(orderModel.getShipment())
                .map(shipment -> shipment.getExtendProps(ShipmentConstants.VEHICLE_POSITIONS))
                .ifPresent(vehiclePositions -> {
                    // 获取仓位体积
                    BigDecimal volume = new BigDecimal(vehiclePositions);
                    cargoFacadeDto.setTotalCargoVolume(volume);
                });

        return cargoFacadeDto;
    }

    /**
     * 货品信息
     */
    private ComputeFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel ofcOrderModel, ExpressOrderModel updatedOrderModel, ExpressOrderModel originalOrderModel) {
        ComputeFacadeRequest.CargoFacadeDto cargoFacadeDto = new ComputeFacadeRequest.CargoFacadeDto();
        if (!expressUccConfigCenter.isFreightNewEnquiryProcessSwitch()) {
            LOGGER.info("快运询价-旧流程");
            // 根据是否信任商家分别处理
            if (TrustSellerUtil.isTrustWeightVolume(originalOrderModel)) {
                LOGGER.info("信任商家：取下单时的重量和体积");
                // 信任商家：取下单时的重量和体积
                // 货品信息里的计算总体积
                cargoFacadeDto.setTotalCargoVolume(originalOrderModel.getCargoDelegate().totalCargoVolume());
                // 货品信息里的计算总重量
                cargoFacadeDto.setTotalCargoWeight(originalOrderModel.getCargoDelegate().totalCargoWeight());
            } else {
                LOGGER.info("非信任商家：取OFC传的询价重量、询价体积");
                // 非信任商家：取OFC传的询价重量、询价体积
                // 询价体积
                if (ofcOrderModel.getEnquiry().getEnquiryVolume() != null
                        && ofcOrderModel.getEnquiry().getEnquiryVolume().getValue() != null){
                    Volume cm3Volume = toCM3(ofcOrderModel.getEnquiry().getEnquiryVolume());
                    cargoFacadeDto.setTotalCargoVolume(cm3Volume.getValue());
                } else {
                    LOGGER.error("非信任商家并且询价体积为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价体积为空");
                }
                // 询价重量
                if (ofcOrderModel.getEnquiry().getEnquiryWeight() != null
                        && ofcOrderModel.getEnquiry().getEnquiryWeight().getValue() != null){
                    Weight kgWeight = toKG(ofcOrderModel.getEnquiry().getEnquiryWeight());
                    cargoFacadeDto.setTotalCargoWeight(kgWeight.getValue());
                }else {
                    LOGGER.error("非信任商家并且询价重量为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价重量为空");
                }
            }

            // 计费数量(从外单里查出来的货品数量)
            cargoFacadeDto.setTotalCargoQuantity(updatedOrderModel.getCargoDelegate().totalCargoQuantity());

            return cargoFacadeDto;
        } else {
            LOGGER.info("快运询价-新流程");
            // 根据是否信任商家分别处理
            if (TrustSellerUtil.isTrustWeightVolume(originalOrderModel)) {
                LOGGER.info("信任商家：取下单时的重量和体积");
                // 信任商家：取下单时的重量和体积
                // 货品信息里的计算总体积
                cargoFacadeDto.setTotalCargoVolume(originalOrderModel.getCargoDelegate().totalCargoVolume());
                // 货品信息里的计算总重量
                cargoFacadeDto.setTotalCargoWeight(originalOrderModel.getCargoDelegate().totalCargoWeight());
                // 货品信息里的计算总数量
                cargoFacadeDto.setTotalCargoQuantity(originalOrderModel.getCargoDelegate().totalCargoQuantity());
            } else {
                LOGGER.info("非信任商家：取OFC传的询价重量、询价体积");
                // 非信任商家：取OFC传的询价重量、询价体积
                // 询价体积
                if (ofcOrderModel.getEnquiry().getEnquiryVolume() != null
                        && ofcOrderModel.getEnquiry().getEnquiryVolume().getValue() != null){
                    Volume cm3Volume = toCM3(ofcOrderModel.getEnquiry().getEnquiryVolume());
                    cargoFacadeDto.setTotalCargoVolume(cm3Volume.getValue());
                } else {
                    LOGGER.error("非信任商家并且询价体积为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价体积为空");
                }
                // 询价重量
                if (ofcOrderModel.getEnquiry().getEnquiryWeight() != null
                        && ofcOrderModel.getEnquiry().getEnquiryWeight().getValue() != null){
                    Weight kgWeight = toKG(ofcOrderModel.getEnquiry().getEnquiryWeight());
                    cargoFacadeDto.setTotalCargoWeight(kgWeight.getValue());
                }else {
                    LOGGER.error("非信任商家并且询价重量为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价重量为空");
                }
                // 计费数量
                if(ofcOrderModel.getEnquiry().getEnquiryQuantity() != null
                        && ofcOrderModel.getEnquiry().getEnquiryQuantity().getValue() != null){
                    // 计费数量(包裹) 询价接口传了就从入参取 终端接入
                    cargoFacadeDto.setTotalCargoQuantity(ofcOrderModel.getEnquiry().getEnquiryQuantity().getValue());
                } else {
                    // 计费数量(从外单里查出来的货品数量)
                    cargoFacadeDto.setTotalCargoQuantity(updatedOrderModel.getCargoDelegate().totalCargoQuantity());
                }
            }

            return cargoFacadeDto;
        }
    }

    /**
     * 货品信息：逆向单接单询价
     */
    private ComputeFacadeRequest.CargoFacadeDto toCreateCargoFacadeDto(ExpressOrderModel orderModel) {
        ComputeFacadeRequest.CargoFacadeDto cargoFacadeDto = new ComputeFacadeRequest.CargoFacadeDto();
        cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
        cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
        cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
        return cargoFacadeDto;
    }

    /**
     * 货品信息：修改场景
     * 按是否信任商家处理
     */
    private ComputeFacadeRequest.CargoFacadeDto toModifyCargoFacadeDto(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        ComputeFacadeRequest.CargoFacadeDto cargoFacadeDto = new ComputeFacadeRequest.CargoFacadeDto();
        if (TrustSellerUtil.isTrustWeightVolume(orderSnapshot)) {
            LOGGER.info("信任商家：取下单时的重量和体积");
            // 信任商家，直接新单没有取原单
            setTotalCargoVolume(cargoFacadeDto, orderModel, orderSnapshot);
            setTotalCargoWeight(cargoFacadeDto, orderModel, orderSnapshot);
            setTotalCargoQuantity(cargoFacadeDto, orderModel, orderSnapshot);
        } else {
            if (!expressUccConfigCenter.isFreightNewEnquiryProcessSwitch()) {
                LOGGER.info("快运询价-旧流程");
                LOGGER.info("非信任商家：取OFC传的询价重量、询价体积");
                // 非信任商家，取复重体积重量，没有再新单没有取原单
                if (hasBillingVolumeValue(orderSnapshot)) {
                    cargoFacadeDto.setTotalCargoVolume(orderSnapshot.getFinance().getBillingVolume().getValue());
                } else {
                    setTotalCargoVolume(cargoFacadeDto, orderModel, orderSnapshot);
                }
                if (hasBillingWeightValue(orderSnapshot)) {
                    cargoFacadeDto.setTotalCargoWeight(orderSnapshot.getFinance().getBillingWeight().getValue());
                } else {
                    setTotalCargoWeight(cargoFacadeDto, orderModel, orderSnapshot);
                }
                setTotalCargoQuantity(cargoFacadeDto, orderModel, orderSnapshot);
            } else {
                LOGGER.info("快运询价-新流程");
                LOGGER.info("非信任商家：取recheckVolume、recheckWeight、actualReceivedQuantity");
                // 体积
                BigDecimal recheckVolume = FreightGetFieldUtils.getRecheckVolumeValue(orderSnapshot);
                if (recheckVolume == null) {
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_FREIGHT_ENQUIRY_INVALID_DATA, "快运订单询价时数据不全:复核体积(recheckVolume)为空:" + orderModel.orderNo());
                    LOGGER.error("复核体积(recheckVolume)为空:" + orderModel.orderNo());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("复核体积(recheckVolume)为空:" + orderModel.orderNo());
                }
                cargoFacadeDto.setTotalCargoVolume(recheckVolume);

                // 重量
                BigDecimal recheckWeight = FreightGetFieldUtils.getRecheckWeightValue(orderSnapshot);
                if (recheckWeight == null) {
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_FREIGHT_ENQUIRY_INVALID_DATA, "快运订单询价时数据不全:复核重量(recheckWeight)为空:" + orderModel.orderNo());
                    LOGGER.error("复核重量(recheckWeight)为空:" + orderModel.orderNo());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("复核重量(recheckWeight)为空:" + orderModel.orderNo());
                }
                cargoFacadeDto.setTotalCargoWeight(recheckWeight);

                // 数量
                BigDecimal actualReceivedQuantity = FreightGetFieldUtils.getActualReceivedQuantityValue(orderSnapshot);
                if (actualReceivedQuantity == null) {
                    actualReceivedQuantity = FreightGetFieldUtils.getTotalCargoQuantity(orderModel);
                    if (actualReceivedQuantity == null) {
                        actualReceivedQuantity = FreightGetFieldUtils.getTotalCargoQuantity(orderSnapshot);
                    }
                }
                cargoFacadeDto.setTotalCargoQuantity(actualReceivedQuantity);
            }
        }
        return cargoFacadeDto;
    }

    /**
     * 发货人信息
     */
    private ComputeFacadeRequest.ConsignorFacadeDto toConsignorFacadeDto(ExpressOrderModel ofcOrderModel, ExpressOrderModel updatedOrderModel) {
        ComputeFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new ComputeFacadeRequest.ConsignorFacadeDto();
        Consignor consignor = updatedOrderModel.getConsignor();
        Address address = consignor.getAddress();
        ComputeFacadeRequest.AddressFacadeDto addressFacadeDto = new ComputeFacadeRequest.AddressFacadeDto();
        if (address != null) {
            // 起始省(外单查出来的)
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            // 发件人二级地址 ofc传递过来的
            addressFacadeDto.setCityNoGis(ofcOrderModel.getEnquiry().getEnquiryStartCityNo());
            // 起始县(外单查出来的)
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 发货人信息：逆向单接单询价
     */
    private ComputeFacadeRequest.ConsignorFacadeDto toCreateConsignorFacadeDto(ExpressOrderModel orderModel) {
        ComputeFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new ComputeFacadeRequest.ConsignorFacadeDto();
        Consignor consignor = orderModel.getConsignor();
        Address address = consignor.getAddress();
        ComputeFacadeRequest.AddressFacadeDto addressFacadeDto = new ComputeFacadeRequest.AddressFacadeDto();
        if (address != null) {
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 发货人信息：修改询价
     * 新单没有取原单
     */
    private ComputeFacadeRequest.ConsignorFacadeDto toModifyConsignorFacadeDto(ExpressOrderModel orderModel) {
        ComputeFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new ComputeFacadeRequest.ConsignorFacadeDto();
        Consignor consignor;
        if (orderModel.getConsignor() != null && orderModel.getConsignor().getAddress() != null) {
            consignor = orderModel.getConsignor();
        } else {
            consignor = orderModel.getOrderSnapshot().getConsignor();
        }
        Address address = consignor.getAddress();
        ComputeFacadeRequest.AddressFacadeDto addressFacadeDto = new ComputeFacadeRequest.AddressFacadeDto();
        if (address != null) {
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 收货人信息
     */
    private ComputeFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(ExpressOrderModel orderModel) {
        ComputeFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new ComputeFacadeRequest.ConsigneeFacadeDto();

        Consignee consignee = orderModel.getConsignee();

        ComputeFacadeRequest.AddressFacadeDto addressFacadeDto = new ComputeFacadeRequest.AddressFacadeDto();
        Address address = consignee.getAddress();
        if (address != null) {
            // 收件人省
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            // 收件人市
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            // 收件人县
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 收件人信息：修改场景
     * 新单没有取原单
     */
    private ComputeFacadeRequest.ConsigneeFacadeDto toModifyConsigneeFacadeDto(ExpressOrderModel orderModel) {
        ComputeFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new ComputeFacadeRequest.ConsigneeFacadeDto();

        Consignee consignee;
        if (orderModel.getConsignee() != null && orderModel.getConsignee().getAddress() != null) {
            consignee = orderModel.getConsignee();
        } else {
            consignee = orderModel.getOrderSnapshot().getConsignee();
        }

        ComputeFacadeRequest.AddressFacadeDto addressFacadeDto = new ComputeFacadeRequest.AddressFacadeDto();
        Address address = consignee.getAddress();
        if (address != null) {
            // 收件人省
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            // 收件人市
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            // 收件人县
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 财务信息
     */
    private ComputeFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(ExpressOrderModel orderModel) {
        ComputeFacadeRequest.FinanceFacadeDto financeFacadeDto = new ComputeFacadeRequest.FinanceFacadeDto();
        financeFacadeDto.setEnquireTime(DateUtils.now());
        financeFacadeDto.setSettlementType(orderModel.getFinance().getSettlementType());

        // 通用计费接口无折扣
        return financeFacadeDto;
    }

    /**
     * 补全财务信息
     */
    public void complementFinanceInfo(ExpressOrderContext expressOrderContext, ComputeFacadeResponse computeFacadeResponse) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        expressOrderModelCreator.setFinanceInfo(this.buildFinanceInfoDto(expressOrderContext, computeFacadeResponse));
        expressOrderContext.getOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

    /**
     * 根据计费接口结果构建FinanceInfoDto
     */
    public FinanceInfoDto buildFinanceInfoDto(ExpressOrderContext expressOrderContext, ComputeFacadeResponse computeFacadeResponse) {
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(computeFacadeResponse.getFinanceFacadeDto().getPreAmount().getAmount());
        preAmount.setCurrencyCode(computeFacadeResponse.getFinanceFacadeDto().getPreAmount().getCurrencyCode());
        financeInfoDto.setPreAmount(preAmount);

        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(computeFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
        discountAmount.setCurrencyCode(computeFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
        financeInfoDto.setDiscountAmount(discountAmount);

        //计费重量
        financeInfoDto.setBillingWeight(computeFacadeResponse.getFinanceFacadeDto().getBillingWeight());

        //计费体积
        financeInfoDto.setBillingVolume(computeFacadeResponse.getFinanceFacadeDto().getBillingVolume());

        //费用明细
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (ComputeFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : computeFacadeResponse.getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount().getAmount());
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount().getCurrencyCode());
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount().getAmount());
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount().getCurrencyCode());

            FinanceDetailInfoDto detailInfoDto = new FinanceDetailInfoDto();
            detailInfoDto.setPreAmount(detailPreAmount);
            detailInfoDto.setDiscountAmount(detailDiscountAmount);
            detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            detailInfoDto.setCostName(detailFacadeDto.getCostName());
            detailInfoDto.setRemark(detailFacadeDto.getRemark());

            financeDetailInfoDtoList.add(detailInfoDto);
        }
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);

        // 快运揽收后修改收货人信息，寄付现结需要设置支付超时时间
        SettlementTypeEnum settlementType = FreightGetFieldUtils.getSettlementType(expressOrderContext);
        if (FreightReaddressUtil.isReaddress(expressOrderContext.getOrderModel())
                && SettlementTypeEnum.CASH_ON_PICK == settlementType) {
            Integer freightPayOutTimeBuffer = expressUccConfigCenter.getFreightPayOutTimeBuffer();
            Date payDeadline = new Date(System.currentTimeMillis() + 60000L * freightPayOutTimeBuffer);
            LOGGER.info("快运揽收后修改收货人信息且寄付现结，支付时间默认为当前时间加{}分钟，payDeadline={}", freightPayOutTimeBuffer, payDeadline);
            financeInfoDto.setPayDeadline(payDeadline);
        } else if (expressOrderContext.getOrderModel().isTmsZx()) {
            Date payDeadline = new Date(System.currentTimeMillis() + 60000L * payOutTimeBuffer);
            financeInfoDto.setPayDeadline(payDeadline);
            LOGGER.info("运力平台-专线包仓，支付时间默认为当前时间加{}分钟，payDeadline={}", payOutTimeBuffer, payDeadline);
        }

        return financeInfoDto;
    }

    /**
     * 补全询价信息
     */
    public void complementEnquiry(ExpressOrderContext expressOrderContext, ComputeFacadeRequest computeFacadeRequest) {
        EnquiryInfoDto enquiryInfoDto = new EnquiryInfoDto();
        // 询价体积
        Optional.ofNullable(computeFacadeRequest.getFinanceFacadeDto().getBillingVolume()).ifPresent(enquiryVolume->{
            VolumeInfoDto volumeInfo = new VolumeInfoDto();
            volumeInfo.setUnit(enquiryVolume.getUnit());
            volumeInfo.setValue(enquiryVolume.getValue());
            enquiryInfoDto.setEnquiryVolume(volumeInfo);
        });
        // 询价重量
        Optional.ofNullable(computeFacadeRequest.getFinanceFacadeDto().getBillingWeight()).ifPresent(enquiryWeight->{
            WeightInfoDto weightInfoDto = new WeightInfoDto();
            weightInfoDto.setUnit(enquiryWeight.getUnit());
            weightInfoDto.setValue(enquiryWeight.getValue());
            enquiryInfoDto.setEnquiryWeight(weightInfoDto);
        });
        // 询价始发省ID
        enquiryInfoDto.setEnquiryStartProvinceNo(computeFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().getProvinceNo());
        // 询价始发城市ID
        enquiryInfoDto.setEnquiryStartCityNo(computeFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().getCityNoGis());
        // 询价始发县ID
        enquiryInfoDto.setEnquiryStartCountyNo(computeFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().getCountyNoGis());
        // 询价目的省ID
        enquiryInfoDto.setEnquiryEndProvinceNo(computeFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().getProvinceNo());
        // 询价目的城市ID
        enquiryInfoDto.setEnquiryEndCityNo(computeFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().getCityNoGis());
        // 询价目的县ID
        enquiryInfoDto.setEnquiryEndCountyNo(computeFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().getCountyNoGis());
        // 询价产品：这里面有可能有包装耗材
        if (CollectionUtils.isNotEmpty(computeFacadeRequest.getProductFacadeDtoList()) ){
            //产品集合
            List<ProductInfoDto> productInfoDtos = Lists.newArrayListWithCapacity(computeFacadeRequest.getProductFacadeDtoList().size());
            //遍历当前费用明细
            computeFacadeRequest.getProductFacadeDtoList().forEach(productFacadeDto -> {
                //产品信息
                ProductInfoDto productInfoDto = new ProductInfoDto();
                //产品编码
                productInfoDto.setProductNo(productFacadeDto.getProductNo());
                //产品类型
                productInfoDto.setProductType(productFacadeDto.getProductType());
                //产品关系(所属主产品编码)
                productInfoDto.setParentNo(productFacadeDto.getParentNo());
                //产品要素属性
                productInfoDto.setProductAttrs(productFacadeDto.getProductAttrs());
                //扩展字段说明
                productInfoDto.setExtendProps(productFacadeDto.getExtendProps());
                //产品信息
                productInfoDtos.add(productInfoDto);
            });
            enquiryInfoDto.setProductInfos(productInfoDtos);
        }
        // 询价时间
        enquiryInfoDto.setEnquireTime(computeFacadeRequest.getFinanceFacadeDto().getEnquireTime());

        // 补全询价对象
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        expressOrderModelCreator.setEnquiryInfo(enquiryInfoDto);
        expressOrderContext.getOrderModel().complement().complementEnquiry(this, expressOrderModelCreator);
    }

    /**
     * 获取单位为cm3的体积
     */
    public Volume toCM3(Volume volume) {
        BigDecimal value = volume.getValue();
        if (VolumeTypeEnum.DM3.getCode().equals(volume.getUnit().getCode())) {
            value = value.multiply(new BigDecimal("1000"));
        }
        if (VolumeTypeEnum.M3.getCode().equals(volume.getUnit().getCode())) {
            value = value.multiply(new BigDecimal("1000000"));
        }
        Volume result = new Volume();
        result.setValue(value);
        result.setUnit(VolumeTypeEnum.CM3);
        return result;
    }

    /**
     * 获取单位为kg的重量
     */
    public static Weight toKG(Weight weight) {
        BigDecimal value = weight.getValue();
        if (WeightTypeEnum.T.getCode().equals(weight.getUnit().getCode())) {
            value = value.multiply(new BigDecimal("1000"));
        }
        if (WeightTypeEnum.G.getCode().equals(weight.getUnit().getCode())) {
            value = value.divide(new BigDecimal("1000"), 4, RoundingMode.HALF_UP);
        }
        Weight result = new Weight();
        result.setValue(value);
        result.setUnit(WeightTypeEnum.KG);
        return result;
    }

    /**
     * 判断计费体积有没有值
     */
    private boolean hasBillingVolumeValue(ExpressOrderModel orderModel) {
        return orderModel != null
                && orderModel.getFinance() != null
                && orderModel.getFinance().getBillingVolume() != null
                && orderModel.getFinance().getBillingVolume().getValue() != null;
    }

    /**
     * 判断计费重量有没有值
     */
    private boolean hasBillingWeightValue(ExpressOrderModel orderModel) {
        return orderModel != null
                && orderModel.getFinance() != null
                && orderModel.getFinance().getBillingWeight() != null
                && orderModel.getFinance().getBillingWeight().getValue() != null;
    }

    /**
     * 修改场景设置总体积
     * 新单没有取原单
     */
    private void setTotalCargoVolume(ComputeFacadeRequest.CargoFacadeDto cargoFacadeDto, ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        if (orderModel.getCargoDelegate() != null
                && orderModel.getCargoDelegate().totalCargoVolume() != null) {
            cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
        } else if (orderSnapshot != null
                && orderSnapshot.getCargoDelegate() != null
                && orderSnapshot.getCargoDelegate().totalCargoVolume() != null){
            cargoFacadeDto.setTotalCargoVolume(orderModel.getOrderSnapshot().getCargoDelegate().totalCargoVolume());
        }
    }

    /**
     * 修改场景设置总重量
     * 新单没有取原单
     */
    private void setTotalCargoWeight(ComputeFacadeRequest.CargoFacadeDto cargoFacadeDto, ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        if (orderModel.getCargoDelegate() != null
                && orderModel.getCargoDelegate().totalCargoWeight() != null) {
            cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());
        } else if (orderSnapshot != null
                && orderSnapshot.getCargoDelegate() != null
                && orderSnapshot.getCargoDelegate().totalCargoWeight() != null){
            cargoFacadeDto.setTotalCargoWeight(orderModel.getOrderSnapshot().getCargoDelegate().totalCargoWeight());
        }
    }

    /**
     * 修改场景设置总数量
     * 新单没有取原单
     */
    private void setTotalCargoQuantity(ComputeFacadeRequest.CargoFacadeDto cargoFacadeDto, ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        if (orderModel.getCargoDelegate() != null
                && orderModel.getCargoDelegate().totalCargoQuantity() != null) {
            cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
        } else if (orderSnapshot != null
                && orderSnapshot.getCargoDelegate() != null
                && orderSnapshot.getCargoDelegate().totalCargoQuantity() != null){
            cargoFacadeDto.setTotalCargoQuantity(orderModel.getOrderSnapshot().getCargoDelegate().totalCargoQuantity());
        }
    }

    /**
     * 修改场景获取订单号
     */
    private String getModifyOrderNo(ExpressOrderModel orderModel) {
        // 当前单
        if (StringUtils.isNotEmpty(orderModel.orderNo())) {
            return orderModel.orderNo();
        }

        // 原单
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (StringUtils.isNotEmpty(orderSnapshot.orderNo())) {
            return orderSnapshot.orderNo();
        }

        LOGGER.error("获取订单号失败");
        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("获取订单号失败");
    }

    /**
     * 获取运单号
     */
    private String getWaybillCode(ExpressOrderModel orderModel) {
        // 当前单
        if (orderModel.getRefOrderInfoDelegate() != null
                && !orderModel.getRefOrderInfoDelegate().isEmpty()
                && StringUtils.isNotEmpty(orderModel.getRefOrderInfoDelegate().getWaybillNo())) {
            return orderModel.getRefOrderInfoDelegate().getWaybillNo();
        }

        // 不从原单获取原因是，逆向单运单号与原单不同

        LOGGER.error("获取运单号失败");
        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("获取运单号失败");
    }

    /**
     * 修改场景获取运单号
     */
    private String getModifyWaybillCode(ExpressOrderModel orderModel) {
        // 当前单
        if (orderModel.getRefOrderInfoDelegate() != null
                && !orderModel.getRefOrderInfoDelegate().isEmpty()
                && StringUtils.isNotEmpty(orderModel.getRefOrderInfoDelegate().getWaybillNo())) {
            return orderModel.getRefOrderInfoDelegate().getWaybillNo();
        }

        // 原单
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot.getRefOrderInfoDelegate() != null
                && !orderSnapshot.getRefOrderInfoDelegate().isEmpty()
                && StringUtils.isNotEmpty(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo())) {
            return orderSnapshot.getRefOrderInfoDelegate().getWaybillNo();
        }

        LOGGER.error("获取运单号失败");
        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("获取运单号失败");
    }

    /**
     * 业务信息
     */
    private ComputeFacadeRequest.BusinessIdentityFacadeDto toBusinessIdentityFacadeDto(ExpressOrderModel orderModel) {
        ComputeFacadeRequest.BusinessIdentityFacadeDto businessIdentityFacadeDto = new ComputeFacadeRequest.BusinessIdentityFacadeDto();
        businessIdentityFacadeDto.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
        businessIdentityFacadeDto.setBusinessType(orderModel.getOrderBusinessIdentity().getBusinessType());
        return businessIdentityFacadeDto;
    }

    /**
     * 快运整车直达增值服务费
     */
    public ComputeFacadeRequest toAddOnProductFeeRequest(ExpressOrderModel orderModel) {
        ComputeFacadeRequest facadeRequest = toFTLDirectCommonRequest(orderModel);
        // 增值服务需要计费，传入全部产品
        facadeRequest.setProductFacadeDtoList(toCreateProductFacadeDto(orderModel));
        // 特殊的值，福佑增值服务
        facadeRequest.setTransactionType(ComputeRpcTranslator.TRANSACTION_TYPE_FTL_ADD_ON_PRODUCT);
        return facadeRequest;
    }

    /**
     * 快运整车直达车型费
     */
    public ComputeFacadeRequest toCarFeeRequest(ExpressOrderModel orderModel) {
        ComputeFacadeRequest facadeRequest = toFTLDirectCommonRequest(orderModel);
        // 车型费不能传增值服务
        facadeRequest.setProductFacadeDtoList(new ArrayList<>(0));
        // 车型费报错时不直接抛出错误
        facadeRequest.setSwallowUnexpectedResult(true);
        return facadeRequest;
    }

    /**
     * 快运整车直达公共请求
     */
    private ComputeFacadeRequest toFTLDirectCommonRequest(ExpressOrderModel orderModel) {
        ComputeFacadeRequest facadeRequest = new ComputeFacadeRequest();

        // 订单号
        facadeRequest.setOrderNo(orderModel.orderNo());

        // 操作人
        facadeRequest.setOperator(orderModel.getOperator());

        // 业务信息
        facadeRequest.setBusinessIdentityFacadeDto(toBusinessIdentityFacadeDto(orderModel));

        // 客户信息
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(orderModel));

        // 运单号
        ComputeFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new ComputeFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);

        // 渠道信息
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));

        // 产品信息：不在此方法赋值

        // 货品信息
        facadeRequest.setCargoFacadeDto(toCreateCargoFacadeDto(orderModel));

        // 发件人信息
        facadeRequest.setConsignorFacadeDto(toCreateConsignorFacadeDto(orderModel));

        // 收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel));

        // 财务相关信息：询价时间取当前时间、结算方式取外单更新值
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel));

        // 运输方式 6，整车
        facadeRequest.setTransportMode(ComputeRpcTranslator.TRANSPORT_MODE_FTL);

        // 派送信息
        facadeRequest.setShipmentFacadeDto(toShipmentFacadeDto(orderModel));

        return facadeRequest;
    }

    /**
     * 派送信息
     */
    private ComputeFacadeRequest.ShipmentFacadeDto toShipmentFacadeDto(ExpressOrderModel orderModel) {
        ComputeFacadeRequest.ShipmentFacadeDto shipmentFacadeDto = new ComputeFacadeRequest.ShipmentFacadeDto();
        Shipment shipment = orderModel.getShipment();
        if (shipment != null) {
            String vehicleType = shipment.getVehicleType();
            if (StringUtils.isNotBlank(vehicleType)) {
                shipmentFacadeDto.setVehicleType(vehicleType);
            }
        }
        return shipmentFacadeDto;
    }

    /**
     * 补齐询价状态
     */
    public void complementEnquiryStatus(ExpressOrderContext expressOrderContext, EnquiryStatusEnum enquiryStatus) {
        if (enquiryStatus == null) {
            return;
        }
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setEnquiryStatus(enquiryStatus);
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementEnquiryStatus(this, expressOrderModelCreator);
    }
}