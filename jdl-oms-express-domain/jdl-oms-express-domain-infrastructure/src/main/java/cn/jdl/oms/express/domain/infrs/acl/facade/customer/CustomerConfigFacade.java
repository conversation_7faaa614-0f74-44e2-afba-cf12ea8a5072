package cn.jdl.oms.express.domain.infrs.acl.facade.customer;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderFusionResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.CustomerTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.MultiAddressFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.rpc.RpcResult;
import cn.jdl.oms.express.domain.infrs.acl.rpc.customer.ICustomerConfigService;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.dict.UnifiedSubErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.ldop.basic.dto.BasicTraderExtendInfoDTO;
import com.jd.ldop.basic.dto.BasicTraderInfoDTO;
import com.jd.ldop.basic.dto.BasicTraderInfoFusionDTO;
import com.jd.ldop.basic.dto.BasicTraderShipAddressDTO;
import com.jd.ldop.basic.dto.BasicTraderShipAddressQueryDTO;
import com.jd.ldop.basic.dto.ResponseDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

/**
 * 客户配置信息校验
 */
@Component
public class CustomerConfigFacade {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerConfigFacade.class);

    @Resource
    private ICustomerConfigService customerConfigService;

    @Resource
    private CustomerTranslator customerTranslator;

    /**
     * 获取商家基础资料信息
     *
     * @param deliveryFulfillmentNo
     * @return
     */
    public BasicTraderResponse getCustomerConfig(String deliveryFulfillmentNo) {
        try {
            RpcResult<ResponseDTO<BasicTraderInfoDTO>> basicResult = customerConfigService.getBaseTraderByCode(deliveryFulfillmentNo);
            if (!basicResult.isSuccess()) {
                if (basicResult.getResult() == null) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                            .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0004.subCode())
                            .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0004.desc());
                } else {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL).withSubMessage(basicResult.getResult().getMessage());
                }

            }
            return customerTranslator.toBasicTraderResponse(basicResult.getResult().getResult());
        } catch (BusinessDomainException e) {
            LOGGER.error("客户配置信息校验业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("客户配置信息校验异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, e)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0024.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0024.desc());
        }

    }

    /**
     * 获取商家基础资料信息
     * 返回上下文的CustomerConfig
     * @param expressOrderContext
     * @return
     */
    public CustomerConfig getCustomerConfig(ExpressOrderContext expressOrderContext) {
        if (null != expressOrderContext.getCustomerConfig()) {
            return expressOrderContext.getCustomerConfig();
        }
        try {
            ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
            String accountNo = orderModel.getCustomer().getAccountNo();
            if(StringUtils.isBlank(accountNo)
                    && null != orderModel.getOrderSnapshot()
                    && null != orderModel.getOrderSnapshot().getCustomer()){
                accountNo = orderModel.getOrderSnapshot().getCustomer().getAccountNo();
            }

            if (StringUtils.isBlank(accountNo)
                    && null != orderModel.getOrderSnapshot()
                    && null != orderModel.getOrderSnapshot().getOrderSnapshot()
                    && null != orderModel.getOrderSnapshot().getOrderSnapshot().getCustomer()){
                accountNo = orderModel.getOrderSnapshot().getOrderSnapshot().getCustomer().getAccountNo();
            }

            RpcResult<ResponseDTO<BasicTraderInfoDTO>> basicResult = customerConfigService.getBaseTraderByCode(accountNo);
            if (!basicResult.isSuccess()) {
                if (basicResult.getResult() == null) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                            .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0004.subCode())
                            .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0004.desc());
                } else {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL).withSubMessage(basicResult.getResult().getMessage());
                }

            }
            CustomerConfig customerConfig = customerTranslator.toCustomerConfig(basicResult.getResult().getResult());
            expressOrderContext.setCustomerConfig(customerConfig);
            return customerConfig;
        } catch (BusinessDomainException e) {
            LOGGER.error("客户配置信息校验业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("客户配置信息校验异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, e)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0024.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0024.desc());
        }

    }


    /**
     * 获取商家基础资料信息
     * 返回上下文的CustomerConfig
     * @param settlementAccountNo 结算账号
     * @return
     */
    public CustomerConfig getCustomerConfigBySettlementAccountNo(String settlementAccountNo) {
        try {
            RpcResult<ResponseDTO<BasicTraderInfoDTO>> basicResult = customerConfigService.getBaseTraderByCode(settlementAccountNo);
            if (!basicResult.isSuccess()) {
                if (basicResult.getResult() == null) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                            .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0005.subCode())
                            .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0005.desc());
                } else {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL).withSubMessage(basicResult.getResult().getMessage());
                }

            }
            CustomerConfig customerConfig = customerTranslator.toCustomerConfig(basicResult.getResult().getResult());
            return customerConfig;
        } catch (BusinessDomainException e) {
            LOGGER.error("客户配置信息校验业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("客户配置信息校验异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, e)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0024.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0024.desc());
        }

    }

    /**
     * 获取商家基础资料扩展信息
     *
     * @param deliveryFulfillmentNo
     * @return
     */
    public BasicTraderResponse getCustomerConfigExt(String deliveryFulfillmentNo) {
        try {
            RpcResult<ResponseDTO<BasicTraderExtendInfoDTO>> basicExtResult = customerConfigService.getBaseTraderExtendByCode(deliveryFulfillmentNo);
            if (!basicExtResult.isSuccess()) {
                if (basicExtResult.getResult() == null) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                            .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0006.subCode())
                            .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0006.desc());
                } else {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL).withSubMessage(basicExtResult.getResult().getMessage());
                }

            }
            BasicTraderExtendInfoDTO basicTraderExtInfoDTO = basicExtResult.getResult().getResult();
            BasicTraderResponse response = new BasicTraderResponse();
            response.setReturnOrderMold(basicTraderExtInfoDTO.getReturnOrderMold());
            return response;
        } catch (BusinessDomainException e) {
            LOGGER.error("客户配置信息校验业务异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("客户配置信息校验异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, e);
        }
    }

    /**
     * 根据商家编码（青龙业主号）查询商家渠道信息
     *
     * @param deliveryFulfillmentNo
     * @return
     */
    public BasicTraderFusionResponse getBasicTraderFusionInfo(String deliveryFulfillmentNo, String appName) {
        try {
            RpcResult<ResponseDTO<BasicTraderInfoFusionDTO>> basicResult = customerConfigService.getBasicTraderFusionInfo(deliveryFulfillmentNo, appName);
            if (!basicResult.isSuccess()) {
                if (basicResult.getResult() == null) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                            .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0004.subCode())
                            .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0004.desc());
                } else {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL).withSubMessage(basicResult.getResult().getMessage());
                }
            }
            return customerTranslator.toBasicTraderFusionResponse(basicResult.getResult().getResult());
        } catch (BusinessDomainException e) {
            LOGGER.error("客户配置渠道信息校验业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("客户配置渠道信息校验异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, e)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0025.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0025.desc());
        }
    }

    /**
     * 逗号分隔符
     */
    private static final String SEPARATOR = ",";

    /**
     * 多地址校验是否通过。调用接口超时或异常，则能力降级跳过校验，直接返回校验通过。
     *
     * @param request
     * @return true 通过；false 未通过
     */
    public boolean multiAddressCheckPassed(MultiAddressFacadeRequest request) {

        LOGGER.info("多地址校验-入参: {}", JSONUtils.beanToJSONDefault(request));

        // 商家编码
        String traderCode = request.getTraderCode();

        // 起始站点/揽收站点
        String startStationNo = request.getStartStationNo();

        /*
         * 预分拣之后，有揽收站点，则执行校验，进人工或超区不需要校验。
         */
        if (StringUtils.isBlank(traderCode) || StringUtils.isBlank(startStationNo)) {
            return true;
        }
        try {
            RpcResult<ResponseDTO<BasicTraderShipAddressQueryDTO>> basicResult = customerConfigService.getTraderShipAddressListByTraderCode(traderCode);
            if (!basicResult.isSuccess()) {
                if (basicResult.getResult() == null) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MULTI_ADDRESS_ANALYSIS_FAIL).withCustom("多地址校验失败");
                } else {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MULTI_ADDRESS_ANALYSIS_FAIL).withSubMessage(basicResult.getResult().getMessage());
                }

            }

            BasicTraderShipAddressQueryDTO response = basicResult.getResult().getResult();
            if (response == null) {
                LOGGER.info("多地址校验-接口返回值为空，跳过逻辑判断");
                return true;
            }
            // 多地址发货校验,0-不限制 1-全渠道限制 2-部分渠道限制
            Integer verification = response.getAddressShippingVerification();
            if (verification == null) {
                LOGGER.info("多地址校验-接口返回值 verification 为空，跳过逻辑判断");
                return true;
            }
            // 多地址发货列表
            List<BasicTraderShipAddressDTO> addressList = response.getBasicTraderShipAddressList();
            if (addressList == null) {
                LOGGER.info("多地址校验-接口返回值 addressList 为空，跳过逻辑判断");
                return true;
            }

            // 是否通过校验
            boolean passed = false;
            // 多地址发货校验,0-不限制 1-全渠道限制 2-部分渠道限制
            switch (verification) {
                case 0:
                    LOGGER.info("多地址校验-verification=0，不限制，校验通过");
                    passed = true;
                    break;

                case 1:// 配置为「全渠道限制」
                    // 校验预分拣揽收网点是否在返回的网点范围内，如果在则校验通过，如果不在则校验失败
                    for (BasicTraderShipAddressDTO address : addressList) {
                        if (address.getSiteId() == null) {
                            LOGGER.info("多地址校验-接口返回值 isDelete: {}, siteId: {}, restrictedChannel: {}，跳过逻辑判断",
                                    address.getIsDelete(), address.getSiteId(), address.getRestrictedChannel());
                            continue;
                        }

                        if (startStationNo.equals(String.valueOf(address.getSiteId()))) {
                            LOGGER.info("多地址校验-揽收站点在返回的网点范围内，校验通过。verification: {}, startStationNo: {}", verification, startStationNo);
                            passed = true;
                            break;
                        }
                    }
                    break;

/* 齐鑫磊确认，本期「部分渠道限制」逻辑暂时屏蔽。
                case 2:// 配置为「部分渠道限制」
                    String systemSubCaller = request.getSystemSubCaller();
                    if (StringUtils.isBlank(systemSubCaller)) {
                        LOGGER.info("多地址校验-systemSubCaller为空，校验通过。verification: {}", verification);
                        passed = true;
                        break;
                    }

                    // 下单渠道是否在返回的渠道范围内
                    boolean isInRestrictedChannel = false;

                    for (BasicTraderShipAddressDTO address : addressList) {
                        // 若是受限渠道，校验预分拣揽收网点是否在返回的网点范围内，如果在则校验通过。restrictedChannel 逗号分隔
                        if (address.getRestrictedChannel() == null) {
                            continue;
                        }
                        HashSet<String> restrictedChannels = new HashSet<>();
                        restrictedChannels.addAll(Arrays.asList(StringUtils.split(address.getRestrictedChannel(), SEPARATOR)));
                        if (restrictedChannels.contains(systemSubCaller)) {

                            isInRestrictedChannel = true;

                            if (address.getSiteId() == null) {
                                LOGGER.info("多地址校验-接口返回值 isDelete: {}, siteId: {}, restrictedChannel: {}，跳过逻辑判断",
                                        address.getIsDelete(), address.getSiteId(), address.getRestrictedChannel());
                                continue;
                            }
                            // 受限渠道相同 且 揽收站点相同
                            if (startStationNo.equals(String.valueOf(address.getSiteId()))) {
                                LOGGER.info("多地址校验-揽收站点在返回的网点范围内，校验通过。verification: {}, startStationNo: {}", verification, startStationNo);
                                passed = true;
                                break;
                            }

                        }

                    }
                    // 若下单渠道编码不在返回的渠道范围内，则校验通过。
                    if (!isInRestrictedChannel) {
                        LOGGER.info("多地址校验-下单渠道编码不在返回的渠道范围内，校验通过。verification: {}, isInRestrictedChannel: {}", verification, isInRestrictedChannel);
                        passed = true;
                    }
                    break;
*/

                default:
                    LOGGER.error("多地址校验-接口返回值异常，verification: {}", verification);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MULTI_ADDRESS_ANALYSIS_FAIL).withCustom("多地址校验接口返回值异常");

            }

            LOGGER.info("多地址校验-出参 passed: {}, verification: {}", passed, verification);

            return passed;
        } catch (Exception e) {
            LOGGER.error("多地址校验-异常", e);
            return true;
        }

    }
}
