package cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo;

import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.BusinessSceneUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 派送基本信息校验处理类
 */
@Component
public class ShipmentValidBaseHandler {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ShipmentValidBaseHandler.class);

    /**
     * 报警
     */
    @Resource
    private UmpUtil umpUtil;

    /**
     * 收件地址是香港澳门，到付不能送自提点
     */
    public void validateHKMOSelfPickupSettlementType(ExpressOrderModel orderModel) {
        try {
            if (!BatrixSwitch.applyByBoolean(BatrixSwitchKey.VALIDATE_HK_MO_SELF_PICKUP_SETTLEMENT_TYPE_SWITCH)) {
                LOGGER.info("跳过校验：validateHKMOSelfPickupSettlementType");
                return;
            }

            // 接单不看快照
            ExpressOrderModel orderSnapshot = BusinessSceneUtil.isCreate(orderModel) ? null : orderModel.getOrderSnapshot();

            // 派送方式必须是自提
            DeliveryTypeEnum deliveryTypeEnum = orderModel.getShipment() != null ? orderModel.getShipment().getDeliveryType() : null;
            if (deliveryTypeEnum == null && orderSnapshot != null && orderSnapshot.getShipment() != null) {
                deliveryTypeEnum = orderSnapshot.getShipment().getDeliveryType();
            }
            if (DeliveryTypeEnum.SELF_PICKUP != deliveryTypeEnum) {
                return;
            }

            // 目的流向必须是香港或澳门
            if (!(orderModel.isToHKMO() || (orderSnapshot != null && orderSnapshot.isToHKMO()))) {
                return;
            }

            // 不支持：结算方式为到付
            // 接单直接取当前单
            // 修改场景：当前单没有取原单。港澳改址场景，当前单（修改请求）的结算方式是改址结算方式，改址费到付也不支持
            SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(orderModel);
            if (SettlementTypeEnum.CASH_ON_DELIVERY == settlementType) {
                String msg = "支付方式为到付现结的运单，暂不支持送自提点";
                LOGGER.error(msg);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
            }

            LOGGER.info("校验通过：validateHKMOSelfPickupSettlementType");
        } catch (Exception e) {
            // todo 稳定后去除报警和catch
            umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_VALIDATE_HK_MO_SELF_PICKUP_SETTLEMENT_TYPE_FAIL_ALARM, "校验报错：收件地址是香港澳门，到付不能送自提点", orderModel.traceId());
            throw e;
        }
    }
}