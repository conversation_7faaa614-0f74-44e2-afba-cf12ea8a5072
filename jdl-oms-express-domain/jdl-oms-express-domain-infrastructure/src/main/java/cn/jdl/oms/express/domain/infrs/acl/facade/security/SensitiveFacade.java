package cn.jdl.oms.express.domain.infrs.acl.facade.security;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.infrs.acl.pl.security.SensitiveFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.security.SensitiveFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.security.SensitiveRpcTranslator;
import cn.jdl.oms.express.domain.infrs.acl.rpc.RpcResult;
import cn.jdl.oms.express.domain.infrs.acl.rpc.security.ISensitiveService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.SensitiveMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer;
import cn.jdl.oms.express.domain.infrs.ohs.locals.thread.ThreadPoolExecutorService;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.SensitiveConstant;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.ai.comment.moderation.domain.ModerationRpcResult;
import com.jd.ai.comment.moderation.domain.TextDetectItemResult;
import com.jd.sensitiveword.domain.SensitiveWordCheckRequest;
import com.jd.sensitiveword.domain.SensitiveWordCheckResult;
import com.jd.sensitiveword.domain.SensitiveWordLexeme;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static java.util.concurrent.TimeUnit.MILLISECONDS;

/**
 * @ProjectName：jdl-oms-express
 * @Package： cn.jdl.oms.express.domain.infrs.acl.facade.security.SensitiveFacade
 * @ClassName: sensitiveValidate
 * @Description: 敏感词校验防腐层
 * @Author： liyong549
 * @CreateDate 2021/3/19 16:41
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Component
public class SensitiveFacade {

    /**
     * 日志
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(SensitiveFacade.class);
    /**
     * 敏感词校验不通过
     */
    private final String NO_PASS = "2";
    /**
     * 防腐层到RPC层请求对象转化
     */
    @Resource
    private SensitiveRpcTranslator sensitiveRpcTranslator;
    /**
     * 敏感词校验服务
     */
    @Resource
    private ISensitiveService sensitiveService;
    /**
     * UCC 开关配置
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    @Value("${jdl.oms.express.sensitive.thread.max.count:2}")
    private Integer threadNum = 2;

    /**
     * 多线程调用服务
     */
    @Resource
    private ThreadPoolExecutorService threadPoolExecutorService;

    /**
     * 敏感词对比JMQ生产者
     */
    @Resource
    private JMQMessageProducer sensitiveWordsJmqProducer;

    /** 敏感词校验服务防腐层请求参数转化 */
    @Resource
    private SensitiveFacadeTranslator sensitiveFacadeTranslator;

    /**
     * @param sensitiveFacadeRequest
     * @return
     * @throws
     * @throws
     * @Description sensitiveWordsValidate 数科敏感词校验服务新
     * <AUTHOR>
     * @createDate 2022/5/28 7:00 下午
     * @lastModify
     */
    public void sensitiveWordsValidate(SensitiveFacadeRequest sensitiveFacadeRequest, ExpressOrderContext context, ExpressOrderModel orderModel) {
        try {
            if (!expressUccConfigCenter.isSensitiveWordsSwitch()) {
                LOGGER.info("敏感词校验降级,敏感词不校验");
                return;
            }
            Map<String, String> textRequest = sensitiveFacadeRequest.getTextMap();
            if (MapUtils.isEmpty(textRequest)) {
                LOGGER.info("数科敏感词校验服务新列表为空跳过");
                return;
            }
            //数据内容安全校验服务新
            RpcResult<ModerationRpcResult<List<TextDetectItemResult>>> rpcResult = sensitiveService.sensitiveWordsValidate(textRequest);
            boolean sensitiveAbSwitch = BatrixSwitch.obtainBooleanByUccKey(BatrixSwitchKey.SENSITIVE_AB_SWITCH);
            if (sensitiveAbSwitch) {
                CompletableFuture.runAsync(() -> {
                    try {
                        SensitiveMessageDto sensitiveMessageDto = new SensitiveMessageDto();
                        sensitiveMessageDto.setOldSensitiveRequest(sensitiveFacadeRequest.getTextMap());
                        sensitiveMessageDto.setOldSensitiveResponse(rpcResult.getResult());
                        sensitiveMessageDto.setOldSensitiveResult(rpcResult.isSuccess());
                        SensitiveFacadeRequest facadeRequestNew = sensitiveFacadeTranslator.toSensitiveFacadeRequest(orderModel);
                        sensitiveMessageDto.setNewSensitiveRequest(sensitiveRpcTranslator.toSensitiveWordCheckRequest(facadeRequestNew));
                        sensitiveMessageDto.setRequestProfile(context.getRequestProfile());
                        sensitiveMessageDto.setBusinessIdentity(context.getBusinessIdentity());
                        sensitiveWordsJmqProducer.send(UUID.randomUUID().toString(), JSONUtils.beanToJSONDefault(sensitiveMessageDto), null);
                    } catch (Throwable t) {
                        LOGGER.error("数科敏感词校验服务新JMQ发送异常", t);
                    }
                });
            }
            if (rpcResult.isSuccess()) {
                LOGGER.info("数科敏感词校验服务新校验通过");
                return;
            } else {
                LOGGER.error("数科敏感词校验服务新校验未通过");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.SENSITIVE_WORDS_FAIL)
                        .withCustom(UnifiedErrorSpec.BasisOrder.SENSITIVE_WORDS_FAIL.desc());
            }
        } catch (BusinessDomainException e) {
            LOGGER.error("数科敏感词校验服务新校验业务异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("数科敏感词校验服务新校验异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.SENSITIVE_WORDS_FAIL, e);
        }
    }

    /**
     * 零售新敏感词校验
     * @param sensitiveFacadeRequest
     */
    public void checkSensitiveWords(SensitiveFacadeRequest sensitiveFacadeRequest) throws Throwable {
        try {
            // 构建入参
            SensitiveWordCheckRequest[] sensitiveWordCheckRequests = sensitiveRpcTranslator.toSensitiveWordCheckRequest(sensitiveFacadeRequest);
            if (null == sensitiveWordCheckRequests || sensitiveWordCheckRequests.length == 0) {
                return;
            }
            // 获取敏感词批量调用，每次批量的大小
            int size = BatrixSwitch.obtainIntegerByUccKey(BatrixSwitchKey.SENSITIVE_WORDS_SIZE, 40);
            int batchSize = (sensitiveWordCheckRequests.length + size - 1) / size; // 向上取整
            List<Callable<Boolean>> tasks = new ArrayList<>(batchSize);
            for (int i = 0; i < batchSize; ++i) {
                int start = i * size; // 起始位置
                int end = Math.min(sensitiveWordCheckRequests.length, start + size); // 结束位置
                SensitiveWordCheckRequest[] rpcRequests = Arrays.copyOfRange(sensitiveWordCheckRequests, start, end);
                tasks.add(() -> checkResult(rpcRequests));
            }

            // 设置超时时间
            List<Future<Boolean>> futures = threadPoolExecutorService.invokeAll(tasks, 1000, MILLISECONDS);
            for (Future<Boolean> future : futures) {
                future.get();
            }

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof DomainException) {
                // 校验失败
                LOGGER.error("敏感词批量检查失败exception: {}", ((DomainException) cause).fullMessage() );
                throw cause;
            }
            // 校验失败
            LOGGER.error("敏感词批量检查失败: ", cause);
            throw cause;
        } catch (CompletionException e) {
            // CompletableFuture 封装异常捕获
            if (e.getCause() instanceof DomainException) {
                // 校验失败
                LOGGER.error("敏感词批量检查失败exception: {}", ((DomainException) e.getCause()).fullMessage() );
                throw e.getCause();
            }
            // 校验失败
            LOGGER.error("敏感词批量检查失败: ", e.getCause());
            throw e.getCause();
        } catch (BusinessDomainException e) {
            // 校验失败
            LOGGER.error("敏感词批量检查失败:{}", e.fullMessage());
            throw e;
        } catch (InfrastructureException e) {
            // 系统异常
            LOGGER.error("敏感词批量检查服务异常:{}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("敏感词批量检查服务异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.SENSITIVE_WORDS_FAIL, e);
        }
    }

    /**
     * 校验零售敏感词结果
     * @param sensitiveWordCheckRequests
     */
    private boolean checkResult(SensitiveWordCheckRequest[] sensitiveWordCheckRequests) {
        RpcResult<SensitiveWordCheckResult[]> rpcResult = sensitiveService.checkSensitiveWordArray(sensitiveWordCheckRequests);
        if (!rpcResult.isSuccess()) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.SENSITIVE_WORDS_FAIL)
                    .withCustom(UnifiedErrorSpec.BasisOrder.SENSITIVE_WORDS_FAIL.desc());
        }

        SensitiveWordCheckResult[] results = rpcResult.getResult();
        for (int i = 0; i < results.length; i++) {
            // result.state在service层已经判断
            // 校验通过
            if (results[i].isPass) { continue; }
            if (SensitiveConstant.PERSONAL_INFO == sensitiveWordCheckRequests[i].useCategory) {
                // 500 直接拒单
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.SENSITIVE_WORDS_FAIL)
                        .withCustom(UnifiedErrorSpec.BasisOrder.SENSITIVE_WORDS_FAIL.desc())
                        .withSubMessage("文本:'" + sensitiveWordCheckRequests[i].text + "'敏感词校验失败");
            }
            // 校验失败 看命中规则
            List<SensitiveWordLexeme> words = results[i].words;
            for (SensitiveWordLexeme word : words) {
                String[] hitTags = word.tags.split(",");
                for (String hitTag : hitTags) {
                    BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.SENSITIVE_WORDS_TAGS, hitTag, val -> {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.SENSITIVE_WORDS_FAIL)
                                .withCustom(UnifiedErrorSpec.BasisOrder.SENSITIVE_WORDS_FAIL.desc())
                                .withSubMessage("文本:'" + word.word + "'命中风控规则:" + hitTag);
                    });
                }
            }
        }

        return true;
    }
}
