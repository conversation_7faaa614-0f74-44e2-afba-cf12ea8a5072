package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing;

import cn.jdl.oms.core.model.DiscountInfo;
import cn.jdl.oms.core.model.FinanceDetailInfo;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.express.domain.converter.VolumeMapper;
import cn.jdl.oms.express.domain.converter.WeightMapper;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.vo.Money;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Condition;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper(uses = {
        WeightMapper.class,
        VolumeMapper.class
})
public interface BillingConvertor {

    BillingConvertor INSTANCE = Mappers.getMapper(BillingConvertor.class);

    MoneyInfo toMoneyInfo(BillingEnquiryFacadeResponse.MoneyFacadeDto moneyFacadeDto);

    MoneyInfoDto toMoneyInfoDto(BillingEnquiryFacadeResponse.MoneyFacadeDto moneyFacadeDto);

    Money toMoney(BillingEnquiryFacadeResponse.MoneyFacadeDto moneyFacadeDto);

    default Map<String, String> toExtendProps(BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountFacadeDto) {
        Map<String, String> extendProps = new HashMap<>();
        if(StringUtils.isNotBlank(discountFacadeDto.getTicketNo())){
            //优惠券编码
            extendProps.put(AttachmentKeyEnum.TICKET_NO.getKey(), discountFacadeDto.getTicketNo());
        }
        return extendProps;
    }

    @Mapping(target = "extendProps", expression = "java(toExtendProps(discountFacadeDto))")
    DiscountInfoDto toDiscountInfoDto(BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountFacadeDto);

    List<DiscountInfoDto> toDiscountInfoDtos(List<BillingEnquiryFacadeResponse.DiscountInfoFacadeDto> discountInfoFacadeDtos);

    @Mapping(target = "discountInfoDtos", source = "discountInfoFacadeDtos")
    FinanceDetailInfoDto toFinanceDetailInfoDto(BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto);

    List<FinanceDetailInfoDto> toFinanceDetailInfoDtos(List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> financeDetailFacadeDtos);

    @Mapping(target = "financeDetailInfos", source = "financeDetailFacadeDtoList")
    FinanceInfoDto toFinanceInfoDto(BillingEnquiryFacadeResponse.FinanceFacadeDto financeFacadeDto);

    @Mapping(target = "extendProps", expression = "java(toExtendProps(discountFacadeDto))")
    DiscountInfo toDiscountInfo(BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountFacadeDto);

    List<DiscountInfo> toDiscountInfos(List<BillingEnquiryFacadeResponse.DiscountInfoFacadeDto> discountInfoFacadeDtos);

    @Mapping(target = "discountInfos", source = "discountInfoFacadeDtos")
    FinanceDetailInfo toFinanceDetailInfo(BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto);

    List<FinanceDetailInfo> toFinanceDetailInfos(List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> financeDetailFacadeDtos);

    @Condition
    default boolean isNotEmpty(List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> list) {
        return null != list && !list.isEmpty();
    }

    @Mapping(target = "billingWeight", source = "calWeight")
    @Mapping(target = "financeDetailInfos", source = "financeDetailFacadeDtoList")
    void complementFinanceInfo(BillingEnquiryFacadeResponse.FinanceFacadeDto financeFacadeDto, @MappingTarget FinanceInfo financeInfo);

}
