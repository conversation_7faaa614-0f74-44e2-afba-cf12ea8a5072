package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing;

import cn.jdl.oms.express.domain.dto.CostInfoDto;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.PointsInfoDto;
import cn.jdl.oms.express.domain.spec.dict.CoordinateTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.IdentityTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefundStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.TransportTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.WarmLayerEnum;
import cn.jdl.oms.express.domain.vo.Activity;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.OrderStatus;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 计费询价防腐层请求数据
 */
@Data
public class BillingEnquiryFacadeRequest {

    private boolean waiFa = false;

    /**
     * 交易业务单元
     */
    private String businessUnit;

    /**
     * 订单号
     */
    @Deprecated
    private String orderNo;

    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;

    /**
     * 订单状态
     */
    private OrderStatus orderStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 询价类型
     * 1、询价 （写台账，产生应收）
     * 2、费用预估（不写台账，估算费用）
     * 3、费用追缴
     * 4、MTD费用预估(不写台账，估算费用包含MTD预估金额）
     * 5、含最大阶梯事后折预估(不写台账，估算费用最大阶梯折扣预估金额）
     */
    private Integer inquiryType;

    /**
     * 操作时间（订单中心服务接收到请求时的时间，接单场景是接单时间，修改是修改时间，取消是取消时间等）
     */
    private Date operateTime;

    /**
     * 客户信息
     */
    private CustomerFacadeDto customerFacadeDto;

    /**
     * 关联单信息
     */
    private RefOrderFacadeDto refOrderFacadeDto;

    /**
     * 渠道信息
     */
    @Deprecated
    private ChannelFacadeDto channelFacadeDto;

    /**
     * 产品信息
     */
    private List<ProductFacadeDto> productFacadeDtoList;

    /**
     * 货品信息
     */
    private CargoFacadeDto cargoFacadeDto;

    /**
     * 发件人信息
     */
    private ConsignorFacadeDto consignorFacadeDto;

    /**
     * 收件人信息
     */
    private ConsigneeFacadeDto consigneeFacadeDto;

    /**
     * 配送信息
     */
    private ShipmentFacadeDto shipmentFacadeDto;

    /**
     * 财务信息
     */
    private FinanceFacadeDto financeFacadeDto;

    /**
     * 原单快照
     */
    private OrderSnapshot orderSnapshot;

    /**
     * 逆向单<b>原单</b>时，需要传递逆向单信息
     */
    private ReverseOrderInfo reverseOrderInfo;

    /**
     * 服务单号
     */
    private String serviceNo;

    /**
     * 拓展字段
     */
    private Map<String, Object> extendProps;

    @Data
    public static class PackageFacadeDto {
        /**
         * 包裹编码
         */
        private String packageNo;
        /**
         * 包裹件数
         */
        private Integer quantity;
        /**
         * 包裹重量
         */
        private BigDecimal weight;
        /**
         * 包裹长度
         */
        private BigDecimal length;
        /**
         * 包裹宽度
         */
        private BigDecimal width;
        /**
         * 包裹高度
         */
        private BigDecimal height;
        /**
         * 包裹体积
         */
        private BigDecimal volume;
        /**
         * 产品列表
         */
        private List<ProductFacadeDto> productList;

        /**
         * 计费入参过滤标识
         */
        private boolean filterBilling = false;
    }

    @Data
    public static class CustomerFacadeDto {

        /**
         * 青龙业主号编码
         */
        private String accountNo;
        /**
         * 青龙业主号名称
         */
        private String accountName;
        /**
         * 事业部编号
         */
        private String account2No;
        /**
         * 事业部名称
         */
        private String account2Name;

    }


    @Data
    public static class ChannelFacadeDto {

        /**
         * 客户唯一业务单据号
         */
        private String customerOrderNo;

        /**
         * 客户的一级业务渠道
         */
        private String channelNo;

        /**
         * 客户的一级业务渠道订单号
         */
        private String channelOrderNo;

        /**
         * 客户的一级业务渠道客户编码
         */
        private String channelCustomerNo;

        /**
         * 渠道操作时间
         */
        private Date channelOperateTime;

        /**
         * 渠道的调用方来源
         */
        private String systemCaller;

        /**
         * 渠道的调用方的子来源
         */
        private String systemSubCaller;

    }

    @Data
    public static class RefOrderFacadeDto {

        /**
         * 正向运单号
         */
        private String waybillNo;

    }

    @Data
    public static class ProductFacadeDto {
        /**
         * 是否是附加服务费
         */
        private boolean isAttachFees;
        /**
         * 产品编码
         */
        private String productNo;
        /**
         * 产品类型：1 主产品 、2 增值产品
         */
        private Integer productType;
        /**
         * 产品关系（所属主产品编码）
         */
        private String parentNo;
        /**
         * 产品要素属性
         */
        private Map<String, String> productAttrs;
        /**
         * 扩展字段说明
         */
        private Map<String, String> extendProps;
        /**
         * 产品名称
         */
        private String productName;

    }

    @Data
    public static class CargoFacadeDto {

        /**
         * 总重量
         */
        private BigDecimal totalCargoWeight;
        /**
         * 总体积
         */
        private BigDecimal totalCargoVolume;
        /**
         * 总数量
         */
        private BigDecimal totalCargoQuantity;
    }

    @Data
    public static class ConsignorFacadeDto {

        /**
         * 发货人姓名
         */
        private String consignorName;
        /**
         * 发货人手机
         */
        private String consignorMobile;
        /**
         * 发货人电话
         */
        private String consignorPhone;
        /**
         * 发货地邮编
         */
        private String consignorZipCode;
        /**
         * 发货人公司
         */
        private String consignorCompany;
        /**
         * 发货人国家编码
         */
        private String consignorNationNo;
        /**
         * 发货人国家名称
         */
        private String consignorNation;
        /**
         * 发货人证件类型
         */
        private IdentityTypeEnum consignorIdType;
        /**
         * 发货人证件号码
         */
        private String consignorIdNo;
        /**
         * 发货人证件姓名
         */
        private String consignorIdName;
        /**
         * 发货人地址信息
         */
        private AddressFacadeDto addressFacadeDto;


    }

    @Data
    public static class ConsigneeFacadeDto {

        /**
         * 收货人姓名
         */
        private String consigneeName;

        /**
         * 收货人手机
         */
        private String consigneeMobile;
        /**
         * 收货人电话
         */
        private String consigneePhone;

        /**
         * 收货地邮编
         */
        private String consigneeZipCode;
        /**
         * 收货人公司
         */
        private String consigneeCompany;
        /**
         * 收货人国家编号
         */
        private String consigneeNationNo;
        /**
         * 收货人国家名称
         */
        private String consigneeNation;
        /**
         * 收货人证件类型
         */
        private IdentityTypeEnum consigneeIdType;
        /**
         * 收货人证件号码
         */
        private String consigneeIdNo;
        /**
         * 收货人证件姓名
         */
        private String consigneeIdName;
        /**
         * 收货人地址信息
         */
        private AddressFacadeDto addressFacadeDto;

    }

    @Data
    public static class AddressFacadeDto {
        /**
         * 省编码
         */
        private String provinceNo;
        /**
         * 省名称
         */
        private String provinceName;
        /**
         * 市编码
         */
        private String cityNo;
        /**
         * 市名称
         */
        private String cityName;
        /**
         * 区/县编码
         */
        private String countyNo;
        /**
         * 区/县名称
         */
        private String countyName;
        /**
         * 乡/镇编码
         */
        private String townNo;
        /**
         * 乡/镇名称
         */
        private String townName;
        /**
         * 详细地址
         */
        private String address;
        /**
         * 坐标系类型
         */
        private CoordinateTypeEnum coordinateType;
        /**
         * 发件地址经度
         */
        private String longitude;
        /**
         * 发件地址纬度
         */
        private String latitude;
        /**
         * GIS解析后省编码
         */
        private String provinceNoGis;
        /**
         * GIS解析后省名称
         */
        private String provinceNameGis;
        /**
         * GIS解析后市编码
         */
        private String cityNoGis;
        /**
         * GIS解析后市名称
         */
        private String cityNameGis;
        /**
         * GIS解析后区编码
         */
        private String countyNoGis;
        /**
         * GIS解析后区名称
         */
        private String countyNameGis;
        /**
         * GIS解析后镇编码
         */
        private String townNoGis;
        /**
         * GIS解析后镇名称
         */
        private String townNameGis;
        /**
         * GIS解析后详细地址
         */
        private String addressGis;
        /**
         * 行政区编码
         */
        private String regionNo;

        /**
         * 行政区名称
         */
        private String regionName;
    }

    @Data
    public static class ShipmentFacadeDto {

        /**
         * 预计送达时间
         * 示例：2021-03-05 11:11:26
         */
        @Deprecated
        private Date planDeliveryTime;
        /**
         * 预计接单时间
         */
        @Deprecated
        private Date planReceiveTime;
        /**
         * 预约送达开始时间
         */
        @Deprecated
        private Date expectDeliveryStartTime;
        /**
         * 预约送达结束时间
         */
        @Deprecated
        private Date expectDeliveryEndTime;
        /**
         * 期望提货开始时间
         */
        @Deprecated
        private Date expectPickupStartTime;
        /**
         * 期望提货结束时间
         */
        @Deprecated
        private Date expectPickupEndTime;
        /**
         * 揽收方式
         */
        private PickupTypeEnum pickupType;
        /**
         * 派送方式
         */
        private DeliveryTypeEnum deliveryType;
        /**
         * 运输类型 ,1-航空、2-陆运
         */
        @Deprecated
        private TransportTypeEnum transportType;
        /**
         * 温层要求（来源产品中心）
         */
        private WarmLayerEnum warmLayer;
        /**
         * 始发站编码
         */
        @Deprecated
        private String startStationNo;
        /**
         * 始发站名称
         */
        @Deprecated
        private String startStationName;
        /**
         * 目的站编码
         */
        @Deprecated
        private String endStationNo;
        /**
         * 目的站名称
         */
        @Deprecated
        private String endStationName;
        /**
         * 发货仓库编码
         */
        @Deprecated
        private String warehouseNo;

        /**
         * 总重量
         */
        @Deprecated
        private BigDecimal totalWeight;

        /**
         * 总体积
         */
        @Deprecated
        private BigDecimal totalVolume;
    }

    @Data
    public static class FinanceFacadeDto {

        /**
         * 结算方式
         * 1：寄付现结，
         * <p>
         * 2：到付现结，
         * <p>
         * 3：寄付月结
         */
        private SettlementTypeEnum settlementType;
        /**
         * 计费重量
         */
        private Weight billingWeight;
        /**
         * 计费体积
         */
        private Volume billingVolume;
        /**
         * 结算账号
         */
        private String settlementAccountNo;

        /**
         * TODO 待删除
         */
        private PointsInfoDto pointsInfoDto;

        /**
         * 抵扣信息
         */
        private List<DeductionInfoDto> deductionInfoDtos;

        /**
         * 询价时间
         */
        private Date enquireTime;
        /**
         * 附加费
         */
        private List<CostInfoDto> attachFees;
    }

    @Data
    public static class OrderSnapshot {

        /**
         * 结算方式
         * 1：寄付现结，
         * <p>
         * 2：到付现结，
         * <p>
         * 3：寄付月结
         */
        private SettlementTypeEnum settlementType;

    }

    /**
     * 逆向单<b>原单</b>询价时，需要传递该对象
     */
    @Data
    public static class ReverseOrderInfo {

        /**
         * 逆向单结算方式
         */
        private SettlementTypeEnum settlementType;
    }

    /**
     * 分拣中心信息
     */
    @Data
    public static class SiteInfo {

        /**
         * 分拣中心省id
         */
        private String siteProvinceId;
        /**
         * 分拣中心市id
         */
        private String siteCityId;
        /**
         * 分拣中心县id
         */
        private String siteCountryId;
    }
}
