package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.PeakPeriodFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.dict.TraderSignEnum;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.TraderSignUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.CALC_PRICE_ITEM_LIST;

/**
 * b2c 修改地址场景外单询价转换器
 */
@Translator
public class B2CModifyEnquiryFacadeTranslator {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(B2CModifyEnquiryFacadeTranslator.class);
    //单据状态 默认传0
    private static final int DEFAULT_ORDER_STATUS = 0;
    // 交付模式
    private static final String DELIVERY_PATTERN = "deliveryPattern";
    // 交付模式 为 1京仓发货
    private static final String DELIVERY_PATTERN_ONE = "1";
    //交付模式 为 2
    private static final String DELIVERY_PATTERN_TWO = "2";
    // 拓展字段
    private static final String SHIPMENT_EXTEND_PROPS ="shipmentExtendProps";
    //eclp
    private static final String ECLP ="eclp";
    // 1代表信任商家
    private static final String TRADER_SIGN_TRUST ="1";
    /**
     * 商家基础资料信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;
    /**
     * 高峰期附加费查询
     */
    @Resource
    private PeakPeriodFacade peakPeriodFacade;

    /**
     * 高峰期附加费转换
     */
    @Resource
    private PeakPeriodFacadeTranslator peakPeriodFacadeTranslator;
    /**
     * 收件人地址改动 计费询价防腐层请求数据转换
     */
    public BillingEnquiryFacadeRequest addressChangeToBillingEnquiryFacadeRequest(ExpressOrderModel billDetailOrderModel,ExpressOrderContext expressOrderContext) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        facadeRequest.setOrderNo(orderModel.orderNo());
        //青龙业主编码和青龙业主号名称  (这个是从外单查询出来的)
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(billDetailOrderModel));
        //关联单号，正向运单号  (这个是从外单查询出来的)
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(billDetailOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        //客户、渠道
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));
        //产品信息  用传过来的订单号查出来有都赋值进去 (这个是从外单查询出来的) 这里面应该从订单中心自己数据库里拿出来“包装耗材” 高峰期附加费
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel.getOrderSnapshot()));
        // 总重量、总体积、总数量（ 从外单里查出来货品数量）
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(billDetailOrderModel,orderModel.getOrderSnapshot()));
        //发件人信息 (这个是从外单查询出来的)  其中的二级地址 市级编码 应该从数据库里拿出来 这里
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(billDetailOrderModel,orderModel.getOrderSnapshot().getEnquiry().getEnquiryStartCityNo()));
        //收件人信息  (这个是从外单查询出来的)
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(billDetailOrderModel));
        //财务相关信息 (询价时间,结算方式)
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel));
        //设置拓展字段
        facadeRequest.setExtendProps(toExtendProps(orderModel));
        return facadeRequest;
    }

    private Map<String, Object> toExtendProps(ExpressOrderModel orderModel) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(orderModel));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS,DEFAULT_ORDER_STATUS );
        // 仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, toDistributionType(orderModel));
        return extendParam;
    }

    /**
     * @description 功能描述:  仓配类型(0：纯配；1：仓配)
     *  接单字段deliveryPattern若为1京仓发货 则该字段赋值1
     *  若为2纯配则该字段赋值0；
     *  接单字段deliveryPattern若为空且systemSubCaller为eclp则该字段赋值为1，
     *  其他情况全部赋值0
     * <AUTHOR>
     * @date 2021/6/29 18:04
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    private Object toDistributionType(ExpressOrderModel orderModel) {
        // 配送的拓展字段 deliveryPattern 从这里获取
        Map<String, String> extendProps = orderModel.getOrderSnapshot().getShipment().getExtendProps();
        if(MapUtils.isNotEmpty(extendProps)){
            String shipmentExtendProps = extendProps.get(SHIPMENT_EXTEND_PROPS);
            if (shipmentExtendProps != null){
                Map map = JSONUtils.jsonToMap(shipmentExtendProps);
                if(MapUtils.isNotEmpty(map)){
                    String deliveryPattern = (String)map.get(DELIVERY_PATTERN);
                    if (DELIVERY_PATTERN_ONE.equals(deliveryPattern)){
                        return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                    }else if (DELIVERY_PATTERN_TWO.equals(deliveryPattern)){
                        return EnquiryConstants.DISTRIBUTION_PURE;
                    }else if (deliveryPattern == null && ECLP.equals(orderModel.getOrderSnapshot().getChannel().getSystemSubCaller())){
                        return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                    }
                }
            }
        }
        return EnquiryConstants.DISTRIBUTION_PURE;

    }

    /**
     * @description 功能描述: B2C拓展信息之结算方式
     * <AUTHOR>
     * @date 2021/6/29 12:33
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    private Object extendParamToSettlementType(ExpressOrderModel orderModel) {
        if (SettlementTypeEnum.CASH_ON_PICK == orderModel.getOrderSnapshot().getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_CASH_ON_PICK;
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY ==  orderModel.getOrderSnapshot().getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_CASH_ON_DELIVERY;
        } else if (SettlementTypeEnum.MONTHLY_PAYMENT ==  orderModel.getOrderSnapshot().getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_MONTHLY_PAYMENT;
        }
        return null;
    }

    private BillingEnquiryFacadeRequest.CustomerFacadeDto toCustomerFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = new BillingEnquiryFacadeRequest.CustomerFacadeDto();
        Customer customer = orderModel.getCustomer();
        customerFacadeDto.setAccountNo(customer.getAccountNo());
        return customerFacadeDto;
    }

    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel orderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        List<? extends IProduct> products = orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            for (IProduct product : products) {
                // COd的不询价
                if (!AddOnProductEnum.getCodCode().contains(product.getProductNo())) {
                    BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                    productFacadeDto.setProductNo(product.getProductNo());
                    productFacadeDto.setProductType(product.getProductType());
                    productFacadeDto.setParentNo(product.getParentNo());
                    productFacadeDto.setProductAttrs(product.getProductAttrs());
                    productFacadeDtos.add(productFacadeDto);
                }
            }
        }
        //主产品编码
        String mainProductNo = orderModel.getProductDelegate().getMainProduct().getProductNo();
        //高峰期附加费时间 如果为空则赋值当前时间 财务表里的(现在还没有添加) 之所以不在查询订单详情的时候查就把高峰期附加费查出来是因为
        // 如果高峰期附加费时间为空的话则需要赋值当前时间 这个时间会影响对应的高峰期附加费，而查询订单详情时的当前时间和现在掉用计费接口的时间是有时间差的
        // 所以为了尽量避免这个时间差的影响，所以就在这里查

        // 先用当前时间，因为财务表里还没有此时间
        String pieceTime = orderModel.getEnquiry().getFormatPeakPeriodTime();
        PeakPeriodRequest request = peakPeriodFacadeTranslator.toPeakPeriodRequest(mainProductNo, pieceTime);
        PeakPeriodResponse response = peakPeriodFacade.queryPeakPeriod(request);
        if (response == null) {
            return productFacadeDtos;
        }
        // 如果有高峰期附加费，则把他放到增值服务里面，去查计费信息
        for (PeakPeriodResponse.PeakPeriod peakPeriod : response.getPeakPeriodList()) {
            BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
            productFacadeDto.setProductNo(peakPeriod.getPeakPeriodNo());
            productFacadeDto.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
            productFacadeDtos.add(productFacadeDto);
        }

        return productFacadeDtos;
    }

    /**
     * 客户、渠道
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ChannelFacadeDto toChannelFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ChannelFacadeDto channelFacadeDto = new BillingEnquiryFacadeRequest.ChannelFacadeDto();
        Channel channel = orderModel.getChannel();
        channelFacadeDto.setChannelNo(channel.getChannelNo());
        return channelFacadeDto;
    }
    /**
     * 收件人信息
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();
        Consignee consignee = orderModel.getConsignee();

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        Address address = consignee.getAddress();
        if (address != null) {
            //收件人省
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            // 收件人市
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            //收件人县
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());

        }
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     *
     * @param billDetailOrderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel billDetailOrderModel,ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        // 计费数量(从外单里查出来的货品数量)
        cargoFacadeDto.setTotalCargoQuantity(billDetailOrderModel.getCargoDelegate().totalCargoQuantity());
        BasicTraderResponse customerConfig = customerConfigFacade.getCustomerConfig(billDetailOrderModel.getCustomer().getAccountNo());
        //在这里判断标位，然后根据标为取不同的值
        //取第24位 信任商家重量体积 取下单时的重量和体积(从外单里查出来的货品数量)
        if(TRADER_SIGN_TRUST.equals(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(),
                TraderSignEnum.CHARGEABLE_WEIGHT.getCode()))){
            // 货品信息里的计算总体积
            cargoFacadeDto.setTotalCargoVolume(billDetailOrderModel.getCargoDelegate().totalCargoVolume());
            // 货品信息里的计算总重量
            cargoFacadeDto.setTotalCargoWeight(billDetailOrderModel.getCargoDelegate().totalCargoWeight());
        }else {
            //计费体积
            if (orderModel.getEnquiry().getEnquiryVolume() != null){
                // 核算体积
                cargoFacadeDto.setTotalCargoVolume(orderModel.getEnquiry().getEnquiryVolume().getValue());
            }else { // 不给赋值的话会询价失败，就是要询价失败，打印日志记录一下原因
                LOGGER.error("客户修改后询价在ofc询价之前,核算体积为空！");
            }
            //计费重量
            if (orderModel.getEnquiry().getEnquiryWeight() != null){
                //核算重量
                cargoFacadeDto.setTotalCargoWeight(orderModel.getEnquiry().getEnquiryWeight().getValue());
            }else { // 不给赋值的话会询价失败，就是要询价失败，打印日志记录一下原因
                LOGGER.error("客户修改后询价在ofc询价之前,核算重量为空！");
            }

        }

        return cargoFacadeDto;
    }

    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toConsignorFacadeDto(ExpressOrderModel orderModel,String cityNo) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        Consignor consignor = orderModel.getConsignor();
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        Address address = consignor.getAddress();
        if (address != null) {
            //发件人省
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            // 发件人市 这个应该是数据库里的 起始市id 财务表里的字段 （现在还没有添加）
            addressFacadeDto.setCityNoGis(cityNo);
            //发件人县
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
        }
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 财务相关信息
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        // b2C改地址时的询价时间是 接单时间
        financeFacadeDto.setEnquireTime(orderModel.getOrderSnapshot().getOperateTime());
        //结算方式 这里在查询外单那里已经补在快照里了,传递给计费询价接口
        financeFacadeDto.setSettlementType(orderModel.getOrderSnapshot().getFinance().getSettlementType());
        return financeFacadeDto;
    }
    /**
     * 补全计费结果信息
     */
    public void complementBillingResult(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();

        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getAmount());
        preAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getCurrencyCode());
        financeInfoDto.setPreAmount(preAmount);

        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
        discountAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
        financeInfoDto.setDiscountAmount(discountAmount);

        //计费重量
        financeInfoDto.setBillingWeight(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingVolume());

        //计费类型
        financeInfoDto.setBillingMode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingMode());

        //积分信息
        financeInfoDto.setPointsInfoDto(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPointsInfoDto());

        //费用明细
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : billingEnquiryFacadeResponse.getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
            FinanceDetailInfoDto detailInfoDto = new FinanceDetailInfoDto();
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount().getAmount());
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount().getCurrencyCode());
            detailInfoDto.setPreAmount(detailPreAmount);
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount().getAmount());
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount().getCurrencyCode());
            detailInfoDto.setDiscountAmount(detailDiscountAmount);
            detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            detailInfoDto.setCostName(detailFacadeDto.getCostName());
            detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            detailInfoDto.setProductName(detailFacadeDto.getProductName());
            detailInfoDto.setRemark(detailFacadeDto.getRemark());
            //折扣明细
            if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
                for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
                    discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());
                    Money money = new Money();
                    money.setAmount(discountInfoFacadeDto.getDiscountedAmount().getAmount());
                    money.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
                    discountInfoDto.setDiscountedAmount(money);
                    discountInfoDtos.add(discountInfoDto);
                }
                detailInfoDto.setDiscountInfoDtos(discountInfoDtos);
            }

            detailInfoDto.setExtendProps(new HashMap<>());
            // 价格项明细
            if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
                detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
            }

            financeDetailInfoDtoList.add(detailInfoDto);
        }
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
        // 收款机构
        financeInfoDto.setCollectionOrgNo(billingEnquiryFacadeResponse.getFinanceFacadeDto().getCollectionOrgNo());
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

}
