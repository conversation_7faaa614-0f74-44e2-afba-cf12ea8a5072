package cn.jdl.oms.express.domain.infrs.acl.pl.address;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.dto.ConsigneeInfoDto;
import cn.jdl.oms.express.domain.dto.ConsignorInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddressSourceEnum;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 功能：防腐层转换器
 *
 * <AUTHOR>
 * @version 0.0.1
 * @return
 * @date 2021/3/24 14:41
 */
@Translator
public class JdAddressFacadeTranslator {

    /**
     * 来源，港澳订单传HKM
     */
    private static final String SOURCE_HKM = "HKM";

    /**
     * 功能: 组装JD地址解析入参
     *
     * @param:
     * @return:
     * @throw:
     * @description: 使用场景为地址解析
     * @author: liufarui
     * @date: 2021/6/12 11:18 下午
     */
    public JdAddressFacadeRequest toJdAddressFacadeRequest(ExpressOrderModel orderModel, Address address) {
        if (null == address || StringUtils.isBlank(address.getAddress())) {
            return null;
        }
        JdAddressFacadeRequest request = new JdAddressFacadeRequest();
        request.setProvinceCode(address.getProvinceNo());
        request.setProvinceName(address.getProvinceName());
        request.setCityCode(address.getCityNo());
        request.setCityName(address.getCityName());
        request.setDistrictCode(address.getCountyNo());
        request.setDistrictName(address.getCountyName());
        request.setTownCode(address.getTownNo());
        request.setTownName(address.getTownName());
        request.setDetailAddress(address.getAddress());
        //request.setFullAddress(address.getFullAddress());
        if ((orderModel != null && orderModel.isHKMO())
                || (orderModel != null && orderModel.getOrderSnapshot() != null && orderModel.getOrderSnapshot().isHKMO())) {
            request.setSource(SOURCE_HKM);
        }
        return request;
    }

    /**
     * 功能: 补充发件人Gis解析后信息
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/12 11:42 下午
     */
    public void complementConsignorGisAddress(ExpressOrderModel orderModel, JdAddressFacadeResponse.Address externalJdAddress) {
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        ConsignorInfoDto consignorInfoDto = new ConsignorInfoDto();
        AddressInfoDto addressInfoDto = getAddressInfoDto(externalJdAddress,orderModel.getOrderBusinessIdentity().getBusinessScene());
        consignorInfoDto.setAddressInfoDto(addressInfoDto);
        modelCreator.setConsignorInfo(consignorInfoDto);
        orderModel.complement().complementConsignorAddressGis(this, modelCreator);
    }

    /**
     * 功能: 补充收件人Gis解析后信息
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/12 11:42 下午
     */
    public void complementConsigneeGisAddress(ExpressOrderModel orderModel, JdAddressFacadeResponse.Address externalJdAddress) {
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
        AddressInfoDto addressInfoDto = getAddressInfoDto(externalJdAddress,orderModel.getOrderBusinessIdentity().getBusinessScene());
        consigneeInfoDto.setAddressInfoDto(addressInfoDto);
        modelCreator.setConsigneeInfo(consigneeInfoDto);
        orderModel.getComplementModel().complementConsigneeAddressGis(this, modelCreator);
    }

    /**
     * 功能: 补全地址信息
     *
     * @param:
     * @return:
     * @throw:
     * @description: 不区分发货地址还是收货地址
     * @author: liufarui
     * @date: 2021/6/12 11:36 下午
     */
    private AddressInfoDto getAddressInfoDto(JdAddressFacadeResponse.Address externalJdAddress,String businessScene) {
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        addressInfoDto.setProvinceNoGis(externalJdAddress.getProvinceNo());
        addressInfoDto.setProvinceNameGis(externalJdAddress.getProvinceName());
        addressInfoDto.setCityNoGis(externalJdAddress.getCityNo());
        addressInfoDto.setCityNameGis(externalJdAddress.getCityName());
        addressInfoDto.setCountyNoGis(externalJdAddress.getCountyNo());
        addressInfoDto.setCountyNameGis(externalJdAddress.getCountyName());
        addressInfoDto.setTownNoGis(externalJdAddress.getTownNo());
        addressInfoDto.setTownNameGis(externalJdAddress.getTownName());
        if(BusinessSceneEnum.MODIFY.getCode().equals(businessScene)){
            //修改场景，如果gis解析后的第四级地址为null,需要赋值空字符串，做清空操作
            if(externalJdAddress.getTownNo() == null){
                addressInfoDto.setTownNoGis("");
            }
            if (externalJdAddress.getTownName() == null ) {
                addressInfoDto.setTownNameGis("");
            }
        }

        // gis解析精准度
        addressInfoDto.setPreciseGis(externalJdAddress.getPreciseGis());
        // gis解析经纬度有值以gis为准，否则用下单的
        if(StringUtils.isNotBlank(externalJdAddress.getLatitude())){
            addressInfoDto.setLatitude(externalJdAddress.getLatitude());
        }
        if(StringUtils.isNotBlank(externalJdAddress.getLongitude())){
            addressInfoDto.setLongitude(externalJdAddress.getLongitude());
        }
        addressInfoDto.setAddressGis(externalJdAddress.getAddress());
        //地址嵌套等级
        addressInfoDto.setConflictLevel(externalJdAddress.getConflictLevel());
        //地址解析来源
        addressInfoDto.setAddressSource(AddressSourceEnum.GIS.getCode());
        //行政区编码
        addressInfoDto.setRegionNo(externalJdAddress.getRegionNo());
        return addressInfoDto;
    }
}
