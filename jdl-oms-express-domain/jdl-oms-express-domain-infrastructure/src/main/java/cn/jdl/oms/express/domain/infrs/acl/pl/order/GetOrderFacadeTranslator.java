package cn.jdl.oms.express.domain.infrs.acl.pl.order;

import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.AttachmentInfo;
import cn.jdl.oms.core.model.FenceInfo;
import cn.jdl.oms.core.model.WarehouseInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.DimensionInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.dto.record.ModifyRecordDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.AttachmentFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.CostFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.CustomsFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.MoneyFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.OrderMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ActivityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AgreementFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AttachmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessIdentityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessSolutionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CargoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ChannelFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsigneeFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsignorFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.DiscountFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceDetailFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FulfillmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.GoodsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.InterceptFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.MarketEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.MoneyFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PointsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PromotionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.QuantityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.RefOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ReturnInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ShipmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.TicketFacade;
import cn.jdl.oms.express.domain.infrs.ohs.locals.security.TdeAcl;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.LengthTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupCodeCreateTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderSubType;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.dict.VolumeTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightTypeEnum;
import cn.jdl.oms.express.domain.vo.Attachment;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Dimension;
import cn.jdl.oms.express.domain.vo.LengthInfo;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Quantity;
import cn.jdl.oms.express.domain.vo.Serial;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.search.dto.OrderModifyInfo;
import com.google.common.collect.Lists;
import com.jdl.cp.core.ts.entity.AdditionPriceInfo;
import com.jdl.cp.core.ts.entity.ConsignBaseInfo;
import cn.jdl.oms.express.shared.common.dict.NodeUsageEnum;
import com.jdl.cp.core.ts.entity.CustomsInfo;
import com.jdl.cp.core.ts.entity.InterceptInfo;
import com.jdl.cp.core.ts.entity.FulfillmentInfo;
import com.jdl.cp.core.ts.entity.ModifyInfo;
import com.jdl.cp.core.ts.entity.Order;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName GetOrderFacadeTranslator
 * @Description 防腐层入参、出参转换
 * <AUTHOR>
 * @Date 2021/3/23 4:13 下午
 * @ModifyDate 2021/3/23 4:13 下午
 * @Version 1.0
 */
@Translator
public class GetOrderFacadeTranslator {

    /**
     * 加、解密服务
     */
    @Resource
    private TdeAcl tdeAcl;

    /**
     * 获取详情请求转换
     *
     * @param context
     * @return
     */
    public GetOrderFacadeRequest toGetOrderFacadeRequest(ExpressOrderContext context) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        //逆向单、改址单查询原单信息(改址单取消时取当前model的订单号，操作人是订单中台)
        if (context.getOrderModel().getOrderType() == OrderTypeEnum.RETURN_ORDER
                || (context.getOrderModel().getOrderType() == OrderTypeEnum.READDRESS && !SystemCallerEnum.EXPRESS_OMS.getCode().equals(context.getOrderModel().getOperator()))) {
            facadeRequest.setOrderNo(context.getOrderModel().getRefOrderInfoDelegate().getOriginalNo());
        } else {
            facadeRequest.setOrderNo(context.getOrderModel().orderNo());
            if (StringUtils.isBlank(context.getOrderModel().orderNo())
                    && StringUtils.isNotBlank(context.getOrderModel().getCustomOrderNo())) {
                facadeRequest.setCustomOrderNo(context.getOrderModel().getCustomOrderNo());
            }
        }
        return facadeRequest;
    }

    /**
     * 获取详情请求转换
     *
     * @param context
     * @return
     */
    public GetOrderFacadeRequest toEcpGetOrderFacadeRequest(ExpressOrderContext context) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setCustomOrderNo(context.getOrderModel().getCustomOrderNo());
        return facadeRequest;
    }

    /**
     * 获取详情请求转换
     */
    public GetOrderFacadeRequest toGetOrderFacadeRequest(String customOrderNo) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setCustomOrderNo(customOrderNo);
        return facadeRequest;
    }

    /**
     * 根据订单号获取详情请求转换
     */
    public GetOrderFacadeRequest toGetOrderFacadeRequestByOrderNo(String orderNo) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setOrderNo(orderNo);
        return facadeRequest;
    }

    /**
     * 将数据库的订单详情转换成防腐层出参
     */
    public GetOrderFacadeResponse toGetOrderFacadeResponse(Order order) throws Exception {
        if (order == null) {
            return null;
        }
        GetOrderFacadeResponse facadeResponse = new GetOrderFacadeResponse();
        if (order != null) {
            facadeResponse.setBusinessIdentity(this.toOrderBusinessIdentity(order));
            facadeResponse.setOrderNo(order.getOrderNo());
            facadeResponse.setParentOrderNo(order.getParentOrderNo());
            facadeResponse.setTenantId(order.getTenantId());
            facadeResponse.setCustomOrderNo(order.getCustomOrderNo());
            //facadeResponse.setOrderId(order.getOrderId());
            facadeResponse.setOrderType(String.valueOf(order.getOrderType()));
            facadeResponse.setOrderSubType(order.getOrderSubType());
            facadeResponse.setOrderStatus(order.getOrderStatus());
            facadeResponse.setCustomOrderNo(order.getCustomOrderNo());
            facadeResponse.setOrderStatusCustom(order.getOrderStatusCustom());
            facadeResponse.setExtendStatus(order.getOrderExtendStatus());
            facadeResponse.setCancelStatus(order.getCancelStatus() != null ?
                    order.getCancelStatus().intValue() : null);
            facadeResponse.setInitiatorType(order.getOrderCreatorNoType());
            facadeResponse.setOperator(order.getOrderCreatorNo());
            facadeResponse.setOperateTime(order.getReceivedTime());
            facadeResponse.setSyncSource(order.getSyncSource());
            if (order.getExtendInfo() != null) {
                facadeResponse.setExtendProps(order.getExtendInfo().getExt());
                if (MapUtils.isEmpty(facadeResponse.getExtendProps())){
                    facadeResponse.setExtendProps(new HashMap<>());
                }
                if (facadeResponse.getExtendProps().containsKey(AttachmentKeyEnum.CREATOR_PHONE.getKey())) {
                    String creatorPhone = facadeResponse.getExtendProps().get(AttachmentKeyEnum.CREATOR_PHONE.getKey());
                    facadeResponse.getExtendProps().put(AttachmentKeyEnum.CREATOR_PHONE.getKey(), tdeAcl.decrypt(creatorPhone));
                }
                if (null != order.getExtendInfo().getModifyInfoSequence()){
                    facadeResponse.getExtendProps().put(OrderConstants.MODIFY_INFO_SEQUENCE, String.valueOf(order.getExtendInfo().getModifyInfoSequence()));
                }
                //订单数据环境标识
                facadeResponse.setEnvironment(order.getExtendInfo().getEnvMark());
            }
            //交易客户信息
            facadeResponse.setCustomer(this.toCustomerFacade(order));
            //渠道信息
            facadeResponse.setChannel(this.toChannelFacade(order));
            //产品信息
            facadeResponse.setProducts(this.toProductFacadeList(order.getServiceProductInfoList()));
            //发货人信息
            facadeResponse.setConsignor(this.toConsignorFacade(order));
            //收货人信息
            facadeResponse.setConsignee(this.toConsigneeFacade(order));
            //货品信息
            facadeResponse.setCargos(this.toCargoFacadeList(order.getCargoInfoList()));
            //商品信息
            facadeResponse.setGoodsList(this.toGoodsFacadeList(order.getGoodsInfoList()));
            //配送信息
            facadeResponse.setShipment(this.toShipmentFacade(order.getShipmentInfo()));
            //财务信息
            facadeResponse.setFinance(this.toFinanceFacade(order.getTransactionCostInfo(), order.getDeductionInfoList()));
            //营销信息
            facadeResponse.setPromotion(this.toPromotionFacade(order.getMarketInfoList()));
            if (BusinessUnitEnum.CN_JDL_TC.getCode().equals(order.getBusinessUnit())) {
                facadeResponse.setRefOrders(this.toRefOrderFacadeList(order.getRelationInfoList()));
            } else {
                //交易关联单
                facadeResponse.setRefOrders(this.toRefOrderFacadeList(order.getRefOrderInfo()));
            }

            facadeResponse.setRemark(order.getRemark());
            facadeResponse.setHiddenMark(order.getHiddenMark());
            //facadeResponse.setOrderId(order.getOrderId());
            facadeResponse.setYn(order.getYn());
            facadeResponse.setInterceptType(order.getInterceptType());
            facadeResponse.setRecheckVolume(toVolume(order.getRecheckVolume(), order.getRecheckVolumeUnit()));
            facadeResponse.setRecheckWeight(toWeight(order.getRecheckWeight(), order.getRecheckWeightUnit()));
            //解决方案
            facadeResponse.setBusinessSolutionFacade(toBusinessSolutionFacade(order.getSolution()));
            // 订单标识
            facadeResponse.setOrderSign(order.getOrderSign());
            // 协议信息
            facadeResponse.setAgreementFacades(this.toAgreementFacade(order.getAgreementInfoList()));
            // 关联单扩展单据类型
            if (null != order.getRefOrderInfo()) {
                facadeResponse.setExtendRefOrder(order.getRefOrderInfo().getExt());
            }
            facadeResponse.setReturnInfoFacade(this.toReturnInfoFacade(order.getReturnInfo()));
            // 改址状态
            facadeResponse.setReaddressStatus(order.getReaddressStatus());
            // 拦截信息
            facadeResponse.setInterceptFacade(toInterceptFacade(order.getInterceptInfo()));

            facadeResponse.setFulfillmentFacade(this.toFulfillmentFacade(order.getFulfillmentInfo()));
            // 跨境报关信息
            facadeResponse.setCustomsFacade(toCustomsFacade(order.getCustomsInfo()));
            // 附件列表
            facadeResponse.setAttachmentFacades(toAttachmentFacades(order.getAttachmentInfoList()));
            // 订单总净重
            facadeResponse.setOrderNetWeight(toWeight(order.getOrderNetWeight(), order.getWeightUnit()));
            // 订单总毛重
            facadeResponse.setOrderWeight(toWeight(order.getOrderWeight(), order.getWeightUnit()));
            // 订单总体积
            facadeResponse.setOrderVolume(toVolume(order.getOrderVolume(), order.getVolumeUnit()));
            //是否百川
            facadeResponse.setSyncSource(order.getSyncSource());
            //操作记录列表
            facadeResponse.setModifyRecordDtos(toModifyRecordDtos(order.getModifyInfoList()));
            //弃货状态
            facadeResponse.setDiscardStatus(order.getDiscardStatus());
        }
        return facadeResponse;
    }

    /**
     * 操作记录
     * @param modifyInfoList
     * @return
     */
    private List<ModifyRecordDto> toModifyRecordDtos(List<ModifyInfo> modifyInfoList) {
        List<ModifyRecordDto> modifyRecordDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(modifyInfoList)){
            modifyInfoList.forEach(modifyInfo -> {
                ModifyRecordDto modifyRecordDto = new ModifyRecordDto();
                modifyRecordDto.setTenantId(modifyInfo.getTenantId());
                modifyRecordDto.setModifyRecordNo(modifyInfo.getModifyNo());
                modifyRecordDto.setModifyRecordSequence(modifyInfo.getModifySequence());
                modifyRecordDto.setModifyRecordType(modifyInfo.getModifyType());
                modifyRecordDto.setModifyRecordStatus(modifyInfo.getModifyStatus());
                modifyRecordDto.setOperator(modifyInfo.getOperator());
                modifyRecordDto.setOperateTime(modifyInfo.getOperateTime());
                modifyRecordDto.setOrderNo(modifyInfo.getOrderNo());
                modifyRecordDto.setModifyRecordMsg(modifyInfo.getModifyMsg());
                modifyRecordDto.setExtendProps(modifyInfo.getExtendProps());
                modifyRecordDtos.add(modifyRecordDto);
            });
        }
        return modifyRecordDtos;
    }

    public List<GetOrderFacadeResponse> toGetOrderFacadeResponseList(List<Order> orderList) throws Exception {
        List<GetOrderFacadeResponse> facadeResponseList = new ArrayList<>();
        for (Order order : orderList) {
            GetOrderFacadeResponse orderFacadeResponse = toGetOrderFacadeResponse(order);
            facadeResponseList.add(orderFacadeResponse);
        }
        return facadeResponseList;
    }

    private ReturnInfoFacade toReturnInfoFacade(Order.ReturnInfo returnInfo) throws Exception {
        if(returnInfo == null){
            return null;
        }
        ReturnInfoFacade returnInfoFacade = new ReturnInfoFacade();
        returnInfoFacade.setReturnType(returnInfo.getReturnType());
        ConsignBaseInfo consignBaseInfo = returnInfo.getConsigneeInfo();
        ConsigneeFacade consigneeFacade = new ConsigneeFacade();
        if (consignBaseInfo != null) {
            consigneeFacade.setConsigneeName(tdeAcl.decrypt(consignBaseInfo.getName()));
            consigneeFacade.setConsigneeMobile(tdeAcl.decrypt(consignBaseInfo.getMobile()));
            consigneeFacade.setConsigneePhone(tdeAcl.decrypt(consignBaseInfo.getPhone()));
        }
        com.jdl.cp.core.ts.entity.AddressInfo addressInfoEntity = returnInfo.getAddressInfo();
        if (addressInfoEntity != null){
            AddressInfo addressInfo = new AddressInfo();
            addressInfo.setProvinceNo(addressInfoEntity.getProvinceNo());
            addressInfo.setProvinceName(addressInfoEntity.getProvinceName());
            addressInfo.setCityNo(addressInfoEntity.getCityNo());
            addressInfo.setCityName(addressInfoEntity.getCityName());
            addressInfo.setCountyNo(addressInfoEntity.getCountyNo());
            addressInfo.setCountyName(addressInfoEntity.getCountyName());
            addressInfo.setTownNo(addressInfoEntity.getTownNo());
            addressInfo.setTownName(addressInfoEntity.getTownName());
            addressInfo.setAddress(tdeAcl.decrypt(addressInfoEntity.getAddress()));
            addressInfo.setCoordinateType(addressInfoEntity.getCoordinateType());
            addressInfo.setLongitude(addressInfoEntity.getLongitude() != null
                    ? String.valueOf(addressInfoEntity.getLongitude()) : null);
            addressInfo.setLatitude(addressInfoEntity.getLatitude() != null
                    ? String.valueOf(addressInfoEntity.getLatitude()) : null);
            addressInfo.setProvinceNoGis(addressInfoEntity.getProvinceNoGis());
            addressInfo.setProvinceNameGis(addressInfoEntity.getProvinceNameGis());
            addressInfo.setCityNoGis(addressInfoEntity.getCityNoGis());
            addressInfo.setCityNameGis(addressInfoEntity.getCityNameGis());
            addressInfo.setCountyNoGis(addressInfoEntity.getCountyNoGis());
            addressInfo.setCountyNameGis(addressInfoEntity.getCountyNameGis());
            addressInfo.setTownNoGis(addressInfoEntity.getTownNoGis());
            addressInfo.setTownNameGis(addressInfoEntity.getTownNameGis());
            addressInfo.setAddressGis(tdeAcl.decrypt(addressInfoEntity.getAddressGis()));
            addressInfo.setFenceTrusted(addressInfoEntity.getFenceTrusted());
            addressInfo.setFenceInfos(this.toFenceInfos(addressInfoEntity.getFenceInfos()));
            consigneeFacade.setAddress(addressInfo);
        }
        returnInfoFacade.setConsigneeFacade(consigneeFacade);
        return returnInfoFacade;
    }

    /**
     * 商品信息转换
     *
     * @param list
     * @return
     */
    private List<GoodsFacade> toGoodsFacadeList(List<Order.GoodsInfo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<GoodsFacade> goodsFacadeList = new ArrayList<>(list.size());
        list.forEach(goodsInfo -> {
            GoodsFacade goodsFacade = new GoodsFacade();
            goodsFacade.setGoodsNo(goodsInfo.getGoodsNo());
            goodsFacade.setGoodsName(goodsInfo.getGoodsName());
            Quantity goodsQuantity = new Quantity();
            if(null != goodsInfo.getGoodsQuantity()){
                goodsQuantity.setValue(new BigDecimal(goodsInfo.getGoodsQuantity()));
            }
            goodsQuantity.setUnit(goodsInfo.getQuantityUnit());
            goodsFacade.setGoodsQuantity(goodsQuantity);
            goodsFacade.setGoodsUniqueCode(goodsInfo.getUniqueMark());

            //货品重量
            WeightInfoDto weightInfoDto = new WeightInfoDto();
            weightInfoDto.setValue(goodsInfo.getGoodsWeight());
            weightInfoDto.setUnit(WeightTypeEnum.of(goodsInfo.getWeightUnit()));
            goodsFacade.setGoodsWeight(weightInfoDto);

            //货品体积
            VolumeInfoDto volumeInfoDto = new VolumeInfoDto();
            volumeInfoDto.setValue(goodsInfo.getGoodsVolume());
            volumeInfoDto.setUnit(VolumeTypeEnum.of(goodsInfo.getVolumeUnit()));
            goodsFacade.setGoodsVolume(volumeInfoDto);

            //货品三维
            DimensionInfoDto dimensionInfoDto = new DimensionInfoDto();
            dimensionInfoDto.setHeight(goodsInfo.getGoodsHeight());
            dimensionInfoDto.setLength(goodsInfo.getGoodsLength());
            dimensionInfoDto.setWidth(goodsInfo.getGoodsWidth());
            dimensionInfoDto.setUnit(LengthTypeEnum.of(goodsInfo.getDimensionUnit()));
            goodsFacade.setGoodsDimension(dimensionInfoDto);

            //促销信息
            Optional.ofNullable(goodsInfo.getSalesInfos()).ifPresent(salesInfos -> {
                goodsFacade.setSalesInfos(salesInfos);
            });

            //商品增值服务
            if (CollectionUtils.isNotEmpty(goodsInfo.getServiceProductInfoList())) {
                List<Product> products = goodsInfo.getServiceProductInfoList().stream().map(serviceProductInfo -> {
                    Product product = new Product();
                    product.setProductNo(serviceProductInfo.getProductNo());
                    product.setProductName(serviceProductInfo.getProductName());
                    product.setParentNo(serviceProductInfo.getParentNo());
                    product.setProductType(serviceProductInfo.getProductType().intValue());
                    product.setExtendProps(serviceProductInfo.getExt());
                    product.setProductAttrs(serviceProductInfo.getAttrs());
                    product.setProductExecutionResult(serviceProductInfo.getProductExecutionResult());
                    return product;
                }).collect(Collectors.toList());
                goodsFacade.setGoodsProductInfos(products);
            }
            goodsFacade.setExtendProps(goodsInfo.getExt());
            // 序列号
            goodsFacade.setGoodsSerialInfos(OrderMapper.INSTANCE.toSerialList(goodsInfo.getGoodsCodesInfoList()));

            //商品净重
            WeightInfoDto netWeight = new WeightInfoDto();
            netWeight.setValue(goodsInfo.getNetWeight());
            netWeight.setUnit(WeightTypeEnum.of(goodsInfo.getWeightUnit()));
            goodsFacade.setNetWeight(netWeight);

            goodsFacadeList.add(goodsFacade);
        });
        return goodsFacadeList;
    }

    /**
     * 业务身份信息转换
     *
     * @param order
     * @return
     */
    private BusinessIdentityFacade toOrderBusinessIdentity(Order order) {
        BusinessIdentityFacade businessIdentity = new BusinessIdentityFacade();
        businessIdentity.setBusinessUnit(order.getBusinessUnit());
        businessIdentity.setBusinessType(order.getBusinessType());
        businessIdentity.setBusinessStrategy(order.getBusinessStrategy());
        businessIdentity.setFulfillmentUnit(order.getFulfillmentUnit());
        return businessIdentity;
    }

    /**
     * 防腐层交易客户信息转换
     *
     * @param order
     * @return
     */
    private CustomerFacade toCustomerFacade(Order order) {
        CustomerFacade customer = new CustomerFacade();
        if (order != null) {
            if (BusinessUnitEnum.CN_JDL_TC.getCode().equals(order.getBusinessUnit())) {
                customer.setAccountNo(order.getAccountExtend1());
                customer.setAccount2No(order.getFulfillmentAccountNo());
                customer.setAccount3No(order.getAccountExtend2());
                customer.setAccountName(order.getAccountExtendName1());
            } else {
                customer.setAccountNo(order.getFulfillmentAccountNo());
                customer.setAccountId(order.getFulfillmentAccountId());
                customer.setAccount2No(order.getAccountExtend1());
                customer.setAccount3No(order.getAccountExtend2());
                customer.setAccountName(order.getFulfillmentAccountName());
                customer.setAccount2Name(order.getAccountExtendName1());
                customer.setAccount3Name(order.getAccountExtendName2());
            }
            if(null != order.getCustomerChannelInfo()){
                customer.setStorePin(order.getCustomerChannelInfo().getStorePin());
                customer.setCustomerNo(order.getCustomerChannelInfo().getCustomerNo());
            }
        }
        return customer;
    }

    /**
     * 防腐层渠道信息转换
     *
     * @param order
     * @return
     */
    private ChannelFacade toChannelFacade(Order order) {
        ChannelFacade channelFacade = new ChannelFacade();
        Order.CustomerChannelInfo channelInfo = order.getCustomerChannelInfo();
        channelFacade.setCustomerOrderNo(order.getCustomerOrderNo());
        if (channelInfo != null) {
            channelFacade.setSystemCaller(channelInfo.getChannelReceiveSource());
            channelFacade.setSystemSubCaller(channelInfo.getChannelReceiveSubSource());
            channelFacade.setChannelOrderNo(channelInfo.getChannelOrderNo());
            channelFacade.setChannelCustomerNo(channelInfo.getChannelCustomerNo());
            channelFacade.setChannelNo(channelInfo.getChannelNo());
            channelFacade.setChannelNo(channelInfo.getChannelNo());
            channelFacade.setExtendProps(channelInfo.getExt());
        }
        return channelFacade;
    }

    /**
     * 防腐层产品信息转换
     *
     * @param productInfoList
     * @return
     */
    private List<ProductFacade> toProductFacadeList(List<Order.ServiceProductInfo> productInfoList) {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return Collections.emptyList();
        }
        return productInfoList.stream().map(productInfo -> {
            ProductFacade productFacade = new ProductFacade();
            productFacade.setProductNo(productInfo.getProductNo());
            productFacade.setProductName(productInfo.getProductName());
            productFacade.setProductType(productInfo.getProductType() != null
                    ? productInfo.getProductType().intValue() : null);
            productFacade.setParentNo(productInfo.getParentNo());
            productFacade.setOriginalProductNo(productInfo.getPreDegradeProductNo());
            productFacade.setOriginalProductName(productInfo.getPreDegradeProductName());
            productFacade.setProductAttrs(productInfo.getAttrs());
            productFacade.setExtendProps(productInfo.getExt());
            return productFacade;
        }).collect(Collectors.toList());
    }

    /**
     * 防腐层发货人信息转换
     *
     * @param order
     * @return
     */
    private ConsignorFacade toConsignorFacade(Order order) throws Exception {
        ConsignorFacade consignorFacade = new ConsignorFacade();
        if (order != null && order.getConsignInfo() != null) {
            Order.ConsignInfo consignorInfo = order.getConsignInfo();
            consignorFacade.setConsignorName(tdeAcl.decrypt(consignorInfo.getConsignorName()));
            consignorFacade.setConsignorMobile(tdeAcl.decrypt(consignorInfo.getConsignorMobile()));
            consignorFacade.setConsignorPhone(tdeAcl.decrypt(consignorInfo.getConsignorPhone()));
            consignorFacade.setConsignorZipCode(consignorInfo.getConsignorZipCode());
            consignorFacade.setConsignorCompany(consignorInfo.getConsignorCompany());
            consignorFacade.setConsignorNationNo(consignorInfo.getConsignorNationNo());
            consignorFacade.setConsignorNation(consignorInfo.getConsignorNation());
            consignorFacade.setConsignorIdType(consignorInfo.getConsignorIdType() != null
                    ? consignorInfo.getConsignorIdType().intValue() : null);
            consignorFacade.setConsignorIdNo(tdeAcl.decrypt(consignorInfo.getConsignorIdNo()));
            consignorFacade.setConsignorIdName(tdeAcl.decrypt(consignorInfo.getConsignorIdName()));
            consignorFacade.setAddress(this.toConsignorAddressInfo(consignorInfo, order.getConsignExtendInfo(), order.getOrderSign()));
            consignorFacade.setConsignorEnName(tdeAcl.decrypt(consignorInfo.getConsignorEnName()));
            consignorFacade.setExtendProps(consignorInfo.getConsignorExt());
        }

        // 获取发货信息的发货仓
        if(null != order && null != order.getLogisticsNodesList()){
            List<Order.LogisticsNodes> logisticsNodesList = order.getLogisticsNodesList();
            for (Order.LogisticsNodes logisticsNodes : logisticsNodesList) {
                // 获取发货
                if(logisticsNodes.getNodeUsage() == NodeUsageEnum.SEND.getCode()){
                    WarehouseInfo warehouseInfo = new WarehouseInfo();
                    warehouseInfo.setWarehouseNo(logisticsNodes.getNodeNo());
                    warehouseInfo.setWarehouseName(logisticsNodes.getNodeName());
                    // nodeType 对应 WarehouseInfo 的 warehouseSource 仓库来源
                    warehouseInfo.setWarehouseSource(logisticsNodes.getNodeType());
                    // 京东仓库编码
                    warehouseInfo.setActualWarehouseNo(logisticsNodes.getActualNodeNo());
                    consignorFacade.setCustomerWarehouse(warehouseInfo);
                }
            }
        }
        return consignorFacade;
    }

    /**
     * 防腐层收货人信息转换
     *
     * @param order
     * @return
     */
    private ConsigneeFacade toConsigneeFacade(Order order) throws Exception {
        ConsigneeFacade consigneeFacade = new ConsigneeFacade();
        if (order != null && order.getConsignInfo() != null) {
            Order.ConsignInfo consigneeInfo = order.getConsignInfo();
            consigneeFacade.setConsigneeName(tdeAcl.decrypt(consigneeInfo.getConsigneeName()));
            consigneeFacade.setConsigneeMobile(tdeAcl.decrypt(consigneeInfo.getConsigneeMobile()));
            consigneeFacade.setConsigneePhone(tdeAcl.decrypt(consigneeInfo.getConsigneePhone()));
            consigneeFacade.setConsigneeZipCode(consigneeInfo.getConsigneeZipCode());
            consigneeFacade.setConsigneeCompany(consigneeInfo.getConsigneeCompany());
            //收货人国家编号
            consigneeFacade.setConsigneeNationNo(consigneeInfo.getConsigneeNationNo());
            //收货人国家名称
            consigneeFacade.setConsigneeNation(consigneeInfo.getConsigneeNation());
            //收货人证件类型
            consigneeFacade.setConsigneeIdType(consigneeInfo.getConsigneeIdType() != null
                    ? consigneeInfo.getConsigneeIdType().intValue() : null);
            //收货人证件号码
            consigneeFacade.setConsigneeIdNo(tdeAcl.decrypt(consigneeInfo.getConsigneeIdNo()));
            //收货人证件姓名
            consigneeFacade.setConsigneeIdName(tdeAcl.decrypt(consigneeInfo.getConsigneeIdName()));
            consigneeFacade.setAddress(this.toConsigneeAddressInfo(consigneeInfo, order.getConsignExtendInfo(), order.getOrderSign()));
            //收寄件人扩展字段兼容逻辑处理,历史数据扩展信息是放在consigneeInfo.getExt()，新数据是放在consigneeInfo.getConsigneeExt()
            Map<String, String> ext = consigneeInfo.getExt();
            if (ext == null) {
                ext = new HashMap<>();
            }
            if (MapUtils.isNotEmpty(consigneeInfo.getConsigneeExt())) {
                ext.putAll(consigneeInfo.getConsigneeExt());
            }
            consigneeFacade.setExtendProps(ext);
        }
        if (order != null && order.getConsignExtendInfo() != null) {
            //收件人邮箱
            consigneeFacade.setConsigneeEmail(order.getConsignExtendInfo().getConsigneeEmail());
        }

        // 获取收货信息的收货仓
        if(null != order && null != order.getLogisticsNodesList()){
            List<Order.LogisticsNodes> logisticsNodesList = order.getLogisticsNodesList();
            for (Order.LogisticsNodes logisticsNodes : logisticsNodesList) {
                // 获取收货
                if(logisticsNodes.getNodeUsage() == NodeUsageEnum.RECEIVE.getCode()){
                    WarehouseInfo warehouseInfo = new WarehouseInfo();
                    warehouseInfo.setWarehouseNo(logisticsNodes.getNodeNo());
                    warehouseInfo.setWarehouseName(logisticsNodes.getNodeName());
                    // nodeType 对应 WarehouseInfo 的 warehouseSource 仓库来源
                    warehouseInfo.setWarehouseSource(logisticsNodes.getNodeType());
                    // 京东仓库编码
                    warehouseInfo.setActualWarehouseNo(logisticsNodes.getActualNodeNo());
                    consigneeFacade.setReceiveWarehouse(warehouseInfo);
                }
            }
        }

        return consigneeFacade;
    }

    /**
     * 防腐层货品信息转换
     *
     * @param cargoInfoList
     * @return
     */
    private List<CargoFacade> toCargoFacadeList(List<Order.CargoInfo> cargoInfoList) {
        if (CollectionUtils.isEmpty(cargoInfoList)) {
            return Collections.emptyList();
        }
        return cargoInfoList.stream().map(cargoInfo -> {
            CargoFacade cargoFacade = new CargoFacade();
            cargoFacade.setCargoName(cargoInfo.getCargoName());
            cargoFacade.setCargoNo(cargoInfo.getCargoNo());
            cargoFacade.setCargoType(cargoInfo.getCargoType());
            cargoFacade.setCargoVolume(this.toVolume(cargoInfo.getCargoVolume(), cargoInfo.getVolumeUnit()));
            //货品尺寸：长、宽、高
            cargoFacade.setCargoDimension(this.toDimension(cargoInfo.getCargoLength(), cargoInfo.getCargoWidth(),
                    cargoInfo.getCargoHeight(), cargoInfo.getDimensionUnit()));
            cargoFacade.setCargoWeight(toWeight(cargoInfo.getCargoWeight(), cargoInfo.getWeightUnit()));
            //TODO
            if (cargoInfo.getCargoQuantity() != null) {
                Quantity cargoQuantity = new Quantity();
                cargoQuantity.setValue(BigDecimal.valueOf(cargoInfo.getCargoQuantity()));
                cargoQuantity.setUnit(cargoInfo.getCargoUnit());//B2C改址新单查原单单位，查不到就会导致改址新单接单校验不通过
                cargoFacade.setCargoQuantity(cargoQuantity);
            }
            //货品内件数量
            if (cargoInfo.getCargoInnerQuantity() != null) {
                cargoFacade.setCargoInnerQuantity(BigDecimal.valueOf(cargoInfo.getCargoInnerQuantity()));
            }
            cargoFacade.setCargoRemark(cargoInfo.getCargoRemark());
            //清真易污染标识
            cargoFacade.setPolluteSign(cargoInfo.getPolluteSign() != null ? cargoInfo.getPolluteSign().intValue() : null);
            //附件赋值
            List<Order.AttachmentInfo> attachmentInfoList = cargoInfo.getCargoAttachmentInfos();
            if (attachmentInfoList != null && !attachmentInfoList.isEmpty()) {
                List<AttachmentInfo> attachmentFaceInfoList = new ArrayList<>();
                for (Order.AttachmentInfo attachmentInfo : attachmentInfoList) {
                    AttachmentInfo attachmentFaceInfo = new AttachmentInfo();
                    attachmentFaceInfo.setAttachmentUrl(attachmentInfo.getAttachmentUrl());//附件路径
                    attachmentFaceInfo.setAttachmentName(attachmentInfo.getAttachmentName());//附件名称
                    attachmentFaceInfo.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());//附件排序
                    attachmentFaceInfo.setAttachmentRemark(attachmentInfo.getAttachmentRemark());//附件备注
                    attachmentFaceInfo.setAttachmentType(attachmentInfo.getAttachmentType());//附件类型
                    attachmentFaceInfo.setAttachmentDocType(attachmentInfo.getAttachmentDocType());//附件文档类型
                    attachmentFaceInfoList.add(attachmentFaceInfo);
                }
                cargoFacade.setAttachmentInfos(attachmentFaceInfoList);
            }
            //是否易损
            cargoFacade.setCargoVulnerable(cargoInfo.getCargoVulnerable());
            //货品标识
            cargoFacade.setCargoSign(cargoInfo.getCargoSign());
            cargoFacade.setExtendProps(cargoInfo.getExt());
            cargoFacade.setSerialInfos(toSerialList(cargoInfo.getCargoCodesInfos()));
            //货品增值服务
            if (CollectionUtils.isNotEmpty(cargoInfo.getServiceProductInfoList())) {
                List<Product> products = cargoInfo.getServiceProductInfoList().stream().map(serviceProductInfo -> {
                    Product product = new Product();
                    product.setProductNo(serviceProductInfo.getProductNo());
                    product.setProductName(serviceProductInfo.getProductName());
                    product.setParentNo(serviceProductInfo.getParentNo());
                    product.setProductType(serviceProductInfo.getProductType().intValue());
                    product.setExtendProps(serviceProductInfo.getExt());
                    product.setProductAttrs(serviceProductInfo.getAttrs());
                    return product;
                }).collect(Collectors.toList());
                cargoFacade.setCargoProductInfos(products);
            }

            return cargoFacade;
        }).collect(Collectors.toList());
    }

    /**
     * 配送信息
     *
     * @param shipmentInfo
     * @return
     */
    private ShipmentFacade toShipmentFacade(Order.ShipmentInfo shipmentInfo) {
        ShipmentFacade shipmentFacade = new ShipmentFacade();
        if (shipmentInfo != null) {
            //预计送达时间
            shipmentFacade.setPlanDeliveryTime(shipmentInfo.getPlanDeliveryTime());
            //预计送达时间段
            shipmentFacade.setPlanDeliveryPeriod(shipmentInfo.getPlanDeliveryPeriod());
            //预约送达开始时间
            shipmentFacade.setExpectDeliveryStartTime(shipmentInfo.getExpectDeliveryStartTime());
            //预约送达结束时间
            shipmentFacade.setExpectDeliveryEndTime(shipmentInfo.getExpectDeliveryEndTime());
            //期望提货开始时间
            shipmentFacade.setExpectPickupStartTime(shipmentInfo.getExpectPickupStartTime());
            //期望提货结束时间
            shipmentFacade.setExpectPickupEndTime(shipmentInfo.getExpectPickupEndTime());
            //揽收方式
            shipmentFacade.setPickupType(shipmentInfo.getPickupType() != null ? shipmentInfo.getPickupType().intValue() : null);
            //派送方式
            shipmentFacade.setDeliveryType(shipmentInfo.getDeliveryType() != null ? shipmentInfo.getDeliveryType().intValue() : null);
            //车辆型号
            shipmentFacade.setVehicleType(shipmentInfo.getVehicleType());
            //温层要求
            shipmentFacade.setWarmLayer(shipmentInfo.getDeliveryTempLayer());
            //始发站编码
            shipmentFacade.setStartStationNo(shipmentInfo.getFirstStationNo());
            //始发站名称
            shipmentFacade.setStartStationName(shipmentInfo.getFirstStationName());
            //始发站点类型
            shipmentFacade.setStartStationType(shipmentInfo.getStartStationType() != null
                    ? shipmentInfo.getStartStationType() : null);
            //目的站编码
            shipmentFacade.setEndStationNo(shipmentInfo.getLastStationNo());
            //目的站名称
            shipmentFacade.setEndStationName(shipmentInfo.getLastStationName());
            //目的站点类型
            shipmentFacade.setEndStationType(shipmentInfo.getEndStationType() != null
                    ? shipmentInfo.getEndStationType() : null);
            //物流中转类型
            shipmentFacade.setTransitType(shipmentInfo.getTransitType());
            //发货仓库编码
            shipmentFacade.setWarehouseNo(shipmentInfo.getWarehouseNo());
            //收货仓库编码
            shipmentFacade.setReceiveWarehouseNo(shipmentInfo.getReceiveWarehouseNo());
            //取件员姓名
            shipmentFacade.setCollector(shipmentInfo.getCollector());
            //运输类型
            shipmentFacade.setTransportType(shipmentInfo.getTransportType() != null
                    ? shipmentInfo.getTransportType().intValue() : null);
            //无接触收货方式
            shipmentFacade.setContactlessType(shipmentInfo.getContactlessType() != null
                    ? shipmentInfo.getContactlessType().intValue() : null);
            //指定地点
            shipmentFacade.setAssignedAddress(shipmentInfo.getAssignedAddress());
            //预计接单时间
            shipmentFacade.setPlanReceiveTime(shipmentInfo.getPlanReceiveTime());
            //取件码
            shipmentFacade.setPickupCode(shipmentInfo.getPickupCode());
            //取件码生产方式
            shipmentFacade.setPickupCodeCreateType(PickupCodeCreateTypeEnum.of(shipmentInfo.getPickupCodeCreateType()));
            //服务要求
            shipmentFacade.setServiceRequirements(shipmentInfo.getServiceRequirements());
            //收货偏好
            shipmentFacade.setReceivingPreference(shipmentInfo.getReceivingPreference());
            shipmentFacade.setShipperNo(shipmentInfo.getShipperNo());
            shipmentFacade.setShipperName(shipmentInfo.getShipperName());
            shipmentFacade.setShipperType(StringUtils.isNotBlank(shipmentInfo.getShipperType()) ? Integer.valueOf(shipmentInfo.getShipperType()) : null);
            // 始发配送中心编码
            shipmentFacade.setStartCenterNo(shipmentInfo.getStartCenterNo());
            // 目的配送中心编码
            shipmentFacade.setEndCenterNo(shipmentInfo.getEndCenterNo());
            //扩展信息
            shipmentFacade.setExtendProps(shipmentInfo.getExt());
            shipmentFacade.setExpectDispatchStartTime(shipmentInfo.getExpectDispatchStartTime());
            shipmentFacade.setExpectDispatchEndTime(shipmentInfo.getExpectDispatchEndTime());
            shipmentFacade.setStartStationTypeL3(shipmentInfo.getStartStationTypeL3());
            shipmentFacade.setEndStationTypeL3(shipmentInfo.getEndStationTypeL3());
        }
        return shipmentFacade;
    }

    /**
     * 防腐层财务信息转换
     *
     * @param costInfo
     * @return
     */
    private FinanceFacade toFinanceFacade(Order.TransactionCostInfo costInfo, List<Order.DeductionInfo> deductionInfos) {
        FinanceFacade financeFacade = new FinanceFacade();
        if (costInfo != null) {
            //结算方式
            financeFacade.setSettlementType(costInfo.getSettlementType() != null
                    ? costInfo.getSettlementType().intValue() : null);
            //是否询价(1-不询价,2-询价)
            financeFacade.setEnquiryType(costInfo.getEnquiry() == null ? null : costInfo.getEnquiry().intValue());
            // 预估运费金额，精度3
            financeFacade.setEstimateAmount(this.toMoney(costInfo.getFreightAmount(),
                    costInfo.getCurrency()));
            // 预估财务信息
            financeFacade.setEstimateFinanceInfo(this.toEstimateFinance(costInfo.getEstimatedFinanceInfo()));
            //财务预占标识, 1：白条预授权
            financeFacade.setPreemptType(costInfo.getPreemptType());
            //1-先款支付；2-后款支付；
            financeFacade.setPaymentStage(costInfo.getPaymentStage());
            //折前金额
            financeFacade.setPreAmount(this.toMoney(costInfo.getPreAmount(), costInfo.getCurrency()));
            //折后金额
            financeFacade.setDiscountAmount(this.toMoney(costInfo.getDiscountAmount(), costInfo.getCurrency()));
            //总优惠金额
            financeFacade.setTotalDiscountAmount(this.toMoney(costInfo.getTotalDiscountAmount(),
                    costInfo.getCurrency()));
            //计费重量
            financeFacade.setBillingWeight(this.toWeight(costInfo.getBillingWeight(), costInfo.getWeightUnit()));
            //计费体积
            financeFacade.setBillingVolume(this.toVolume(costInfo.getBillingVolume(), costInfo.getVolumeUnit()));
            //计费模式
            financeFacade.setBillingMode(costInfo.getBillingMode());
            //支付账号
            financeFacade.setPaymentAccountNo(costInfo.getCustomerDeductionAccountNo());
            //收款机构
            financeFacade.setCollectionOrgNo(costInfo.getCollectionOrgNo());
            //支付方式
            financeFacade.setPayment(costInfo.getPaymentType() != null ? costInfo.getPaymentType().intValue() : null);
            //实际支付方式
            financeFacade.setActualPaymentType(costInfo.getActualPaymentType());
            //结算账号
            financeFacade.setSettlementAccountNo(costInfo.getSettlementAccountNo());
            //支付状态
            financeFacade.setPaymentStatus(costInfo.getPaymentStatus());
            //退款状态
            financeFacade.setRefundStatus(costInfo.getRefundStatus());
            //支付截止时间
            financeFacade.setPayDeadline(costInfo.getPayDeadline());
            // 费用明细
            financeFacade.setFinanceDetails(this.toFinanceDetailFacadeList(costInfo.getFinanceDetailInfoList(), costInfo.getCurrency()));
            //积分信息
            financeFacade.setPointsFacade(toPointsFacade(costInfo));
            //询价状态
            financeFacade.setEnquiryStatus(costInfo.getEnquiryStatus());
            //财务备注
            financeFacade.setRemark(costInfo.getRemark());
            //始发市id
            financeFacade.setEnquiryStartCityNo(costInfo.getEnquiryStartCityNo());
            //高峰期附加费时间
            financeFacade.setPeakPeriodTime(costInfo.getPeakPeriodTime());

            // 支付单号
            financeFacade.setPaymentNo(costInfo.getPaymentNo());
            // 支付时间
            financeFacade.setPaymentTime(costInfo.getPaymentTime());

            //收费要求信息
            if (CollectionUtils.isNotEmpty(costInfo.getCostInfos())) {
                List<CostInfo> costInfos = costInfo.getCostInfos().stream().map(cost -> {
                    CostInfo costInfoDto = new CostInfo();
                    costInfoDto.setCostNo(cost.getCostNo());
                    costInfoDto.setCostName(cost.getCostName());
                    costInfoDto.setChargingSource(cost.getChargingSource());
                    costInfoDto.setSettlementAccountNo(cost.getSettlementAccountNo());
                    costInfoDto.setAdditionPriceInfo(toAdditionPriceInfo(cost.getAdditionPriceInfo()));
                    costInfoDto.setExtendProps(cost.getExt());
                    return costInfoDto;
                }).collect(Collectors.toList());
                financeFacade.setCostInfos(costInfos);
            }
            //扩展属性
            financeFacade.setExtendProps(costInfo.getExt());

            //附加费用
            if (CollectionUtils.isNotEmpty(costInfo.getAttachFees())) {
                List<CostInfo> attachFees = costInfo.getAttachFees().stream().map(attachFee -> {
                    CostInfo costInfoDto = new CostInfo();
                    costInfoDto.setCostNo(attachFee.getCostNo());
                    costInfoDto.setCostName(attachFee.getCostName());
                    costInfoDto.setChargingSource(attachFee.getChargingSource());
                    costInfoDto.setSettlementAccountNo(attachFee.getSettlementAccountNo());
                    costInfoDto.setExtendProps(attachFee.getExt());
                    return costInfoDto;
                }).collect(Collectors.toList());
                financeFacade.setAttachFees(attachFees);
                // 预估税金
                financeFacade.setEstimatedTax(MoneyFacadeMapper.INSTANCE.toMoney(costInfo.getEstimatedTax()));

                // 真实税金
                financeFacade.setActualTax(MoneyFacadeMapper.INSTANCE.toMoney(costInfo.getActualTax()));

                // 费用支付状态归集
                Map<String, String> payStatusMap = costInfo.getPayStatusMap();
                if (MapUtils.isNotEmpty(payStatusMap)) {
                    financeFacade.setPayStatusMap(payStatusMap);
                }
            }

            // 支付人Pin集合
            financeFacade.setPayerPins(costInfo.getPayerPins());
            // 税金结算方式
            financeFacade.setTaxSettlementType(costInfo.getTaxSettlementType());
        }
        //抵扣信息转换
        if (CollectionUtils.isNotEmpty(deductionInfos)) {
            List<DeductionInfoDto> deductionInfoDtos = new ArrayList<>(deductionInfos.size());
            deductionInfos.forEach(info -> {
                DeductionInfoDto dto = new DeductionInfoDto();
                //抵扣编码
                dto.setDeductionNo(info.getDeductionNo());
                // 抵扣金额
                MoneyInfoDto moneyInfoDto = new MoneyInfoDto();
                moneyInfoDto.setAmount(info.getDeductionAmount());
                moneyInfoDto.setCurrencyCode(CurrencyCodeEnum.of(info.getCurrency()));
                dto.setDeductionAmount(moneyInfoDto);
                // 抵扣方
                dto.setDeductionOrg(info.getDeductionOrg());
                //扩展信息
                dto.setExtendProps(info.getExt());
                //向谁收
                dto.setChargingSource(info.getChargingSource());
                deductionInfoDtos.add(dto);
            });
            financeFacade.setDeductionInfoDtos(deductionInfoDtos);
        }

        // 预占金额
        financeFacade.setOccupyAmount(MoneyFacadeMapper.INSTANCE.toMoney(costInfo.getOccupyAmount()));
        // 预占模式
        financeFacade.setOccupyMode(costInfo.getOccupyMode());
        if (costInfo.getMultiPartiesTotalAmounts() != null) {
            List<CostInfo> costInfoList = new ArrayList<>();
            for(com.jdl.cp.core.ts.entity.CostInfo data :costInfo.getMultiPartiesTotalAmounts()){
                CostInfo vo = toCostInfoVo(data);
                if (vo != null) {
                    costInfoList.add(vo);
                }
            }
            financeFacade.setMultiPartiesTotalAmounts(costInfoList);
        }
        return financeFacade;
    }

    private cn.jdl.oms.express.domain.vo.AdditionPriceInfo toAdditionPriceInfo(AdditionPriceInfo additionPriceInfo) {
        cn.jdl.oms.express.domain.vo.AdditionPriceInfo info = new cn.jdl.oms.express.domain.vo.AdditionPriceInfo();
        if(Objects.isNull(additionPriceInfo)) {
            return info;
        }
        info.setFormulaNo(additionPriceInfo.getFormulaNo());
        info.setPriceItems(additionPriceInfo.getPriceItems());
        return info;
    }

    /**
     * 数据层对象转换为VO
     *
     * @param dataCost
     * @return
     */
    public CostInfo toCostInfoVo(com.jdl.cp.core.ts.entity.CostInfo dataCost) {
        if (dataCost == null) {
            return null;
        }
        CostInfo costInfo = new CostInfo();
        Money preAmount = new Money();
        preAmount.setAmount(dataCost.getPreAmount());
        preAmount.setCurrency(CurrencyCodeEnum.of(dataCost.getPreAmountCurrency()));
        costInfo.setPreAmount(preAmount);
        Money discountAmount = new Money();
        discountAmount.setAmount(dataCost.getDiscountAmount());
        discountAmount.setCurrency(CurrencyCodeEnum.of(dataCost.getDiscountAmountCurrency()));
        costInfo.setDiscountAmount(discountAmount);
        costInfo.setChargingSource(dataCost.getChargingSource());
        costInfo.setSettlementType(SettlementTypeEnum.of(dataCost.getSettlementType()));
        costInfo.setPaymentStage(PaymentStageEnum.of(dataCost.getPaymentStage()));
        costInfo.setExtendProps(dataCost.getExt());
        return costInfo;
    }
    /**
     * 预估财务信息
     *
     * @param transactionCostInfo
     * @return
     */
    private FinanceFacade toEstimateFinance(Order.TransactionCostInfo transactionCostInfo) {
        if (transactionCostInfo == null) {
            return null;
        }

        // 预估财务信息对象
        FinanceFacade estimateFinanceInfo = new FinanceFacade();
        // 预估-折前金额
        Optional.ofNullable(transactionCostInfo.getPreAmount()).ifPresent(estimatePreAmount -> {
            Money estimatePreAmountFacade = new Money();
            estimatePreAmountFacade.setAmount(estimatePreAmount);
            estimatePreAmountFacade.setCurrency(CurrencyCodeEnum.of(transactionCostInfo.getPreAmountCurrency()));
            estimateFinanceInfo.setPreAmount(estimatePreAmountFacade);
        });
        // 预估-折后金额
        Optional.ofNullable(transactionCostInfo.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
            Money estimateDiscountAmountFacade = new Money();
            estimateDiscountAmountFacade.setAmount(estimateDiscountAmount);
            estimateDiscountAmountFacade.setCurrency(CurrencyCodeEnum.of(transactionCostInfo.getDiscountAmountCurrency()));
            estimateFinanceInfo.setDiscountAmount(estimateDiscountAmountFacade);
        });
        // 预估-加价后总金额
        Optional.ofNullable(transactionCostInfo.getTotalAdditionAmount()).ifPresent(totalAdditionAmount -> {
            estimateFinanceInfo.setTotalAdditionAmount(MoneyFacadeMapper.INSTANCE.toMoney(totalAdditionAmount));
        });
        // 预估-计费重量
        Optional.ofNullable(transactionCostInfo.getBillingWeight()).ifPresent(estimateBillingWeight -> {
            Weight estimateBillingWeightFacade = new Weight();
            estimateBillingWeightFacade.setValue(estimateBillingWeight);
            estimateBillingWeightFacade.setUnit(WeightTypeEnum.of(transactionCostInfo.getWeightUnit()));
            estimateFinanceInfo.setBillingWeight(estimateBillingWeightFacade);
        });
        // 预估-计费体积
        Optional.ofNullable(transactionCostInfo.getBillingVolume()).ifPresent(estimateBillingVolume -> {
            Volume estimateBillingVolumeFacade = new Volume();
            estimateBillingVolumeFacade.setValue(estimateBillingVolume);
            estimateBillingVolumeFacade.setUnit(VolumeTypeEnum.of(transactionCostInfo.getVolumeUnit()));
            estimateFinanceInfo.setBillingVolume(estimateBillingVolumeFacade);
        });
        // 预估-费用明细
        Optional.ofNullable(transactionCostInfo.getFinanceDetailInfoList()).ifPresent(estimateFinanceDetailInfoList -> {
            List<FinanceDetailFacade> estimateFinanceDetails = new ArrayList<>(estimateFinanceDetailInfoList.size());
            estimateFinanceDetailInfoList.forEach(estimateFinanceDetailInfo -> {
                // 预估-费用明细对象
                FinanceDetailFacade estimateFinanceDetailFacade = new FinanceDetailFacade();
                // 预估-费用编号
                estimateFinanceDetailFacade.setCostNo(estimateFinanceDetailInfo.getCostNo());
                // 预估-费用名称
                estimateFinanceDetailFacade.setCostName(estimateFinanceDetailInfo.getCostName());
                // 预估-费用产品编码
                estimateFinanceDetailFacade.setProductNo(estimateFinanceDetailInfo.getProductNo());
                // 预估-费用产品名称
                estimateFinanceDetailFacade.setProductName(estimateFinanceDetailInfo.getProductName());
                // 预估-折扣信息
                if (CollectionUtils.isNotEmpty(estimateFinanceDetailInfo.getMarketInfoList())) {
                    List<DiscountFacade> estimateDiscountFacades = new ArrayList<>(estimateFinanceDetailInfo.getMarketInfoList().size());
                    estimateFinanceDetailInfo.getMarketInfoList().forEach(marketInfo -> {
                        DiscountFacade estimateDiscountFacade = new DiscountFacade();
                        // 预估-折扣编号
                        estimateDiscountFacade.setDiscountNo(marketInfo.getCouponNo());
                        // 预估-折扣类型
                        estimateDiscountFacade.setDiscountType(marketInfo.getCouponType());
                        // 预估-折扣金额
                        if (marketInfo.getDiscountAmount() != null) {
                            Money estimateDiscountAmount = new Money();
                            estimateDiscountAmount.setAmount(marketInfo.getDiscountAmount());
                            estimateDiscountAmount.setCurrency(CurrencyCodeEnum.of(marketInfo.getCurrency()));
                            estimateDiscountFacade.setDiscountedAmount(estimateDiscountAmount);
                        }
                        estimateDiscountFacades.add(estimateDiscountFacade);
                    });

                    estimateFinanceDetailFacade.setDiscountFacades(estimateDiscountFacades);
                }
                // 预估-折前金额
                Optional.ofNullable(estimateFinanceDetailInfo.getPreAmount()).ifPresent(estimatePreAmount -> {
                    Money estimatePreAmountFacade = new Money();
                    estimatePreAmountFacade.setAmount(estimatePreAmount);
                    estimatePreAmountFacade.setCurrency(CurrencyCodeEnum.of(estimateFinanceDetailInfo.getPreAmountCurrency()));
                    estimateFinanceDetailFacade.setPreAmount(estimatePreAmountFacade);
                });

                // 预估-折后金额
                Optional.ofNullable(estimateFinanceDetailInfo.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
                    Money estimateDiscountAmountFacade = new Money();
                    estimateDiscountAmountFacade.setAmount(estimateDiscountAmount);
                    estimateDiscountAmountFacade.setCurrency(CurrencyCodeEnum.of(estimateFinanceDetailInfo.getDiscountAmountCurrency()));
                    estimateFinanceDetailFacade.setDiscountAmount(estimateDiscountAmountFacade);
                });

                // 预估-加价后金额
                Optional.ofNullable(estimateFinanceDetailInfo.getAdditionAmount()).ifPresent(additionAmount -> {
                    estimateFinanceDetailFacade.setAdditionAmount(MoneyFacadeMapper.INSTANCE.toMoney(additionAmount));
                });

                // 预估-扩展字段
                estimateFinanceDetailFacade.setExtendProps(estimateFinanceDetailInfo.getExt());

                estimateFinanceDetails.add(estimateFinanceDetailFacade);
            });

            estimateFinanceInfo.setFinanceDetails(estimateFinanceDetails);

        });

        return estimateFinanceInfo;
    }

    /**
     * 积分信息
     *
     * @param costInfo
     * @return
     */
    private PointsFacade toPointsFacade(Order.TransactionCostInfo costInfo) {
        if (costInfo == null || costInfo.getRedeemPointsQuantity() == null) {
            return null;
        }
        PointsFacade pointsFacade = new PointsFacade();

        QuantityFacade redeemPointsQuantity = new QuantityFacade();
        redeemPointsQuantity.setValue(costInfo.getRedeemPointsQuantity());
        redeemPointsQuantity.setUnit(costInfo.getQuantityUnit());
        pointsFacade.setRedeemPointsQuantity(redeemPointsQuantity);

        MoneyFacade moneyFacade = new MoneyFacade();
        moneyFacade.setAmount(costInfo.getRedeemPointsAmount());
        moneyFacade.setCurrency(CurrencyCodeEnum.of(costInfo.getCurrency()));
        pointsFacade.setRedeemPointsAmount(moneyFacade);

        return pointsFacade;
    }

    /**
     * 积分信息
     *
     * @param financeDetailInfo
     * @return
     */
    private PointsFacade toPointsFacade(Order.FinanceDetailInfo financeDetailInfo) {
        if (financeDetailInfo == null || financeDetailInfo.getRedeemPointsQuantity() == null) {
            return null;
        }
        PointsFacade pointsFacade = new PointsFacade();

        QuantityFacade redeemPointsQuantity = new QuantityFacade();
        redeemPointsQuantity.setValue(financeDetailInfo.getRedeemPointsQuantity());
        pointsFacade.setRedeemPointsQuantity(redeemPointsQuantity);

        MoneyFacade moneyFacade = new MoneyFacade();
        moneyFacade.setAmount(financeDetailInfo.getRedeemPointsAmount());
        moneyFacade.setCurrency(CurrencyCodeEnum.CNY);
        pointsFacade.setRedeemPointsAmount(moneyFacade);

        return pointsFacade;
    }

    /**
     * 防腐层财务信息明细转换
     *
     * @param detailInfoList
     * @param currency
     * @return
     */
    private List<FinanceDetailFacade> toFinanceDetailFacadeList(List<Order.FinanceDetailInfo> detailInfoList, String currency) {
        if (CollectionUtils.isEmpty(detailInfoList)) {
            return Collections.emptyList();
        }
        return detailInfoList.stream().map(detailInfo -> {
            FinanceDetailFacade detailFacade = new FinanceDetailFacade();
            detailFacade.setCostNo(detailInfo.getCostNo());
            //费用名称
            detailFacade.setCostName(detailInfo.getCostName());
            detailFacade.setProductNo(detailInfo.getProductNo());
            detailFacade.setProductName(detailInfo.getProductName());

            if (CollectionUtils.isNotEmpty(detailInfo.getMarketInfoList())) {
                List<DiscountFacade> discountFacades = new ArrayList<>(detailInfo.getMarketInfoList().size());
                detailInfo.getMarketInfoList().forEach(marketInfo -> {
                    DiscountFacade discountFacade = new DiscountFacade();
                    discountFacade.setDiscountNo(marketInfo.getCouponNo());
                    discountFacade.setDiscountType(marketInfo.getCouponType());
                    if (marketInfo.getDiscountAmount() != null) {
                        Money discountedAmount = new Money();
                        discountedAmount.setAmount(marketInfo.getDiscountAmount());
                        discountedAmount.setCurrency(CurrencyCodeEnum.of(marketInfo.getCurrency()));
                        discountFacade.setDiscountedAmount(discountedAmount);
                    }
                    discountFacade.setExtendProps(marketInfo.getExt());
                    discountFacades.add(discountFacade);
                });
                detailFacade.setDiscountFacades(discountFacades);
            }

            //折前金额
            detailFacade.setPreAmount(this.toMoney(detailInfo.getPreAmount(), currency));
            //折后金额
            detailFacade.setDiscountAmount(this.toMoney(detailInfo.getDiscountAmount(), currency));
            detailFacade.setRemark(detailInfo.getRemark());

            //积分信息
            detailFacade.setPointsFacade(toPointsFacade(detailInfo));
            // 费用明细向谁收
            detailFacade.setChargingSource(detailInfo.getChargingSource());
            // 扩展字段
            detailFacade.setExtendProps(detailInfo.getExt());
            return detailFacade;
        }).collect(Collectors.toList());

    }

    /**
     * 营销信息
     *
     * @param marketInfoList
     * @return
     */
    private PromotionFacade toPromotionFacade(List<Order.MarketInfo> marketInfoList) {
        PromotionFacade promotionFacade = new PromotionFacade();
        if (CollectionUtils.isNotEmpty(marketInfoList)) {
            //券信息列表
            List<TicketFacade> tickets = new ArrayList<>(marketInfoList.size());
            //折扣信息列表
            List<DiscountFacade> discounts = new ArrayList<>(marketInfoList.size());
            //营销活动
            List<ActivityFacade> activities = new ArrayList<>(marketInfoList.size());
            //营销折扣信息列表
            List<DiscountFacade> operationDiscounts = new ArrayList<>(marketInfoList.size());

            //所有的营销活动信息，不排除无效的
            marketInfoList.forEach(marketInfo -> {
                if (MarketEnum.COUPON.getCode().equals(marketInfo.getCouponSource())) {
                    TicketFacade ticketFacade = new TicketFacade();
                    ticketFacade.setTicketNo(marketInfo.getCouponNo());
                    ticketFacade.setTicketCategory(marketInfo.getCouponCategory());
                    if (StringUtils.isNotBlank(marketInfo.getCouponType())) {
                        ticketFacade.setTicketType(Integer.valueOf(marketInfo.getCouponType()));
                    }
                    ticketFacade.setTicketDiscountAmount(this.toMoney(marketInfo.getDiscountAmount(), marketInfo.getCurrency()));
                    ticketFacade.setTicketDescription(marketInfo.getDescription());
                    ticketFacade.setTicketDiscountRate(marketInfo.getDiscountRate());
                    ticketFacade.setTicketDiscountUpperLimit(this.toMoney(marketInfo.getDiscountUpperLimit(), marketInfo.getCurrency()));
                    ticketFacade.setCouponStatus(marketInfo.getCouponStatus());
                    ticketFacade.setTicketUseAmount(this.toMoney(marketInfo.getUseAmount(), marketInfo.getCurrency()));

                    if (marketInfo.getPromotionSign() != null && !marketInfo.getPromotionSign().isEmpty()) {
                        String ticketSource = marketInfo.getPromotionSign().get(PromotionSignEnum.TICKET_SOURCE.getKey());
                        if (StringUtils.isNotBlank(ticketSource)) {
                            ticketFacade.setTicketSource(Integer.valueOf(ticketSource));
                        }
                    }
                    //批次号
                    ticketFacade.setTicketBatchNo(marketInfo.getCouponBatchNo());
                    tickets.add(ticketFacade);//券信息列表
                }
                if (MarketEnum.DISCOUNT.getCode().equals(marketInfo.getCouponSource())) {
                    DiscountFacade discountFacade = new DiscountFacade();
                    discountFacade.setDiscountNo(marketInfo.getCouponNo());
                    discountFacade.setDiscountType(marketInfo.getCouponType());
                    discountFacade.setExtendProps(marketInfo.getExt());
                    discounts.add(discountFacade);//折扣信息列表
                }
                if (MarketEnum.ACTIVITY.getCode().equals(marketInfo.getCouponSource())) {
                    ActivityFacade activityFacade = new ActivityFacade();
                    activityFacade.setActivityNo(marketInfo.getCouponNo());
                    activityFacade.setActivityName(marketInfo.getCouponName());
                    activityFacade.setActivityStatus(marketInfo.getCouponStatus());
                    activityFacade.setActivityValue(marketInfo.getCouponValue());
                    activities.add(activityFacade);//营销活动
                }
                if (MarketEnum.OPERATION_DISCOUNT.getCode().equals(marketInfo.getCouponSource())) {
                    DiscountFacade discountFacade = new DiscountFacade();
                    discountFacade.setDiscountNo(marketInfo.getCouponNo());
                    discountFacade.setDiscountType(marketInfo.getCouponType());
                    discountFacade.setExtendProps(marketInfo.getExt());
                    operationDiscounts.add(discountFacade);//营销折扣信息列表
                }
            });
            promotionFacade.setTickets(tickets);
            promotionFacade.setDiscounts(discounts);
            promotionFacade.setActivities(activities);
            promotionFacade.setOperationDiscounts(operationDiscounts);
        }
        return promotionFacade;
    }

    /**
     * 防腐层交易关联单转换
     *
     * @param relationInfoList
     * @return
     */
    private List<RefOrderFacade> toRefOrderFacadeList(List<Order.RelationInfo> relationInfoList) {
        if (CollectionUtils.isEmpty(relationInfoList)) {
            return null;
        }
        List<RefOrderFacade> refOrderFacadeList = new ArrayList<>();

        for (Order.RelationInfo relationInfo : relationInfoList) {
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderNo(relationInfo.getRefOrderNo());
            refOrderFacade.setRefOrderType(relationInfo.getRefOrderType());
            if (relationInfo.getRefOrderSubType() != null) {
                refOrderFacade.setRefOrderSubType(Integer.valueOf(relationInfo.getRefOrderSubType().intValue()));
            }
            refOrderFacadeList.add(refOrderFacade);
        }
        return refOrderFacadeList;
    }

    /**
     * 防腐层交易关联单转换
     *
     * @param refOrderInfo
     * @return
     */
    private List<RefOrderFacade> toRefOrderFacadeList(Order.RefOrderInfo refOrderInfo) {
        if (refOrderInfo == null) {
            return null;
        }
        List<RefOrderFacade> refOrderFacadeList = new ArrayList<>();
        //原订单
        if (CollectionUtils.isNotEmpty(refOrderInfo.getOriginalOrderNos())) {
            /*refOrderInfo.getOriginalOrderNos().forEach(originalOrderNo -> {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(originalOrderNo);
                //TODO 关联单处理--目前统一赋值为500，先测试后面考虑模型构建
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.ORDER.getCode());
                refOrderFacade.setRefOrderSubType(null);
                refOrderFacadeList.add(refOrderFacade);
            });*/

            for (String originalOrderNo : refOrderInfo.getOriginalOrderNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(originalOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.ORDER.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }
        //运单号
        if (CollectionUtils.isNotEmpty(refOrderInfo.getWaybillNos())) {
            /*refOrderInfo.getWaybillNos().forEach(wayBill -> {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(wayBill);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.DELIVERY.getCode());
                refOrderFacade.setRefOrderSubType(RefOrderSubType.DeliveryEnum.FORWARD_DELIVERY.getCode());
                refOrderFacadeList.add(refOrderFacade);
            });*/

            for (String waybillNo : refOrderInfo.getWaybillNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(waybillNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.DELIVERY.getCode());
                refOrderFacade.setRefOrderSubType(RefOrderSubType.DeliveryEnum.FORWARD_DELIVERY.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }

        //预约单号
        if (CollectionUtils.isNotEmpty(refOrderInfo.getReservationOrderNos())) {
            refOrderInfo.getReservationOrderNos().forEach(reservation -> {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(reservation);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.WORK_ORDER.getCode());
                refOrderFacade.setRefOrderSubType(RefOrderSubType.WorkOrderEnum.RESERVATION.getCode());
                refOrderFacadeList.add(refOrderFacade);
            });
        }

        //采购单号
        if (CollectionUtils.isNotEmpty(refOrderInfo.getPurchaseOrderNos())) {
            refOrderInfo.getPurchaseOrderNos().forEach(purchase -> {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(purchase);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.WORK_ORDER.getCode());
                refOrderFacade.setRefOrderSubType(RefOrderSubType.WorkOrderEnum.INBOUND.getCode());
                refOrderFacadeList.add(refOrderFacade);
            });
        }
        //改址单
        if (CollectionUtils.isNotEmpty(refOrderInfo.getReaddressOrderNos())) {
            /*refOrderInfo.getReaddressOrderNos().forEach(readdressOrderNo -> {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(readdressOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.READDRESS.getCode());
                refOrderFacadeList.add(refOrderFacade);
            });*/
            for (String readdressOrderNo : refOrderInfo.getReaddressOrderNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(readdressOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.READDRESS.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }
        //逆向单
        if (CollectionUtils.isNotEmpty(refOrderInfo.getReverseOrderNos())) {
            /*refOrderInfo.getReverseOrderNos().forEach(reverseOrderNo -> {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(reverseOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.RETURN_ORDER.getCode());
                refOrderFacadeList.add(refOrderFacade);
            });*/

            for (String reverseOrderNo : refOrderInfo.getReverseOrderNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(reverseOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.RETURN_ORDER.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }
        //询价单
        if (CollectionUtils.isNotEmpty(refOrderInfo.getEnquiryOrderNos())) {
            /*refOrderInfo.getEnquiryOrderNos().forEach(enquiryOrderNo -> {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(enquiryOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.ENQUIRY.getCode());
                refOrderFacadeList.add(refOrderFacade);
            });*/

            for (String enquiryOrderNo : refOrderInfo.getEnquiryOrderNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(enquiryOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.ENQUIRY.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }

        //送取同步派送运单号
        if (CollectionUtils.isNotEmpty(refOrderInfo.getDeliveryWaybillNos())) {
            for (String deliveryWaybillNo : refOrderInfo.getDeliveryWaybillNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(deliveryWaybillNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }

        //送取同步取件运单号
        if (CollectionUtils.isNotEmpty(refOrderInfo.getPickupWaybillNos())) {
            for (String pickupWaybillNo : refOrderInfo.getPickupWaybillNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(pickupWaybillNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }

        //送取同步派送订单号
        if (CollectionUtils.isNotEmpty(refOrderInfo.getDeliveryOrderNos())) {
            for (String deliveryOrderNo : refOrderInfo.getDeliveryOrderNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(deliveryOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_DELIVERY_ORDER.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }

        //送取同步取件订单号
        if (CollectionUtils.isNotEmpty(refOrderInfo.getPickupOrderNos())) {
            for (String pickupOrderNo : refOrderInfo.getPickupOrderNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(pickupOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.DELIVERY_PICKUP_PICKUP_ORDER.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }
        if (CollectionUtils.isNotEmpty(refOrderInfo.getChildOrderNos())) {
            for (String childOrderNo : refOrderInfo.getChildOrderNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(childOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.CHILD.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }
        //服务询价单订单号
        if (CollectionUtils.isNotEmpty(refOrderInfo.getServiceEnquiryOrderNos())) {
            for (String orderNo : refOrderInfo.getServiceEnquiryOrderNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(orderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.SERVICE_ENQUIRY_ORDER.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }
        //服务询价单运单号
        if (CollectionUtils.isNotEmpty(refOrderInfo.getServiceEnquiryWaybillNos())) {
            for (String waybillNo : refOrderInfo.getServiceEnquiryWaybillNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(waybillNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.SERVICE_ENQUIRY_WAYBILL.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }
        // 集单号
        if(CollectionUtils.isNotEmpty(refOrderInfo.getCollectionOrderNos())){
            for (String collectionOrderNo : refOrderInfo.getCollectionOrderNos()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(collectionOrderNo);
                refOrderFacade.setRefOrderType(RefOrderTypeEnum.COLLECTION_ORDER.getCode());
                refOrderFacadeList.add(refOrderFacade);
            }
        }
        return refOrderFacadeList;
    }


    /**
     * 防腐层体积转换
     *
     * @param value
     * @param unit
     * @return
     */
    private Volume toVolume(BigDecimal value, String unit) {
        Volume volume = new Volume();
        volume.setValue(value);
        volume.setUnit(StringUtils.isNotBlank(unit) ? VolumeTypeEnum.of(unit) : VolumeTypeEnum.CM3);
        return volume;
    }

    /**
     * 防腐层长、宽、高转换
     *
     * @param length
     * @param width
     * @param height
     * @param unit
     * @return
     */
    private Dimension toDimension(BigDecimal length, BigDecimal width, BigDecimal height, String unit) {
        Dimension dimension = new Dimension();
        dimension.setLength(length);
        dimension.setWidth(width);
        dimension.setHeight(height);
        dimension.setUnit(StringUtils.isNotBlank(unit) ? LengthTypeEnum.of(unit) : LengthTypeEnum.CM);
        return dimension;
    }

    /**
     * 防腐层重量转换
     *
     * @param value
     * @param unit
     * @return
     */
    private Weight toWeight(BigDecimal value, String unit) {
        Weight weight = new Weight();
        weight.setValue(value);
        weight.setUnit(StringUtils.isNotBlank(unit) ? WeightTypeEnum.of(unit) : WeightTypeEnum.KG);
        return weight;
    }

    /**
     * 防腐层金额转换
     *
     * @param amount
     * @param currency
     * @return
     */
    private Money toMoney(BigDecimal amount, String currency) {
        Money money = new Money();
        money.setAmount(amount);
        if(StringUtils.isNotBlank(currency)){
            money.setCurrency(CurrencyCodeEnum.of(currency));
        }
        return money;
    }

    /**
     * 发货人、收货人地址信息转换
     *
     * @param consignorInfo
     * @param consignExtendInfo
     * @return
     */
    private AddressInfo toConsignorAddressInfo(Order.ConsignInfo consignorInfo, Order.ConsignExtendInfo consignExtendInfo, Map<String, String> orderSign) throws Exception {
        AddressInfo addressInfo = new AddressInfo();
        if (consignorInfo != null) {
            addressInfo.setProvinceNo(consignorInfo.getConsignorProvinceNo());
            addressInfo.setProvinceName(consignorInfo.getConsignorProvinceName());
            addressInfo.setCityNo(consignorInfo.getConsignorCityNo());
            addressInfo.setCityName(consignorInfo.getConsignorCityName());
            addressInfo.setCountyNo(consignorInfo.getConsignorCountyNo());
            addressInfo.setCountyName(consignorInfo.getConsignorCountyName());
            addressInfo.setTownNo(consignorInfo.getConsignorTownNo());
            addressInfo.setTownName(consignorInfo.getConsignorTownName());
            addressInfo.setAddress(tdeAcl.decrypt(consignorInfo.getConsignorAddress()));
            addressInfo.setCoordinateType(consignorInfo.getConsignorCoordinateType());
            addressInfo.setLongitude(consignorInfo.getConsignorLongitude() != null
                    ? String.valueOf(consignorInfo.getConsignorLongitude()) : null);
            addressInfo.setLatitude(consignorInfo.getConsignorLatitude() != null
                    ? String.valueOf(consignorInfo.getConsignorLatitude()) : null);
            addressInfo.setProvinceNoGis(consignorInfo.getConsignorProvinceNoGis());
            addressInfo.setProvinceNameGis(consignorInfo.getConsignorProvinceNameGis());
            addressInfo.setCityNoGis(consignorInfo.getConsignorCityNoGis());
            addressInfo.setCityNameGis(consignorInfo.getConsignorCityNameGis());
            addressInfo.setCountyNoGis(consignorInfo.getConsignorCountyNoGis());
            addressInfo.setCountyNameGis(consignorInfo.getConsignorCountyNameGis());
            addressInfo.setTownNoGis(consignorInfo.getConsignorTownNoGis());
            addressInfo.setTownNameGis(consignorInfo.getConsignorTownNameGis());
            if (consignExtendInfo != null) {
                addressInfo.setAddressGis(tdeAcl.decrypt(consignExtendInfo.getConsignorAddressGis()));
                addressInfo.setPreciseGis(consignExtendInfo.getConsignorPreciseGis());
                addressInfo.setChinaPostAddressCode(consignExtendInfo.getConsignorChinaPostAddressCode());
            }
            if (orderSign != null) {
                String fenceTrusted = orderSign.get(OrderSignEnum.CONSIGNOR_FENCE_TRUSTED.getCode());
                if (StringUtils.isNotBlank(fenceTrusted)) {
                    addressInfo.setFenceTrusted(Integer.valueOf(fenceTrusted));
                }
            }
            addressInfo.setFenceInfos(this.toFenceInfos(consignorInfo.getConsignorFenceList()));
            addressInfo.setRegionNo(consignorInfo.getConsignorRegionNo());
            addressInfo.setRegionName(consignorInfo.getConsignorRegionName());
            addressInfo.setEnCityName(consignorInfo.getConsignorEnCity());
            addressInfo.setEnAddress(tdeAcl.decrypt(consignorInfo.getConsignorEnAddress()));
            addressInfo.setPoiCode(consignorInfo.getConsignorPoiCode());
            addressInfo.setPoiName(tdeAcl.decrypt(consignorInfo.getConsignorPoiName()));
            addressInfo.setHouseNumber(tdeAcl.decrypt(consignorInfo.getConsignorHouseNumber()));
            addressInfo.setExtendProps(consignorInfo.getConsignorAddressExt());
        }
        return addressInfo;
    }

    private AddressInfo toConsigneeAddressInfo(Order.ConsignInfo consigneeInfo, Order.ConsignExtendInfo consignExtendInfo, Map<String, String> orderSign) throws Exception {
        AddressInfo addressInfo = new AddressInfo();
        if (consigneeInfo != null) {
            addressInfo.setProvinceNo(consigneeInfo.getConsigneeProvinceNo());
            addressInfo.setProvinceName(consigneeInfo.getConsigneeProvinceName());
            addressInfo.setCityNo(consigneeInfo.getConsigneeCityNo());
            addressInfo.setCityName(consigneeInfo.getConsigneeCityName());
            addressInfo.setCountyNo(consigneeInfo.getConsigneeCountyNo());
            addressInfo.setCountyName(consigneeInfo.getConsigneeCountyName());
            addressInfo.setTownNo(consigneeInfo.getConsigneeTownNo());
            addressInfo.setTownName(consigneeInfo.getConsigneeTownName());
            addressInfo.setAddress(tdeAcl.decrypt(consigneeInfo.getConsigneeAddress()));
            addressInfo.setCoordinateType(consigneeInfo.getConsigneeCoordinateType());
            addressInfo.setLongitude(consigneeInfo.getConsigneeLongitude() != null
                    ? String.valueOf(consigneeInfo.getConsigneeLongitude()) : null);
            addressInfo.setLatitude(consigneeInfo.getConsigneeLatitude() != null
                    ? String.valueOf(consigneeInfo.getConsigneeLatitude()) : null);
            addressInfo.setProvinceNoGis(consigneeInfo.getConsigneeProvinceNoGis());
            addressInfo.setProvinceNameGis(consigneeInfo.getConsigneeProvinceNameGis());
            addressInfo.setCityNoGis(consigneeInfo.getConsigneeCityNoGis());
            addressInfo.setCityNameGis(consigneeInfo.getConsigneeCityNameGis());
            addressInfo.setCountyNoGis(consigneeInfo.getConsigneeCountyNoGis());
            addressInfo.setCountyNameGis(consigneeInfo.getConsigneeCountyNameGis());
            addressInfo.setTownNoGis(consigneeInfo.getConsigneeTownNoGis());
            addressInfo.setTownNameGis(consigneeInfo.getConsigneeTownNameGis());
            if (consignExtendInfo != null) {
                addressInfo.setAddressGis(tdeAcl.decrypt(consignExtendInfo.getConsigneeAddressGis()));
                addressInfo.setPreciseGis(consignExtendInfo.getConsigneePreciseGis());
                addressInfo.setChinaPostAddressCode(consignExtendInfo.getConsigneeChinaPostAddressCode());
            }
            if (orderSign != null) {
                String fenceTrusted = orderSign.get(OrderSignEnum.CONSIGNEE_FENCE_TRUSTED.getCode());
                if (StringUtils.isNotBlank(fenceTrusted)) {
                    addressInfo.setFenceTrusted(Integer.valueOf(fenceTrusted));
                }
            }
            addressInfo.setFenceInfos(this.toFenceInfos(consigneeInfo.getConsigneeFenceList()));
            addressInfo.setAddressSource(consigneeInfo.getConsigneeAddressSource());
            addressInfo.setRegionNo(consigneeInfo.getConsigneeRegionNo());
            addressInfo.setRegionName(consigneeInfo.getConsigneeRegionName());
            addressInfo.setEnCityName(consigneeInfo.getConsigneeEnCity());
            addressInfo.setEnAddress(tdeAcl.decrypt(consigneeInfo.getConsigneeEnAddress()));
            addressInfo.setPoiCode(consigneeInfo.getConsigneePoiCode());
            addressInfo.setPoiName(tdeAcl.decrypt(consigneeInfo.getConsigneePoiName()));
            addressInfo.setHouseNumber(tdeAcl.decrypt(consigneeInfo.getConsigneeHouseNumber()));
            addressInfo.setExtendProps(consigneeInfo.getConsigneeAddressExt());
        }
        return addressInfo;
    }

    /**
     * 围栏信息列表转换
     * @param fences
     * @return
     */
    private List<FenceInfo> toFenceInfos(List<Order.FenceInfo> fences) {
        if (CollectionUtils.isEmpty(fences)) {
            return null;
        }

        return fences.stream().map(this::toFenceInfo).collect(Collectors.toList());
    }

    /**
     * 围栏对象转换
     * @param fence
     * @return
     */
    private FenceInfo toFenceInfo(Order.FenceInfo fence) {
        FenceInfo fenceInfo = new FenceInfo();
        fenceInfo.setFenceId(fence.getFenceId());
        fenceInfo.setFenceType(fence.getFenceType());
        return fenceInfo;
    }

    /**
     * 功能：补全订单ID号
     *
     * @param orderModel 1
     * @param order      2
     * @return void
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/4/1 21:48
     */
/*    public void toComplementOrderId(ExpressOrderModel orderModel, GetOrderFacadeResponse order) {
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        modelCreator.setOrderId(order.getOrderId());
        orderModel.complement().complementOrderId(this, modelCreator);

    }*/

    /**
     * @return void
     * <AUTHOR>
     * @Description 补全订单部分信息
     * @Date 12:55 2021/5/24
     * @Param [orderModel, order]
     **/
/*    public void toComplementCustomOrderPartInfo(ExpressOrderModel orderModel, GetOrderFacadeResponse order) {
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        modelCreator.setCustomOrderNo(order.getCustomOrderNo());
        modelCreator.setOrderId(order.getOrderId());
        orderModel.complement().complementOrderId(this, modelCreator);
        orderModel.complement().complementCustomOrderNo(this, modelCreator);

    }*/

    /**
     * 转换为是否百川订单请求
     *
     * @param context
     * @return
     */
    public GetOrderExistsRequest toGetOrderExistsRequest(ExpressOrderContext context) {
        GetOrderExistsRequest request = new GetOrderExistsRequest();
        request.setOrderNo(context.getOrderModel().orderNo());
        if (StringUtils.isBlank(context.getOrderModel().orderNo())
                && StringUtils.isNotBlank(context.getOrderModel().getCustomOrderNo())) {
            request.setCustomOrderNo(context.getOrderModel().getCustomOrderNo());
        }

        return request;
    }

    /**
     * 解决方案信息转换
     *
     * @param solution
     * @return
     */
    public BusinessSolutionFacade toBusinessSolutionFacade(Order.Solution solution) {
        if (solution == null) {
            return null;
        }
        BusinessSolutionFacade facade = new BusinessSolutionFacade();
        facade.setBusinessSolutionNo(solution.getBusinessSolutionNo());
        facade.setBusinessSolutionName(solution.getBusinessSolutionName());
        facade.setProductAttrs(solution.getProductAttrs());
        return facade;
    }

    /**
     * 协议信息转换
     * @param agreementInfos
     * @return
     */
    private List<AgreementFacade> toAgreementFacade(List<Order.AgreementInfo> agreementInfos) {
        if (CollectionUtils.isEmpty(agreementInfos)) {
            return null;
        }

        List<AgreementFacade> agreementFacades = new ArrayList<>(agreementInfos.size());
        for (Order.AgreementInfo agreementInfo : agreementInfos) {
            if (null == agreementInfo) {
                continue;
            }
            AgreementFacade agreementFacade = new AgreementFacade();
            agreementFacade.setAgreementType(agreementInfo.getAgreementType());
            agreementFacade.setAgreementId(agreementInfo.getAgreementId());
            agreementFacade.setSigner(agreementInfo.getSigner());
            agreementFacade.setSigningTime(agreementInfo.getSigningTime());
            agreementFacade.setExtendProps(agreementInfo.getExtendProps());
            agreementFacades.add(agreementFacade);
        }

        return agreementFacades;
    }

    /**
     * 拦截信息转换
     */
    private InterceptFacade toInterceptFacade(InterceptInfo interceptInfo) throws Exception {
        if (interceptInfo == null || interceptInfo.getAddressInfo() == null) {
            return null;
        }
        InterceptFacade interceptFacade = new InterceptFacade();
        interceptFacade.setInterceptAddress(toAddressInfo(interceptInfo.getAddressInfo()));
        interceptFacade.setInterceptHandlingMode(interceptInfo.getInterceptHandlingMode());
        interceptFacade.setInterceptStationNo(interceptInfo.getInterceptStationNo());
        interceptFacade.setInterceptResultTime(interceptInfo.getInterceptResultTime());
        interceptFacade.setInterceptRouteNode(interceptInfo.getInterceptRouteNode());
        interceptFacade.setInterceptPayAccountNo(interceptInfo.getInterceptPayAccountNo());
        return interceptFacade;
    }

    /**
     * 拦截信息的地址信息转换
     */
    private AddressInfo toAddressInfo(com.jdl.cp.core.ts.entity.AddressInfo sourceAddressInfo) throws Exception {
        AddressInfo addressInfo = new AddressInfo();
        if (sourceAddressInfo != null) {
            addressInfo.setProvinceNo(sourceAddressInfo.getProvinceNo());
            addressInfo.setProvinceName(sourceAddressInfo.getProvinceName());
            addressInfo.setCityNo(sourceAddressInfo.getCityNo());
            addressInfo.setCityName(sourceAddressInfo.getCityName());
            addressInfo.setCountyNo(sourceAddressInfo.getCountyNo());
            addressInfo.setCountyName(sourceAddressInfo.getCountyName());
            addressInfo.setTownNo(sourceAddressInfo.getTownNo());
            addressInfo.setTownName(sourceAddressInfo.getTownName());
            addressInfo.setAddress(tdeAcl.decrypt(sourceAddressInfo.getAddress()));
            addressInfo.setCoordinateType(sourceAddressInfo.getCoordinateType());
            addressInfo.setLongitude(sourceAddressInfo.getLongitude() != null
                    ? String.valueOf(sourceAddressInfo.getLongitude()) : null);
            addressInfo.setLatitude(sourceAddressInfo.getLatitude() != null
                    ? String.valueOf(sourceAddressInfo.getLatitude()) : null);
            addressInfo.setProvinceNoGis(sourceAddressInfo.getProvinceNoGis());
            addressInfo.setProvinceNameGis(sourceAddressInfo.getProvinceNameGis());
            addressInfo.setCityNoGis(sourceAddressInfo.getCityNoGis());
            addressInfo.setCityNameGis(sourceAddressInfo.getCityNameGis());
            addressInfo.setCountyNoGis(sourceAddressInfo.getCountyNoGis());
            addressInfo.setCountyNameGis(sourceAddressInfo.getCountyNameGis());
            addressInfo.setTownNoGis(sourceAddressInfo.getTownNoGis());
            addressInfo.setTownNameGis(sourceAddressInfo.getTownNameGis());
            addressInfo.setAddressGis(tdeAcl.decrypt(sourceAddressInfo.getAddressGis()));
            addressInfo.setPreciseGis(sourceAddressInfo.getPreciseGis());
            addressInfo.setChinaPostAddressCode(sourceAddressInfo.getChinaPostAddressCode());
            addressInfo.setFenceTrusted(sourceAddressInfo.getFenceTrusted());
            addressInfo.setFenceInfos(this.toFenceInfos(sourceAddressInfo.getFenceInfos()));
        }
        return addressInfo;
    }

    /**
     * 履约信息
     * @param fulfillmentInfo
     * @return
     */
    private FulfillmentFacade toFulfillmentFacade(FulfillmentInfo fulfillmentInfo) {
        if (fulfillmentInfo == null) {
            return null;
        }
        FulfillmentFacade fulfillmentFacade = new FulfillmentFacade();
        fulfillmentFacade.setFulfillmentSign(fulfillmentInfo.getFulfillmentSign());
        fulfillmentFacade.setExtendProps(fulfillmentInfo.getExtendProps());
        if (fulfillmentInfo.getActualReceivedQuantity() != null) {
            Quantity receivedQuantity = new Quantity();
            receivedQuantity.setValue(fulfillmentInfo.getActualReceivedQuantity());
            receivedQuantity.setUnit(fulfillmentInfo.getPackageUnit());
            fulfillmentFacade.setActualReceivedQuantity(receivedQuantity);
        }
        fulfillmentFacade.setActualPickupTime(fulfillmentInfo.getActualPickupTime());
        fulfillmentFacade.setActualSignedTime(fulfillmentInfo.getActualSignedTime());

        if (fulfillmentInfo.getPackageMaxLen() != null) {
            LengthInfo lengthInfo = new LengthInfo();
            lengthInfo.setValue(fulfillmentInfo.getPackageMaxLen().getValue());
            lengthInfo.setUnit(LengthTypeEnum.of(fulfillmentInfo.getPackageMaxLen().getUnit()));
            fulfillmentFacade.setPackageMaxLen(lengthInfo);
        }
        return fulfillmentFacade;
    }

    /**
     * 跨境报关信息转换
     */
    private CustomsFacade toCustomsFacade(CustomsInfo customsInfo) {
        if (customsInfo == null) {
            return null;
        }
        return CustomsFacadeMapper.INSTANCE.toCustomsFacade(customsInfo);
    }

    /**
     * 附件列表转换
     */
    private List<AttachmentFacade> toAttachmentFacades(List<Order.AttachmentInfo> attachmentInfos) {
        if (CollectionUtils.isEmpty(attachmentInfos)) {
            return null;
        }
        return AttachmentFacadeMapper.INSTANCE.toAttachmentFacadesOrder(attachmentInfos);
    }

    /**
     * 批量查询订单防腐层返回转换
     * @param rpcResponses
     * @return
     * @throws Exception
     */
    public List<GetOrderFacadeResponse> toGetBatchOrderFacadeResponse(List<Order> rpcResponses) throws Exception {
        if (CollectionUtils.isEmpty(rpcResponses)) {
            return Collections.emptyList();
        }
        List<GetOrderFacadeResponse> result = Lists.newArrayListWithCapacity(rpcResponses.size());
        for (Order order : rpcResponses) {
            result.add(toGetOrderFacadeResponse(order));
        }
        return result;
    }

    /**
     * 批量查询订单防腐层请求转换
     * @param orderNos
     * @return
     */
    public GetBatchOrderFacadeRequest toGetBatchOrderFacadeRequest(List<String> orderNos) {
        GetBatchOrderFacadeRequest facadeRequest = new GetBatchOrderFacadeRequest();
        facadeRequest.setOrderNos(orderNos);
        return facadeRequest;
    }

    public ModifyRecordDto toModifyRecordDto(OrderModifyInfo modifyInfo) {
        ModifyRecordDto modifyRecordDto = new ModifyRecordDto();
        modifyRecordDto.setTenantId(modifyInfo.getTenantId());
        modifyRecordDto.setModifyRecordNo(modifyInfo.getModifyNo());
        modifyRecordDto.setModifyRecordSequence(modifyInfo.getModifySequence());
        modifyRecordDto.setModifyRecordType(modifyInfo.getModifyType());
        modifyRecordDto.setModifyRecordStatus(modifyInfo.getModifyStatus());
        modifyRecordDto.setOperator(modifyInfo.getOperator());
        modifyRecordDto.setOperateTime(modifyInfo.getOperateTime());
        modifyRecordDto.setOrderNo(modifyInfo.getOrderNo());
        modifyRecordDto.setModifyRecordMsg(modifyInfo.getModifyMsg());
        modifyRecordDto.setExtendProps(modifyInfo.getExtendProps());
        return modifyRecordDto;
    }

    /**
     * 转换货品序列号
     * @param cargoCodesInfos
     * @return
     */
    private List<Serial> toSerialList(List<Order.CargoCodesInfo> cargoCodesInfos) {
        if (CollectionUtils.isEmpty(cargoCodesInfos)) {
            return Collections.emptyList();
        }
        List<Serial> serialFacades = new ArrayList<>(cargoCodesInfos.size());
        for(Order.CargoCodesInfo cargoCodesInfo : cargoCodesInfos){
            Serial serialFacade = new Serial();
            if (cargoCodesInfo.getCargoIdType() != null) {
                serialFacade.setSerialType(Integer.valueOf(cargoCodesInfo.getCargoIdType()));
            }
            serialFacade.setSerialNo(cargoCodesInfo.getCargoIdCode());
            serialFacades.add(serialFacade);
        }
        return serialFacades;
    }
}
