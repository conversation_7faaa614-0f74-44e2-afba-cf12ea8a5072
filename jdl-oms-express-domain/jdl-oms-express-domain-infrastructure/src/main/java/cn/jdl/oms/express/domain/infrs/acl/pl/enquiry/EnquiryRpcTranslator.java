package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.dto.CostInfoDto;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.PointsInfoDto;
import cn.jdl.oms.express.domain.dto.QuantityInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse.MoneyFacadeDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingSplitFacadeRequest;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InboundTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.MedicalWarmLayerEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.VolumeTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.WarmLayerEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightTypeEnum;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BusinessConstants;
import cn.jdl.oms.express.shared.common.constant.MagicCommonConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.TypeConversion;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.jd.ccjf.data.domain.compute.ComputeFeeInfo;
import com.jd.ccjf.data.domain.compute.StandardComputeResult;
import com.jd.ccjf.data.domain.compute.lenglian.QlLLComputeRequest;
import com.jd.lbs.product.inquiry.dto.request.ProcessParam;
import com.jd.lbs.product.inquiry.dto.request.ProductParam;
import com.jd.lbs.product.inquiry.dto.request.StandardInquiryResultProcessRequest;
import com.jd.lbs.product.inquiry.dto.request.StandardProductInquiryRequest;
import com.jd.lbs.product.inquiry.dto.response.DaDaDeliverFeeDto;
import com.jd.lbs.product.inquiry.dto.response.DiscountInquiryDetail;
import com.jd.lbs.product.inquiry.dto.response.ProductInquiryFeeInfo;
import com.jd.lbs.product.inquiry.dto.response.StandardInquiryResult;
import com.jd.lbs.product.inquiry.dto.response.StandardInquiryResultProcessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 询价服务数据转换器
 */
@Translator
@Slf4j
public class EnquiryRpcTranslator {

    /**
     * C2C青龙业主号
     */
    private static final String C2C_QL_ACCOUNT_NO = "010K239824";

    /**
     * 是否优惠券：1：是
     */
    private static final int IS_COUPON = 1;

    /**
     * 费用项信息
     */
    private static final String CALC_PRICE_ITEM_LIST = "calcPriceItemList";

    private static final String COST_NO = "costNo";

    private static final String DEDUCTION_AMOUNT = "deductionAmount";

    /**
     * 来源系统
     */
    private static final String QLLL_SYSTEM_SOURCE_CODE = "QLXT";
    /**
     * 单据类型
     */
    private static final String QLLL_ORDER_TYPE = "QLYD";
    /**
     * 运单状态: 揽收-21
     */
    private static final int QLLL_WAYBILL_STATUS = 21;

    private static final byte YES = 1;
    /**
     * 冷链运营模式：共配-1，专车-2
     */
    private static final Integer DEDICATED_VEHICLE = 2;
    /**
     * 冷链卡班小票
     */
    private static final String LLKBXP = "LLKBXP";
    /**
     * B网冷链纯配运单-非卡班
     */
    private static final String QLYD_B_LL_PRO = "QLYD_B_LL_PRO";
    /**
     * 冷链医药零担
     */
    private static final String QL_B_LL_YY = "QL_B_LL_YY";
    /**
     * 冷链卡班
     */
    private static final String QLYD_B_LL = "QLYD_B_LL";
    /**
     * 青龙冷链一口价实时计费-无送仓类型
     */
    private static final Integer QL_NONE_TYPE = 0;
    /**
     * 青龙冷链一口价实时计费-送京仓
     */
    private static final Integer QL_SEND_JD_WAREHOUSE_TYPE = 3;
    /**
     * 青龙冷链一口价实时计费-送外仓
     */
    private static final Integer QL_SEND_OPEN_WAREHOUSE_TYPE = 4;

    /**
     * 未匹配折扣计算明细
     */
    private static final String MISMATCH_DISCOUNT_DETAILS = "mismatchDiscountDetails";

    /**
     * 耗材费用明细
     */
    private static final String MATERIAL_FEE_DETAILS = "materialFeeDetails";

    /**
     * 费用扩展信息
     */
    public static final String FEE_EXTEND_INFO = "FEE_EXTEND_INFO";

    /**
     * 青龙冷链一口价实时计费温层映射
     */
    private static final Map<WarmLayerEnum, String> fixedPriceWarmLayerMapping = new HashMap<>();

    /**
     * 青龙冷链一口价实时计费交易类型映射
     */
    private static final Map<String, String> TRANSACTION_TYPE_MAPPING = new HashMap<>();
    static {
        fixedPriceWarmLayerMapping.put(WarmLayerEnum.COMMON, WarmLayerEnum.COMMON.getBillingCode());
        fixedPriceWarmLayerMapping.put(WarmLayerEnum.USUAL, WarmLayerEnum.USUAL.getBillingCode());
        fixedPriceWarmLayerMapping.put(WarmLayerEnum.ALIVE, WarmLayerEnum.ALIVE.getBillingCode());
        fixedPriceWarmLayerMapping.put(WarmLayerEnum.CONTROL, WarmLayerEnum.CONTROL.getBillingCode());
        fixedPriceWarmLayerMapping.put(WarmLayerEnum.COLD, WarmLayerEnum.COLD.getBillingCode());
        fixedPriceWarmLayerMapping.put(WarmLayerEnum.FREEZING, WarmLayerEnum.FREEZING.getBillingCode());
        fixedPriceWarmLayerMapping.put(WarmLayerEnum.MEDICAL_USUAL, MedicalWarmLayerEnum.MEDICAL_USUAL.getBillingCode());
        fixedPriceWarmLayerMapping.put(WarmLayerEnum.MEDICAL_CONTROL, MedicalWarmLayerEnum.MEDICAL_CONTROL.getBillingCode());
        fixedPriceWarmLayerMapping.put(WarmLayerEnum.MEDICAL_COLD, MedicalWarmLayerEnum.MEDICAL_COLD.getBillingCode());
        fixedPriceWarmLayerMapping.put(WarmLayerEnum.MEDICAL_FREEZING, MedicalWarmLayerEnum.MEDICAL_FREEZING.getBillingCode());

        TRANSACTION_TYPE_MAPPING.put(ProductEnum.LLKB.getCode(), QLYD_B_LL);
        TRANSACTION_TYPE_MAPPING.put(ProductEnum.LLCP.getCode(), QLYD_B_LL_PRO);
        TRANSACTION_TYPE_MAPPING.put(ProductEnum.LLZC.getCode(), QLYD_B_LL_PRO);
        TRANSACTION_TYPE_MAPPING.put(ProductEnum.YYLD.getCode(), QL_B_LL_YY);
        TRANSACTION_TYPE_MAPPING.put(ProductEnum.YYDP.getCode(), QL_B_LL_YY);
        TRANSACTION_TYPE_MAPPING.put(ProductEnum.YLLD.getCode(), QL_B_LL_YY);
        TRANSACTION_TYPE_MAPPING.put(ProductEnum.YYZC.getCode(), QLYD_B_LL_PRO);
        TRANSACTION_TYPE_MAPPING.put(ProductEnum.LLXP.getCode(), LLKBXP);
    }


    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    public StandardInquiryResultProcessRequest toStandardInquiryResultProcessRequest(BillingSplitFacadeRequest facadeRequest) {
        StandardInquiryResultProcessRequest rpcRequest = new StandardInquiryResultProcessRequest();
        rpcRequest.setBusinessNo(facadeRequest.getRefOrderFacade().getWaybillNo());
        rpcRequest.setFeeInfos(facadeRequest.getFeeInfos());
        rpcRequest.setProcessParams(this.toProcessParams(facadeRequest.getDeductionFacades()));
        return rpcRequest;
    }

    /**
     * 加工信息列表转换
     * @param deductionFacades
     * @return
     */
    List<ProcessParam> toProcessParams(List<BillingSplitFacadeRequest.DeductionFacade> deductionFacades) {
        if (CollectionUtils.isEmpty(deductionFacades)) {
            return null;
        }

        return deductionFacades.stream()
                .map(this::toProcessParam)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 加工信息转换
     * @param deductionFacade
     * @return
     */
    ProcessParam toProcessParam(BillingSplitFacadeRequest.DeductionFacade deductionFacade) {
        if (null == deductionFacade) {
            return null;
        }
        ProcessParam processParam = new ProcessParam();
        processParam.setProductCode(deductionFacade.getProductNo());
        Map<String, String> param = new HashMap<>();
        param.put(COST_NO, deductionFacade.getCostNo());
        param.put(DEDUCTION_AMOUNT, deductionFacade.getDeductionAmount().toString());
        processParam.setProcessParam(param);
        return processParam;
    }

    /**
     * 询价费用拆分防腐层请求转换
     * @param response
     * @return
     */
    public BillingEnquiryFacadeResponse toBillingSplitFacadeResponse(StandardInquiryResultProcessResponse response) {
        BillingEnquiryFacadeResponse facadeResponse = new BillingEnquiryFacadeResponse();
        BillingEnquiryFacadeResponse.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeResponse.FinanceFacadeDto();

        //折后金额
        BillingEnquiryFacadeResponse.MoneyFacadeDto discountAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
        discountAmount.setAmount(TypeConversion.stringToBigDecimal(response.getTotalAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
        discountAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
        financeFacadeDto.setDiscountAmount(discountAmount);

        //费用明细
        List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> detailFacadeDtoList = new ArrayList<>();
        //总积分数量
        BigDecimal totalPoints = BigDecimal.ZERO;
        //总积分金额
        BigDecimal totalPointsAmount = BigDecimal.ZERO;
        //
        Map<String, String> ticketInfos = new HashMap<>();

        List<ProductInquiryFeeInfo> feeInfoList = response.getFeeInfos();
        for (ProductInquiryFeeInfo feeInfo : feeInfoList) {

            BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto = new BillingEnquiryFacadeResponse.FinanceDetailFacadeDto();
            detailFacadeDto.setProductNo(feeInfo.getProductCode());
            detailFacadeDto.setProductName(feeInfo.getProductName());
            detailFacadeDto.setCostNo(feeInfo.getCostNo());
            detailFacadeDto.setCostName(feeInfo.getCostName());

            PointsInfoDto pointsInfoDto = new PointsInfoDto();
            // 积分信息
            if (feeInfo.getRewardPoints() != null) {
                totalPoints = totalPoints.add(feeInfo.getRewardPoints());
                QuantityInfoDto redeemPointsQuantity = new QuantityInfoDto();
                redeemPointsQuantity.setValue(feeInfo.getRewardPoints());
                pointsInfoDto.setRedeemPointsQuantity(redeemPointsQuantity);
            }

            // 折前金额
            BillingEnquiryFacadeResponse.MoneyFacadeDto detailPreAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
            detailPreAmount.setAmount(TypeConversion.stringToBigDecimal(feeInfo.getPreAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
            detailPreAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            detailFacadeDto.setPreAmount(detailPreAmount);

            // 折后金额
            BillingEnquiryFacadeResponse.MoneyFacadeDto detailDiscountAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
            detailDiscountAmount.setAmount(TypeConversion.stringToBigDecimal(feeInfo.getAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
            detailDiscountAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            detailFacadeDto.setDiscountAmount(detailDiscountAmount);

            detailFacadeDto.setExtendProps(new HashMap<>());
            // 价格项明细
            if (null != feeInfo.getCalcPriceItemList()) {
                detailFacadeDto.getExtendProps().put(CALC_PRICE_ITEM_LIST ,JSONUtils.beanToJSONDefault(feeInfo.getCalcPriceItemList()));
            }

            //折扣明细
            if (CollectionUtils.isNotEmpty(feeInfo.getDiscountDetails())) {

                List<BillingEnquiryFacadeResponse.DiscountInfoFacadeDto> discountInfoFacadeDtos = new ArrayList<>();
                for (DiscountInquiryDetail discountDetail : feeInfo.getDiscountDetails()) {
                    //积分
                    if ("8".equals(discountDetail.getDiscountType())) {
                        totalPointsAmount = totalPointsAmount.add(discountDetail.getAmount());
                        MoneyInfoDto redeemPointsAmount = new MoneyInfoDto();
                        redeemPointsAmount.setAmount(discountDetail.getAmount());
                        redeemPointsAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
                        pointsInfoDto.setRedeemPointsAmount(redeemPointsAmount);
                    } else {
                        BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto = new BillingEnquiryFacadeResponse.DiscountInfoFacadeDto();
                        discountInfoFacadeDto.setDiscountNo(discountDetail.getDiscountNo());
                        discountInfoFacadeDto.setDiscountType(discountDetail.getDiscountType());
                        MoneyFacadeDto moneyFacadeDto = new MoneyFacadeDto();
                        moneyFacadeDto.setAmount(TypeConversion.stringToBigDecimal(discountDetail.getAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
                        moneyFacadeDto.setCurrencyCode(CurrencyCodeEnum.CNY);
                        discountInfoFacadeDto.setDiscountedAmount(moneyFacadeDto);

                        discountInfoFacadeDtos.add(discountInfoFacadeDto);
                        if (discountDetail.getIsCoupon() != null && IS_COUPON == discountDetail.getIsCoupon()) {
                            //是优惠券
                            ticketInfos.put(discountDetail.getCouponNo(), discountDetail.getCouponNo());
                            discountInfoFacadeDto.setTicketNo(discountDetail.getCouponNo());
                        }
                    }
                }
                detailFacadeDto.setDiscountInfoFacadeDtos(discountInfoFacadeDtos);
            }
            detailFacadeDtoList.add(detailFacadeDto);
        }
        financeFacadeDto.setFinanceDetailFacadeDtoList(detailFacadeDtoList);

        //询价后使用的优惠券
        facadeResponse.setTicketInfos(ticketInfos);

        if (totalPoints.intValue() > 0) {
            PointsInfoDto pointsInfoDto = new PointsInfoDto();
            QuantityInfoDto redeemPointsQuantity = new QuantityInfoDto();
            redeemPointsQuantity.setValue(totalPoints);
            pointsInfoDto.setRedeemPointsQuantity(redeemPointsQuantity);
            MoneyInfoDto redeemPointsAmount = new MoneyInfoDto();
            redeemPointsAmount.setAmount(totalPointsAmount);
            redeemPointsAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
            pointsInfoDto.setRedeemPointsAmount(redeemPointsAmount);
            financeFacadeDto.setPointsInfoDto(pointsInfoDto);
        }

        // 费用明细
        facadeResponse.setFinanceFacadeDto(financeFacadeDto);

        return facadeResponse;
    }

    /**
     * 获取达达支付截止时间
     * @param dadaFeeInfo
     * @return
     */
    private Date getPayDeadLine(DaDaDeliverFeeDto dadaFeeInfo) {
        if (null == dadaFeeInfo) {
            return null;
        }
        //支付截止时间
        try {
            return DateUtils.timestampMillisToDate(dadaFeeInfo.getExpiredTime());
        } catch (ParseException e) {
            // 不做处理
        }

        return null;
    }

    /**
     * 计费询价服务请求对象数据拼装
     */
    public StandardProductInquiryRequest toStandardProductInquiryRequest(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        StandardProductInquiryRequest standardProductInquiryRequest = new StandardProductInquiryRequest();
        //客户编码
        standardProductInquiryRequest.setSellerNo(toBillingSellerNo(billingEnquiryFacadeRequest));
        //业务单号
        standardProductInquiryRequest.setBusinessNo(billingEnquiryFacadeRequest.getRefOrderFacadeDto().getWaybillNo());
        //产品信息
        standardProductInquiryRequest.setProductParams(toProductParamList(billingEnquiryFacadeRequest));
        //计费日期 产品:传当前时间值 (c2c传当前时间 EnquiryFacadeTranslator 是在这里设置的，B2C是询价时间 )
        standardProductInquiryRequest.setExpDate(getExpDate(billingEnquiryFacadeRequest));
        //总重量
        standardProductInquiryRequest.setWeight(billingEnquiryFacadeRequest.getCargoFacadeDto().getTotalCargoWeight());
        //总体积
        standardProductInquiryRequest.setVolume(billingEnquiryFacadeRequest.getCargoFacadeDto().getTotalCargoVolume());
        //总件数
        if (BusinessUnitEnum.CN_JDL_TC.businessUnit().equals(billingEnquiryFacadeRequest.getBusinessUnit())) {
            standardProductInquiryRequest.setQty(0);
        } else {
            try {
                standardProductInquiryRequest.setQty(billingEnquiryFacadeRequest.getCargoFacadeDto().getTotalCargoQuantity().intValueExact());
            } catch (ArithmeticException e) {
                log.error("包裹数量异常,值是 {}", billingEnquiryFacadeRequest.getCargoFacadeDto().getTotalCargoQuantity(), e);
                standardProductInquiryRequest.setQty(billingEnquiryFacadeRequest.getCargoFacadeDto().getTotalCargoQuantity().intValue());
            }
        }

        //起始地址，优先 Gis
        BillingEnquiryFacadeRequest.AddressFacadeDto consignorAddress = billingEnquiryFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto();
        if (consignorAddress != null) {
            standardProductInquiryRequest.setStartOneAddress(consignorAddress.getProvinceNoGis());
            standardProductInquiryRequest.setStartTwoAddress(consignorAddress.getCityNoGis());
            standardProductInquiryRequest.setStartThreeAddress(consignorAddress.getCountyNoGis());
            standardProductInquiryRequest.setStartCountry(consignorAddress.getRegionNo());
        }
        //目的地址
        BillingEnquiryFacadeRequest.AddressFacadeDto consigneeAddress = billingEnquiryFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto();
        if (consigneeAddress != null) {
            standardProductInquiryRequest.setEndOneAddress(consigneeAddress.getProvinceNoGis());
            standardProductInquiryRequest.setEndTwoAddress(consigneeAddress.getCityNoGis());
            standardProductInquiryRequest.setEndThreeAddress(consigneeAddress.getCountyNoGis());
            standardProductInquiryRequest.setEndCountry(consigneeAddress.getRegionNo());
        }
        //设置扩展字段
        standardProductInquiryRequest.setExtendParam(billingEnquiryFacadeRequest.getExtendProps());
        //询价类型
        standardProductInquiryRequest.setInquiryType(billingEnquiryFacadeRequest.getInquiryType());
        return standardProductInquiryRequest;
    }

    /**
     * 获取询价时间，逆向单原单询价时，为原单操作时间，否则新建时间
     *
     * @param billingEnquiryFacadeRequest
     * @return
     */
    private Date getExpDate(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        //TODO 有需求变更，目前只传当前时间，后续变更再改
        // FinanceFacadeDto 一定不为空 因为前面是new的,传财务表里面的询价时间
        return billingEnquiryFacadeRequest.getFinanceFacadeDto().getEnquireTime();
    }

    /**
     * 计费客户编码
     */
    private String toBillingSellerNo(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        if(BusinessUnitEnum.CN_JDL_CC_B2B.getCode().equals(billingEnquiryFacadeRequest.getBusinessUnit())) {
            return billingEnquiryFacadeRequest.getCustomerFacadeDto().getAccountNo();
        }
        if (isFreightBusiness(billingEnquiryFacadeRequest.getBusinessUnit())) {
            // 快运需要传事业部编号（account2No EBU开头）
            return billingEnquiryFacadeRequest.getCustomerFacadeDto().getAccount2No();
        }
        //客户编码（青龙业主号或结算账号(优先)）
        if (billingEnquiryFacadeRequest.getFinanceFacadeDto() != null
                && StringUtils.isNotBlank(billingEnquiryFacadeRequest.getFinanceFacadeDto().getSettlementAccountNo())) {
            return billingEnquiryFacadeRequest.getFinanceFacadeDto().getSettlementAccountNo();
        }
        return billingEnquiryFacadeRequest.getCustomerFacadeDto().getAccountNo();
    }

    /**
     * 判断是否是快运业务
     * @return
     */
    private boolean isFreightBusiness(String businessUnit){
        if (BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.getCode().equals(businessUnit)
                || BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(businessUnit)) {
            // 快运需要传事业部编号（account2No EBU开头）
            return true;
        }
        return false;
    }


    /**
     * 产品信息
     */
    private List<ProductParam> toProductParamList(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        List<ProductParam> productParamList = null;
        if (CollectionUtils.isNotEmpty(billingEnquiryFacadeRequest.getProductFacadeDtoList())) {
            productParamList = new ArrayList<>();
            for (BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto : billingEnquiryFacadeRequest.getProductFacadeDtoList()) {
                if (isJumpOverCodC2C(billingEnquiryFacadeRequest, productFacadeDto)) {
                    continue;
                }
                ProductParam productParam = new ProductParam();
                // 直接透传
                productParam.setProductCode(productFacadeDto.getProductNo());
                productParam.setProductElement(productFacadeDto.getProductAttrs());
                productParamList.add(productParam);
            }
        }
        //抵扣信息转换
        if (billingEnquiryFacadeRequest.getFinanceFacadeDto() != null
                && CollectionUtils.isNotEmpty(billingEnquiryFacadeRequest.getFinanceFacadeDto().getDeductionInfoDtos())) {
            if (productParamList == null) {
                productParamList = new ArrayList<>();
            }
            for (DeductionInfoDto deductionInfoDto : billingEnquiryFacadeRequest.getFinanceFacadeDto().getDeductionInfoDtos()) {
                ProductParam productParam = new ProductParam();
                //抵扣信息的编码，需要作为产品编码传给计费，同时产品要素里还要传抵扣产品编码
                productParam.setProductCode(deductionInfoDto.getDeductionNo());
                Map<String, String> attrs = new HashMap<>();
                attrs.put("deductibleProduct", deductionInfoDto.getDeductionNo());
                productParam.setProductElement(attrs);
                productParamList.add(productParam);
            }
        }
        //附加费信息转换
        if (billingEnquiryFacadeRequest.getFinanceFacadeDto() != null
                && CollectionUtils.isNotEmpty(billingEnquiryFacadeRequest.getFinanceFacadeDto().getAttachFees())) {
            if (productParamList == null) {
                productParamList = new ArrayList<>();
            }
            for (CostInfoDto costInfoDto : billingEnquiryFacadeRequest.getFinanceFacadeDto().getAttachFees()) {
                ProductParam productParam = new ProductParam();
                //抵扣信息的编码，需要作为产品编码传给计费，同时产品要素里还要传抵扣产品编码
                productParam.setProductCode(costInfoDto.getCostNo());
                if(MapUtils.isNotEmpty(costInfoDto.getAttrs())){
                    productParam.setProductElement(costInfoDto.getAttrs());
                }
                productParamList.add(productParam);
            }
        }
        return productParamList;
    }

    /**
     * C2C
     * a) 青龙业主号等于010K239824，如果存在代收货款增值服务则屏蔽，不传给计费询价；
     * b) 订单类型是改址单，如果存在代收货款增资服务则屏蔽代收货款增值服务，不传给计费询价；
     *
     * @param billingEnquiryFacadeRequest
     * @param productFacadeDto
     * @return
     */
    private boolean isJumpOverCodC2C(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest,
                                     BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto) {
        if (billingEnquiryFacadeRequest != null && productFacadeDto != null &&
                BusinessUnitEnum.CN_JDL_C2C.businessUnit().equals(billingEnquiryFacadeRequest.getBusinessUnit())) {
            return AddOnProductEnum.JDL_COD_TOC.getCode().equals(productFacadeDto.getProductNo()) &&
                    (C2C_QL_ACCOUNT_NO.equals(billingEnquiryFacadeRequest.getCustomerFacadeDto().getAccountNo()) ||
                            OrderTypeEnum.READDRESS.equals(billingEnquiryFacadeRequest.getOrderType()));
        }
        return false;
    }

    /**
     * 计费询价服务响应对象数据拼装
     */
    public BillingEnquiryFacadeResponse toBillingEnquiryFacadeResponse(StandardInquiryResult standardInquiryResult) throws Exception {
        BillingEnquiryFacadeResponse billingEnquiryFacadeResponse = new BillingEnquiryFacadeResponse();
        BillingEnquiryFacadeResponse.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeResponse.FinanceFacadeDto();

        //币种处理
        CurrencyCodeEnum currencyCodeEnum = getCurrencyCodeEnum(standardInquiryResult);

        // 港澳改址改派需求后，存在不计费场景，totalPreAmount、totalAmount也会为null。此处不赋值零，调用方处理
        if (standardInquiryResult.getTotalPreAmount() != null) {
            //折前金额
            BillingEnquiryFacadeResponse.MoneyFacadeDto preAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
            preAmount.setAmount(TypeConversion.stringToBigDecimal(standardInquiryResult.getTotalPreAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
            preAmount.setCurrencyCode(currencyCodeEnum);
            financeFacadeDto.setPreAmount(preAmount);
        }

        if (standardInquiryResult.getTotalAmount() != null) {
            //折后金额
            BillingEnquiryFacadeResponse.MoneyFacadeDto discountAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
            discountAmount.setAmount(TypeConversion.stringToBigDecimal(standardInquiryResult.getTotalAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
            discountAmount.setCurrencyCode(currencyCodeEnum);
            financeFacadeDto.setDiscountAmount(discountAmount);
        }

        //换汇后【加价后总金额】
        if (null != standardInquiryResult.getTotalAdditionAmount()) {
            BillingEnquiryFacadeResponse.MoneyFacadeDto totalAdditionAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
            totalAdditionAmount.setAmount(TypeConversion.stringToBigDecimal(standardInquiryResult.getTotalAdditionAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
            totalAdditionAmount.setCurrencyCode(currencyCodeEnum);
            financeFacadeDto.setTotalAdditionAmount(totalAdditionAmount);
        }

        //计费重量
        if (standardInquiryResult.getWeight() != null) {
            WeightInfoDto billingWeight = new WeightInfoDto();
            billingWeight.setValue(standardInquiryResult.getWeight());
            financeFacadeDto.setBillingWeight(billingWeight);
        }

        //计费重量
        if (standardInquiryResult.getCalWeight() != null) {
            WeightInfoDto calWeight = new WeightInfoDto();
            calWeight.setValue(standardInquiryResult.getCalWeight());
            financeFacadeDto.setCalWeight(calWeight);
        }

        //计费体积
        if (standardInquiryResult.getVolume() != null) {
            VolumeInfoDto billVolume = new VolumeInfoDto();
            billVolume.setValue(standardInquiryResult.getVolume());
            financeFacadeDto.setBillingVolume(billVolume);
        }

        //计费类型
        if (standardInquiryResult.getComputeType() != null) {
            financeFacadeDto.setBillingMode(standardInquiryResult.getComputeType().toString());
        }

        // 重货/泡货
        if (standardInquiryResult.getHeavyBubbleType() != null) {
            financeFacadeDto.setHeavyBubbleType(standardInquiryResult.getHeavyBubbleType().toString());
        }

        // 因平台订单需要以相同入参再次调用费用拆分接口，
        billingEnquiryFacadeResponse.setFeeInfos(standardInquiryResult.getFeeInfos());

        //费用明细
        List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> detailFacadeDtoList = new ArrayList<>();
        //总积分数量
        BigDecimal totalPoints = BigDecimal.ZERO;
        //总积分金额
        BigDecimal totalPointsAmount = BigDecimal.ZERO;
        //询价单号
        String enquiryOrderNo = null;
        //支付截止时间
        Date payDeadLine = null;
        Map<String, String> ticketInfos = new HashMap<>();

        List<ProductInquiryFeeInfo> feeInfoList = standardInquiryResult.getFeeInfos();
        for (ProductInquiryFeeInfo feeInfo : feeInfoList) {

            BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto = new BillingEnquiryFacadeResponse.FinanceDetailFacadeDto();
            detailFacadeDto.setProductNo(feeInfo.getProductCode());
            detailFacadeDto.setProductName(feeInfo.getProductName());
            detailFacadeDto.setCostNo(feeInfo.getCostNo());
            detailFacadeDto.setCostName(feeInfo.getCostName());

            PointsInfoDto pointsInfoDto = new PointsInfoDto();
            // 积分信息
            if (feeInfo.getRewardPoints() != null) {
                totalPoints = totalPoints.add(feeInfo.getRewardPoints());
                QuantityInfoDto redeemPointsQuantity = new QuantityInfoDto();
                redeemPointsQuantity.setValue(feeInfo.getRewardPoints());
                pointsInfoDto.setRedeemPointsQuantity(redeemPointsQuantity);
                detailFacadeDto.setPointsInfoDto(pointsInfoDto);
            }

            // 折前金额
            BillingEnquiryFacadeResponse.MoneyFacadeDto detailPreAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
            detailPreAmount.setAmount(TypeConversion.stringToBigDecimal(feeInfo.getPreAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
            detailPreAmount.setCurrencyCode(currencyCodeEnum);
            detailFacadeDto.setPreAmount(detailPreAmount);

            // 折后金额
            BillingEnquiryFacadeResponse.MoneyFacadeDto detailDiscountAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
            detailDiscountAmount.setAmount(TypeConversion.stringToBigDecimal(feeInfo.getAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
            detailDiscountAmount.setCurrencyCode(currencyCodeEnum);
            detailFacadeDto.setDiscountAmount(detailDiscountAmount);

            // 加价后金额
            if (null != feeInfo.getAdditionAmount()) {
                BillingEnquiryFacadeResponse.MoneyFacadeDto additionAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
                additionAmount.setAmount(TypeConversion.stringToBigDecimal(feeInfo.getAdditionAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
                additionAmount.setCurrencyCode(currencyCodeEnum);
                detailFacadeDto.setAdditionAmount(additionAmount);
            }

            detailFacadeDto.setExtendProps(new HashMap<>());
            // 价格项明细
            if (null != feeInfo.getCalcPriceItemList()) {
                detailFacadeDto.getExtendProps().put(CALC_PRICE_ITEM_LIST ,JSONUtils.beanToJSONDefault(feeInfo.getCalcPriceItemList()));
            }

            // 未匹配折扣计算明细
            if (null != feeInfo.getMismatchDiscountDetails()) {
                detailFacadeDto.getExtendProps().put(MISMATCH_DISCOUNT_DETAILS ,JSONUtils.beanToJSONDefault(feeInfo.getMismatchDiscountDetails()));
            }

            // 耗材费用明细
            if (null != feeInfo.getMaterialFees()) {
                detailFacadeDto.getExtendProps().put(MATERIAL_FEE_DETAILS ,JSONUtils.beanToJSONDefault(feeInfo.getMaterialFees()));
            }

            // 费用扩展信息
            if (null != feeInfo.getFeeExtendInfo()) {
                detailFacadeDto.getExtendProps().put(FEE_EXTEND_INFO, JSONUtils.beanToJSONDefault(feeInfo.getFeeExtendInfo()));
            }

            //折扣明细
            if (CollectionUtils.isNotEmpty(feeInfo.getDiscountDetails())) {
                List<BillingEnquiryFacadeResponse.DiscountInfoFacadeDto> discountInfoFacadeDtos = new ArrayList<>();
                for (DiscountInquiryDetail discountDetail : feeInfo.getDiscountDetails()) {
                    //积分
                    if ("8".equals(discountDetail.getDiscountType())) {
                        totalPointsAmount = totalPointsAmount.add(discountDetail.getAmount());
                        MoneyInfoDto redeemPointsAmount = new MoneyInfoDto();
                        redeemPointsAmount.setAmount(discountDetail.getAmount());
                        redeemPointsAmount.setCurrencyCode(currencyCodeEnum);
                        pointsInfoDto.setRedeemPointsAmount(redeemPointsAmount);
                    } else {
                        BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto = new BillingEnquiryFacadeResponse.DiscountInfoFacadeDto();
                        discountInfoFacadeDto.setDiscountNo(discountDetail.getDiscountNo());
                        discountInfoFacadeDto.setDiscountType(discountDetail.getDiscountType());
                        MoneyFacadeDto moneyFacadeDto = new MoneyFacadeDto();
                        moneyFacadeDto.setAmount(TypeConversion.stringToBigDecimal(discountDetail.getAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
                        moneyFacadeDto.setCurrencyCode(currencyCodeEnum);
                        discountInfoFacadeDto.setDiscountedAmount(moneyFacadeDto);

                        if (null != discountDetail.getPureDiscountAmount()) {
                            MoneyFacadeDto pureDiscount = new MoneyFacadeDto();
                            pureDiscount.setAmount(TypeConversion.stringToBigDecimal(discountDetail.getPureDiscountAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
                            pureDiscount.setCurrencyCode(currencyCodeEnum);
                            discountInfoFacadeDto.setPureDiscountAmount(pureDiscount);
                        }

                        discountInfoFacadeDtos.add(discountInfoFacadeDto);
                        if (discountDetail.getIsCoupon() != null && IS_COUPON == discountDetail.getIsCoupon()) {
                            //是优惠券
                            ticketInfos.put(discountDetail.getCouponNo(), discountDetail.getCouponNo());
                            discountInfoFacadeDto.setTicketNo(discountDetail.getCouponNo());
                        }
                    }
                }
                detailFacadeDto.setDiscountInfoFacadeDtos(discountInfoFacadeDtos);
            }
            detailFacadeDtoList.add(detailFacadeDto);

            if (feeInfo.getDaDaDeliverFeeDto() != null) {
                //询价单
                enquiryOrderNo = feeInfo.getDaDaDeliverFeeDto().getDeliveryNo();
                if (feeInfo.getDaDaDeliverFeeDto().getExpiredTime() != null) {
                    //支付截止时间
                    payDeadLine = DateUtils.timestampMillisToDate(feeInfo.getDaDaDeliverFeeDto().getExpiredTime());
                }

            }

            if (feeInfo.getForwardInquiryResult() != null) {
                // 中铁方案ID / 达达询价单号
                enquiryOrderNo = feeInfo.getForwardInquiryResult().getInquireOrderNo();
            }
        }

        //询价后使用的优惠券
        billingEnquiryFacadeResponse.setTicketInfos(ticketInfos);
        if (totalPoints.intValue() > 0) {
            PointsInfoDto pointsInfoDto = new PointsInfoDto();
            QuantityInfoDto redeemPointsQuantity = new QuantityInfoDto();
            redeemPointsQuantity.setValue(totalPoints);
            pointsInfoDto.setRedeemPointsQuantity(redeemPointsQuantity);
            MoneyInfoDto redeemPointsAmount = new MoneyInfoDto();
            redeemPointsAmount.setAmount(totalPointsAmount);
            redeemPointsAmount.setCurrencyCode(currencyCodeEnum);
            pointsInfoDto.setRedeemPointsAmount(redeemPointsAmount);
            financeFacadeDto.setPointsInfoDto(pointsInfoDto);
        }
        financeFacadeDto.setFinanceDetailFacadeDtoList(detailFacadeDtoList);
        //支付截止时间
        financeFacadeDto.setPayDeadline(payDeadLine);
        //询价单号
        billingEnquiryFacadeResponse.setEnquiryOrderNo(enquiryOrderNo);
        //汇率
        if(StringUtils.isNotBlank(standardInquiryResult.getExchangeRate())){
            financeFacadeDto.setExchangeRate(standardInquiryResult.getExchangeRate());
        }
        //换汇前金额
        if(StringUtils.isNotBlank(standardInquiryResult.getCurrency())
                && StringUtils.isNotBlank(standardInquiryResult.getExchangeCurrency())
                && !standardInquiryResult.getExchangeCurrency().equals(standardInquiryResult.getCurrency())){
            //折前金额
            if(null != standardInquiryResult.getBeforeExPreAmount()){
                BillingEnquiryFacadeResponse.MoneyFacadeDto beforeExchangePreAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
                beforeExchangePreAmount.setAmount(TypeConversion.stringToBigDecimal(standardInquiryResult.getBeforeExPreAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
                beforeExchangePreAmount.setCurrencyCode(CurrencyCodeEnum.of(standardInquiryResult.getCurrency()));
                financeFacadeDto.setBeforeExchangePreAmount(beforeExchangePreAmount);
            }
            //折后金额
            if(null != standardInquiryResult.getBeforeExAmount()){
                BillingEnquiryFacadeResponse.MoneyFacadeDto beforeExchangeDiscountAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
                beforeExchangeDiscountAmount.setAmount(TypeConversion.stringToBigDecimal(standardInquiryResult.getBeforeExAmount().toString(), BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, null));
                beforeExchangeDiscountAmount.setCurrencyCode(CurrencyCodeEnum.of(standardInquiryResult.getCurrency()));
                financeFacadeDto.setBeforeExchangeDiscountAmount(beforeExchangeDiscountAmount);
            }
        }
        billingEnquiryFacadeResponse.setFinanceFacadeDto(financeFacadeDto);
        return billingEnquiryFacadeResponse;
    }

    /**
     * 冷链一口价实时计算请求对象数据拼装
     */
    public QlLLComputeRequest toFixedPriceQlLLComputeRequest(BillingEnquiryFacadeRequest computeRequest) {
        QlLLComputeRequest request = new QlLLComputeRequest();
        request.setSystemSource(QLLL_SYSTEM_SOURCE_CODE);
        request.setOrderType(QLLL_ORDER_TYPE);
        //询价专用定价策略交易类型
        request.setTransactionType(getQlLLTransactionType(getMainProduct(computeRequest.getProductFacadeDtoList())));
        //运单状态-揽收
        request.setTransbillStatus(QLLL_WAYBILL_STATUS);
        //青龙业主号
        request.setBdOwnerNo(computeRequest.getCustomerFacadeDto().getAccountNo());
        //青龙运单号
        request.setTransbillCode(computeRequest.getRefOrderFacadeDto().getWaybillNo());
        //揽收时间：当前时间
        request.setExpDate(new Date());
        BillingEnquiryFacadeRequest.ProductFacadeDto mainProductDto = getMainProduct(computeRequest.getProductFacadeDtoList());
        if(mainProductDto != null && (ProductEnum.LLKB.getCode().equals(mainProductDto.getProductNo())
                || ProductEnum.LLXP.getCode().equals(mainProductDto.getProductNo()))) {
            //温层
            Optional.ofNullable(computeRequest.getShipmentFacadeDto().getWarmLayer()).ifPresent(warmLayer ->
                    request.setTemptureNum(TypeConversion.stringToInteger(fixedPriceWarmLayerMapping.get(warmLayer))));
        }
        //是否冷链上门揽收
        Optional.ofNullable(computeRequest.getShipmentFacadeDto().getPickupType())
                .ifPresent(type -> request.setIsCollectParcel(type == PickupTypeEnum.ON_SITE_PICK ? MagicCommonConstants.BYTE_1 : MagicCommonConstants.BYTE_0));
        //是否冷链终端派送
        Optional.ofNullable(computeRequest.getShipmentFacadeDto().getDeliveryType())
                .ifPresent(type -> request.setIsTerminalDelivery(type == DeliveryTypeEnum.TO_DOOR ? MagicCommonConstants.BYTE_1 : MagicCommonConstants.BYTE_0));
        //起始省、市、县
        Optional.ofNullable(computeRequest.getConsignorFacadeDto().getAddressFacadeDto()).ifPresent(addressFacadeDto -> {
            request.setStartProvinceCode(TypeConversion.stringToInteger(addressFacadeDto.getProvinceNoGis()));
            request.setStartCityCode(TypeConversion.stringToInteger(addressFacadeDto.getCityNoGis()));
            request.setStartCountyCode(TypeConversion.stringToInteger(addressFacadeDto.getCountyNoGis()));
        });
        //目的省、市、县
        Optional.ofNullable(computeRequest.getConsigneeFacadeDto().getAddressFacadeDto()).ifPresent(addressFacadeDto -> {
            request.setDestProvinceCode(TypeConversion.stringToInteger(addressFacadeDto.getProvinceNoGis()));
            request.setDestCityCode(TypeConversion.stringToInteger(addressFacadeDto.getCityNoGis()));
            request.setDestCountyCode(TypeConversion.stringToInteger(addressFacadeDto.getCountyNoGis()));
        });
        BillingEnquiryFacadeRequest.SiteInfo startSiteInfo = (BillingEnquiryFacadeRequest.SiteInfo)computeRequest.getExtendProps().get(OrderConstants.startSiteInfo);
        Optional.ofNullable(startSiteInfo).ifPresent(extendProp -> {
            //始发转运中心省、市、县
            request.setStartTcProvinceCode(TypeConversion.stringToInteger(startSiteInfo.getSiteProvinceId()));
            request.setStartTcCityCode(TypeConversion.stringToInteger(startSiteInfo.getSiteCityId()));
            request.setStartTcCountyCode(TypeConversion.stringToInteger(startSiteInfo.getSiteCountryId()));
        });
        BillingEnquiryFacadeRequest.SiteInfo endSiteInfo = (BillingEnquiryFacadeRequest.SiteInfo)computeRequest.getExtendProps().get(OrderConstants.endSiteInfo);
        Optional.ofNullable(endSiteInfo).ifPresent(extendProp -> {
            //目的转运中心省、市、县
            request.setDestTcProvinceCode(TypeConversion.stringToInteger(endSiteInfo.getSiteProvinceId()));
            request.setDestTcCityCode(TypeConversion.stringToInteger(endSiteInfo.getSiteCityId()));
            request.setDestTcCountyCode(TypeConversion.stringToInteger(endSiteInfo.getSiteCountryId()));
        });
        //总重量
        Optional.ofNullable(computeRequest.getCargoFacadeDto().getTotalCargoWeight())
                .ifPresent(weight -> request.setWeight(TypeConversion.decimalToDouble(computeRequest.getCargoFacadeDto().getTotalCargoWeight())));
        //总体积
        Optional.ofNullable(computeRequest.getCargoFacadeDto().getTotalCargoVolume())
                .ifPresent(volume -> request.setVolume(TypeConversion.decimalToDouble(computeRequest.getCargoFacadeDto().getTotalCargoVolume())));
        //总件数
        Optional.ofNullable(computeRequest.getCargoFacadeDto().getTotalCargoQuantity())
                .ifPresent(qty -> request.setQty(TypeConversion.decimalToInt(computeRequest.getCargoFacadeDto().getTotalCargoQuantity())));
        //箱数
        Optional.ofNullable(computeRequest.getCargoFacadeDto().getTotalCargoQuantity())
                .ifPresent(qty -> request.setBoxNum(TypeConversion.decimalToInt(computeRequest.getCargoFacadeDto().getTotalCargoQuantity())));
        //产品相关
        Optional.ofNullable(getMainProduct(computeRequest.getProductFacadeDtoList())).ifPresent(mainProduct ->
                request.setProductType(ProductEnum.LLXP.getCode().equals(mainProduct.getProductNo()) ? ProductEnum.LLKB.getCode() : mainProduct.getProductNo()));
        //冷链运营模式
        BillingEnquiryFacadeRequest.ProductFacadeDto operation = getTargetProductDto(computeRequest.getProductFacadeDtoList(), Arrays.asList(ProductEnum.LLZC.getCode(), ProductEnum.YYZC.getCode(), ProductEnum.LLCP.getCode()));
        Optional.ofNullable(operation).ifPresent(product -> {
            //冷链运营模式：共配-1，专车-2
            if(ProductEnum.LLCP.getCode().equals(product.getProductNo())){
                request.setColdOperation(MagicCommonConstants.NUM_1);
            }else{
                request.setColdOperation(DEDICATED_VEHICLE);
            }
        });
        //资源调节费
        request.setIsAdjustResources(MagicCommonConstants.NUM_0);
        BillingEnquiryFacadeRequest.ProductFacadeDto adjustProduct = getTargetProductDto(computeRequest.getProductFacadeDtoList(), Arrays.asList(AddOnProductEnum.CC_SPRING_FESTIVAL_SURCHARGE_TOB.getCode()));
        Optional.ofNullable(adjustProduct).ifPresent(product -> request.setIsAdjustResources(MagicCommonConstants.NUM_1));
        //保价相关
        BillingEnquiryFacadeRequest.ProductFacadeDto insuredProduct = getTargetProductDto(computeRequest.getProductFacadeDtoList(), AddOnProductEnum.getInsuredValueCode());
        Optional.ofNullable(insuredProduct).ifPresent(product -> {
            //是否保价
            request.setIsInsured(YES);
            //保价金额
            Optional.ofNullable(product.getProductAttrs())
                    .ifPresent(productAttrs -> request.setInsuredValue(TypeConversion.stringToDouble(productAttrs.get(AddOnProductAttrEnum.GUARANTEE_VALUE.getCode()))));
        });
        //代收货款相关
        BillingEnquiryFacadeRequest.ProductFacadeDto codProduct = getTargetProductDto(computeRequest.getProductFacadeDtoList(), AddOnProductEnum.getCodCode());
        Optional.ofNullable(codProduct).ifPresent(product -> {
            //是否代收货款
            request.setIsCodService(YES);
            //代收货款金额
            Optional.ofNullable(product.getProductAttrs())
                    .ifPresent(productAttrs -> request.setCodMoney(TypeConversion.stringToDouble(productAttrs.get(AddOnProductAttrEnum.COD.getCode()))));
        });
        //送货入仓相关
        BillingEnquiryFacadeRequest.ProductFacadeDto deliveryToWarehouse = getTargetProductDto(computeRequest.getProductFacadeDtoList(), AddOnProductEnum.getDeliveryToWarehouseCode());
        Optional.ofNullable(deliveryToWarehouse).ifPresent(product -> {
            request.setIsGoodsWarehoused(YES);
            //送仓类型
            Optional.ofNullable(product.getProductAttrs()).ifPresent(productAttrs -> inboundType2SendWarehouseType(request, productAttrs.get(AddOnProductAttrEnum.INBOUND_TYPE.getCode())));
        });
        //精准送仓
        BillingEnquiryFacadeRequest.ProductFacadeDto precisionDeliveryToWarehouse = getTargetProductDto(computeRequest.getProductFacadeDtoList(), AddOnProductEnum.getPrecisionDeliveryToWarehouseCode());
        Optional.ofNullable(precisionDeliveryToWarehouse).ifPresent(product -> request.setIsAdjective((int) YES));
        //签单返还
        BillingEnquiryFacadeRequest.ProductFacadeDto signReturn = getTargetProductDto(computeRequest.getProductFacadeDtoList(), AddOnProductEnum.getSignReturnCode());
        Optional.ofNullable(signReturn).ifPresent(product -> {
            request.setIsReceiptCollectService(YES);
            request.setReceiptCollectType(getSignReturnType(product));
        });
        //上楼
        BillingEnquiryFacadeRequest.ProductFacadeDto heavyUpstairs = getTargetProductDto(computeRequest.getProductFacadeDtoList(), AddOnProductEnum.getHeavyUpstairsCode());
        Optional.ofNullable(heavyUpstairs).ifPresent(product -> request.setIsUpstairsService(YES));
        //装车
        BillingEnquiryFacadeRequest.ProductFacadeDto loading = getTargetProductDto(computeRequest.getProductFacadeDtoList(), AddOnProductEnum.getLoadingCode());
        Optional.ofNullable(loading).ifPresent(product -> request.setIsLoadingService(YES));
        //卸车
        BillingEnquiryFacadeRequest.ProductFacadeDto unLoading = getTargetProductDto(computeRequest.getProductFacadeDtoList(), AddOnProductEnum.getUnLoadingCode());
        Optional.ofNullable(unLoading).ifPresent(product -> request.setIsUnloadingService(YES));
        //装卸车
        BillingEnquiryFacadeRequest.ProductFacadeDto loadingAndUnLoading = getTargetProductDto(computeRequest.getProductFacadeDtoList(), AddOnProductEnum.getLoadingAndUnLoadingCode());
        Optional.ofNullable(loadingAndUnLoading).ifPresent(product ->
                Optional.ofNullable(product.getProductAttrs()).ifPresent(productAttrs -> {
                    if (productAttrs.get(AddOnProductAttrEnum.LOADING_TYPE.getCode()) == null) {
                        return;
                    }
                    if (AddOnProductAttrEnum.LOADING_TYPE_LOADING.getCode().equals(productAttrs.get(AddOnProductAttrEnum.LOADING_TYPE.getCode()))) {
                        request.setIsLoadingService(YES);
                    }
                    if (AddOnProductAttrEnum.LOADING_TYPE_UNLOADING.getCode().equals(productAttrs.get(AddOnProductAttrEnum.LOADING_TYPE.getCode()))) {
                        request.setIsUnloadingService(YES);
                    }
                }));
        return request;
    }

    /**
     * 获取特定增值产品
     * @param productFacadeDtos
     * @param productNos
     * @return
     */
    private BillingEnquiryFacadeRequest.ProductFacadeDto getTargetProductDto(List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos, List<String> productNos) {
        if (CollectionUtils.isEmpty(productFacadeDtos)) {
            return null;
        }
        for (BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto : productFacadeDtos) {
            if (productNos.contains(productFacadeDto.getProductNo())) {
                return productFacadeDto;
            }
        }
        return null;
    }

    /**
     * 获取主产品
     * @param productFacadeDtos
     * @return
     */
    private BillingEnquiryFacadeRequest.ProductFacadeDto getMainProduct(List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos) {
        if (CollectionUtils.isEmpty(productFacadeDtos)) {
            return null;
        }
        for (BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto : productFacadeDtos) {
            if (ServiceProductTypeEnum.MAIN_PRODUCT.getCode().intValue() == productFacadeDto.getProductType()) {
                return productFacadeDto;
            }
        }
        return null;
    }

    /**
     * 获取签单返还类型
     */
    private Byte getSignReturnType(BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto) {
        boolean written = false;
        boolean electronic = false;
        String reReceiveStr = productFacadeDto.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE.getCode());
        if (StringUtils.isNotBlank(reReceiveStr)) {
            JSONArray reReceiveArr = JSON.parseArray(reReceiveStr);
            for (int i = 0; i < reReceiveArr.size(); i++) {
                if (AddOnProductAttrEnum.WRITTEN_TYPE.getCode().equals(reReceiveArr.get(i))) {
                    written = true;
                }
                if (AddOnProductAttrEnum.ELECTRONIC.getCode().equals(reReceiveArr.get(i))) {
                    electronic = true;
                }
            }
        }
        if (!written && !electronic) {
            return null;
        }
        // 签单原单返+签单照片
        if (written && electronic) {
            return 3;
        }
        // 签单原单返
        if (written) {
            return 1;
        }
        // 签单照片
        return 2;
    }

    /**
     * 获取询价专用定价策略交易类型
     * @return
     */
    private String getQlLLTransactionType(BillingEnquiryFacadeRequest.ProductFacadeDto mainProduct) {
        if (mainProduct == null || !TRANSACTION_TYPE_MAPPING.containsKey(mainProduct.getProductNo())) {
            return QLYD_B_LL_PRO;
        }
        return TRANSACTION_TYPE_MAPPING.get(mainProduct.getProductNo());
    }

    /**
     * 增值要素inboundType转换为入参sendWarehouseType
     * @param request
     * @param inboundType
     */
    private void inboundType2SendWarehouseType(QlLLComputeRequest request, String inboundType) {
        Integer sendWarehouseType = QL_NONE_TYPE;
        if(InboundTypeEnum.SEND_JD_WAREHOUSE.getCode().equals(inboundType)) {
            sendWarehouseType = QL_SEND_JD_WAREHOUSE_TYPE;
        } else if (InboundTypeEnum.SEND_OPEN_WAREHOUSE.getCode().equals(inboundType)) {
            sendWarehouseType = QL_SEND_OPEN_WAREHOUSE_TYPE;
        }
        request.setSendWarehouseType(sendWarehouseType);
    }

    /**
     * 冷链一口价实时计算响应对象数据拼装
     */
    public BillingEnquiryFacadeResponse toBillingEnquiryFacadeResponse(QlLLComputeRequest qlLLComputeRequest, StandardComputeResult standardComputeResult) {
        //折前金额
        Double totalAmountDouble = standardComputeResult.getTotalAmount();
        BigDecimal totalAmount = new BigDecimal(Double.toString(totalAmountDouble));
        BillingEnquiryFacadeResponse.MoneyFacadeDto preAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
        preAmount.setAmount(totalAmount);
        preAmount.setCurrencyCode(CurrencyCodeEnum.CNY);

        //折后金额
        BillingEnquiryFacadeResponse.MoneyFacadeDto discountAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
        discountAmount.setAmount(totalAmount);
        discountAmount.setCurrencyCode(CurrencyCodeEnum.CNY);

        //总优惠金额
        BillingEnquiryFacadeResponse.MoneyFacadeDto totalDiscountAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
        totalDiscountAmount.setAmount(BigDecimal.ZERO);
        totalDiscountAmount.setCurrencyCode(CurrencyCodeEnum.CNY);

        //计费重量
        Double weightDouble = qlLLComputeRequest.getWeight();
        WeightInfoDto billingWeight = new WeightInfoDto();
        billingWeight.setValue(new BigDecimal(Double.toString(weightDouble)));
        billingWeight.setUnit(WeightTypeEnum.KG);

        //计费体积
        Double volumeDouble = qlLLComputeRequest.getVolume();
        VolumeInfoDto billingVolume = new VolumeInfoDto();
        billingVolume.setValue(new BigDecimal(Double.toString(volumeDouble)));
        billingVolume.setUnit(VolumeTypeEnum.CM3);

        //费用明细
        List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> financeDetailFacadeDtoList = new ArrayList<>();
        List<ComputeFeeInfo> feeInfos = standardComputeResult.getFeeInfos();
        if (CollectionUtils.isNotEmpty(feeInfos)) {
            for (ComputeFeeInfo computeFeeInfo : feeInfos) {
                BillingEnquiryFacadeResponse.FinanceDetailFacadeDto financeDetailFacadeDto = new BillingEnquiryFacadeResponse.FinanceDetailFacadeDto();
                //费用编号
                financeDetailFacadeDto.setCostNo(computeFeeInfo.getCostNo());
                //费用名称
                financeDetailFacadeDto.setCostName(computeFeeInfo.getCostName());
                //折前金额
                Double amountDouble = computeFeeInfo.getAmount();
                BillingEnquiryFacadeResponse.MoneyFacadeDto detailPreAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
                detailPreAmount.setAmount(new BigDecimal(Double.toString(amountDouble)));
                detailPreAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
                financeDetailFacadeDto.setPreAmount(detailPreAmount);
                //折后金额
                BillingEnquiryFacadeResponse.MoneyFacadeDto detailDiscountAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
                detailDiscountAmount.setAmount(new BigDecimal(Double.toString(amountDouble)));
                detailDiscountAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
                financeDetailFacadeDto.setDiscountAmount(detailDiscountAmount);
                //备注
                financeDetailFacadeDto.setRemark(computeFeeInfo.getRemark());

                financeDetailFacadeDtoList.add(financeDetailFacadeDto);
            }
        }

        BillingEnquiryFacadeResponse.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeResponse.FinanceFacadeDto();
        financeFacadeDto.setPreAmount(preAmount);
        financeFacadeDto.setDiscountAmount(discountAmount);
        financeFacadeDto.setTotalDiscountAmount(totalDiscountAmount);
        financeFacadeDto.setBillingWeight(billingWeight);
        financeFacadeDto.setBillingVolume(billingVolume);
        financeFacadeDto.setFinanceDetailFacadeDtoList(financeDetailFacadeDtoList);

        BillingEnquiryFacadeResponse response = new BillingEnquiryFacadeResponse();
        response.setFinanceFacadeDto(financeFacadeDto);
        return response;
    }

    /**
     * 根据询价响应获取币种，默认人民币
     */
    private CurrencyCodeEnum getCurrencyCodeEnum(StandardInquiryResult standardInquiryResult) {
        CurrencyCodeEnum currencyCodeEnum = CurrencyCodeEnum.CNY;
        if (standardInquiryResult != null
                && standardInquiryResult.getExchangeCurrency() != null
                && CurrencyCodeEnum.ofALL(standardInquiryResult.getExchangeCurrency()) != null) {
            currencyCodeEnum = CurrencyCodeEnum.ofALL(standardInquiryResult.getExchangeCurrency());
        }
        return currencyCodeEnum;
    }
}
