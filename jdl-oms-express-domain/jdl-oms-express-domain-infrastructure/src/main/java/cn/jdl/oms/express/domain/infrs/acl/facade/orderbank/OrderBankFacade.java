package cn.jdl.oms.express.domain.infrs.acl.facade.orderbank;

import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.ModifyDueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.ReceivableFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.ots.OrderResourceFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.pos.WebPosExtOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.pos.WebPosPayOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.pos.WebPosPayOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.pos.WebPosYunFacadeRequest;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderBankFlowDto;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Package： cn.jdl.oms.express.domain.infrs.acl.facade.orderbank
 * @ClassName: OrderBankFacade
 * @Description:台账接口
 * @Author： zhangqi
 * @CreateDate 2021/3/31 17:46
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Component
public class OrderBankFacade {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderBankFacade.class);

    /**
     * B商家修改
     */
    @Resource
    private ModifyDueFacade modifyDueFacade;
    /**
     * B商家新增
     */
    @Resource
    private ReceivableFacade receivableFacade;
    /**
     * Pos寄付
     */
    @Resource
    private WebPosPayOrderFacade webPosPayOrderFacade;
    /**
     * Pos到付
     */
    @Resource
    private WebPosYunFacade webPosYunFacade;

    /**
     * 外单台账新增
     */
    @Resource
    private OrderResourceFacade orderResourceFacade;

    /**
     * 转换器
     */
    @Resource
    private OrderBankFacadeTranslator orderBankFacadeTranslator;


    /**
     * 台账处理结果状态 成功
     */
    private static final Integer ORDER_BANK_FLOW_STATUS_SUCCESS = 1;

    /**
     * 台账处理结果状态 失败
     */
    private static final Integer ORDER_BANK_FLOW_STATUS_FAILURE = 2;

    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    /**
     * 台账查询
     */
    @Resource
    private RetailOrderBankFacade retailOrderBankFacade;

    /**
     * 非商城订单应收
     */
    @Resource
    private WebPosExtOrderFacade webPosExtOrderFacade;

    /**
     * 初始化Or修改台账
     *
     * @param orderBankFacadeRequest
     * @return
     */
    public OrderBankFacadeResponse saveOrUpdate(OrderBankFacadeRequest orderBankFacadeRequest, Class triggerClass) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".saveOrUpdate"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        OrderBankFacadeResponse orderBankFacadeResponse = null;
        try {
            LOGGER.info("台账操作触发类【{}】入参 orderBankFacadeRequest:{}", triggerClass.getSimpleName(), JSONUtils.beanToJSONDefault(orderBankFacadeRequest));
            //pos寄付
            OrderBankFacadeRequest.PosJfYun posJfYun = orderBankFacadeRequest.getPosJfYun();
            if (posJfYun != null) {
                LOGGER.info("台账操作-pos寄付");
                WebPosPayOrderFacadeRequest webPosPayOrderFacadeRequest = orderBankFacadeTranslator.toWebPosPayOrderFacadeRequest(orderBankFacadeRequest);
                LOGGER.info("台账操作-pos寄付 入参 webPosPayOrderFacadeRequest:{}", JSONUtils.beanToJSONDefault(webPosPayOrderFacadeRequest));
                WebPosPayOrderFacadeResponse response = webPosPayOrderFacade.saveExtOrderAccounts(webPosPayOrderFacadeRequest);
                LOGGER.info("台账操作-pos寄付 出参 response:{}", JSONUtils.beanToJSONDefault(response));
                if (response != null) {
                    orderBankFacadeResponse = new OrderBankFacadeResponse();
                    orderBankFacadeResponse.setPosPayAppNo(response.getPayAppNo());
                }
            }
            //pos到付
            OrderBankFacadeRequest.PosYun posYun = orderBankFacadeRequest.getPosYun();
            if (posYun != null) {
                LOGGER.info("台账操作-pos到付");
                WebPosYunFacadeRequest webPosYunFacadeRequest = orderBankFacadeTranslator.toWebPosYunFacadeRequest(orderBankFacadeRequest);
                LOGGER.info("台账操作-pos到付 入参 webPosYunFacadeRequest:{}", JSONUtils.beanToJSONDefault(webPosYunFacadeRequest));
                webPosYunFacade.saveYunOrderAccountsByType(webPosYunFacadeRequest);
                // orderBankFacadeResponse == null：始化台账时PosJfYun和PosYun都会写，先判断orderBankFacadeResponse为null避免payAppNo被运单号覆盖
                if (StringUtils.isNotEmpty(orderBankFacadeRequest.getWaybillNo()) && orderBankFacadeResponse == null) {
                    //支付单号：pos到付取运单号
                    orderBankFacadeResponse = new OrderBankFacadeResponse();
                    orderBankFacadeResponse.setPosPayAppNo(orderBankFacadeRequest.getWaybillNo());
                }
            }
            // 非商城订单应收（仅涉及到快运C2C整车）
            OrderBankFacadeRequest.PosExtYun posExtYun = orderBankFacadeRequest.getPosExtYun();
            if (posExtYun != null) {
                LOGGER.info("台账操作-非商城订单应收");
                WebPosExtOrderFacadeRequest webPosExtOrderFacadeRequest = orderBankFacadeTranslator.toWebPosExtOrderFacadeRequest(orderBankFacadeRequest);
                LOGGER.info("台账操作-非商城订单应收 入参 webPosPayOrderFacadeRequest:{}", JSONUtils.beanToJSONDefault(webPosExtOrderFacadeRequest));
                webPosExtOrderFacade.saveExtOrderAccounts(webPosExtOrderFacadeRequest);
            }
            //B商家创建
            OrderBankFacadeRequest.BMerchantCreate bMerchantCreate = orderBankFacadeRequest.getBMerchantCreate();
            if (bMerchantCreate != null) {
                LOGGER.info("台账操作-B商家创建");
                ReceivableFacadeRequest receivableFacadeRequest = orderBankFacadeTranslator.toReceivableFacadeRequest(orderBankFacadeRequest);
                LOGGER.info("台账操作-B商家创建 入参 receivableFacadeRequest:{}", JSONUtils.beanToJSONDefault(receivableFacadeRequest));
                receivableFacade.createDue(receivableFacadeRequest);
            }
            //B商家修改
            OrderBankFacadeRequest.BMerchantModify bMerchantModify = orderBankFacadeRequest.getBMerchantModify();
            if (bMerchantModify != null) {
                LOGGER.info("台账操作-B商家修改");
                ModifyDueFacadeRequest modifyDueFacadeRequest = orderBankFacadeTranslator.toModifyDueFacadeRequest(orderBankFacadeRequest);
                LOGGER.info("台账操作-B商家修改 入参 modifyDueFacadeRequest:{}", JSONUtils.beanToJSONDefault(modifyDueFacadeRequest));
                modifyDueFacade.modifyDue(modifyDueFacadeRequest);
            }
            //B商家到付修改
            OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = orderBankFacadeRequest.getBMerchantDfModify();
            if (bMerchantDfModify != null) {
                LOGGER.info("台账操作-B商家到付修改");
                ModifyDueFacadeRequest modifyDueFacadeRequest = orderBankFacadeTranslator.toModifyDueFacadeRequest(orderBankFacadeRequest, bMerchantDfModify);
                LOGGER.info("台账操作-B商家到付修改 入参 modifyDueFacadeRequest:{}", JSONUtils.beanToJSONDefault(modifyDueFacadeRequest));
                modifyDueFacade.modifyDue(modifyDueFacadeRequest);
            }
            //B商家寄付修改
            OrderBankFacadeRequest.BMerchantModify bMerchantJfModify = orderBankFacadeRequest.getBMerchantJfModify();
            if (bMerchantJfModify != null) {
                LOGGER.info("台账操作-B商家寄付修改");
                ModifyDueFacadeRequest modifyDueFacadeRequest = orderBankFacadeTranslator.toModifyDueFacadeRequest(orderBankFacadeRequest, bMerchantJfModify);
                LOGGER.info("台账操作-B商家寄付修改 入参 modifyDueFacadeRequest:{}", JSONUtils.beanToJSONDefault(modifyDueFacadeRequest));
                modifyDueFacade.modifyDue(modifyDueFacadeRequest);
            }
            //B商家COD修改
            OrderBankFacadeRequest.BMerchantModify bMerchantCodModify = orderBankFacadeRequest.getBMerchantCodModify();
            if (bMerchantCodModify != null) {
                LOGGER.info("台账操作-B商家COD修改");
                ModifyDueFacadeRequest modifyDueFacadeRequest = orderBankFacadeTranslator.toModifyDueFacadeRequest(orderBankFacadeRequest, bMerchantCodModify);
                LOGGER.info("台账操作-B商家COD修改 入参 modifyDueFacadeRequest:{}", JSONUtils.beanToJSONDefault(modifyDueFacadeRequest));
                modifyDueFacade.modifyDue(modifyDueFacadeRequest);
            }
            //外单台账新增--B2C为后款支付，不写外单台账，外单台账和在线支付有
            OrderBankFacadeRequest.OtsCreate otsCreate = orderBankFacadeRequest.getOtsCreate();
            if (otsCreate != null) {
                // 根据商户ID决定是新增覆盖或者新增外单台账
                OrderResourceFacadeRequest orderResourceFacadeRequest = orderBankFacadeTranslator.toOrderResourceFacadeRequest(orderBankFacadeRequest);
                if (MerchantEnum.FREIGHT_READDRESS.getMerchantId().equals(otsCreate.getMerchantId())
                        || otsCreate.isCreateReplaceFlag()) {
                    // 新增覆盖外单台账
                    setCreateReplaceVer(orderResourceFacadeRequest);
                    LOGGER.info("台账操作-外单台账新增覆盖 入参 orderResourceFacadeRequest:{}", JSONUtils.beanToJSONDefault(orderResourceFacadeRequest));
                    orderResourceFacade.createReplaceOrder(orderResourceFacadeRequest);
                } else {
                    // 新增外单台账
                    LOGGER.info("台账操作-外单台账新增 入参 orderResourceFacadeRequest:{}", JSONUtils.beanToJSONDefault(orderResourceFacadeRequest));
                    orderResourceFacade.createOrder(orderResourceFacadeRequest);
                }
            }

            //记录台账流水
            sendMsg(orderBankFacadeRequest, triggerClass.getSimpleName(), orderBankFacadeResponse == null ? "" : orderBankFacadeResponse.getPosPayAppNo(), ORDER_BANK_FLOW_STATUS_SUCCESS, "");
            LOGGER.info("台账操作触发类【{}】 出参 orderBankFacadeResponse:{}", triggerClass.getSimpleName(), JSONUtils.beanToJSONDefault(orderBankFacadeResponse));
        } catch (InfrastructureException e) {
            LOGGER.error("台账新增或者修改异常", e);
            Profiler.functionError(callerInfo);
            sendMsg(orderBankFacadeRequest, triggerClass.getSimpleName(), orderBankFacadeResponse == null ? "" : orderBankFacadeResponse.getPosPayAppNo(),
                    ORDER_BANK_FLOW_STATUS_FAILURE, "[" + e.getMessage() + "][" + e.getCustom() + "][" + e.subCode() + "][" + e.subMessage() + "]");
            throw e;
        } catch (Exception e) {
            LOGGER.error("台账新增或者修改异常", e);
            Profiler.functionError(callerInfo);
            sendMsg(orderBankFacadeRequest, triggerClass.getSimpleName(), orderBankFacadeResponse == null ? "" : orderBankFacadeResponse.getPosPayAppNo(), ORDER_BANK_FLOW_STATUS_FAILURE, e.getMessage());
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.LEDGER_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return orderBankFacadeResponse;
    }

    /**
     * 台账清理
     * @param orderBankFacadeRequest
     * @return
     */
    public void clear(OrderBankFacadeRequest orderBankFacadeRequest) {
        // 外单台账取消
        if (null != orderBankFacadeRequest.getOtsCancel()) {
            LOGGER.info("台账操作-外单台账取消");
            OrderResourceFacadeRequest orderResourceFacadeRequest = orderBankFacadeTranslator.toCancelOrderResourceFacadeRequest(orderBankFacadeRequest);
            LOGGER.info("台账操作-外单台账取消 入参 orderResourceFacadeRequest:{}", JSONUtils.beanToJSONDefault(orderResourceFacadeRequest));
            orderResourceFacade.cancelOrder(orderResourceFacadeRequest);
        }
    }

    /**
     * 台账流水消息发送
     *
     * @param request
     * @param className
     * @param posPayAppNo
     * @param errMsg
     */
    private void sendMsg(OrderBankFacadeRequest request, String className, String posPayAppNo, int status, String errMsg) {
        OrderBankFlowDto dto = orderBankFacadeTranslator.toOrderBankFlowDto(request, className, posPayAppNo, status, errMsg);
        try {
            expressOrderFlowService.sendOrderBankRecordMq(dto);
        } catch (Exception e) {
            LOGGER.error("台账流水消息发送失败！OrderBankFlowDto:{}", JSONUtils.beanToJSONDefault(dto), e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ORDER_BANK_FLOW_FAILURE, "台账流水消息发送失败！" + JSONUtils.beanToJSONDefault(dto));
        }
    }

    /**
     * 查询并设置台账版本
     */
    private void setCreateReplaceVer(OrderResourceFacadeRequest orderResourceFacadeRequest) {
        Integer orderBankVer = retailOrderBankFacade.queryOrderBankVer(orderResourceFacadeRequest.getOrderId(), orderResourceFacadeRequest.getMerchantId());
        if (orderBankVer == null) {
            // 设置初始版本
            orderBankVer = 0;
        } else {
            // 版本自增
            orderBankVer++;
        }
        orderResourceFacadeRequest.setVer(orderBankVer);
    }

    /**
     * 修改台账
     * @param orderBankFacadeMiddleRequest
     * @return
     */
    public OrderBankFacadeResponse modifyOrderBank(OrderBankFacadeMiddleRequest orderBankFacadeMiddleRequest, Class triggerClass) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".modifyOrderBank"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        OrderBankFacadeResponse orderBankFacadeResponse = null;
        try {
            LOGGER.info("台账操作触发类【{}】入参 orderBankFacadeRequest:{}", triggerClass.getSimpleName(), JSONUtils.beanToJSONDefault(orderBankFacadeMiddleRequest));
            //pos寄付
            OrderBankFacadeRequest.PosJfYun posJfYun = orderBankFacadeMiddleRequest.getPosJfYun();
            if (posJfYun != null) {
                LOGGER.info("台账操作-pos寄付");
                WebPosPayOrderFacadeRequest webPosPayOrderFacadeRequest = orderBankFacadeTranslator.toWebPosPayOrderFacadeRequest(orderBankFacadeMiddleRequest);
                LOGGER.info("台账操作-pos寄付 入参 webPosPayOrderFacadeRequest:{}", JSONUtils.beanToJSONDefault(webPosPayOrderFacadeRequest));
                WebPosPayOrderFacadeResponse response = webPosPayOrderFacade.saveExtOrderAccounts(webPosPayOrderFacadeRequest);
                LOGGER.info("台账操作-pos寄付 出参 response:{}", JSONUtils.beanToJSONDefault(response));
                if (response != null) {
                    orderBankFacadeResponse = new OrderBankFacadeResponse();
                    orderBankFacadeResponse.setPosPayAppNo(response.getPayAppNo());
                }
            }
            //pos到付
            OrderBankFacadeRequest.PosYun posYun = orderBankFacadeMiddleRequest.getPosYun();
            if (posYun != null) {
                LOGGER.info("台账操作-pos到付");
                WebPosYunFacadeRequest webPosYunFacadeRequest = orderBankFacadeTranslator.toWebPosYunFacadeRequest(orderBankFacadeMiddleRequest);
                LOGGER.info("台账操作-pos到付 入参 webPosYunFacadeRequest:{}", JSONUtils.beanToJSONDefault(webPosYunFacadeRequest));
                webPosYunFacade.saveYunOrderAccountsByType(webPosYunFacadeRequest);
                // orderBankFacadeResponse == null：始化台账时PosJfYun和PosYun都会写，先判断orderBankFacadeResponse为null避免payAppNo被运单号覆盖
                if (StringUtils.isNotEmpty(orderBankFacadeMiddleRequest.getWaybillNo()) && orderBankFacadeResponse == null) {
                    //支付单号：pos到付取运单号
                    orderBankFacadeResponse = new OrderBankFacadeResponse();
                    orderBankFacadeResponse.setPosPayAppNo(orderBankFacadeMiddleRequest.getWaybillNo());
                }
            }
            //B商家创建
            OrderBankFacadeRequest.BMerchantCreate bMerchantCreate = orderBankFacadeMiddleRequest.getBMerchantCreate();
            if (bMerchantCreate != null) {
                LOGGER.info("台账操作-B商家创建");
                ReceivableFacadeRequest receivableFacadeRequest = orderBankFacadeTranslator.toReceivableFacadeRequest(orderBankFacadeMiddleRequest);
                LOGGER.info("台账操作-B商家创建 入参 receivableFacadeRequest:{}", JSONUtils.beanToJSONDefault(receivableFacadeRequest));
                receivableFacade.createDue(receivableFacadeRequest);
            }
            //B商家到付修改
            OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = orderBankFacadeMiddleRequest.getBMerchantDfModify();
            if (bMerchantDfModify != null) {
                LOGGER.info("台账操作-B商家到付修改");
                ModifyDueFacadeRequest modifyDueFacadeRequest = orderBankFacadeTranslator.toModifyDueFacadeRequest(orderBankFacadeMiddleRequest,bMerchantDfModify);
                LOGGER.info("台账操作-B商家到付修改 入参 modifyDueFacadeRequest:{}", JSONUtils.beanToJSONDefault(modifyDueFacadeRequest));
                modifyDueFacade.modifyDue(modifyDueFacadeRequest);
            }

            //B商家寄付修改
            OrderBankFacadeRequest.BMerchantModify bMerchantJfModify = orderBankFacadeMiddleRequest.getBMerchantJfModify();
            if (bMerchantJfModify != null) {
                LOGGER.info("台账操作-B商家寄付修改");
                ModifyDueFacadeRequest modifyDueFacadeRequest = orderBankFacadeTranslator.toModifyDueFacadeRequest(orderBankFacadeMiddleRequest,bMerchantJfModify);
                LOGGER.info("台账操作-B商家寄付修改 入参 modifyDueFacadeRequest:{}", JSONUtils.beanToJSONDefault(modifyDueFacadeRequest));
                modifyDueFacade.modifyDue(modifyDueFacadeRequest);
            }

            //外单台账新增--B2C为后款支付，不写外单台账，外单台账和在线支付有
            OrderBankFacadeRequest.OtsCreate otsCreate = orderBankFacadeMiddleRequest.getOtsCreate();
            if (otsCreate != null) {
                // 根据商户ID决定是新增覆盖或者新增外单台账
                OrderResourceFacadeRequest orderResourceFacadeRequest = orderBankFacadeTranslator.toOrderResourceFacadeRequest(orderBankFacadeMiddleRequest);
                if (MerchantEnum.FREIGHT_READDRESS.getMerchantId().equals(otsCreate.getMerchantId())
                        || otsCreate.isCreateReplaceFlag()) {
                    // 新增覆盖外单台账
                    setCreateReplaceVer(orderResourceFacadeRequest);
                    LOGGER.info("台账操作-外单台账新增覆盖 入参 orderResourceFacadeRequest:{}", JSONUtils.beanToJSONDefault(orderResourceFacadeRequest));
                    orderResourceFacade.createReplaceOrder(orderResourceFacadeRequest);
                } else {
                    // 新增外单台账
                    LOGGER.info("台账操作-外单台账新增 入参 orderResourceFacadeRequest:{}", JSONUtils.beanToJSONDefault(orderResourceFacadeRequest));
                    orderResourceFacade.createOrder(orderResourceFacadeRequest);
                }
            }

            //记录台账流水
            sendMsg(orderBankFacadeMiddleRequest, triggerClass.getSimpleName(), orderBankFacadeResponse == null ? "" : orderBankFacadeResponse.getPosPayAppNo(), ORDER_BANK_FLOW_STATUS_SUCCESS, "");
            LOGGER.info("台账操作触发类【{}】 出参 orderBankFacadeResponse:{}", triggerClass.getSimpleName(), JSONUtils.beanToJSONDefault(orderBankFacadeResponse));
        } catch (BusinessDomainException e) {
            LOGGER.error("台账新增或者修改异常", e);
            Profiler.functionError(callerInfo);
            sendMsg(orderBankFacadeMiddleRequest, triggerClass.getSimpleName(), orderBankFacadeResponse == null ? "" : orderBankFacadeResponse.getPosPayAppNo(),
                    ORDER_BANK_FLOW_STATUS_FAILURE, "[" + e.getMessage() + "][" + e.getCustom() + "][" + e.subCode() + "][" + e.subMessage() + "]");
            throw e;
        } catch (Exception e) {
            LOGGER.error("台账新增或者修改异常", e);
            Profiler.functionError(callerInfo);
            sendMsg(orderBankFacadeMiddleRequest, triggerClass.getSimpleName(), orderBankFacadeResponse == null ? "" : orderBankFacadeResponse.getPosPayAppNo(), ORDER_BANK_FLOW_STATUS_FAILURE, e.getMessage());
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.LEDGER_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return orderBankFacadeResponse;
    }
}
