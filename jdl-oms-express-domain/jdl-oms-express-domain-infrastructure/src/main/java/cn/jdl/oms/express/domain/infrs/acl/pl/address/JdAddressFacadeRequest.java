package cn.jdl.oms.express.domain.infrs.acl.pl.address;

import lombok.Data;

import java.io.Serializable;

@Data
public class JdAddressFacadeRequest implements Serializable {

    private static final long serialVersionUID = -8199949034201229659L;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 区编码
     */
    private String districtCode;
    /**
     * 区名称
     */
    private String districtName;
    /**
     * 乡、镇编码
     */
    private String townCode;
    /**
     * 乡、镇名称
     */
    private String townName;
    /**
     * 详细
     */
    private String detailAddress;
    /**
     * 全地址
     */
    private String fullAddress;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 来源
     * 港澳订单传 HKM , 其余不传
     */
    private String source;
}
