package cn.jdl.oms.express.domain.infrs.acl.pl.enquiry;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.address.AddressBasicPrimaryWSFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description  C2B 接单时 逆向单询价facade转换器
 * @Copyright    &copy;2022 JDL.CN All Right Reserved
 * <AUTHOR> liu
 * @date         2022/5/31
 * @version      1.0
 * @since        1.8
 */
@Translator
public class C2BCreateEnquiryFacadeTranslator {
    /** 单据状态 默认传0 */
    private static final int DEFAULT_ORDER_STATUS = 0;
    /** 交付模式 */
    private static final String DELIVERY_PATTERN = "deliveryPattern";
    /** 交付模式 为 1京仓发货 */
    private static final String DELIVERY_PATTERN_ONE = "1";
    /** 交付模式 为 2 */
    private static final String DELIVERY_PATTERN_TWO = "2";
    /** 拓展字段 */
    private static final String SHIPMENT_EXTEND_PROPS ="shipmentExtendProps";
    /** eclp */
    private static final String ECLP ="eclp";

    /**
     * @description 功能描述:  逆向单询价查询青龙基础资料获取起始省市县facade
     * <AUTHOR>
     * @date 2021/7/1 9:33
     * @param
     * @throws
     * @return
     */
    @Resource
    private AddressBasicPrimaryWSFacade addressBasicPrimaryWSFacade;

    /**
     * @description 功能描述: 逆向单询价查询青龙基础资料获取起始省市县facade转换器
     * <AUTHOR>
     * @date 2021/7/1 9:33
     * @param
     * @throws
     * @return
     */
    @Resource
    private AddressBasicPrimaryWSFacadeTranslator addressBasicPrimaryWSFacadeTranslator;

    /**
     * @description 功能描述: 逆向单询价转换器
     * <AUTHOR>
     * @date 2021/6/30 20:15
     * @param expressOrderContext 原单, reverseOrderModel 当前单
     * @throws
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest
     */
    public BillingEnquiryFacadeRequest toReverseBillingEnquiryFacadeRequest(ExpressOrderContext expressOrderContext, ExpressOrderModel reverseOrderModel) {
        //经胡智阳与计费确认，单次逆向和多次逆向给计费的扩展信息中的订单状态orderStatus都传0（单据状态 默认传0），不再做特殊区分处理
        return toBillingEnquiryFacadeRequest(expressOrderContext);
    }

    /**
     * 计费询价防腐层请求数据转换
     */
    public BillingEnquiryFacadeRequest toBillingEnquiryFacadeRequest(ExpressOrderContext expressOrderContext) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        facadeRequest.setOrderNo(orderModel.orderNo());
        //青龙业主编码和青龙业主号名称
        facadeRequest.setCustomerFacadeDto(toCustomerFacadeDto(orderModel));
        //关联单号，正向运单号
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getRefOrderInfoDelegate().getWaybillNo());
        facadeRequest.setRefOrderFacadeDto(refOrderFacadeDto);
        //客户、渠道
        facadeRequest.setChannelFacadeDto(toChannelFacadeDto(orderModel));
        //产品信息  用传过来的订单号查出来有都赋值进去
        facadeRequest.setProductFacadeDtoList(toProductFacadeDto(orderModel));
        // 总重量、总体积、
        facadeRequest.setCargoFacadeDto(toCargoFacadeDto(orderModel));
        //发件人信息
        facadeRequest.setConsignorFacadeDto(toConsignorFacadeDto(orderModel));
        //收件人信息
        facadeRequest.setConsigneeFacadeDto(toConsigneeFacadeDto(orderModel));
        //财务相关信息
        facadeRequest.setFinanceFacadeDto(toFinanceFacadeDto(orderModel));
        //设置拓展字段
        facadeRequest.setExtendProps(toExtendProps(orderModel));
        return facadeRequest;
    }

    /**
     * @description 功能描述: 添加拓展字段
     * <AUTHOR>
     * @date 2021/6/29 12:27
     * @param orderModel
     * @throws
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String, Object> toExtendProps(ExpressOrderModel orderModel) {
        //设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        //结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, extendParamToSettlementType(orderModel));
        //单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        //逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS,DEFAULT_ORDER_STATUS);
        // 仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, toDistributionType(orderModel));
        return extendParam;
    }

    /**
     * @description 功能描述:  仓配类型(0：纯配；1：仓配)
     *  接单字段deliveryPattern若为1京仓发货 则该字段赋值1
     *  若为2纯配则该字段赋值0；
     *  接单字段deliveryPattern若为空且systemSubCaller为eclp则该字段赋值为1，
     *  其他情况全部赋值0
     * <AUTHOR>
     * @date 2021/6/29 18:04
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    private Object toDistributionType(ExpressOrderModel orderModel) {
        // 配送的拓展字段 deliveryPattern 从这里获取
        Map<String, String> extendProps = orderModel.getShipment().getExtendProps();
        if(MapUtils.isNotEmpty(extendProps)){
            String shipmentExtendProps = extendProps.get(SHIPMENT_EXTEND_PROPS);
            if (shipmentExtendProps != null){
                Map map = JSONUtils.jsonToMap(shipmentExtendProps);
                if(MapUtils.isNotEmpty(map)){
                    String deliveryPattern = (String)map.get(DELIVERY_PATTERN);
                    if (DELIVERY_PATTERN_ONE.equals(deliveryPattern)){
                        return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                    }else if (DELIVERY_PATTERN_TWO.equals(deliveryPattern)){
                        return EnquiryConstants.DISTRIBUTION_PURE;
                    }else if (deliveryPattern == null && ECLP.equals(orderModel.getChannel().getSystemSubCaller())){
                        return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
                    }
                }
            }
        }
        return EnquiryConstants.DISTRIBUTION_PURE;

    }


    /**
     * @description 功能描述: 拓展信息之结算方式
     * <AUTHOR>
     * @date 2021/6/29 12:33
     * @param orderModel
     * @throws
     * @return java.lang.Object
     */
    private Object extendParamToSettlementType(ExpressOrderModel orderModel) {
        if (SettlementTypeEnum.CASH_ON_PICK == orderModel.getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_CASH_ON_PICK;
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY ==  orderModel.getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_CASH_ON_DELIVERY;
        } else if (SettlementTypeEnum.MONTHLY_PAYMENT ==  orderModel.getFinance().getSettlementType()) {
            return EnquiryConstants.SETTLEMENT_MONTHLY_PAYMENT;
        }
        return null;
    }

    /**
     * 补全计费结果信息
     */
    public void complementBillingResult(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();

        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getAmount());
        preAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPreAmount().getCurrencyCode());
        financeInfoDto.setPreAmount(preAmount);

        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
        discountAmount.setCurrencyCode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
        financeInfoDto.setDiscountAmount(discountAmount);

        //计费重量
        financeInfoDto.setBillingWeight(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingVolume());

        //计费类型
        financeInfoDto.setBillingMode(billingEnquiryFacadeResponse.getFinanceFacadeDto().getBillingMode());

        //积分信息
        financeInfoDto.setPointsInfoDto(billingEnquiryFacadeResponse.getFinanceFacadeDto().getPointsInfoDto());

        //费用明细
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : billingEnquiryFacadeResponse.getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
            FinanceDetailInfoDto detailInfoDto = new FinanceDetailInfoDto();
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount().getAmount());
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount().getCurrencyCode());
            detailInfoDto.setPreAmount(detailPreAmount);
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount().getAmount());
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount().getCurrencyCode());
            detailInfoDto.setDiscountAmount(detailDiscountAmount);
            detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            detailInfoDto.setCostName(detailFacadeDto.getCostName());
            detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            detailInfoDto.setProductName(detailFacadeDto.getProductName());
            detailInfoDto.setRemark(detailFacadeDto.getRemark());
            //折扣明细
            if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
                for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
                    discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());
                    Money money = new Money();
                    money.setAmount(discountInfoFacadeDto.getDiscountedAmount().getAmount());
                    money.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
                    discountInfoDto.setDiscountedAmount(money);
                    discountInfoDtos.add(discountInfoDto);
                }
                detailInfoDto.setDiscountInfoDtos(discountInfoDtos);
            }
            financeDetailInfoDtoList.add(detailInfoDto);
        }
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
        // 收款机构
        financeInfoDto.setCollectionOrgNo(billingEnquiryFacadeResponse.getFinanceFacadeDto().getCollectionOrgNo());
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

    /**
     * 客户信息门面转换
     * @param orderModel 订单模型
     * @return 客户信息门面
     */
    private BillingEnquiryFacadeRequest.CustomerFacadeDto toCustomerFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = new BillingEnquiryFacadeRequest.CustomerFacadeDto();
        Customer customer = orderModel.getCustomer();
        customerFacadeDto.setAccountNo(customer.getAccountNo());
        return customerFacadeDto;
    }

    /**
     * 产品列表门面dto转换
     * @param orderModel 订单模型
     * @return 产品列表门面dto
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDto(ExpressOrderModel orderModel) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        // 原来单的增值服务
        List<? extends IProduct> products = orderModel.getProductDelegate().getProducts();
        if (CollectionUtils.isNotEmpty(products)) {
            for (IProduct product : products) {
                // COd的不询价
                if (!AddOnProductEnum.getCodCode().contains(product.getProductNo()) &&
                        !AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode().equals(product.getProductNo())){
                    BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                    productFacadeDto.setProductNo(product.getProductNo());
                    productFacadeDto.setProductType(product.getProductType());
                    productFacadeDto.setParentNo(product.getParentNo());
                    productFacadeDto.setProductAttrs(product.getProductAttrs());
                    productFacadeDtos.add(productFacadeDto);
                }
            }
        }

        return productFacadeDtos;
    }

    /**
     * 客户、渠道
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ChannelFacadeDto toChannelFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ChannelFacadeDto channelFacadeDto = new BillingEnquiryFacadeRequest.ChannelFacadeDto();
        Channel channel = orderModel.getChannel();
        channelFacadeDto.setChannelNo(channel.getChannelNo());
        return channelFacadeDto;
    }



    /**
     * 收件人信息 传过来的orderModel
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();

        Consignee consignee = orderModel.getConsignee();

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        Address address = consignee.getAddress();
        if (address != null) {
            //收件人省
            addressFacadeDto.setProvinceNoGis(address.getProvinceNoGis());
            addressFacadeDto.setProvinceNo(address.getProvinceNo());
            // 收件人市
            addressFacadeDto.setCityNoGis(address.getCityNoGis());
            addressFacadeDto.setCityNo(address.getCityNo());
            //收件人县
            addressFacadeDto.setCountyNoGis(address.getCountyNoGis());
            addressFacadeDto.setCountyNo(address.getCountyNo());

        }
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        // 计费数量
        cargoFacadeDto.setTotalCargoQuantity(orderModel.getCargoDelegate().totalCargoQuantity());
        // 货品信息里的计算总体积
        cargoFacadeDto.setTotalCargoVolume(orderModel.getCargoDelegate().totalCargoVolume());
        // 货品信息里的计算总重量
        cargoFacadeDto.setTotalCargoWeight(orderModel.getCargoDelegate().totalCargoWeight());

        return cargoFacadeDto;
    }
    /**
     * 财务相关信息
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        // c2b的是用接单时间
        financeFacadeDto.setEnquireTime(orderModel.getOperateTime());
        //结算方式
        financeFacadeDto.setSettlementType(orderModel.getFinance().getSettlementType());

        // 抵扣信息 只有逆向
        // 下了运费保，抵扣编码是OFC传过来的，所以抵扣信息在当前订单的fiance信息
        if (null != orderModel.getFinance().getDeductionDelegate()) {
            financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) orderModel.getFinance().getDeductionDelegate().getDeductions()));
        }
        return financeFacadeDto;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductions
     * @return
     */
    private List<DeductionInfoDto> toDeductionInfoDtos(List<Deduction> deductions) {
        if (CollectionUtils.isEmpty(deductions)) {
            return null;
        }
        List<DeductionInfoDto> deductionInfoDtos = new ArrayList<>(deductions.size());
        deductions.forEach(deduction -> {
            if (deduction != null) {
                DeductionInfoDto dto = new DeductionInfoDto();
                //抵扣编码
                dto.setDeductionNo(deduction.getDeductionNo());
                // 抵扣金额
                dto.setDeductionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(deduction.getDeductionAmount()));
                //扩展信息
                dto.setExtendProps(deduction.getExtendProps());
                deductionInfoDtos.add(dto);
            }
        });
        return deductionInfoDtos;
    }

    /**
     * 发件人信息门面转换
     * @param orderModel 订单信息
     * @return BillingEnquiryFacadeRequest.ConsignorFacadeDto
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toConsignorFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();

        // 逆向单询价和寄付现结一样 根据终端揽收时传的站点ID，调青龙基础资料 获取cityId countryId provinceId
        // 若逆向新单传入了揽收站点ID，则用传入的揽收站点ID
        // 若未传入揽收站点ID则取该逆向单的原单的派送站点
        String stationNo = orderModel.getShipment().getEndStationNo();
        if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()){
            stationNo = getReturnEndStationNo(orderModel);
        }

        AddressBasicPrimaryWSFacadeRequest addressBasicPrimaryWSFacadeRequest = addressBasicPrimaryWSFacadeTranslator.toAddressBasicPrimaryWSFacadeRequest(stationNo);

        AddressBasicPrimaryWSFacadeResponse addressBasicPrimaryWSFacadeResponse = addressBasicPrimaryWSFacade.getBaseSiteBySiteId(Integer.valueOf(addressBasicPrimaryWSFacadeRequest.getSiteId()));
        //起始省
        addressFacadeDto.setProvinceNoGis(addressBasicPrimaryWSFacadeResponse.getProvinceId());
        addressFacadeDto.setProvinceNo(addressBasicPrimaryWSFacadeResponse.getProvinceId());
        // 起始市
        addressFacadeDto.setCityNoGis(addressBasicPrimaryWSFacadeResponse.getCityId());
        addressFacadeDto.setCityNo(addressBasicPrimaryWSFacadeResponse.getCityId());
        //起始县
        addressFacadeDto.setCountyNoGis(addressBasicPrimaryWSFacadeResponse.getCountryId());
        addressFacadeDto.setCountyNo(addressBasicPrimaryWSFacadeResponse.getCountryId());

        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 逆向单新单询价时，起始省市县赋值逻辑为：
     * 若逆向新单传入了揽收站点ID，则用传入的揽收站点ID
     * 若未传入揽收站点ID则取该逆向单的原单的派送站点
     *
     * @param orderModel 订单模型
     * @return 站点ID
     */
    private String getReturnEndStationNo(ExpressOrderModel orderModel){
        String stationNo = orderModel.getShipment().getStartStationNo();
        // 逆向单新单揽收站点非必填，因此需要判断是否为空
        if (StringUtils.isBlank(stationNo)) {
            //若未传入揽收站点ID则取该逆向单原单的末端站点endstationno调青龙基础资料查询获取省市县id；
            stationNo = orderModel.getOrderSnapshot().getShipment().getEndStationNo();
        }
        return stationNo;
    }

}

