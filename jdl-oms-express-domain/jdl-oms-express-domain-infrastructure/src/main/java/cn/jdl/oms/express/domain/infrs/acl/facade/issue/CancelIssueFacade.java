package cn.jdl.oms.express.domain.infrs.acl.facade.issue;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.ofc.api.oms.common.model.CancelOrderOfcRequest;
import cn.jdl.ofc.api.oms.common.model.CancelOrderOfcResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CancelWaybillFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CancelInterceptFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CancelInterceptFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CancelIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CancelIssueFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CancelIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CancelIssueRpcTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CancelWaybillFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.rpc.RpcResult;
import cn.jdl.oms.express.domain.infrs.acl.rpc.issue.ICancelIssueService;
import cn.jdl.oms.express.domain.spec.dict.CancelStatusEnum;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.shared.common.constant.ABConstants;
import cn.jdl.oms.express.shared.common.dict.HttpHeaderEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.exception.IssueBusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.jsf.gd.util.RpcContext;
import com.jd.ldop.center.api.ResponseDTO;
import com.jd.ldop.center.api.waybill.dto.WaybillCancelDTO;
import com.jd.ldop.tools.validator.ValidationResult;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @ProjectName：jdl-oms-express-c2c-infrastructure
 * @Package： cn.jdl.oms.express.domain.infrs.acl.facade.issue
 * @ClassName: CancelIssueFacade
 * @Description: 取消下发防腐层
 * @Author： liyong549
 * @CreateDate 2021/3/31 9:24
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Component
public class CancelIssueFacade {
    private static final Logger LOGGER = LoggerFactory.getLogger(CancelIssueFacade.class);

    /**
     * 防腐层取消下发参数转换
     */
    @Resource
    private CancelIssueFacadeTranslator facadeTranslator;
    /**
     * 取消下发服务参数转换
     */
    @Resource
    private CancelIssueRpcTranslator rpcTranslator;
    /**
     * 取消下发服务
     */
    @Resource
    private ICancelIssueService cancelIssueService;

    /**
     * 外单重复取消码
     */
    private static String REPEAT_CANCEL_CODE = "31042";


    /**
     * 取消下发
     *
     * @param request
     * @return
     * <AUTHOR>
     */
    public CancelIssueFacadeResponse cancelIssue(RequestProfile requestProfile, CancelIssueFacadeRequest request, OrderBusinessIdentity businessIdentity) {
        try {
            CancelOrderOfcRequest ofcRequest = rpcTranslator.toCancelExpressOrderOfcRequest(request);
            //给分流程jsf上线分传递达标标识
            RpcContext rpcContext = RpcContext.getContext();
            //下发履约标识
            rpcContext.setAttachment(HttpHeaderEnum.FULFILLMENT_UNIT.getCode(), businessIdentity.getFulfillmentUnit());
            //下发traceId
            rpcContext.setAttachment(HttpHeaderEnum.TRACE_ID.getCode(), requestProfile.getTraceId());
            //环境标识
            Optional.ofNullable(request.getExtendProps()).ifPresent(extendProps ->
                    rpcContext.setAttachment(ABConstants.ABENVIRONMENT_FLAG,
                            request.getExtendProps().get(ABConstants.ABENVIRONMENT_FLAG)));
            LOGGER.info("取消统一下发履约层rpcContext={}", JSONUtils.beanToJSONDefault(rpcContext));
            if (LOGGER.isDebugEnabled()){
                LOGGER.info("取消统一下发履约层rpcContext={}", JSONUtils.beanToJSONDefault(rpcContext));
            }

            RpcResult<CancelOrderOfcResponse> rpcResult = cancelIssueService.cancelOrder(requestProfile, ofcRequest);
            if (rpcResult.isSuccess()) {
                return facadeTranslator.toCancelIssueFacadeResponse(rpcResult.getResult());
            } else {
                if (rpcResult.getResult() != null) {
                    throw new IssueBusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_ISSUE_FAIL)
                            .withCustom("取消下发失败")
                            .withSubCode(rpcResult.getResult().getCode())
                            .withSubMessage(rpcResult.getResult().getMessage());
                } else {
                    throw new IssueBusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_ISSUE_FAIL)
                            .withCustom("取消下发失败");
                }
            }
        } catch (IssueBusinessDomainException e) {
            LOGGER.error("取消下发异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("取消下发异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.ORDER_ISSUE_FAIL, e).withCustom("取消（拦截）统一下发履约层异常");
        }
    }

    /**
     * 取消拦截下发
     */
    public CancelInterceptFacadeResponse cancelInterceptIssue(RequestProfile requestProfile, CancelInterceptFacadeRequest cancelInterceptFacadeRequest) {
        try {
            WaybillCancelDTO waybillCancelDTO = rpcTranslator.toCancelInterceptRequest(cancelInterceptFacadeRequest);
            RpcResult<ResponseDTO> rpcResult = cancelIssueService.cancelIntercept(requestProfile, waybillCancelDTO);
            CancelInterceptFacadeResponse cancelInterceptFacadeResponse = new CancelInterceptFacadeResponse();
            if (rpcResult == null || rpcResult.getResult() == null) {
                return null;
            }
            ResponseDTO responseDTO = rpcResult.getResult();
            if (CollectionUtils.isEmpty(responseDTO.getValidationResultList())) {
                return null;
            }
            List<ValidationResult> list = responseDTO.getValidationResultList();
            ValidationResult validationResult = list.get(0);
            if (validationResult == null) {
                return null;
            }
            cancelInterceptFacadeResponse.setCode(validationResult.getCode());
            cancelInterceptFacadeResponse.setMessage(validationResult.getMessage());
            switch (validationResult.getCode()) {
                //该运单已经取消成功
                //10000-取消成功（首次调用）
                //10001-该运单已经取消成功
                case "10000":
                case "10001":
                    cancelInterceptFacadeResponse.setCancelStatusEnum(CancelStatusEnum.CANCEL_SUCCESS);
                    break;
                //10003-拦截中（首次调用）
                //10002-该单已发起过拦截申请/处于拦截中
                case "10002":
                case "10003":
                    cancelInterceptFacadeResponse.setCancelStatusEnum(CancelStatusEnum.CANCELLING);
                    break;
                //10004-拦截或取消失败
                case "10004":
                default:
                    cancelInterceptFacadeResponse.setCancelStatusEnum(CancelStatusEnum.CANCEL_FAIL);
                    break;
            }
            return cancelInterceptFacadeResponse;
        } catch (IssueBusinessDomainException e) {
            LOGGER.error("取消拦截下发异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("取消拦截下发异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.ORDER_ISSUE_FAIL, e).withCustom("取消拦截下发异常");
        }
    }


    /**
     * 运单取消
     * @param request
     * @return
     */
    public CancelWaybillFacadeResponse cancelWaybill(RequestProfile requestProfile, CancelWaybillFacadeRequest request){
        try{
            WaybillCancelDTO waybillCancelDTO = rpcTranslator.toCancelWaybillRequest(request);
            RpcResult<ResponseDTO> rpcResult = cancelIssueService.cancelWaybill(requestProfile,waybillCancelDTO);
            CancelWaybillFacadeResponse cancelFacadeResponse = new CancelWaybillFacadeResponse();
            if (rpcResult.isSuccess()) {
                cancelFacadeResponse.setCancelStatusEnum(CancelStatusEnum.CANCEL_SUCCESS);
                return cancelFacadeResponse;
            } else {
                cancelFacadeResponse.setCancelStatusEnum(CancelStatusEnum.CANCEL_FAIL);
                if (rpcResult.getResult() != null) {
                    if (CollectionUtils.isNotEmpty(rpcResult.getResult().getValidationResultList())) {
                        ValidationResult validationResult = (ValidationResult) rpcResult.getResult().getValidationResultList().get(0);
                        if(REPEAT_CANCEL_CODE.equals(validationResult.getCode())){
                            cancelFacadeResponse.setCancelStatusEnum(CancelStatusEnum.CANCEL_SUCCESS);
                        }
                        cancelFacadeResponse.setCode(validationResult.getCode());
                        cancelFacadeResponse.setMessage(validationResult.getMessage());
                    } else {
                        cancelFacadeResponse.setCode(rpcResult.getResult().getStatusCode()+"");
                        cancelFacadeResponse.setMessage(rpcResult.getResult().getStatusMessage());
                    }
                    return cancelFacadeResponse;
                } else {
                    throw new IssueBusinessDomainException(UnifiedErrorSpec.BasisOrder.WAY_BILL_CANCEL_FAIL).
                            withCustom("运单取消失败");
                }
            }
        } catch (BusinessDomainException e) {
            LOGGER.error("取消拦截下发异常: {}", e.getMessage());
            throw e;
        } catch (Exception e){
            LOGGER.error("取消拦截下发异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.ORDER_ISSUE_FAIL, e).withCustom("运单取消服务异常");
        }

    }

}