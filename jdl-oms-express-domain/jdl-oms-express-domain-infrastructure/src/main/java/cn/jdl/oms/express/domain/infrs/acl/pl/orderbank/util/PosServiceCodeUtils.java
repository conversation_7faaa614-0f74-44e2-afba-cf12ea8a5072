package cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util;


import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;

import static cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_B2C;
import static cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER;

/**
 * @ClassName PosServiceCodeUtils
 * @Description 获取Pos应收ServiceCode工具类
 * <AUTHOR>
 * @Version 1.0
 */
public class PosServiceCodeUtils {
    /**
     * 获取serviceCode通用方法（新）
     *
     * @param orderModel 订单模型
     * @return ServiceCode
     */
    public static @Nullable
    String getPosServiceCodeByBusinessUnit(@NotNull ExpressOrderModel orderModel) {
        if (orderModel == null || orderModel.getOrderBusinessIdentity() == null) {
            return null;
        }

        BusinessUnitEnum bizUnit = BusinessUnitEnum.fromCode(orderModel.getOrderBusinessIdentity().getBusinessUnit());
        if (bizUnit == CN_JDL_FREIGHT_CONSUMER) {
            return "30JD1704";
        } else if (bizUnit == CN_JDL_B2C) {
            return "30JD1311";
        }
        return null;
    }
}
