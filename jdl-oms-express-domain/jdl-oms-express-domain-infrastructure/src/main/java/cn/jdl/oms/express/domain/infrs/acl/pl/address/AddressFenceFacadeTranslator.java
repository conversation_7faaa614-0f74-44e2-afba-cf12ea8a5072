package cn.jdl.oms.express.domain.infrs.acl.pl.address;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.dto.ConsigneeInfoDto;
import cn.jdl.oms.express.domain.dto.ConsignorInfoDto;
import cn.jdl.oms.express.domain.dto.FenceInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.vo.Address;
import com.jd.lbs.geofencing.api.dto.CustomPresortResponseDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Translator
public class AddressFenceFacadeTranslator {

    /**
     * 时效围栏：F2
     */
    private static final String AGING_BUSINESS_TYPE = "F2";

    /**
     * 疫情围栏：F3
     */
    private static final String RESTRICT_BUSINESS_TYPE = "F3";

    /**
     * 围栏类型
     */
    private static final List<String> BUSINESS_TYPES = Arrays.asList(AGING_BUSINESS_TYPE, RESTRICT_BUSINESS_TYPE);

    /**
     * 功能: 组装JD地址解析入参
     *
     * @param:
     * @return:
     * @throw:
     * @description: 使用场景为地址解析
     * @author: liufarui
     * @date: 2021/6/12 11:18 下午
     */
    public AddressFenceFacadeRequest toAddressFenceFacadeRequest(Address address, String businessId) {
        if (null == address || StringUtils.isBlank(address.getAddress()) || StringUtils.isBlank(address.getProvinceNoGis())) {
            return null;
        }
        AddressFenceFacadeRequest request = new AddressFenceFacadeRequest();
        request.setProvinceCode(address.getProvinceNoGis());
        request.setProvinceName(address.getProvinceNameGis());
        request.setCityCode(address.getCityNoGis());
        request.setCityName(address.getCityNameGis());
        request.setDistrictCode(address.getCountyNoGis());
        request.setDistrictName(address.getCountyNameGis());
        request.setTownCode(address.getTownNoGis());
        request.setTownName(address.getTownNameGis());
        request.setFullAddress(packageFullAddress(address));
        request.setBusinessId(businessId);
        request.setJdAddressCode(1);
        request.setLongitude(address.getLongitude());
        request.setLatitude(address.getLatitude());
        request.setBusinessTypes(BUSINESS_TYPES);

        return request;
    }

    /**
     * 组装全地址
     *
     * @param address
     * @return
     */
    private String packageFullAddress(Address address) {
        StringBuilder builder = new StringBuilder();
        if (StringUtils.isNotBlank(address.getProvinceNameGis())) {
            builder.append(address.getProvinceNameGis());
        }
        if (StringUtils.isNotBlank(address.getCityNameGis())) {
            builder.append(address.getCityNameGis());
        }
        if (StringUtils.isNotBlank(address.getCountyNameGis())) {
            builder.append(address.getCountyNameGis());
        }
        if (StringUtils.isNotBlank(address.getTownNameGis())) {
            builder.append(address.getTownNameGis());
        }
        if (StringUtils.isNotBlank(address.getAddress())) {
            builder.append(address.getAddress());
        } else {
            builder.append(address.getAddressGis());
        }
        return builder.toString();
    }

    /**
     * 功能: 补充收件人Gis地址围栏
     */
    public void complementConsignorAddressFence(ExpressOrderModel orderModel, List<CustomPresortResponseDto> customPresortResponseDtoList) {
        if (CollectionUtils.isEmpty(customPresortResponseDtoList)) {
            return;
        }
        List<FenceInfoDto> fenceInfos = new ArrayList<>(customPresortResponseDtoList.size());
        for (CustomPresortResponseDto responseDto : customPresortResponseDtoList) {
            FenceInfoDto fenceInfo = new FenceInfoDto();
            fenceInfo.setFenceId(responseDto.getFenceId());
            fenceInfo.setFenceType(responseDto.getBusinessType());
            fenceInfos.add(fenceInfo);
        }
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        ConsignorInfoDto consignorInfoDto = new ConsignorInfoDto();

        AddressInfoDto addressInfoDto = new AddressInfoDto();
        addressInfoDto.setFenceInfos(fenceInfos);
        consignorInfoDto.setAddressInfoDto(addressInfoDto);
        modelCreator.setConsignorInfo(consignorInfoDto);
        orderModel.getComplementModel().complementConsignorAddressFence(this, modelCreator);
    }

    /**
     * 功能: 补充收件人Gis地址围栏
     */
    public void complementConsigneeAddressFence(ExpressOrderModel orderModel, List<CustomPresortResponseDto> customPresortResponseDtoList) {
        if (CollectionUtils.isEmpty(customPresortResponseDtoList)) {
            return;
        }
        List<FenceInfoDto> fenceInfos = new ArrayList<>(customPresortResponseDtoList.size());
        for (CustomPresortResponseDto responseDto : customPresortResponseDtoList) {
            FenceInfoDto fenceInfo = new FenceInfoDto();
            fenceInfo.setFenceId(responseDto.getFenceId());
            fenceInfo.setFenceType(responseDto.getBusinessType());
            fenceInfos.add(fenceInfo);
        }
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();

        AddressInfoDto addressInfoDto = new AddressInfoDto();
        addressInfoDto.setFenceInfos(fenceInfos);
        consigneeInfoDto.setAddressInfoDto(addressInfoDto);
        modelCreator.setConsigneeInfo(consigneeInfoDto);
        orderModel.getComplementModel().complementConsigneeAddressFence(this, modelCreator);
    }
}
