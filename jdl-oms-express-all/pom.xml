<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.jdl.oms</groupId>
    <artifactId>jdl-oms-express-all</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <name>jdl-oms-express-all</name>
    <url>http://maven.apache.org</url>
    <modules>
        <module>../jdl-oms-express-shared-common</module>
        <module>../jdl-oms-express-client</module>
        <module>../jdl-oms-express-horizontal</module>
        <module>../jdl-oms-express-vertical-c2c</module>
        <module>../jdl-oms-express-vertical-o2o</module>
        <module>../jdl-oms-express-vertical-b2c</module>
        <module>../jdl-oms-express-vertical-c2b</module>
        <module>../jdl-oms-express-vertical-fs</module>
        <module>../jdl-oms-express-vertical-jxd</module>
        <module>../jdl-oms-express-vertical-ecp</module>
        <module>../jdl-oms-express-vertical-packing</module>
        <module>../jdl-oms-express-domain</module>
        <module>../jdl-oms-express-application</module>
        <module>../jdl-oms-express-web</module>
        <module>../jdl-oms-express-test</module>
        <module>../jdl-oms-express-vertical-tc</module>
        <module>../jdl-oms-express-vertical-uep</module>
        <module>../jdl-oms-express-vertical-las</module>
        <module>../jdl-oms-express-vertical-cc</module>
        <module>../jdl-oms-express-vertical-freight</module>
        <module>../jdl-oms-express-vertical-lm</module>
        <module>../jdl-oms-express-vertical-contract</module>
        <module>../jdl-oms-express-vertical-intl</module>
        <module>../jdl-oms-express-vertical-tms</module>
    </modules>
</project>
