package cn.jdl.oms.express.model;


import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.Map;

/**
 * @ProjectName： jdl-oms-express-client
 * @Package： cn.jdl.oms.express.client.dto
 * @ClassName: ReacceptExpressOrderRequest
 * @Description: 纯配订单重新受理统一申请入参
 * @Author： zhangmaojie
 * @CreateDate 2023/8/5 14:22 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
public class ReacceptExpressOrderRequest implements Serializable {

    private static final long serialVersionUID = -458820952124096368L;
    /**
     * 业务身份
     */
    private BusinessIdentity businessIdentity;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 渠道信息
     */
    private ChannelInfo channelInfo;

    /**
     * 操作类型
     * 50：快运C2C整车客户确认
     * 100：冷链整车询价确认、快运整车商家确认
     * 200：快运整车重新询价
     */
    private Integer operationType;
    /**
     * 操作人唯一标识
     */
    @Length(
            max = 50,
            message = "操作人唯一标识(operator)长度不能超过50"
    )
    private String operator;
    /**
     * 扩展字段
     */
    private Map<String, String> extendProps;

    /**
     * 派送信息
     */
    private ShipmentInfo shipmentInfo;

    public BusinessIdentity getBusinessIdentity() {
        return businessIdentity;
    }

    public void setBusinessIdentity(BusinessIdentity businessIdentity) {
        this.businessIdentity = businessIdentity;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public ChannelInfo getChannelInfo() {
        return channelInfo;
    }

    public void setChannelInfo(ChannelInfo channelInfo) {
        this.channelInfo = channelInfo;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Map<String, String> getExtendProps() {
        return extendProps;
    }

    public void setExtendProps(Map<String, String> extendProps) {
        this.extendProps = extendProps;
    }

    public ShipmentInfo getShipmentInfo() {
        return shipmentInfo;
    }

    public void setShipmentInfo(ShipmentInfo shipmentInfo) {
        this.shipmentInfo = shipmentInfo;
    }
}
