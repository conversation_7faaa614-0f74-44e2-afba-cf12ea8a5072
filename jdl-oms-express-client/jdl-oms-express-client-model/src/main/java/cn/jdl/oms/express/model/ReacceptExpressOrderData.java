package cn.jdl.oms.express.model;

import java.io.Serializable;
import java.util.Map;

/**
 * @ProjectName： jdl-oms-express-client
 * @Package： cn.jdl.oms.express.client.dto.data
 * @ClassName: ReacceptExpressOrderData
 * @Description: 订单中心纯配订单重新受理返回数据对象
 * @Author： <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate 2023/8/5 14:36 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
public class ReacceptExpressOrderData implements Serializable {

    private static final long serialVersionUID = -6414301594053615676L;

    /**
     * 重新受理结果
     * 1【再受理成功】
     * 2【再受理失败】
     */
    private Integer code;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 扩展信息
     */
    private Map<String, String> extendProps;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Map<String, String> getExtendProps() {
        return extendProps;
    }

    public void setExtendProps(Map<String, String> extendProps) {
        this.extendProps = extendProps;
    }
}
