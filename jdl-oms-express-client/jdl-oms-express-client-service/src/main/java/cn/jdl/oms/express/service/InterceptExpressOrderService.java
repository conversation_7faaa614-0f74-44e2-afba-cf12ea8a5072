package cn.jdl.oms.express.service;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.model.InterceptExpressOrderBatchRequest;
import cn.jdl.oms.express.model.InterceptExpressOrderBatchResponse;
import cn.jdl.oms.express.model.InterceptExpressOrderRequest;
import cn.jdl.oms.express.model.InterceptExpressOrderResponse;

/**
 * 订单中心纯配订单拦截标准服务
 *
 * @author: lufahai
 * @Date: 2021/5/26
 * @Version 1.0
 * https://cf.jd.com/pages/viewpage.action?pageId=503947250
 */
public interface InterceptExpressOrderService {

    /**
     * 订单中心纯配订单拦截标准服务
     *
     * @param requestProfile 用于处理国际化多语言、多租户、业务身份以及未来扩展的需求
     * @param request        纯配拦截服务申请入参对象
     * @return InterceptExpressOrderResponse
     */
    InterceptExpressOrderResponse interceptOrder(RequestProfile requestProfile, InterceptExpressOrderRequest request);


    /**
     * @param requestProfile 用于处理国际化多语言、多租户、业务身份以及未来扩展的需求
     * @param request        纯配批量拦截服务申请入参对象
     * @return  InterceptExpressOrderBatchResponse
     */
    public InterceptExpressOrderBatchResponse interceptBatchOrder(RequestProfile requestProfile, InterceptExpressOrderBatchRequest request);

}
