package cn.jdl.oms.express.service;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.model.CancelExpressOrderBatchRequest;
import cn.jdl.oms.express.model.CancelExpressOrderBatchResponse;
import cn.jdl.oms.express.model.CancelExpressOrderRequest;
import cn.jdl.oms.express.model.CancelExpressOrderResponse;

/**
 * @ProjectName： jdl-oms-express
 * @Package： cn.jdl.oms.express.client.api
 * @ClassName: CancelExpressOrderService
 * @Description: 订单中心纯配订单取消标准服务
 * @Author： wangjingzhao
 * @CreateDate 2021/3/8 8:01 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 * https://cf.jd.com/pages/viewpage.action?pageId=440443539
 */
public interface CancelExpressOrderService {


    /**
     * @param requestProfile 用于处理国际化多语言、多租户、业务身份以及未来扩展的需求
     * @param request        纯配取消服务申请入参对象
     * @return CancelExpressOrderResponse
     * 取消结果返回对象
     * @throws
     * @throws
     * @Description 订单中心纯配订单取消标准服务
     * <AUTHOR>
     * @createDate 2021/3/8 8:31 下午
     * @lastModify
     */
    public CancelExpressOrderResponse cancelOrder(RequestProfile requestProfile, CancelExpressOrderRequest request);



    /**
     *
     *
     * @Description 订单中心纯配订单批量取消标准服务
     *
     * <AUTHOR>
     * @createDate  2021/5/19 8:57 上午
     *
     * @param requestProfile 用于处理国际化多语言、多租户、业务身份以及未来扩展的需求
     * @param request        纯配取消服务申请入参对象
     *
     * @return  CancelExpressOrderBatchResponse
     *
     * @exception
     *
     * @throws
     *
     * @lastModify
     *
     */
    public CancelExpressOrderBatchResponse cancelBatchOrder(RequestProfile requestProfile, CancelExpressOrderBatchRequest request);

}
